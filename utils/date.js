// 原生 JavaScript 实现日期格式化功能，避免外部依赖

// 格式化日期
export const formatDate = timestamp => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()

  // 判断是否是同一天
  const isSameDay = (date1, date2) => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    )
  }

  // 判断是否是昨天
  const isYesterday = (date1, date2) => {
    const yesterday = new Date(date2)
    yesterday.setDate(yesterday.getDate() - 1)
    return isSameDay(date1, yesterday)
  }

  if (isSameDay(date, now)) {
    return '今天'
  }

  if (isYesterday(date, now)) {
    return '昨天'
  }

  if (date.getFullYear() === now.getFullYear()) {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }

  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}

// 格式化时间
export const formatTime = timestamp => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()

  // 判断是否是同一天
  const isSameDay = (date1, date2) => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    )
  }

  // 判断是否是昨天
  const isYesterday = (date1, date2) => {
    const yesterday = new Date(date2)
    yesterday.setDate(yesterday.getDate() - 1)
    return isSameDay(date1, yesterday)
  }

  // 格式化时分
  const formatHourMinute = date => {
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }

  if (isSameDay(date, now)) {
    return formatHourMinute(date)
  }

  if (isYesterday(date, now)) {
    return `昨天 ${formatHourMinute(date)}`
  }

  if (date.getFullYear() === now.getFullYear()) {
    return `${date.getMonth() + 1}月${date.getDate()}日 ${formatHourMinute(date)}`
  }

  return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()} ${formatHourMinute(date)}`
}
// 格式化列表时间（非当天不显示时分）
export const formatListTime = timestamp => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()

  // 判断是否是同一天
  const isSameDay = (date1, date2) => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    )
  }

  // 判断是否是昨天
  const isYesterday = (date1, date2) => {
    const yesterday = new Date(date2)
    yesterday.setDate(yesterday.getDate() - 1)
    return isSameDay(date1, yesterday)
  }

  // 格式化时分
  const formatHourMinute = date => {
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }

  if (isSameDay(date, now)) {
    return formatHourMinute(date)
  }

  if (isYesterday(date, now)) {
    return `昨天`
  }

  if (date.getFullYear() === now.getFullYear()) {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }

  return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
}
// 格式化相对时间
export const formatRelativeTime = timestamp => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}
