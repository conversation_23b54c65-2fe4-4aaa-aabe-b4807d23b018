import { WKSDK, MessageContent } from 'wukongimjssdk'
class LocationContent extends MessageContent {
  lng: number = 0 // 纬度
  lat: number = 0 // 经度
  title!: string // 位置标题
  address!: string // 具体地址
  img!: string // 封面图远程地址
  contentType: number = 101 // 自定义消息类型
  // 解码
  decodeJSON(content: any) {
    this.lng = content['lng'] || 0
    this.lat = content['lat'] || 0
    this.title = content['title'] || ''
    this.address = content['address'] || ''
    this.img = content['img'] || ''
  }
  // 编码
  encodeJSON() {
    return { lng: this.lng, lat: this.lat, title: this.title, address: this.address, img: this.img }
  }
}
const contentTypeLocation = 101 // 自定义消息类型
WKSDK.shared().register(contentTypeLocation, () => new LocationContent())
export default LocationContent
