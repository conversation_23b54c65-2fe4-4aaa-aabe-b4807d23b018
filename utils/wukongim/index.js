import {
  WKSDK,
  ChannelInfo,
  ConnectStatus,
  Conversation,
  ConversationExtra,
  Message,
  MessageText,
  Channel,
  ChannelTypePerson,
  ChannelTypeGroup,
  MessageImage,
  Subscriber,
  Mention,
  MessageExtra,
  SendackPacket,
  SendOptions,
  Setting
} from 'wukongimjssdk'
import LocationContent from './LocationContent'
import { ref } from 'vue'
import { conversationApi, groupsApi } from '@/common/api'
import { useUserStore } from '@/stores/user'
import { useConversationStore } from '@/stores/conversation'
import { useGroupStore } from '@/stores/group'
import { storeToRefs } from 'pinia'

class WKIMManager {
  constructor() {
    if (!WKIMManager.instance) {
      this.init()
      WKIMManager.instance = this
    }
    return WKIMManager.instance
  }

  init() {
    // 初始化SDK
    const client = WKSDK.shared()
    // setTimeout(() => {
    //   console.log('WKSDK.shared().config--------------->', client.config)
    //   window.client = client
    // }, 10000)
    this.sdk = client

    // 连接状态
    this.connectionStatus = ref('disconnected')

    // 消息监听器
    this.messageListeners = new Set()

    // 连接状态监听器
    this.connectionListeners = new Set()
    this.conversationStore = null

    // 设置监听器
    this.setupListeners()

    // 设置数据源提供者
    this.sdk.config.provider = {
      // 同步会话数据源
      syncConversationsCallback: async () => {
        try {
          const userStore = useUserStore()
          const conversationStore = useConversationStore()
          const currentUID = userStore.userInfo?.userId

          // 先获取置顶会话列表，避免列表闪烁
          let pinnedChannelIds = []
          try {
            const pinnedResponse = await conversationApi.getTopConversation({
              uid: currentUID
            })
            if (
              pinnedResponse &&
              pinnedResponse.code === 200 &&
              Array.isArray(pinnedResponse.data)
            ) {
              pinnedChannelIds = pinnedResponse.data
              // 更新store中的置顶ID列表
              conversationStore.pinnedChannelIds = pinnedChannelIds
            }
          } catch (error) {
            console.error('获取置顶会话列表失败:', error)
          }

          // 获取最近会话列表
          let hisorySessions = await conversationApi.syncConversations({
            uid: currentUID, // 使用 Pinia store 中的用户 uid
            version: 0, //  当前客户端的会话最大版本号(从保存的结果里取最大的version，如果本地没有数据则传0)，
            last_msg_seqs: '', //   客户端所有频道会话的最后一条消息序列号拼接出来的同步串 格式： channelID:channelType:last_msg_seq|channelID:channelType:last_msg_seq  （此字段非必填，如果不填就获取全量数据，填写了获取增量数据，看你自己的需求。）
            msg_count: 10 // 每个会话获取最大的消息数量，一般为app点进去第一屏的数据
          })
          const groupHistory = hisorySessions.filter(v => v.channel_type == 2)
          hisorySessions = hisorySessions.filter(v => v.channel_type == 1)
          if (hisorySessions.length > 0) {
            this.handleGroupHistory(groupHistory)
          }
          const usersRes = await conversationApi.getUserInfo(hisorySessions.map(v => v.channel_id))
          // 将对象格式转换为数组，并将 key 作为 uid 添加到对象中
          const users = Object.entries(usersRes.data).map(([uid, userData]) => ({
            ...userData,
            img: userData.img.includes('http')
              ? userData.img
              : 'http://47.123.3.183:9000/' + userData.img,
            uid
          }))
          const res = {
            conversations: hisorySessions,
            users
          }
          const conversations = []
          // Convert conversations
          if (res.conversations && res.conversations.length > 0) {
            for (const convMap of res.conversations) {
              const conversation = new Conversation()
              conversation.channel = new Channel(convMap.channel_id, convMap.channel_type)
              conversation.unread = convMap.unread || 0
              conversation.timestamp = convMap.timestamp || 0
              // 设置置顶状态
              conversation.isPinned = pinnedChannelIds.includes(convMap.channel_id)
              conversation.recents = convMap.recents.map(v => {
                v.timestamp = v.timestamp * 1000
                v.payload = this.decodeMessageContent(v.payload)
                v.type = this.forMateMsgType(v.payload)
                v.content = v.type === 'text' ? v.payload.content : v.payload
                v.fromUID = v.from_uid
                v.avatar =
                  userStore.userInfo?.userId == v.from_uid
                    ? ''
                    : usersRes.data[v.from_uid].img.includes('http')
                      ? user.img
                      : 'http://47.123.3.183:9000/' + usersRes.data[v.from_uid].img

                return v
              })
              conversation.recents = conversation.recents.reverse()
              const user = res.users.find(u => u.uid === convMap.channel_id)
              if (user) {
                conversation.channel.channelID = user.uid
                conversation.channel.channelType = '1'
                conversation.channel.title = user.nickname
                conversation.channel.logo = user.img.includes('http')
                  ? user.img
                  : 'http://47.123.3.183:9000/' + user.img
                conversation.channel.orgData = user // 保存原始数据
                conversation.channel.avatar = user.img.includes('http')
                  ? user.img
                  : 'http://47.123.3.183:9000/' + user.img
              }
              // const group = res.groups.find(u => u.group_no === convMap.channel_id)
              // if (group) {
              //   conversation.channel.channelID = group.group_no
              //   conversation.channel.channelType = '2'
              //   conversation.channel.title = group.name
              //   conversation.channel.logo = group.avatar || ''
              //   conversation.channel.orgData = group // 保存原始数据
              // }
              // Handle recent messages
              if (convMap.recents && convMap.recents.length > 0) {
                const recent = convMap.recents[0]
                const message = new Message()
                message.messageID = recent.message_id
                message.messageSeq = recent.message_seq
                message.clientMsgNo = recent.client_msg_no
                message.fromUID = recent.from_uid
                message.timestamp = recent.timestamp
                message.payload = recent.payload
                message.content = recent.content
                message.type = recent.type
                message.messageSeq = recent.message_seq
                conversation.lastMessage = message
              }
              // console.log('conversation--->', conversation)
              conversations.push(conversation)
            }
          }

          // 分离置顶和非置顶会话
          const pinned = conversations.filter(conv => conv.isPinned)
          const unpinned = conversations.filter(conv => !conv.isPinned)

          // 按时间戳排序（最新的在前面）
          pinned.sort((a, b) => b.timestamp - a.timestamp)
          unpinned.sort((a, b) => b.timestamp - a.timestamp)

          // 合并列表，置顶的在前面
          const conversationsNew = [...pinned, ...unpinned]

          conversationStore.setConversationList(conversationsNew || [])
          return conversationsNew
        } catch (error) {
          console.error('同步会话失败:', error)
          return []
        }
      },
      // 同步频道消息数据源
      syncMessagesCallback: async (channel, opts) => {
        try {
          debugger
          const userStore = useUserStore()
          const conversationStore = useConversationStore()
          const currentUID = userStore.userInfo?.userId
          // TODO: 这里应该调用你的后端API来获取消息列表
          console.log('同步消息请求:', channel, opts)
          const hisorySessions = await conversationApi.syncMessages({
            login_uid: currentUID, // 当前登录用户uid
            channel_id: channel.channelID, //  频道ID
            channel_type: channel.channelType, // 频道类型
            ...opts
          })
          const messages = hisorySessions.messages.map(v => {
            v.timestamp = v.timestamp * 1000
            v.payload = this.decodeMessageContent(v.payload)
            v.type = this.forMateMsgType(v.payload)
            v.content = v.type === 'text' ? v.payload.content : v.payload
            v.fromUID = v.from_uid
            v.avatar = ''
            return v
          })

          return {
            messages: messages.reverse(),
            hasMore: hisorySessions.more === 1
          }
        } catch (error) {
          console.error('同步消息失败:', error)
          return {
            messages: [],
            hasMore: false
          }
        }
      },
      // 发送确认回调
      sendackCallback: packet => {
        console.log('消息发送确认 clientSeq->', packet.clientSeq)
        if (packet.reasonCode === 1) {
          console.log('消息发送成功')
          // 发送成功，可以更新本地消息状态
        } else {
          console.error('消息发送失败, reasonCode:', packet.reasonCode)
          // 发送失败处理
        }
      },
      // 获取频道资料数据源
      channelInfoCallback: async channel => {
        try {
          // TODO: 这里应该调用你的后端API来获取频道资料
          const channelInfo = new ChannelInfo()
          if (channel.channelType === ChannelTypePerson) {
            // 如果是个人频道
            const user = res.users.find(u => u.uid === channel.channelID)
            if (user) {
              channelInfo.channelID = user.uid
              channelInfo.channelType = ChannelTypePerson
              channelInfo.title = user.name
              channelInfo.logo = user.avatar || ''
              channelInfo.orgData = user // 保存原始数据
            }
          } else if (channel.channelType === ChannelTypeGroup) {
            // 如果是群组频道
            const group = res.groups.find(g => g.group_no === channel.channelID)
            if (group) {
              channelInfo.channelID = group.group_no
              channelInfo.channelType = ChannelTypeGroup
              channelInfo.title = group.name
              channelInfo.logo = group.avatar || ''
              channelInfo.orgData = group // 保存原始数据
            }
          }
          return channelInfo
        } catch (error) {
          console.error('获取频道资料失败:', error)
          return new ChannelInfo()
        }
      },
      // 同步频道订阅者数据源
      syncSubscribersCallback: async (channel, version) => {
        try {
          // TODO: 这里应该调用你的后端API来获取频道订阅者列表
          const subscribers = []

          // 示例：如果是群组频道，使用示例数据构建订阅者列表
          if (channel.channelType === ChannelTypeGroup) {
            // 假设res.users中的用户都是群成员
            for (const user of res.users) {
              const subscriber = new Subscriber()
              subscriber.uid = user.uid
              subscriber.name = user.name
              subscriber.role = user.role || 0 // 角色，如：0-普通成员，1-管理员，2-群主
              subscriber.status = user.status || 1 // 状态，如：1-正常，2-已退出
              subscriber.version = version
              subscriber.orgData = user // 保存原始数据
              subscribers.push(subscriber)
            }
          }

          return subscribers
        } catch (error) {
          console.error('同步频道订阅者失败:', error)
          return []
        }
      }
    }
  }
  async handleGroupHistory(hisorySessions) {
    let list = []
    let groupRes = await groupsApi.getGroups({})
    groupRes = groupRes.rows
    // 确保groupRes是数组
    if (!Array.isArray(groupRes)) {
      console.error('获取群组列表失败:', groupRes)
      return
    }

    // 过滤出与会话相关的群组
    list = hisorySessions.filter(v => groupRes.some(g => g.channelId == v.channel_id))

    const res = {
      conversations: list,
      groupRes
    }
    const conversations = []
    // Convert conversations
    if (res.conversations && res.conversations.length > 0) {
      for (const convMap of res.conversations) {
        const conversation = new Conversation()
        conversation.channel = new Channel(convMap.channel_id, convMap.channel_type)
        conversation.unread = convMap.unread || 0
        conversation.timestamp = convMap.timestamp || 0

        // 找到对应的群组信息
        const groupInfo = res.groupRes.find(g => g.channelId == convMap.channel_id)
        if (groupInfo) {
          conversation.groupName = groupInfo.groupName
          conversation.groupImg = groupInfo.groupImg.includes('http')
            ? groupInfo.groupImg
            : 'http://47.123.3.183:9000/' + groupInfo.groupImg
          conversation.groupCode = groupInfo.groupCode
          conversation.channelId = groupInfo.channelId
          conversation.memberCount = groupInfo.memberCount
          conversation.applyStatus = groupInfo.applyStatus
        }

        if (convMap.recents && convMap.recents.length > 0) {
          const recent = convMap.recents[0]
          const message = new Message()
          message.messageID = recent.message_id
          message.messageSeq = recent.message_seq
          message.clientMsgNo = recent.client_msg_no
          message.fromUID = recent.from_uid
          message.timestamp = recent.timestamp * 1000 // 转换为毫秒

          // 解码消息内容并设置类型
          const decodedPayload = this.decodeMessageContent(recent.payload)
          message.payload = decodedPayload
          message.type = this.forMateMsgType(decodedPayload)

          // 根据消息类型设置内容
          if (message.type === 'text' && decodedPayload && decodedPayload.content) {
            message.content = decodedPayload.content
          } else if (message.type === 'image') {
            message.content = '[图片]'
          } else if (message.type === 'voice') {
            message.content = '[语音]'
          } else if (message.type === 'location') {
            message.content = '[位置]'
          } else {
            message.content = '[未知消息类型]'
          }

          message.messageSeq = recent.message_seq

          conversation.lastMessage = message
          conversation.lastSpeaker = recent.from_uid
          conversation.lastMessageTime = recent.timestamp * 1000 // 转换为毫秒

          // 加载群成员信息，以便后续显示发送者昵称
          if (conversation.groupCode) {
            const groupStore = useGroupStore()
            groupStore.fetchAndStoreGroupMembers(conversation.groupCode)
          }
        }

        // 保存原始群组数据
        conversation.groupRes = res.groupRes.find(g => g.channelId == convMap.channel_id)

        console.log('处理后的群组会话:', conversation)
        conversations.push(conversation)
      }
    }

    // 如果没有会话数据，但有群组数据，直接使用群组数据
    if (conversations.length === 0 && res.groupRes.length > 0) {
      for (const group of res.groupRes) {
        const conversation = new Conversation()
        conversation.channel = new Channel(group.channelId, 2)
        conversation.groupName = group.groupName
        conversation.groupImg = group.groupImg.includes('http')
          ? group.groupImg
          : 'http://47.123.3.183:9000/' + group.groupImg
        conversation.groupCode = group.groupCode
        conversation.channelId = group.channelId
        conversation.memberCount = group.memberCount
        conversation.applyStatus = group.applyStatus
        conversation.timestamp = new Date(group.enterDate || Date.now()).getTime()
        conversation.groupRes = group

        // 加载群成员信息
        if (conversation.groupCode) {
          const groupStore = useGroupStore()
          groupStore.fetchAndStoreGroupMembers(conversation.groupCode)
        }

        conversations.push(conversation)
      }
    }

    const conversationsNew = conversations.sort((a, b) => b.timestamp - a.timestamp)
    const groupStore = useGroupStore()
    groupStore.setGroupHistoryList(conversationsNew)
  }

  // 处理接收到的消息
  async handleMessageReceived(message) {
    console.log('🔍 全局收到消息:', message)
    try {
      // 使用SDK的conversationManager直接获取会话列表
      const conversations = await this.sdk.conversationManager.sync()
      console.log('获取会话列表:', conversations)

      // 保留置顶状态
      if (conversations && conversations.length > 0 && this.conversationStore) {
        // 获取现有的置顶ID列表
        const pinnedChannelIds = this.conversationStore.pinnedChannelIds || []

        // 为新的会话列表设置置顶状态
        conversations.forEach(conv => {
          conv.isPinned = pinnedChannelIds.includes(conv.channel.channelID)
        })

        // 分离置顶和非置顶会话
        const pinned = conversations.filter(conv => conv.isPinned)
        const unpinned = conversations.filter(conv => !conv.isPinned)

        // 按时间戳排序
        pinned.sort((a, b) => b.timestamp - a.timestamp)
        unpinned.sort((a, b) => b.timestamp - a.timestamp)

        // 更新会话存储，保持置顶会话在前面
        this.conversationStore.updateConversations([...pinned, ...unpinned])
      }

      // 更新TabBar未读消息数量
      this.updateTabBarBadge()
    } catch (error) {
      console.error('处理消息时获取会话列表失败:', error)
    }
  }

  // 更新TabBar未读消息数量
  async updateTabBarBadge() {
    try {
      console.log('WKIMManager: 更新TabBar未读消息数量')

      // 获取所有未读消息数量
      const unreadCount = this.getAllUnreadCount()
      console.log('SDK未读消息数量:', unreadCount)

      // 获取群组未读消息数量
      const groupStore = useGroupStore()
      const conversationStore = useConversationStore()

      // 使用store中的方法获取未读消息数量
      const messageUnreadCount = conversationStore.getAllUnreadCount()
      const groupUnreadCount = groupStore.getAllGroupUnreadCount()

      // 计算总未读数量
      const totalUnreadCount = messageUnreadCount + groupUnreadCount
      console.log(
        '总未读消息数量:',
        totalUnreadCount,
        '(消息:',
        messageUnreadCount,
        ', 群组:',
        groupUnreadCount,
        ')'
      )

      // 更新TabBar角标
      if (totalUnreadCount > 0) {
        console.log('设置TabBar角标:', totalUnreadCount)
        uni.setTabBarBadge({
          index: 2, // 消息选项卡的索引
          text: totalUnreadCount > 99 ? '99+' : totalUnreadCount.toString(),
          success: () => {
            console.log('WKIMManager: 设置TabBar角标成功')
          },
          fail: err => {
            console.error('WKIMManager: 设置TabBar角标失败:', err)
          }
        })
      } else {
        // 如果没有未读消息，移除角标
        uni.removeTabBarBadge({
          index: 2,
          success: () => {
            console.log('WKIMManager: 移除TabBar角标成功')
          },
          fail: err => {
            console.error('WKIMManager: 移除TabBar角标失败:', err)
          }
        })
      }
    } catch (error) {
      console.error('WKIMManager: 更新未读消息角标失败:', error)
    }
  }
  setupListeners() {
    // 消息监听
    this.sdk.chatManager.addMessageListener(message => {
      // 通知所有注册的监听器
      this.messageListeners.forEach(listener => {
        try {
          // 使用bind确保正确的this上下文
          listener.bind(this)(message)
        } catch (error) {
          console.error('消息监听器处理出错:', error)
        }
      })
    })

    // 连接状态监听
    this.sdk.connectManager.addConnectStatusListener((status, reasonCode) => {
      console.log('ConnectStatus--->', ConnectStatus)
      console.log('连接状态变化:', status, '原因码:', reasonCode)

      if (status === 1 || status === 2) {
        this.connectionStatus.value = 'connected'
        console.log('✅ WuKongIM连接成功')
      } else if (status === '0') {
        this.connectionStatus.value = 'connecting'
        console.log('🔄 WuKongIM连接中...')
      } else if (status === ConnectStatus.disconnect) {
        this.connectionStatus.value = 'disconnected'
        console.log('❌ WuKongIM连接断开, reasonCode:', reasonCode)

        // 详细的错误信息
        let errorMsg = '连接断开'
        switch (reasonCode) {
          case 1006:
            errorMsg = '连接异常关闭，可能是网络问题或服务器不可达'
            break
          case 2:
            errorMsg = '认证失败，请检查uid和token是否正确'
            break
          case 1000:
            errorMsg = '正常关闭连接'
            break
          case 1001:
            errorMsg = '端点离开'
            break
          case 1002:
            errorMsg = '协议错误'
            break
          case 1003:
            errorMsg = '不支持的数据类型'
            break
          default:
            errorMsg = `未知错误，错误码: ${reasonCode}`
        }
        console.error('连接错误详情:', errorMsg)

        // 如果是网络问题，尝试重连
        if (reasonCode === 1006) {
          console.log('检测到网络连接问题，3秒后尝试重连...')
          setTimeout(() => {
            if (this.connectionStatus.value === 'disconnected') {
              console.log('开始自动重连...')
              this.sdk.connectManager.connect()
            }
          }, 3000)
        }
      }

      this.connectionListeners.forEach(listener => listener(status, reasonCode))
    })
  }
  async initConnect() {
    try {
      this.conversationStore = useConversationStore()
      const imConfig = uni.getStorageSync('im_config')
      if (!imConfig) {
        console.log('未找到IM配置信息')
        return
      }

      const { wsUrl, userId, token } = imConfig
      console.log('初始化悟空IM:', { wsUrl, userId })

      // 添加消息监听器 - 绑定this上下文
      this.addMessageListener(this.handleMessageReceived.bind(this))

      // 添加连接状态监听器
      this.addConnectionListener((status, reasonCode) => {
        console.log('IM连接状态变化:', status, '原因码:', reasonCode)
      })

      // 添加会话监听器 - 当会话列表发生变化时更新未读消息数量
      this.addConversationListener((conversation, action) => {
        // 延迟更新TabBar角标，确保状态已更新
        setTimeout(() => {
          this.updateTabBarBadge()
        }, 100)
      })

      // 连接悟空IM
      await this.connect(userId, token, wsUrl)
      console.log('✅ WuKongIM连接成功')

      // 获取会话列表 - 不需要单独获取置顶会话，因为syncConversationsCallback已经处理了
      const conversations = await this.getConversationList()
      console.log('获取会话列表:', conversations)

      // 更新会话存储
      if (conversations && conversations.length > 0) {
        this.conversationStore.updateConversations(conversations)
      }
    } catch (error) {
      console.error('初始化悟空IM失败:', error)
    }
  }
  // 检测网络连接
  async checkNetworkConnection(wsUrl) {
    try {
      console.log('🔍 检测网络连接...')

      // 如果wsUrl包含协议，直接使用；否则添加默认协议
      let fullWsUrl = wsUrl
      if (!wsUrl.startsWith('ws://') && !wsUrl.startsWith('wss://')) {
        fullWsUrl = 'ws://' + wsUrl
      }

      // 提取主机和端口
      const url = new URL(fullWsUrl.replace('ws://', 'http://').replace('wss://', 'https://'))
      const host = url.hostname
      const port = url.port || (fullWsUrl.startsWith('wss://') ? 443 : 80)

      console.log(`📡 检测服务器: ${host}:${port}`)

      // 尝试HTTP请求来检测服务器是否可达
      const testUrl = `${url.protocol}//${host}:${port}/health`

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)

      try {
        const response = await fetch(testUrl, {
          method: 'GET',
          signal: controller.signal,
          mode: 'no-cors' // 避免CORS问题
        })
        clearTimeout(timeoutId)
        console.log('✅ 服务器可达')
        return true
      } catch (fetchError) {
        clearTimeout(timeoutId)
        console.log('⚠️ HTTP检测失败，但这可能是正常的（CORS限制）')
        return true // 不因为CORS失败而判断网络不通
      }
    } catch (error) {
      console.error('❌ 网络检测失败:', error.message)
      return false
    }
  }

  // 连接服务器
  connect(uid, token, wsUrl = null) {
    console.log('token------------<', token)
    const finalWsUrl = wsUrl || process.env.VUE_APP_WUKONG_WS_URL || 'ws://1903.eu.org:5200'
    this.sdk.config.addr = finalWsUrl
    this.sdk.config.uid = uid
    ;(this.sdk.config.token = uid), //token
      (this.sdk.config.deviceFlag = 0)
    this.sdk.config.deviceLevel = 0
    this.sdk.connectManager.connect()
  }

  // 断开连接
  disconnect() {
    this.sdk.connectManager.disconnect()
  }

  // 发送消息
  sendMessage(channelID, channelType, msgType, content, metadata = {}) {
    try {
      let msgContent
      if (msgType === 'text') {
        msgContent = new MessageText(content)
      } else if (msgType === 'image') {
        msgContent = new MessageImage()
        msgContent.url = content.url // 图片的下载地址
        msgContent.width = content.width // 图片宽度
        msgContent.height = content.height // 图片高度
        msgContent.displayWidth = content.displayWidth // 图片宽度
        msgContent.displayHeight = content.displayHeight // 图片高度
        // 处理图片内容
        if (typeof content === 'string') {
          // 如果是字符串，直接作为URL
          msgContent.url = content
          msgContent.width = 0
          msgContent.height = 0
        } else if (typeof content === 'object' && content !== null) {
          // 如果是对象，包含url、width、height等信息
          msgContent.url = content.url || ''
          msgContent.width = content.width || 0
          msgContent.height = content.height || 0
        }
      } else if (msgType === 'location') {
        msgContent = new LocationContent()
        msgContent.lng = content.lng // 经度
        msgContent.lat = content.lat // 纬度
        msgContent.title = content.title // 位置标题
        msgContent.address = content.address // 具体地址
        msgContent.img = content.img // 图片的下载地址
      } else {
        console.error('不支持的消息类型:', msgType)
        return null
      }

      if (msgContent) {
        const channel = new Channel(channelID, channelType)

        // 添加元数据信息
        if (metadata && Object.keys(metadata).length > 0) {
          // 处理@用户信息
          if (metadata.mention) {
            // 直接使用传入的mention对象
            const uidsMention = new Mention()
            if (metadata.mention.all) {
              uidsMention.all = true
            }
            if (metadata.mention.uids && Array.isArray(metadata.mention.uids)) {
              uidsMention.uids = metadata.mention.uids
            }
            msgContent.mention = uidsMention
            console.log('设置mention:', uidsMention)
          }
          // 兼容旧的mentionedUsers格式
          else if (metadata.mentionedUsers && metadata.mentionedUsers.length > 0) {
            const uidsMention = new Mention()
            uidsMention.uids = metadata.mentionedUsers
            msgContent.mention = uidsMention
            console.log('使用mentionedUsers设置mention:', uidsMention)
          }

          // 添加其他元数据
          for (const key in metadata) {
            if (key !== 'mentionedUsers' && key !== 'mention' && metadata.hasOwnProperty(key)) {
              msgContent[key] = metadata[key]
            }
          }
        }

        this.sdk.chatManager.send(msgContent, channel)
      }
      return null
    } catch (error) {
      console.error('发送消息出错:', error)
      throw error
    }
  }

  // 获取历史消息
  syncMessages(
    channel,
    opts = {
      start_message_seq: 0, // 开始消息列号（结果包含start_message_seq的消息）
      end_message_seq: 0, // 结束消息列号（结果不包含end_message_seq的消息）
      limit: 30, // 消息数量限制
      pull_mode: 1 // 拉取模式 0:向下拉取 1:向上拉取
    }
  ) {
    return this.sdk.chatManager.syncMessages(channel, opts)
  }

  // 添加消息监听器
  addMessageListener(listener) {
    this.messageListeners.add(listener)
  }

  // 移除消息监听器
  removeMessageListener(listener) {
    this.messageListeners.delete(listener)
  }

  // 添加连接状态监听器
  addConnectionListener(listener) {
    this.connectionListeners.add(listener)
  }

  // 移除连接状态监听器
  removeConnectionListener(listener) {
    this.connectionListeners.delete(listener)
  }

  // 获取当前连接状态
  getConnectionStatus() {
    return this.connectionStatus.value
  }

  // 获取最近会话列表
  async getConversationList() {
    try {
      // 直接调用同步方法
      const conversations = await this.sdk.conversationManager.sync()
      return conversations || []
    } catch (error) {
      console.error('获取会话列表失败:', error)
      return []
    }
  }

  // 添加会话监听器
  addConversationListener(listener) {
    this.sdk.conversationManager.addConversationListener((conversation, action) => {
      // if (action === WKSDK.ConversationAction.add) {
      //   // 新增最近会话
      //   console.log('新增会话:', conversation)
      // } else if (action === WKSDK.ConversationAction.update) {
      //   // 更新最近会话
      //   console.log('更新会话:', conversation)
      // } else if (action === WKSDK.ConversationAction.remove) {
      //   // 删除最近会话
      //   console.log('删除会话:', conversation)
      // }
      listener(conversation, action)
    })
  }

  // 移除会话监听器
  removeConversationListener(listener) {
    this.sdk.conversationManager.removeConversationListener(listener)
  }

  // 获取某个频道的最近会话
  findConversation(channel) {
    return this.sdk.conversationManager.findConversation(channel)
  }

  // 移除一个频道的最近会话
  removeConversation(channelId, channelType) {
    const channel = new Channel(channelId, channelType)
    debugger
    return this.sdk.conversationManager.removeConversation(channel)
  }

  // 获取所有未读数量
  getAllUnreadCount() {
    return this.sdk.conversationManager.getAllUnreadCount()
  }

  // 创建一个空会话
  createEmptyConversation(channel) {
    return this.sdk.conversationManager.createEmptyConversation(channel)
  }

  // 获取频道信息
  getChannelInfo(channel) {
    return this.sdk.channelManager.getChannelInfo(channel)
  }

  // 强制从服务器获取频道资料
  fetchChannelInfo(channel) {
    return this.sdk.channelManager.fetchChannelInfo(channel)
  }

  // 添加频道资料监听器
  addChannelInfoListener(listener) {
    this.sdk.channelManager.addListener(listener)
  }

  // 移除频道资料监听器
  removeChannelInfoListener(listener) {
    this.sdk.channelManager.removeListener(listener)
  }

  // 同步频道的订阅者列表
  syncSubscribers(channelId, channelType) {
    const channel = new Channel(channelId, channelType)
    return this.sdk.channelManager.syncSubscribes(channel)
  }

  // 获取频道订阅者列表
  getSubscribers(channel) {
    return this.sdk.channelManager.getSubscribes(channel)
  }

  // 获取我在频道内的订阅者身份
  getMySubscriberInfo(channel) {
    return this.sdk.channelManager.getSubscribeOfMe(channel)
  }

  // 添加频道订阅者变化监听器
  addSubscriberChangeListener(listener) {
    this.sdk.channelManager.addSubscriberChangeListener(listener)
  }

  // 移除频道订阅者变化监听器
  removeSubscriberChangeListener(listener) {
    this.sdk.channelManager.removeSubscriberChangeListener(listener)
  }

  // 更新频道信息
  updateChannelInfo(channel, info) {
    return this.sdk.channelManager.updateChannelInfo(channel, info)
  }

  // 连接诊断工具
  async diagnoseConnection() {
    console.log('🔍 开始连接诊断...')

    const wsUrl = this.sdk.config.addr || 'ws://1903.eu.org:5200'
    const uid = this.sdk.config.uid
    const token = this.sdk.config.token

    console.log('📋 诊断信息:')
    console.log('- WebSocket地址:', wsUrl)
    console.log('- 用户ID:', uid)
    console.log('- Token状态:', token ? '已设置' : '未设置')
    console.log('- 当前连接状态:', this.connectionStatus.value)
    console.log('- 地址来源:', wsUrl === 'ws://1903.eu.org:5200' ? '默认地址' : '动态获取地址')

    // 检查基础配置
    const issues = []

    if (!wsUrl) {
      issues.push('❌ WebSocket地址未设置')
    } else if (!wsUrl.startsWith('ws://') && !wsUrl.startsWith('wss://')) {
      issues.push('❌ WebSocket地址格式错误')
    }

    if (!uid) {
      issues.push('❌ 用户ID未设置')
    }

    if (!token) {
      issues.push('❌ Token未设置')
    }

    // 检查网络连接
    try {
      const networkOk = await this.checkNetworkConnection(wsUrl)
      if (!networkOk) {
        issues.push('❌ 网络连接失败')
      } else {
        console.log('✅ 网络连接正常')
      }
    } catch (error) {
      issues.push('❌ 网络检测异常: ' + error.message)
    }

    if (issues.length > 0) {
      console.error('🚨 发现以下问题:')
      issues.forEach(issue => console.error(issue))
      return {
        success: false,
        issues: issues
      }
    } else {
      console.log('✅ 诊断完成，未发现明显问题')
      return {
        success: true,
        issues: []
      }
    }
  }

  // 解码 base64 消息内容
  decodeMessageContent(base64Content) {
    try {
      if (!base64Content) return null

      // 处理 base64 字符串，确保它是有效的
      const base64 = base64Content.replace(/\s/g, '')

      // 解码 base64 字符串
      const decodedStr = decodeURIComponent(escape(atob(base64)))

      // 解析 JSON
      const content = JSON.parse(decodedStr)

      // 处理图片 URL
      if (content.type === 2 && content.url) {
        // 如果是图片消息，确保 URL 是完整的
        if (!content.url.startsWith('http')) {
          content.url = `http://47.123.3.183:9000/${content.url}`
        }
      }

      return content
    } catch (error) {
      console.error('解码消息内容失败:', error)
      return null
    }
  }
  forMateMsgType(obj) {
    switch (obj.type) {
      case 1:
        return 'text'
      case 2:
        return 'image'
      case 101:
        return 'location'
      case 4:
        return 'voice'
      default:
        return '5'
    }
  }
}

// 导出单例实例
export default new WKIMManager()
