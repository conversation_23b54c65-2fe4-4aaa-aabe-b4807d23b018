import { checkOpenGPSServiceByAndroid } from './openSettings.js'

export function getLocation() {
    return new Promise((resolve, reject) => {
        uni.getLocation({
            type: "wgs84",
            success(res) {
                resolve(res)
            },
            fail: function (err) {
                uni.showModal({
                    title: '提示',
                    content: '需要开启定位权限才能获取当前位置，请前往设置中心开启。',
                    confirmText: '去开启',
                    showCancel: false,
                    success: function(res) {
                        if (res.confirm) {
                            // 用户点击确认，跳转到设置页面
                            checkOpenGPSServiceByAndroid()
                        }
                    }
                });
                reject(err)
            },
            complete(com) { },
        });
    })
}

// 检查定位权限状态
export function checkLocationPermission() {
    return new Promise((resolve, reject) => {
        uni.getLocation({
            type: 'wgs84',
            success: (res) => {
                // 已授权
                resolve({
                    authorized: true,
                    data: res
                })
            },
            fail: (err) => {
                // 未授权
                resolve({
                    authorized: false,
                    error: err
                })
            }
        })
    })
} 