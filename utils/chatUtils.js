/**
 * 聊天相关工具函数
 */

/**
 * 估算消息高度
 * @param {Object} message 消息对象
 * @returns {Number} 估算的消息高度（单位：px）
 */
export function estimateMessageHeight(message) {
  // 参数检查
  if (!message) return 100

  // 基础高度（头像、时间等固定元素）
  let baseHeight = 100

  // 根据消息类型计算额外高度
  const messageType = message.type || 'text'

  switch (messageType) {
    case 'text':
      // 文本消息高度估算：每15个字符一行，每行约30px
      if (message.content && typeof message.content === 'string') {
        const textLength = message.content.length
        const lines = Math.ceil(textLength / 15)
        return baseHeight + lines * 30
      } else if (message.content && message.content.text) {
        const textLength = message.content.text.length
        const lines = Math.ceil(textLength / 15)
        return baseHeight + lines * 30
      }
      return baseHeight

    case 'image':
      // 图片消息高度
      if (message.content && message.content.displayHeight) {
        return baseHeight + message.content.displayHeight
      }
      return baseHeight + 120 // 默认图片高度

    case 'voice':
      // 语音消息高度固定
      return baseHeight + 40

    case 'location':
      // 位置消息高度固定
      return baseHeight + 80

    default:
      return baseHeight
  }
}

/**
 * 计算日期分隔符是否需要显示
 * @param {Object} currentMessage 当前消息
 * @param {Object} previousMessage 前一条消息
 * @returns {Boolean} 是否需要显示日期分隔符
 */
export function shouldShowDateDivider(currentMessage, previousMessage) {
  if (!previousMessage) return true

  const currentDate = new Date(currentMessage.timestamp)
  const prevDate = new Date(previousMessage.timestamp)

  return currentDate.toDateString() !== prevDate.toDateString()
}

/**
 * 优化滚动位置计算
 * @param {Number} newMessagesCount 新消息数量
 * @param {Array} messages 消息列表
 * @param {Number} currentScrollTop 当前滚动位置
 * @returns {Number} 新的滚动位置
 */
export function calculateScrollPosition(newMessagesCount, messages, currentScrollTop) {
  if (newMessagesCount <= 0) return currentScrollTop

  // 计算新增消息的总高度
  let newContentHeight = 0
  for (let i = 0; i < newMessagesCount; i++) {
    if (messages[i]) {
      newContentHeight += estimateMessageHeight(messages[i])
    } else {
      newContentHeight += 100 // 默认高度
    }
  }

  return currentScrollTop + newContentHeight
}
