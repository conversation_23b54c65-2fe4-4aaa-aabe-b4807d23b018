<script>
  import { version } from './package.json'
  // #ifdef APP
  import checkUpdate from '@/uni_modules/uni-upgrade-center-app/utils/check-update'
  // #endif
  import { useUserStore } from '@/stores/user'
  import { useConversationStore } from '@/stores/conversation'
  import { useGroupStore } from '@/stores/group'
  import { ref, provide } from 'vue'
  import WKIMManager from '@/utils/wukongim'
  import { userApi } from '@/common/api'

  // 创建一个 Symbol 作为 injection key
  const globalDataKey = Symbol('globalData')

  export default {
    setup() {
      const userStore = useUserStore()
      const globalData = ref({
        test: ''
      })

      // 使用 provide 提供全局数据
      provide(globalDataKey, globalData)

      // 处理 univerify 登录
      const handleUniverifyLogin = result => {
        userStore.setUniverifyLogin(true)
        if (result.errMsg !== 'login:ok') {
          userStore.setUniverifyErrorMsg(result.errMsg)
        }
      }
      return {
        userStore,
        handleUniverifyLogin,
        updateUnreadBadge: null, // 将在onLaunch中初始化
        handleMessageReceived: null // 消息监听器
      }
    },
    async onLaunch() {
      console.log('应用启动')
      uni.onTabBarMidButtonTap(function (e) {
        console.log('中间按钮点击', e)
        uni.navigateTo({
          url: '/pages/post/choose',
          animationType: 'slide-out-bottom',
          animationDuration: 200
        })
      })
      // 初始化用户状态
      this.userStore.initUserState()

      // 检查token是否存在
      const token = this.userStore.token
      console.log('当前token:', token)

      if (!token) {
        console.log('没有token，跳转到登录页面')
        // 如果没有token，跳转到登录页面
        uni.reLaunch({
          url: '/pages/login/login'
        })
        return
      }

      // 初始化未读消息更新定时器
      // this.initUnreadBadgeUpdater()

      // 检查悟空IM连接状态
      const connectionStatus = WKIMManager.getConnectionStatus()
      if (connectionStatus !== 'connected') {
        console.log('悟空IM未连接，尝试重新连接')
        WKIMManager.initConnect()
      }

      // 获取会话列表
      let list = await WKIMManager.getConversationList()
      console.log('获取到会话列表********:', list)
      const conversationStore = useConversationStore()
      conversationStore.setConversationList(list || [])

      // 添加消息监听器
      this.setupMessageListener()

      // 检查用户是否已完成初始设置
      console.log('检查用户设置状态...')
      const setupRequired = this.userStore.checkUserSetupRequired()
      console.log('是否需要完成设置:', setupRequired)

      // 先检查本地存储状态
      const userSetupStatus = uni.getStorageSync('userSetupStatus')

      if (!userSetupStatus) {
        console.log('本地无设置状态，调用接口检查用户标签设置状态...')
        // 调用接口检查用户是否已完成标签选择
        try {
          const result = await this.checkUserTagStatus()
          if (result && result.data && result.data.tagDone === 1) {
            console.log('用户已完成标签选择，无需跳转choose页面')
            // 用户已完成标签选择，更新本地状态
            uni.setStorageSync('userSetupStatus', 'completed')
          } else {
            console.log('用户未完成标签选择，跳转到标签选择页面')
            // 用户未完成标签选择，跳转到choose页面
            uni.reLaunch({
              url: '/pages/login/choose'
            })
            return
          }
        } catch (error) {
          console.error('检查用户标签状态失败:', error)
          // 接口调用失败，默认跳转到choose页面
          uni.reLaunch({
            url: '/pages/login/choose'
          })
          return
        }
      } else {
        console.log('已完成初始设置，继续正常流程')
      }

      // #ifdef H5
      console.log(
        `%c hello uniapp %c v${version} `,
        'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
        'background:#007aff ;padding: 1px; border-radius: 0 3px 3px 0;  color: #fff; font-weight: bold;'
      )
      // #endif
      console.log('App Launch')
      // #ifdef APP-PLUS
      // App平台检测升级，服务端代码是通过uniCloud的云函数实现的，详情可参考：https://ext.dcloud.net.cn/plugin?id=4542
      if (plus.runtime.appid !== 'HBuilder') {
        // 真机运行不需要检查更新，真机运行时appid固定为'HBuilder'，这是调试基座的appid
        checkUpdate()
      }
      // #endif
      // 一键登录预登陆，可以显著提高登录速度
      uni.preLogin({
        provider: 'univerify',
        success: res => {
          this.handleUniverifyLogin(res)
          console.log('preLogin success: ', res)
        },
        fail: res => {
          this.handleUniverifyLogin(res)
          // 失败
          console.log('preLogin fail res: ', res)
        }
      })
    },
    onShow() {
      console.log('App Show')
      // 获取推送客户端ID
      this.getPushClientId()

      // 恢复未读消息更新
      if (!this.updateUnreadBadge) {
        // this.initUnreadBadgeUpdater()
      } else {
        // 立即更新一次
        // this.updateTabBarBadge()
      }

      // 如果消息监听器不存在，重新设置
      if (!this.handleMessageReceived) {
        this.setupMessageListener()
      }
    },
    onHide() {
      console.log('App Hide')
      // 暂停未读消息更新
      if (this.updateUnreadBadge) {
        clearInterval(this.updateUnreadBadge)
      }
    },
    onUnload() {
      // 移除消息监听器
      if (this.handleMessageReceived) {
        WKIMManager.removeMessageListener(this.handleMessageReceived)
      }
      // 清除未读消息更新定时器
      if (this.updateUnreadBadge) {
        clearInterval(this.updateUnreadBadge)
      }
    },

    methods: {
      // 检查用户标签设置状态
      async checkUserTagStatus() {
        try {
          const result = await userApi.queryDetail()
          console.log('调用/user/queryDetail接口成功:', result)
          return result
        } catch (error) {
          console.error('调用/user/queryDetail接口失败:', error)
          throw error
        }
      },

      // 获取推送客户端ID
      getPushClientId() {
        uni.getPushClientId({
          success: res => {
            console.log('获取到pushClientId:', res)
            if (res.cid) {
              // 直接存储clientId，不依赖uniIdCo
              uni.setStorageSync('pushClientId', res.cid)

              // 验证存储是否成功
              const storedCid = uni.getStorageSync('pushClientId')
              console.log('存储后的pushClientId:', storedCid)
              // 如果您需要将clientId发送到服务器，可以在这里添加相关代码
              // 例如：this.sendPushClientIdToServer(res.cid)
            }
          },
          fail: err => {
            console.error('获取pushClientId失败:', err)
          }
        })
      },

      // 设置消息监听器
      setupMessageListener() {
        // 定义消息监听函数
        this.handleMessageReceived = message => {
          console.log('收到新消息:', message)
          // 收到新消息后立即更新未读消息数量
          this.updateTabBarBadge()
        }

        // 添加消息监听器
        WKIMManager.addMessageListener(this.handleMessageReceived)
      },

      // 初始化未读消息更新定时器
      initUnreadBadgeUpdater() {
        console.log('初始化未读消息更新定时器')

        // 延迟执行，确保应用完全加载
        setTimeout(() => {
          // 立即执行一次
          this.updateTabBarBadge()

          // 设置定时器，每3秒更新一次
          this.updateUnreadBadge = setInterval(() => {
            this.updateTabBarBadge()
          }, 3000)
        }, 1000)
      },

      // 更新TabBar未读消息数量
      async updateTabBarBadge() {
        try {
          console.log('开始更新TabBar未读消息数量')

          // 获取消息列表未读数量
          const conversationStore = useConversationStore()
          const groupStore = useGroupStore()

          // 计算消息列表未读数量
          let messageUnreadCount = 0
          if (conversationStore.conversationList && conversationStore.conversationList.length > 0) {
            messageUnreadCount = conversationStore.conversationList.reduce((total, conv) => {
              return total + (conv.unread || 0)
            }, 0)
          }

          // 计算群组未读数量
          let groupUnreadCount = 0
          if (groupStore.groupHistoryList && groupStore.groupHistoryList.length > 0) {
            groupUnreadCount = groupStore.groupHistoryList.reduce((total, group) => {
              // 优先使用unread字段
              if (group.unread !== undefined) {
                return total + group.unread
              }
              // 如果有原始群组数据，尝试从中获取
              if (group.groupRes && group.groupRes.unread !== undefined) {
                return total + group.groupRes.unread
              }
              return total
            }, 0)
          }

          // 计算总未读数量
          const totalUnreadCount = messageUnreadCount + groupUnreadCount
          console.log(
            '未读消息总数:',
            totalUnreadCount,
            '(消息:',
            messageUnreadCount,
            ', 群组:',
            groupUnreadCount,
            ')'
          )

          // 更新用户Store中的未读数量
          this.userStore.setUnreadCount(totalUnreadCount)

          // 设置TabBar角标
          if (totalUnreadCount > 0) {
            console.log('设置TabBar角标:', totalUnreadCount)

            // 使用uni.setTabBarBadge设置角标
            uni.setTabBarBadge({
              index: 2, // 消息选项卡的索引
              text: totalUnreadCount > 99 ? '99+' : totalUnreadCount.toString(),
              success: () => {
                console.log('设置TabBar角标成功')
              },
              fail: err => {
                console.error('设置TabBar角标失败:', err)
              }
            })
          } else {
            // 如果没有未读消息，移除角标
            uni.removeTabBarBadge({
              index: 2,
              success: () => {
                console.log('移除TabBar角标成功')
              },
              fail: err => {
                console.error('移除TabBar角标失败:', err)
              }
            })
          }
        } catch (error) {
          console.error('更新未读消息角标失败:', error)
        }
      }
    }
  }
</script>

<style lang="scss">
  @import '@/uni_modules/uni-scss/index.scss';
  /* #ifndef APP-PLUS-NVUE */
  /* uni.css - 通用组件、模板样式库，可以当作一套ui库应用 */
  @import './common/uni.css';
  @import '@/static/customicons.css';
  @import '@/static/fonts/iconfont.css';
  /* H5 兼容 pc 所需 */
  /* #ifdef H5 */
  @media screen and (min-width: 768px) {
    body {
      overflow-y: scroll;
    }
  }

  /* 顶栏通栏样式 */
  /* .uni-top-window {
	    left: 0;
	    right: 0;
	} */

  uni-page-body {
    background-color: #f5f5f5 !important;
    min-height: 100% !important;
    width: 100vw;
    // position: fixed;
  }

  .uni-top-window uni-tabbar .uni-tabbar {
    background-color: #fff !important;
  }
  .uni-tabbar {
    background: linear-gradient(180deg, #d7f3de 0%, rgba(255, 255, 255, 1) 100%) !important;
  }
  .uni-app--showleftwindow .hideOnPc {
    display: none !important;
  }

  /* #endif */
  /* #endif */

  /* 以下样式用于 hello uni-app 演示所需 */
  page {
    background-color: #efeff4;
    height: 100%;
    font-size: 28rpx;
    /* line-height: 1.8; */
  }

  .fix-pc-padding {
    padding: 0 50px;
  }

  .uni-header-logo {
    padding: 30rpx;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 10rpx;
  }

  .uni-header-image {
    width: 100px;
    height: 100px;
  }

  .uni-hello-text {
    color: #7a7e83;
  }

  .uni-hello-addfile {
    text-align: center;
    line-height: 300rpx;
    background: #fff;
    padding: 50rpx;
    margin-top: 10px;
    font-size: 38rpx;
    color: #808080;
  }
</style>
