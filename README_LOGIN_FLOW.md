# 登录流程和状态管理说明

## 支持的登录方式

### 1. 一键登录（Univerify）
- 支持三大运营商（移动、联通、电信）
- 自动获取本机号码，无需手动输入
- 登录成功后检查用户初始设置状态

### 2. 手机号登录
- 手动输入手机号
- 发送短信验证码
- 输入4位验证码完成登录
- 登录成功后检查用户初始设置状态

### 3. 微信登录
- 调用微信授权
- 获取用户基本信息
- 登录成功后检查用户初始设置状态

### 4. QQ登录
- 调用QQ授权
- 获取用户基本信息
- 登录成功后检查用户初始设置状态

## 用户初始设置流程

### 第一步：标签选择（choose页面）
- 用户选择感兴趣的标签分类
- 包括：娱乐、动漫&读书、游戏、体育、时尚美妆
- 完成后保存到本地存储
- 跳转到性格测评页面

### 第二步：性格测评（character页面）
- 5道性格测评题目
- 根据答案生成个性化测评结果
- 支持DISC性格类型分析
- 完成后保存到本地存储
- 跳转到首页

## 状态管理

### 用户设置状态（userSetupStatus）
```javascript
{
  hasCompletedChoose: false,    // 是否完成标签选择
  hasCompletedCharacter: false, // 是否完成性格测评
  selectedTags: [],             // 用户选择的标签
  characterResult: null         // 性格测评结果
}
```

### 状态持久化
- 使用 `uni.setStorageSync('userSetupStatus', status)` 保存
- 应用启动时自动恢复状态
- 退出登录时清除状态

### 状态检查
- `userStore.checkUserSetupRequired()` - 检查是否需要完成初始设置
- `userStore.hasCompletedSetup` - 检查是否已完成所有设置

## 登录流程

### 首次登录
1. 用户选择登录方式
2. 完成登录验证
3. 检查用户设置状态
4. 未完成 → 跳转到标签选择页面
5. 完成标签选择 → 跳转到性格测评页面
6. 完成性格测评 → 跳转到首页

### 再次登录
1. 用户选择登录方式
2. 完成登录验证
3. 检查用户设置状态
4. 已完成 → 直接跳转到首页

## 页面跳转逻辑

### 登录成功后的跳转
```javascript
// 检查用户是否已完成初始设置
if (userStore.checkUserSetupRequired()) {
  // 未完成初始设置，跳转到标签选择页面
  uni.navigateTo({
    url: '/pages/login/choose'
  })
} else {
  // 已完成初始设置，直接跳转到首页
  uni.switchTab({
    url: '/pages/index/index'
  })
}
```

### 应用启动时的检查
```javascript
// 在App.vue的onLaunch中
if (this.userStore.checkUserSetupRequired()) {
  // 未完成初始设置，跳转到标签选择页面
  uni.reLaunch({
    url: '/pages/login/choose'
  })
}
```

## 注意事项

1. **状态一致性**：所有登录方式都使用相同的状态检查逻辑
2. **持久化存储**：用户设置状态会保存到本地，重启应用后仍然有效
3. **退出登录**：退出登录时会清除所有状态，包括设置状态
4. **错误处理**：登录失败时会显示相应的错误提示
5. **用户体验**：避免重复设置，提升用户体验

## 文件结构

```
pages/login/
├── login.vue          # 主登录页面（一键登录、微信、QQ）
├── phone-login.vue    # 手机号输入页面
├── verify-code.vue    # 验证码输入页面
├── choose.vue         # 标签选择页面
└── character.vue      # 性格测评页面

stores/
└── user.js           # 用户状态管理

App.vue              # 应用启动时的状态检查
``` 