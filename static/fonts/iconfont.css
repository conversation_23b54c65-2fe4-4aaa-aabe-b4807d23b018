@font-face {
  font-family: "iconfont";
  src: url('../iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-activity:before {
  content: "\e600";
}

.icon-post:before {
  content: "\e601";
}

.icon-image:before {
  content: "\e602";
}

.icon-location:before {
  content: "\e603";
}

.icon-topic:before {
  content: "\e604";
}

.icon-public:before {
  content: "\e605";
}

.icon-private:before {
  content: "\e606";
}

.icon-arrow-right:before {
  content: "\e607";
} 