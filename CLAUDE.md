# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 uni-app(Vue 3) 开发的社交服务平台 "小岛屿"，支持iOS、Android和小程序多端发布。项目包含用户系统、即时通讯、社交功能、技能服务、商城等核心模块。

## 常用开发命令

### 运行项目
```bash
# H5端
npm run dev:h5

# 微信小程序
npm run dev:mp-weixin

# App端
npm run dev:app
```

### 构建项目
```bash
# H5构建
npm run build:h5

# 微信小程序构建  
npm run build:mp-weixin

# App构建
npm run build:app
```

### 代码格式化和检查
```bash
# 格式化代码
npx prettier --write .

# 运行ESLint检查
npx eslint pages/ components/ stores/
```

## 核心架构

### 状态管理 (Pinia)
使用Pinia进行状态管理，主要store位于 `stores/` 目录：

- `stores/user.js` - 用户登录状态、个人信息、第三方登录
- `stores/conversation.js` - 会话列表、消息历史、IM连接状态  
- `stores/group.js` - 群组管理和频道信息

### API配置
API请求统一通过 `common/api.js` 管理：
- 基础URL: `http://47.116.217.156:8020/api`
- 自动token管理和请求拦截
- 统一错误处理和响应格式化

主要API模块：
- `userApi` - 用户相关接口
- `postApi` - 动态发布管理  
- `messageApi` - IM消息系统
- `skillApi` - 达人技能管理
- `shopApi` - 商城功能

### 即时通讯 (WuKongIM)
项目集成WuKongIM SDK，相关配置：
- SDK版本: `wukongimjssdk@^1.3.1`
- 连接状态管理在 `stores/conversation.js`
- 消息处理逻辑在 `pages/chat/` 目录

### 路由和页面结构
使用uni-app标准路由，配置在 `pages.json`：
- 首页：`pages/index/index` (关注动态)
- 消息：`pages/conversation/index` 
- 商城：`pages/shopping/shopping`
- 个人中心：`pages/profile/profile`
- 聊天页面：`pages/chat/index`

大部分页面使用自定义导航栏 (`navigationStyle: "custom"`)

## 关键功能实现

### 多种登录方式
支持以下登录方式，相关代码在 `stores/user.js`：
- 短信验证码登录
- 微信登录 (AppID: `wxd55a943553bb707a`)
- QQ登录
- 一键登录(运营商)

### 图片处理
图片上传和处理相关工具在 `common/utils.js`：
- 图片压缩和格式转换
- 多图上传处理
- 图片URL验证

### 地理位置服务
集成高德地图SDK，配置在 `manifest.json`：
- iOS Key: `b215b325ecbf24e2a671e41e55bbf7a9`
- Android Key: `293cb2f5e29bb3577d52e625a8ecf638`
- 位置权限处理在各页面的onLoad生命周期

## 开发规范

### 文件命名
- 组件文件：大驼峰命名 (PascalCase)
- 页面文件：小写中横线 (kebab-case)
- 工具函数：小驼峰命名 (camelCase)

### 状态管理使用
```javascript
import { useUserStore } from '@/stores/user'

// 在setup中使用
const userStore = useUserStore()
await userStore.login(credentials)
```

### API调用规范
```javascript
import { userApi } from '@/common/api'

// 使用try-catch处理异步请求
try {
  const result = await userApi.getUserInfo()
  // 处理成功响应
} catch (error) {
  // 统一错误处理已在request中实现
}
```

### 样式开发
使用 `uni.scss` 中的CSS变量：
```scss
.container {
  background-color: var(--uni-bg-color);
  color: var(--uni-text-color);
  padding: var(--uni-spacing-lg);
}
```

## 调试和测试

### 测试配置
项目配置了Jest测试环境，测试文件位于对应功能目录下：
- 组件测试：`components/*/test.js`
- 页面测试：`pages/*/test.js`

### 真机调试
- iOS: 需要配置证书和描述文件
- Android: 需要配置签名信息
- 小程序: 使用微信开发者工具

### 常见问题排查
1. **IM连接失败**: 检查网络状态和WuKongIM服务配置
2. **第三方登录失败**: 验证AppID配置和平台证书
3. **地图显示异常**: 检查Key配置和权限申请
4. **图片上传失败**: 检查文件大小和格式限制

## 部署和发布

### 环境配置
- 开发环境：使用HBuilderX标准基座
- 生产环境：使用云端打包或自定义基座

### 平台特殊配置
- **iOS**: 需要配置UniversalLinks和推送证书
- **Android**: 需要配置应用签名和权限
- **小程序**: 需要配置域名白名单和业务域名

### 版本管理
版本信息在 `manifest.json` 中维护：
- `versionName`: 显示版本号
- `versionCode`: 内部版本号

## 项目依赖

### 主要依赖
- `pinia@^2.1.7` - 状态管理
- `dayjs@^1.11.13` - 时间处理
- `wukongimjssdk@^1.3.1` - 即时通讯

### 开发依赖  
- `@vue/eslint-config-prettier@^10.2.0` - 代码格式化
- `eslint-plugin-prettier@^5.4.1` - 代码检查
- `prettier@^3.5.3` - 代码格式化工具

## 安全注意事项

- API接口已配置请求拦截和token管理
- 敏感配置信息不应提交到代码仓库
- 第三方登录的AppSecret保存在服务端
- 用户数据采用加密存储

## 性能优化建议

- 使用图片懒加载减少内存占用
- 大列表采用虚拟滚动优化渲染
- 合理使用组件缓存避免重复渲染
- 图片资源压缩和CDN加速