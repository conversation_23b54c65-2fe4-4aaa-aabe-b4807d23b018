<template>
	<view class="container">
		<!-- 核销码区域 -->
		<view v-if="orderInfo.status === 'paid' && orderInfo.verificationCode" class="verification-code-section">
			<view class="verification-code-text-row">
				<text class="verification-code-text">{{ orderInfo.verificationCode }}</text>
				<text class="verification-code-label">核销码</text>
			</view>
			<text class="verification-code-desc">已经准备好，快快来核销吧～</text>
		</view>
		<view v-if="orderInfo.status === 'pending'" class="dzf-section">
			<image src="/static/images/order/djd.png" mode="aspectFit" />
			<view class="verification-code-text-row">
				<text class="verification-code-text">待支付</text>
				<text class="verification-code-desc">需付款 <text class="verification-code-text-red">
					<text v-if="orderInfo.payMethod === '5'">{{ orderInfo.payAmount / 10 }}贝壳币</text>
					<text v-else>¥{{ orderInfo.payAmount / 100 }}</text>
				</text> 剩余：<text class="verification-code-text-red-o">{{ countdown.minutes }}</text> 分钟<text class="verification-code-text-red-o">{{ countdown.seconds }}</text> 秒后订单自动关闭</text>
			</view>
		</view>
		<view v-if="orderInfo.status === 'pended'" class="dzf-section">
			<image src="/static/images/order/djd.png" mode="aspectFit" />
			<view class="verification-code-text-row">
				<text class="verification-code-text">待接单</text>
				<text class="verification-code-desc">订单已收到，商家火速处理中~ </text>
			</view>
		</view>
		<view v-if="orderInfo.status === 'completed'" class="dzf-section">
			<image src="/static/images/order/djd.png" mode="aspectFit" />
			<view class="verification-code-text-row">
				<text class="verification-code-text">已完成</text>
				<text class="verification-code-desc">订单已完成，再来一单吧~ </text>
			</view>
		</view>
		<view v-if="orderInfo.status === 'cancelled'" class="dzf-section">
			<image src="/static/images/order/djd.png" mode="aspectFit" />
			<view class="verification-code-text-row">
				<text class="verification-code-text">已取消</text>
				<text class="verification-code-desc">订单已取消，请重新下单~ </text>
			</view>
		</view>
		<view v-if="orderInfo.status === 'working'" class="dzf-section">
			<image src="/static/images/order/djd.png" mode="aspectFit" />
			<view class="verification-code-text-row">
				<text class="verification-code-text">商家准备中</text>
				<text class="verification-code-desc">商家准备中，请耐心等待~ </text>
			</view>
		</view>
		<view v-if="orderInfo.status === 'refunding'" class="dzf-section">
			<image src="/static/images/order/djd.png" mode="aspectFit" />
			<view class="verification-code-text-row">
				<text class="verification-code-text">退款中</text>
				<text class="verification-code-desc">退款处理正在处理，请耐心等待 </text>
			</view>
		</view>
		<view v-if="orderInfo.status === 'refunded'" class="dzf-section">
			<image src="/static/images/order/djd.png" mode="aspectFit" />
			<view class="verification-code-text-row">
				<text class="verification-code-text">已退款</text>
				<text class="verification-code-desc">退款处理已完成，请留意账户变动 </text>
			</view>
		</view>
		<!-- 联系信息（部分状态显示） -->
		<view v-if="orderInfo.deliveryType === 1" class="contact-section">
			<view class="contact-item">
				<uni-icons type="location" class="contact-icon" size="16" color="#333" />
				<text class="contact-address">{{ getContactAddress() }}</text>
			</view>
			<view class="contact-item">
				<!-- <uni-icons type="person" size="16" color="#666" /> -->
				 <text class="contact-icon"></text>
				<text class="contact-text">{{ orderInfo.receiverName }} {{ orderInfo.receiverPhone }}</text>
			</view>
		</view>
		<!-- 商品信息 -->
		<view class="section product-section">
			<view class="shop-header">
				<view class="shop-info">
					<image class="shop-icon" src="/static/images/shoping/info.png" mode="aspectFit" />
					<text class="shop-name">{{ orderInfo.shopName }}</text>
					<uni-icons type="right" size="14" color="#D1D1D1" />
				</view>
				<view class="shop-arrow">
					<image @click="callStore" class="service-icon" src="/static/images/shoping/kf.png" mode="aspectFit" />
				</view>
			</view>
			
			<view class="product-item">
				<view class="product-item-top">
					<image 
						class="product-image" 
						:src="orderInfo.productImage || '/static/images/default-product.png'" 
						mode="aspectFill"
					/>
					<view class="product-info">
						<text class="product-name">{{ orderInfo.productName }}</text>
						<text class="product-spec">{{ orderInfo.productSpec }}</text>
						<view class="product-price-row">
							<text class="product-quantity">×{{ orderInfo.quantity }}</text>
							<text class="product-price">
								<text v-if="orderInfo.orderStatus === '0'">剩余应付: </text>				
								<text class="product-price-value" v-if="orderInfo.payMethod === '5'">{{ orderInfo.totalAmount / 10 }}贝壳币</text>
								<text class="product-price-value" v-else>¥{{ orderInfo.totalAmount / 100 }}</text>
							</text>
						</view>
					</view>
				</view>
				<view class="product-item-bottom">
					<view class="info-row" v-if="orderInfo.deliveryType === 2">
						<text class="info-label">自提地点</text>
						<text class="info-value">{{ orderInfo.merchantAddress || '' }}</text>
					</view>
					<view class="info-row" v-if="orderInfo.orderMessage">
						<text class="info-label">留言</text>
						<text class="info-value">{{ orderInfo.orderMessage || '' }}</text>
					</view>
				</view>
			</view>

			<!-- 商品评分（仅已完成订单显示） -->
			<view v-if="orderInfo.status === 'completed' && orderInfo.rating" class="product-rating" @click="viewRatingDetail">
				<view class="rating-row">
					<text class="rating-label">商品评分:</text>
					<view class="stars">
						<uni-icons 
							v-for="i in 5" 
							:key="i" 
							:type="i <= orderInfo.rating ? 'star-filled' : 'star'"
							size="16" 
							:color="i <= orderInfo.rating ? '#FFD700' : '#ddd'"
						/>
					</view>
					<text class="rating-score">{{ orderInfo.rating }}</text>
				</view>
				<text class="rating-desc">{{ getRatingDesc(orderInfo.rating) }}</text>
			</view>
		</view>

		<!-- 订单信息 -->
		<view class="section order-info-section">
			<view class="section-header">
				<text class="section-title">订单信息</text>
			</view>
			
			<view class="info-row">
				<text class="info-label">订单编号</text>
				<view class="info-value-row">
					<text class="info-value">{{ orderInfo.orderNumber }}</text>
					<text class="copy-btn" @click="copyOrderNumber">复制</text>
				</view>
			</view>
			
			<view class="info-row">
				<text class="info-label">下单时间</text>
				<text class="info-value">{{ orderInfo.createTime }}</text>
			</view>
			
			<view class="info-row">
				<text class="info-label">商品总额</text>
				<text class="info-value" v-if="orderInfo.payMethod === '5'">{{ orderInfo.totalAmount / 10 }}贝壳币</text>
				<text class="info-value" v-else>¥{{ orderInfo.totalAmount / 100 }}</text>
			</view>
			
			<view class="info-row total-row">
				<text class="info-label">应付金额：</text>
				<text class="total-amount" v-if="orderInfo.payMethod === '5'">{{ orderInfo.payAmount / 10 }}贝壳币</text>
				<text class="total-amount" v-else>¥{{ orderInfo.payAmount / 100 }}</text>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-bar" :class="{ 'safe-bottom': true }">
			<!-- 待支付状态 -->
			<template v-if="orderInfo.status === 'pending'">
				<view class="action-buttons">
					<button class="action-btn primary-btn ljzf-btn" @click="payOrder">立即支付</button>
				</view>
			</template>
			
			<template v-else-if="orderInfo.status === 'pended'">
				<view class="action-buttons">
					<!-- <button v-if="refundApplicationId" class="action-btn secondary-btn square-btn" @click="viewRefundDetail">退款详情</button> -->
					<button class="action-btn primary-btn square-btn" @click="applyRefund">申请退款</button>
				</view>
			</template>
			<!-- 已支付状态
			<template v-else-if="orderInfo.status === 'paid'">
				<view class="action-buttons">
					// <button v-if="refundApplicationId" class="action-btn secondary-btn square-btn" @click="viewRefundDetail">退款详情</button>
					<button class="action-btn primary-btn square-btn" @click="applyRefund">申请退款</button>
				</view>
			</template> -->
			
			<!-- 已发货/已连接状态
			<template v-else-if="orderInfo.status === 'shipped'">
				<view class="action-buttons">
					<button v-if="refundApplicationId" class="action-btn secondary-btn square-btn" @click="viewRefundDetail">退款详情</button>
					<button v-else class="action-btn primary-btn square-btn" @click="applyRefund">申请退款</button>
				</view>
			</template> -->
			
			<!-- 已完成状态 -->
			<template v-else-if="orderInfo.status === 'completed'">
				<view class="action-buttons">
					<!-- <button v-if="refundApplicationId" class="action-btn secondary-btn square-btn" @click="viewRefundDetail">退款详情</button>
					<button v-else class="action-btn primary-btn square-btn" @click="applyRefund">申请退款</button> -->
					<button v-if="canReview" class="action-btn square-btn" style="margin-left: 20rpx;" @click="evaluateOrder">评价</button>
					<button v-else class="action-btn square-btn" style="margin-left: 20rpx;" @click="viewRatingDetail">查看评价</button>
					</view>
				</template>
					
					<!-- 退款中状态
					<template v-else-if="refundApplicationId">
						<view class="action-buttons">
							<button v-if="refundApplicationId" class="action-btn secondary-btn square-btn" @click="viewRefundDetail">退款详情</button>
				</view>
			</template> -->
			
			<!-- 已取消/已退款状态
			<template v-else-if="orderInfo.status === 'cancelled' || orderInfo.status === 'refunded' || orderInfo.status === 'refunding'">
				<view class="action-buttons">
					<button class="action-btn secondary-btn square-btn" @click="viewRefundDetail">退款详情</button>
				</view>
			</template> -->
		</view>

		<!-- 取消订单弹窗 -->
		<uni-popup ref="cancelPopup" type="bottom" background-color="#fff">
			<view class="cancel-popup">
				<view class="popup-header">
					<text class="popup-title">取消订单</text>
					<uni-icons type="closeempty" size="20" @click="closeCancelPopup" />
				</view>
				<view class="cancel-reasons">
					<view 
						v-for="(reason, index) in cancelReasons" 
						:key="index"
						:class="['reason-item', selectedReasonIndex === index ? 'selected' : '']"
						@click="selectReason(index)"
					>
						<text class="reason-text">{{ reason }}</text>
						<uni-icons 
							:type="selectedReasonIndex === index ? 'checkmarkempty' : 'circle'" 
							size="20" 
							:color="selectedReasonIndex === index ? '#007AFF' : '#ddd'"
						/>
					</view>
				</view>
				<view class="popup-actions">
					<button class="popup-btn cancel-popup-btn" @click="closeCancelPopup">取消</button>
					<button class="popup-btn confirm-popup-btn" @click="confirmCancel">确认取消</button>
				</view>
			</view>
		</uni-popup>

		<!-- 评价弹窗 -->
		<view v-if="showRatingPopup" class="popup-mask" @click.self="closeRatingPopup">
			<view class="rating-popup">
				<view class="rating-header">
					<text>服务评价</text>
					<uni-icons type="closeempty" size="20" class="close-icon" @click="closeRatingPopup" />
				</view>
				<view class="rating-content">
					<view class="rating-stars">
						<uni-icons 
							v-for="i in 5" 
							:key="i" 
							:type="i <= rating ? 'star-filled' : 'star'"
							size="28" 
							:color="i <= rating ? '#FFD700' : '#ddd'"
							@click="setRating(i)"
						/>
					</view>
					<!-- <text class="rating-text">{{ ratingDesc }}</text> -->
					<view class="divider"></view>
					<textarea 
						class="rating-textarea" 
						placeholder="展开说说对商品的想法吧..." 
						v-model="ratingComment"
						maxlength="200"
						:placeholder-style="{ color: '#999', fontSize: '28rpx' }"
					></textarea>
					<view class="rating-images">
						<view 
							v-for="(image, index) in ratingImages" 
							:key="index" 
							class="image-item"
						>
							<image :src="image" mode="aspectFill" />
							<view class="delete-icon" @click="removeImage(index)">
								<uni-icons type="closeempty" size="20" color="#fff" />
							</view>
						</view>
						<view class="add-image" @click="chooseImage" v-if="ratingImages.length < 3">
							<view class="camera-icon">
								<uni-icons type="camera" size="20" color="#999" />
							</view>
							<text class="add-text">图片</text>
						</view>
					</view>
				</view>
				<view class="rating-footer">
					<button class="submit-btn" @click="submitRating">发布</button>
				</view>
			</view>
		</view>
		
		<!-- 评价详情弹窗 -->
		<view v-if="showRatingDetailPopup" class="popup-mask" @click.self="closeRatingDetailPopup">
			<view class="rating-detail-popup">
				<view class="rating-header">
					<text>服务评价</text>
					<uni-icons type="closeempty" size="20" class="close-icon" @click="closeRatingDetailPopup" />
				</view>
				<view class="rating-content">
					<!-- 用户信息行 -->
					<view class="review-user-row">
						<image class="review-avatar" :src="reviewInfo.userAvatar || '/static/images/default-avatar.png'" />
						<view class="review-user-meta">
							<view class="review-user-nick">
								<text>{{ reviewInfo.userNickname || reviewInfo.userNickName || '匿名用户' }}</text>
								<!-- 认证标识，如有字段可加 -->
								<!-- <image v-if="reviewInfo.isVerified" class="review-verified-icon" src="/static/icons/verified.png" /> -->
							</view>
							<view class="review-stars">
								<uni-icons v-for="i in 5" :key="i" :type="i <= (reviewInfo.rating || 0) ? 'star-filled' : 'star'" size="20" :color="i <= (reviewInfo.rating || 0) ? '#FFD700' : '#ddd'" />
							</view>
						</view>
						<text class="review-time">{{ reviewInfo.relativeTime || (reviewInfo.reviewTime) }}</text>
					</view>
					<!-- 内容 -->
					<view class="review-content-text">{{ reviewInfo.reviewContent }}</view>
					<!-- 图片 -->
					<view class="review-images" v-if="reviewInfo.reviewImagesArray && reviewInfo.reviewImagesArray.length">
						<image v-for="(img, i) in reviewInfo.reviewImagesArray" :key="i" :src="img" class="review-image" mode="aspectFill" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { orderApi, payApi, fileApi } from '@/common/api';
export default {
	data() {
		return {
			// 订单信息
			orderInfo: {
				id: '',
				orderNumber: '',
				status: '', // pending, paid, shipped, completed, cancelled, refunding
				shopName: '',
				productImage: '',
				productName: '',
				productSpec: '',
				productPrice: '',
				quantity: '',
				buyerInfo: '',
				pickupLocation: '',
				createTime: '',
				totalAmount: '',
				payAmount: '',
				rating: null, // 评分，1-5星
				merchantAddress: '',
			},
			// 取消原因列表
			cancelReasons: [
				'不想要了',
				'信息填写错误',
				'重复下单',
				'商品降价了',
				'其他原因'
			],
			selectedReasonIndex: -1,
			refundApplicationId: '',
			// 评价相关
			rating: 4, // 默认4星评价
			ratingComment: '', // 评价内容
			ratingImages: [], // 评价图片
			canReview: false, // 是否可以评价
			reviewInfo: {}, // 评价信息
			showRatingPopup: false, // 控制评价弹窗显示
			showRatingDetailPopup: false, // 控制评价详情弹窗显示
			countdown: {
				minutes: '00',
				seconds: '00'
			},
			countdownTimer: null, // 倒计时定时器
		}
	},
	
	computed: {
		/**
		 * 根据评分获取评价描述
		 */
		ratingDesc() {
			const descMap = {
				1: '非常差',
				2: '较差',
				3: '一般',
				4: '非常满意，继续努力',
				5: '非常满意，继续努力'
			};
			return descMap[this.rating] || '';
		}
	},
	
	onLoad(options) {
		this.fetchOrderDetailById(options.orderNo || options.orderId);
		this.getOrderDetailByOrderNo(options.id);
		this.refundApplicationId = options.refundApplicationId === 'null' ? '' : options.refundApplicationId;
	},
	
	onUnload() {
		// 页面卸载时清除定时器
		if (this.countdownTimer) {
			clearInterval(this.countdownTimer);
			this.countdownTimer = null;
		}
	},

	methods: {
		async getOrderDetailByOrderNo(orderNo) {
			if(!orderNo) return;
			const res = await orderApi.getOrderDetailByOrderNo({orderId: orderNo});
			if(res.code === 200){
				this.canReview = res.data.canReview;
				this.reviewInfo = res.data.reviewInfo;
			}
		},
		/**
		 * 通过API获取订单详情
		 */
		async fetchOrderDetailById(orderId) {
			uni.showLoading({ title: '加载中...' });
			
			try {
				// 实际项目中应调用API获取订单详情
				const res = await orderApi.getOrderDetail({orderId: orderId});
				this.orderInfo = this.formatOrderInfo(res.data);
				
				// 如果是待支付状态，启动倒计时
				if (this.orderInfo.status === 'pending') {
					this.startCountdown();
				}
				
				// // 模拟获取订单详情
				// const mockData = this.getMockOrderData();
				// this.orderInfo = { ...this.orderInfo, ...mockData };
				
			} catch (error) {
				console.error('获取订单详情失败:', error);
				uni.showToast({
					title: '获取订单详情失败',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		/**
		 * 开始倒计时
		 */
		startCountdown() {
			// 清除之前的定时器
			if (this.countdownTimer) {
				clearInterval(this.countdownTimer);
			}
			
			// 计算剩余时间（假设订单15分钟后自动关闭）
			const createTime = new Date(this.orderInfo.createTime).getTime();
			const now = new Date().getTime();
			const orderTimeout = 15 * 60 * 1000; // 15分钟
			const endTime = createTime + orderTimeout;
			let remainingTime = endTime - now;
			
			// 如果已经超时，显示00:00
			if (remainingTime <= 0) {
				this.countdown.minutes = '00';
				this.countdown.seconds = '00';
				return;
			}
			
			// 更新倒计时显示
			const updateCountdown = () => {
				remainingTime = endTime - new Date().getTime();
				
				if (remainingTime <= 0) {
					this.countdown.minutes = '00';
					this.countdown.seconds = '00';
					clearInterval(this.countdownTimer);
					this.countdownTimer = null;
					
					// 倒计时结束，可以刷新订单状态
					this.fetchOrderDetailById(this.orderInfo.orderNumber);
					return;
				}
				
				const minutes = Math.floor(remainingTime / (1000 * 60));
				const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);
				
				this.countdown.minutes = minutes < 10 ? '0' + minutes : minutes;
				this.countdown.seconds = seconds < 10 ? '0' + seconds : seconds;
			};
			
			// 立即更新一次
			updateCountdown();
			
			// 每秒更新一次
			this.countdownTimer = setInterval(updateCountdown, 1000);
		},
		
		/**
		 * 格式化订单信息，统一数据结构
		 */
		formatOrderInfo(order) {
			// 从API返回的数据结构中提取所需信息
			const productInfo = order.productInfos && order.productInfos[0] || {};
			const sku = productInfo.skuList && productInfo.skuList[0] || {};
			const goods = productInfo.tgood || {};
			const merchant = order.merchantInfo || {};
			const orderInfo = order.torder || {};
			const verification = order.verification || {};
			// 构建统一的订单信息结构
			return {
				id: orderInfo.id || '',
				orderNumber: orderInfo.orderCode || '',
				status: this.mapOrderStatus(orderInfo.orderStatus, verification.verificationStatus),
				shopName: merchant.merchantName,
				productImage: goods.mainPic,
				productName: goods.goodsName || '',
				productSpec: sku.specName || '',
				quantity: 1, // 默认数量，实际应从订单商品明细中获取
				buyerInfo: orderInfo.accountCode,
				pickupLocation: merchant.merchantAddress || '',
				createTime: orderInfo.createTime || new Date().toLocaleString(),
				totalAmount: orderInfo.totalAmount,
				payAmount: orderInfo.totalAmount,
				rating: null, // 评分暂无
				merchantInfo: merchant,
				deliveryType: orderInfo.deliveryType,
				merchantAddress: merchant.merchantAddress || '',
				verificationCode: verification.verificationCode || '', // 核销码
				verificationType: verification.verificationType || '', // 核销类型
				orderMessage: orderInfo.orderMessage || '', // 订单留言
				deliveryAddress: orderInfo.deliveryAddress || '', // 配送类型
				receiverName: orderInfo.receiverName || '', // 配送收货人
				receiverPhone: orderInfo.receiverPhone || '', // 配送收货人电话
				reservePhone: orderInfo.reservePhone || '', // 自提电话
				payMethod: orderInfo.payMethod || '', // 支付方式
			};
		},
		
		/**
		 * 映射订单状态
		 */
		mapOrderStatus(status, verificationStatus) {
			// 将不同来源的订单状态映射为统一格式
			const statusMap = {
				'0': 'pending',  // 待支付
				'1': 'pended', // 待商家接单
				'5': 'paid',     // 待核销/待发货
				'3': 'completed', // 已完成
				'2': 'refunding', // 退款中
				'4': 'working', // 制作中
				'-6': 'refunding', // 退款中
				'-2': 'refunded', // 已退款
				'-1': 'cancelled', // 已取消
				'shipped': 'shipped'
			};
			
			// 当 status 为 1 且 verificationStatus 为 1 时，表示已完成
			if (status === '1' && verificationStatus === 1) {
				return 'completed';
			}
			
			return statusMap[status] || status || 'pending';
		},

		/**
		 * 获取模拟订单数据
		 */
		getMockOrderData() {
			const statusMap = {
				'1': { status: 'pending', rating: null },
				'2': { status: 'paid', rating: null },
				'3': { status: 'shipped', rating: null },
				'4': { status: 'completed', rating: 5 },
				'5': { status: 'completed', rating: 4 },
				'6': { status: 'refunding', rating: null },
				'7': { status: 'cancelled', rating: null }
			};
			
			const mockStatus = statusMap[this.orderInfo.id] || { status: 'pending', rating: null };
			
			return {
				...mockStatus,
				// 根据状态调整一些显示内容
				...(mockStatus.status === 'completed' && {
					productSpec: '冷/不另外加糖，好评价: 自然采光效果很棒很棒很棒很棒很棒！服务态度很好评价很长很长很长'
				}),
				...(mockStatus.status === 'refunding' && {
					productSpec: '冷/不另外加糖，退款中...'
				}),
				...(mockStatus.status === 'cancelled' && {
					productSpec: '冷/不另外加糖，订单已取消'
				})
			};
		},

		/**
		 * 获取状态文本
		 */
		getStatusText(status) {
			const statusMap = {
				'pending': '待支付',
				'paid': '待核销',
				'shipped': '已连接',
				'completed': '已完成',
				'cancelled': '已退款',
				'refunding': '退款中'
			};
			return statusMap[status] || '未知状态';
		},

		/**
		 * 获取状态描述
		 */
		getStatusDesc(status) {
			const descMap = {
				'pending': '剩余应付: ¥188.0  剩余: 23分钟49秒后订单自动关闭',
				'paid': '已连接成功，快接联系朋友～',
				'shipped': '已连接成功，快接联系朋友～',
				'completed': '订单已完成，再来一单吧～',
				'cancelled': '退款已完成，请检查余额变动',
				'refunding': '退款处理中，请耐心等待'
			};
			return descMap[status] || '';
		},

		/**
		 * 获取状态额外信息
		 */
		getStatusExtra(status) {
			if (status === 'pending') {
				return '3556';
			} else if (status === 'paid' && this.orderInfo.verificationCode) {
				return this.orderInfo.verificationCode;
			} else if (status === 'paid' || status === 'shipped') {
				return '';
			} else if (status === 'completed') {
				return '';
			} else if (status === 'cancelled' || status === 'refunding') {
				return '';
			}
			return '';
		},

		/**
		 * 获取联系人地址
		 */
		getContactAddress() {
			return this.orderInfo.deliveryAddress || '暂无地址信息';
		},
		
		/**
		 * 获取退款时间
		 */
		getRefundTime() {
			return this.orderInfo.refundTime || '2025-07-12 15:35:59';
		},
		
		/**
		 * 获取退款原因
		 */
		getRefundReason() {
			return this.orderInfo.refundReason || '商品质量问题';
		},
		
		/**
		 * 获取评分描述
		 */
		getRatingDesc(rating) {
			const descMap = {
				1: '非常差',
				2: '较差',
				3: '一般',
				4: '满意',
				5: '非常满意'
			};
			return descMap[rating] || '';
		},
		
		/**
		 * 复制订单编号
		 */
		copyOrderNumber() {
			uni.setClipboardData({
				data: this.orderInfo.orderNumber,
				success: () => {
					uni.showToast({
						title: '复制成功',
						icon: 'success'
					});
				}
			});
		},
		
		/**
		 * 取消订单
		 */
		cancelOrder() {
			this.selectedReasonIndex = -1;
			this.$refs.cancelPopup.open();
		},

		/**
		 * 关闭取消弹窗
		 */
		closeCancelPopup() {
			this.$refs.cancelPopup.close();
		},

		/**
		 * 选择取消原因
		 */
		selectReason(index) {
			this.selectedReasonIndex = index;
		},

		/**
		 * 确认取消订单
		 */
		async confirmCancel() {
			const result = await this.showConfirmDialog('确定要取消这个订单吗？');
			if (!result) return;
			const res = await orderApi.cancelOrder({
				orderNo: this.orderInfo.orderNumber
			});
			if(res.code === 200){
				uni.showToast({
					title: '订单取消成功',
					icon: 'success'
				});
				this.loadOrderDetail();
				this.closeCancelPopup();
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});
			}
		},
		
		/**
		 * 显示确认对话框
		 */
		showConfirmDialog(content) {
			return new Promise((resolve) => {
				uni.showModal({
					title: '提示',
					content,
					success: (res) => {
						resolve(res.confirm);
					}
				});
			});
		},

		/**
		 * 支付订单
		 */
		async payOrder() {
			const res = await payApi.initiatePaymentById({
				// orderId: order.id,
				orderNo: this.orderInfo.orderNumber,
				payMethod: 1
			});
			const payParams = res.data;
			uni.requestPayment({
					provider: 'wxpay',
					orderInfo: {
						"appid": payParams.appId,  // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
						"noncestr": payParams.nonceStr, // 随机字符串
						"package": "Sign=WXPay",        // 固定值
						"partnerid": payParams.mchId,      // 微信支付商户号
						"prepayid": payParams.prepayId, // 统一下单订单号
						"timestamp": payParams.timeStamp,        // 时间戳（单位：秒）
						"sign": payParams.sign // 签名，这里用的 MD5/RSA 签名
					}, // 微信支付订单信息
					success: () => {
						console.log('success');
						uni.showToast({ title: '支付成功', icon: 'success' });
						// 更新订单状态
						this.orderInfo.status = 'paid';
					},
					fail: (err) => {
						console.log('err',err);
						uni.showToast({ title: '支付失败', icon: 'none' });
					}
				});
		},

		/**
		 * 申请退款
		 */
		applyRefund() {
			uni.navigateTo({
				url: `/pages/orders/refund?orderId=${this.orderInfo.orderNumber}&amount=${this.orderInfo.totalAmount}&payMethod=${this.orderInfo.payMethod}`
			});
		},

		/**
		 * 确认收货
		 */
		confirmReceive() {
			uni.showModal({
				title: '确认收货',
				content: '确认已收到商品？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({ title: '确认中...' });
						
						setTimeout(() => {
							uni.hideLoading();
							uni.showToast({
								title: '确认成功',
								icon: 'success'
							});
							
							// 更新订单状态
							this.orderInfo.status = 'completed';
							this.orderInfo.rating = 5;
						}, 1000);
					}
				}
			});
		},

		/**
		 * 删除订单
		 */
		deleteOrder() {
			uni.showModal({
				title: '删除订单',
				content: '确定要删除这个订单吗？删除后无法恢复。',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({ title: '删除中...' });
						
						setTimeout(() => {
							uni.hideLoading();
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
							
							// 返回上一页
							setTimeout(() => {
								uni.navigateBack({
									delta: 1
								});
							}, 1500);
						}, 1000);
					}
				}
			});
		},

		/**
		 * 评价商品
		 */
		evaluateOrder() {
			this.rating = 4; // 默认4星评分
			this.ratingComment = ''; // 重置评论
			this.ratingImages = []; // 重置图片
			this.showRatingPopup = true;
		},
		
		/**
		 * 关闭评价弹窗
		 */
		closeRatingPopup() {
			this.showRatingPopup = false;
		},
		
		/**
		 * 设置评分
		 */
		setRating(rating) {
			this.rating = rating;
		},
		
		/**
		 * 选择图片
		 */
		chooseImage() {
			if (this.ratingImages.length >= 3) {
				uni.showToast({
					title: '最多只能上传3张图片',
					icon: 'none'
				});
				return;
			}
			
			uni.chooseImage({
				count: 3 - this.ratingImages.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: async (res) => {
					console.log(res);
					const res2 = await fileApi.uploadMultiple(res.tempFilePaths);
					console.log(res2);
					this.ratingImages = [...this.ratingImages, ...res2.map(item => ('http://************:9000/' + item.data))];
				}
			});
		},
		
		/**
		 * 移除图片
		 */
		removeImage(index) {
			this.ratingImages.splice(index, 1);
		},
		
		/**
		 * 提交评价
		 */
		async submitRating() {
			if (!this.rating || this.rating === 0) {
				uni.showToast({
					title: '请选择评分',
					icon: 'none'
				});
				return;
			}
			
			try {
				const res = await orderApi.reviewSubmit({
					orderId: this.orderInfo.id,
					rating: this.rating,
					reviewContent: this.ratingComment,
					reviewImages: this.ratingImages
				});
				
				if (res.code === 200) {
					// 提交成功
					uni.showToast({
						title: '评价成功',
						icon: 'success'
					});
					this.fetchOrderDetailById(this.orderInfo.orderNumber);
					this.getOrderDetailByOrderNo(this.orderInfo.id);
					// 关闭弹窗
					this.closeRatingPopup();
				} else {
					uni.showToast({
						title: res.msg || '评价失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('提交评价失败:', error);
				uni.showToast({
					title: '评价失败',
					icon: 'none'
				});
			}
		},

		/**
		 * 再次下单
		 */
		reorder() {
			uni.showLoading({ title: '正在处理...' });
			
			// 模拟再次下单流程
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '已添加到购物车',
					icon: 'success'
				});
				
				// 跳转到商品详情或购物车
				setTimeout(() => {
					uni.navigateTo({
						url: `/pages/shopping/productDetails?id=${this.orderInfo.id}`
					});
				}, 1000);
			}, 1000);
		},
		callStore() {
			uni.makePhoneCall({ phoneNumber: this.orderInfo.merchantTel });
		},
		/**
		 * 查看退款详情
		 */
		viewRefundDetail() {
			// 根据订单状态确定退款状态
			let refundStatus = 'pending';
			if (this.orderInfo.status === 'cancelled') {
				refundStatus = 'success';
			} else if (this.orderInfo.status === 'refunding') {
				refundStatus = 'pending';
			}
			
			// 跳转到退款详情页面
			uni.navigateTo({
				url: `/pages/orders/refundDetail?refundId=${this.refundApplicationId}&status=${refundStatus}&orderId=${this.orderInfo.id}`
			});
		},

		/**
		 * 查看评价详情
		 */
		viewRatingDetail() {
			this.showRatingDetailPopup = true;
		},

		/**
		 * 关闭评价详情弹窗
		 */
		closeRatingDetailPopup() {
			this.showRatingDetailPopup = false;
		}
	}
}
</script>

<style lang="scss" scoped>
::v-deep .uni-page-head{
	background-color: #fff !important;
}
/* 通用样式 */
.container {
	background: #F4F8FB;
	min-height: calc(100vh - 120rpx);
	padding-bottom: 120rpx;
}

/* 状态头部 */
.status-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 30rpx;
	color: #fff;
	margin-bottom: 20rpx;
}

.status-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.status-icon {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.status-info {
	flex: 1;
}

.status-text {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.status-desc {
	font-size: 24rpx;
	opacity: 0.9;
	line-height: 1.4;
}

.status-extra {
	align-self: flex-start;
	margin-top: 10rpx;
}

.status-extra-text {
	font-size: 36rpx;
	font-weight: bold;
	color: rgba(255, 255, 255, 0.9);
}

/* 核销码 */
.verification-section {
	background: #FFFFFF;
	padding: 30rpx;
	margin: 0 20rpx 20rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.verification-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 16rpx;
}

.verification-code {
	font-size: 48rpx;
	font-weight: bold;
	color: #4CD964;
	text-align: center;
	margin-bottom: 12rpx;
	font-family: Arial, sans-serif;
}

.verification-tip {
	font-size: 24rpx;
	color: #999;
	text-align: center;
}

/* 联系信息 */
.contact-section {
	background: #FFFFFF;
	padding: 30rpx;
	margin: 0 20rpx 20rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.contact-item {
	display: flex;
	align-items: baseline;
	margin-bottom: 16rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.contact-text {
	font-weight: 500;
	font-size: 28rpx;
	color: #666666;
}
.contact-address{
	font-weight: 500;
	font-size: 32rpx;
	color: #000000;
}
.contact-icon{
	min-width: 40rpx;
	text-align: left;
}
/* 通用区块样式 */
.section {
	background: #FFFFFF;
	margin: 0 20rpx 20rpx;
	padding: 0 30rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
	padding: 30rpx 0 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
	font-weight: 600;
	font-size: 28rpx;
	color: #000000;
}

.shop-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
}

.shop-name {
	margin-right: 8rpx;
	flex: 1;
}

.shop-arrow {
	padding: 4rpx;
}

.product-item {
	padding: 30rpx;
	background: #FFFFFF;
	.product-item-top {
		display: flex;
		align-items: flex-start;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid rgba(209, 209, 209, 0.5);
		.product-image {
			width: 100rpx;
			height: 100rpx;
			margin-right: 24rpx;
			border-radius: 8rpx;
		}
		
		.product-info {
			flex: 1;
		}
		
		.product-name {
			display: block;
			font-weight: 500;
			font-size: 24rpx;
			color: #000000;
		}
		
		.product-spec {
			display: block;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			margin-bottom: 2rpx;
		}
		
		.product-price-row {
			display: flex;
			align-items: center;
			justify-content: space-between;
			.product-price {
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
			}
			.product-price-value {
				font-weight: 600;
				font-size: 24rpx;
				color: #000000;
			}
		}
		
		
		.product-quantity {
			font-family: HelveticaNeue;
			font-size: 24rpx;
			color: #999999;
		}
	}
	.product-item-bottom{
		.info-row{
			padding: 0;
			margin-top: 16rpx;
			border-bottom: none;
			.info-label{
				font-weight: 500;
				font-size: 24rpx;
				color: #999999;
				min-width: 100rpx;
			}
			.info-value{
				font-weight: 500;
				font-size: 24rpx;
				color: #000000;
			}
		}
	}
}


/* 商品评分 */
.product-rating {
	padding: 20rpx 30rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
	background: #FFFFFF;
}

.rating-row {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.rating-label {
	font-size: 24rpx;
	color: #666;
	margin-right: 16rpx;
}

.stars {
	display: flex;
	align-items: center;
	margin-right: 12rpx;
}

.rating-score {
	font-size: 32rpx;
	color: #4CD964;
	font-weight: bold;
	margin-left: 12rpx;
}

.rating-desc {
	font-size: 24rpx;
	color: #4CD964;
}

/* 信息行 */
.info-row {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	margin-top: 16rpx;
	
	&.total-row {
		border-top: 1rpx solid #f0f0f0;
		margin-top: 20rpx;
		padding: 20rpx 0;
		justify-content: flex-end;
		.info-label{
			font-weight: 500;
			margin-left: 10rpx;
			font-size: 24rpx;
			color: #999999;
			min-width: 100rpx;
		}
		.total-amount {
			font-weight: 600;
			font-size: 24rpx;
			color: #000000;
		}
	}
	.info-label{
		font-weight: 500;
		font-size: 24rpx;
		color: #999999;
		min-width: 100rpx;
	}
	.info-value{
			font-weight: 500;
			font-size: 24rpx;
			color: #000000;
		}
	}
	
	.info-value-row {
		display: flex;
		align-items: center;
		flex: 1;
		justify-content: flex-end;
	}

.copy-btn {
	margin-left: 20rpx;
	padding: 2rpx 16rpx;
	border: 2rpx solid #D1D1D1;
	border-radius: 20rpx;
	font-weight: 400;
	font-size: 24rpx;
	color: #000000;
}



/* 底部操作栏 */
.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #FFFFFF;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #eee;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	&.safe-bottom {
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	}
}

.action-buttons {
	gap: 20rpx;
	text-align: right;
}

.action-btn {
	font-size: 28rpx;
	border-radius: 50rpx;
	padding: 0 40rpx;
	height: 72rpx;
	line-height: 72rpx;
	background: #FFFFFF;
	color: #666;
	text-align: right;
	border-radius: 46rpx;
	
	&.primary-btn {
		background: #4CD964;
		color: #FFFFFF;
		border-color: #4CD964;
	}
	
	&.secondary-btn {
		border-color: #ddd;
		color: #666;
	}

	&.square-btn {
		font-weight: 500;
		display: inline-block;
		font-size: 32rpx;
		color: #333333;
		background: #FFFFFF;
		border: 2rpx solid rgba(209, 209, 209, 0.6);
	}
	&.ljzf-btn{
		font-weight: 500;
		display: inline-block;
		font-size: 32rpx;
		color: #fff;
		background: #66D47E;
	}
}

/* 取消订单弹窗 */
.cancel-popup {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 0 0 40rpx 0;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 30rpx 20rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.cancel-reasons {
	padding: 0 30rpx;
}

.reason-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f8f8f8;
	
	&:last-child {
		border-bottom: none;
	}
	
	&.selected {
		.reason-text {
			color: #4CD964;
		}
	}
}

.reason-text {
	font-size: 28rpx;
	color: #333;
}

.popup-actions {
	display: flex;
	align-items: center;
	padding: 30rpx 30rpx 0 30rpx;
	gap: 20rpx;
}

.popup-btn {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
	
	&.cancel-popup-btn {
		background: #f8f8f8;
		color: #666;
		border: none;
	}
	
	&.confirm-popup-btn {
		background: #4CD964;
		color: #fff;
		border: none;
	}
}

/* 商品信息 */
.product-section {
	padding: 0;
	background: #FFFFFF;
	margin: 0 20rpx 20rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.shop-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 30rpx 20rpx 30rpx;
	margin: 0;
}

.shop-info {
	display: flex;
	align-items: center;
}

.shop-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
}

.service-icon {
	width: 32rpx;
	height: 32rpx;
	margin-left: 12rpx;
}

.shop-name {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}
// 
.dzf-section{
	display: flex;
	padding: 30rpx;
	image{
		width: 54rpx;
		height: 70rpx;
	}
	.verification-code-text-row{
		display: flex;
		margin-left: 20rpx;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
		.verification-code-desc{
			font-size: 24rpx;
			color: #666;
			.verification-code-text-red{
				color: #000000;
			}
			.verification-code-text-red-o{
				color: #FF5F54;
				font-weight: bold;
				font-size: 26rpx;
			}
		}
	}
}
/* 核销码区域 */
.verification-code-section {
	background: #F4F8FB;
	padding: 30rpx;
	margin: 0 0 20rpx;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	.verification-code-text {
		font-size: 54rpx;
		font-weight: bold;
		font-family: FontName;
		color: #66D47E;
		margin-bottom: 8rpx;
		letter-spacing: 2rpx;
	}
	
	.verification-code-label {
		background: #333333;
		border-radius: 13rpx 13rpx 13rpx 0rpx;
		font-weight: 600;
		padding: 4rpx 8rpx;
		font-size: 16rpx;
		color: #FFFFFF;
		margin-left: 10rpx;
	}
	
	.verification-code-desc {
		font-weight: 500;
		font-size: 23rpx;
		color: #666666;
	}
}

/* 弹窗遮罩 */
.popup-mask {
	position: fixed;
	left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.3);
	z-index: 999;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

/* 评价弹窗 */
.rating-popup {
	width: 100vw;
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	box-sizing: border-box;
	animation: popupIn .2s;
	max-height: 90vh;
	overflow: hidden;
}

@keyframes popupIn { 
	from { transform: translateY(100%); } 
	to { transform: translateY(0); } 
}

.rating-header {
	padding: 32rpx 24rpx 18rpx 24rpx;
	font-weight: 600;
	font-size: 31rpx;
	color: #000000;
	text-align: center;
	position: relative;
	border-bottom: 1rpx solid #f0f0f0;
	
	.close-icon {
		position: absolute;
		right: 24rpx;
		top: 32rpx;
		color: #bbb;
	}
}

.rating-content {
	padding: 0 24rpx 24rpx 24rpx;
	max-height: 70vh;
	overflow-y: auto;
	
	&::-webkit-scrollbar {
		display: none;
	}
}

.rating-stars {
	display: flex;
	justify-content: center;
	margin-bottom: 20rpx;
	align-items: center;
	
	.uni-icons {
		margin: 0 10rpx;
		cursor: pointer;
	}
}

.rating-text {
	text-align: center;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
	font-weight: 400;
}

.divider {
	height: 1rpx;
	background-color: #f0f0f0;
	margin: 20rpx 0;
	width: 100%;
}

.rating-textarea {
	width: 100%;
	height: 180rpx;
	background-color: #F8F8F8;
	border-radius: 12rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 30rpx;
	box-sizing: border-box;
	border: none;
	resize: none;
	
	&::placeholder {
		color: #999;
	}
}

.rating-images {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 30rpx;
	gap: 20rpx;
	align-items: flex-start;
}

.image-item {
	width: 160rpx;
	height: 160rpx;
	position: relative;
	border-radius: 8rpx;
	overflow: hidden;
	flex-shrink: 0;
	
	image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.delete-icon {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

.image-item-no-delete {
	width: 160rpx;
	height: 160rpx;
	position: relative;
	border-radius: 8rpx;
	overflow: hidden;
	flex-shrink: 0;
	
	image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.add-image {
	width: 160rpx;
	height: 160rpx;
	background-color: #F8F8F8;
	border: 2rpx solid #ddd;
	border-radius: 8rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	
	.camera-icon {
		margin-bottom: 10rpx;
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.add-text {
		font-size: 24rpx;
		color: #999;
		font-weight: 400;
	}
}

.rating-footer {
	padding: 0 24rpx 24rpx;
	border-top: 1rpx solid #f0f0f0;
	margin-top: 20rpx;
}

.submit-btn {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	background-color: #4CD964;
	color: #FFFFFF;
	font-size: 32rpx;
	border-radius: 45rpx;
	text-align: center;
	border: none;
	font-weight: 500;
	
	&[disabled] {
		background-color: #cccccc;
		color: #ffffff;
	}
}

/* 评价详情弹窗 */
.rating-detail-popup {
	width: 100vw;
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	box-sizing: border-box;
	animation: popupIn .2s;
	max-height: 90vh;
	min-height: 600rpx;
	overflow: hidden;
}

.rating-comment {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	margin-bottom: 30rpx;
	background-color: #F8F8F8;
	border-radius: 12rpx;
	padding: 20rpx;
	min-height: 60rpx;
}

.no-content-text {
	font-size: 28rpx;
	color: #999;
	text-align: center;
	width: 100%;
	padding: 40rpx 0;
}

/* 新增样式 */
.review-user-row {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16rpx;
	margin-top: 16rpx;
}

.review-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	margin-right: 16rpx;
	flex-shrink: 0;
}

.review-user-meta {
	flex: 1;
}

.review-user-nick {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 4rpx;
}

.review-stars {
	display: flex;
	align-items: center;
}

.review-time {
	font-size: 24rpx;
	color: #999;
	margin-left: 20rpx;
	flex-shrink: 0;
}

.review-content-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	margin-bottom: 20rpx;
	padding-left: 70rpx;
}

.review-images {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	padding-left: 70rpx;
}

.review-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	overflow: hidden;
	flex-shrink: 0;
}
uni-button:after{
	display: none;
}
</style>
