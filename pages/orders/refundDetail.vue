<template>
	<view class="refund-detail-container">
		<!-- 顶部状态卡片 -->
		 <view class="status-card-container">
		<view class="status-card">
			<view class="status-icon">
				<image v-if="refundStatus === '2'" src="../../static/images/order/refunderror.png" mode="aspectFit"></image>
				<image v-else src="../../static/images/order/refunding.png" mode="aspectFit"></image>
			</view>
			<view class="status-info">
				<view class="status-title">
					<text v-if="refundStatus === '0'">退款中</text>
					<text v-else-if="refundStatus === '1'">退款成功</text>
					<text v-else-if="refundStatus === '2'">退款失败</text>
				</view>
				<view class="status-desc">
					<text v-if="refundStatus === '0'">将原路退回微信账户</text>
					<text v-else-if="refundStatus === '1'">将原路退回支付宝账户</text>
					<text v-else-if="refundStatus === '2'">将原路退回岛鹏账户</text>
				</view>
			</view>
				<view class="refund-amount">¥{{(refundInfo.refundAmount / 100)}}</view>
			</view>
		
		<!-- 退款进度时间线 -->
		<view class="refund-timeline">
			<!-- 第一个节点：退款成功/失败/处理中 -->
			 <view class="timeline-line1"></view>
			<view class="timeline-item">
				<view class="timeline-dot" :class="{'active': true}">
					<image src="/static/images/order/pedding.png" mode="aspectFit"></image>
				</view>
				<view class="timeline-content">
					<view class="timeline-title" :class="{'active': true}">
						<text v-if="refundStatus === '1'">退款成功</text>
						<text v-else-if="refundStatus === '0'">系统生成退单，退款处理中</text>
						<text v-else-if="refundStatus === '2'">退款失败，可联系卖家沟通协议</text>
					</view>
					<view class="timeline-time">{{refundInfo.createTime}}</view>
				</view>
			</view>
			
			<!-- 第二个节点：系统处理中 -->
			<view class="timeline-item">
				<view class="timeline-dot">
					<image src="/static/images/order/pedding.png" mode="aspectFit"></image>
					</view>
				<view class="timeline-content">
					<view class="timeline-title" :class="{'active': refundStatus === '1' || refundStatus === '2'}">系统生成退单，退款处理中</view>
					<view class="timeline-time">{{refundInfo.processTime}}</view>
				</view>
			</view>
			
			<!-- 第三个节点：申请退款 -->
			<view class="timeline-item">
				<view class="timeline-dot">
					<image src="/static/images/order/pedding.png" mode="aspectFit"></image>
				</view>
				<view class="timeline-content">
					<view class="timeline-title" :class="{'active': true}">买家申请退款</view>
					<view class="timeline-time">{{refundInfo.applyTime}}</view>
				</view>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
import { orderApi } from '@/common/api';
	export default {
		data() {
			return {
				isDev: true, // 开发环境标识，生产环境应设置为 false
				refundStatus: 'pending', // 退款状态：pending-处理中，success-成功，failed-失败
				refundId: '', // 退款ID
				refundInfo: {
					amount: '200.00',
					reason: '商品质量问题',
					refundNo: 'RF202507120001',
					applyTime: '2025-07-12 15:35:59',
					processTime: '2025-07-12 15:35:59',
					createTime: '2025-07-12 14:21:32'
				}
			}
		},
		onLoad(options) {
			// 获取传递过来的退款ID和状态
			if (options.refundId) {
				this.refundId = options.refundId;
			}
			
			if (options.status) {
				this.refundStatus = options.status;
			}
			
			// 实际项目中应该通过API获取退款详情
			this.getRefundDetail();
		},
		methods: {
			/**
			 * 获取退款详情
			 */
			async getRefundDetail() {
				const res = await orderApi.getRefundDetail({id: this.refundId});
				if(res.code === 200){
					this.refundInfo = res.data;
					this.refundStatus = res.data.applicationStatus;
				}
			},
			
			/**
			 * 联系卖家
			 */
			contactSeller() {
				// 实现联系卖家的逻辑，如跳转到聊天页面
				uni.showToast({
					title: '跳转到聊天页面',
					icon: 'none'
				});
				
				// 实际项目中可能是跳转到聊天页面
				// uni.navigateTo({
				//   url: `/pages/chat/index?userId=${sellerId}`
				// });
			},
			
			/**
			 * 切换退款状态（仅用于测试）
			 */
			changeStatus(status) {
				this.refundStatus = status;
			}
		}
	}
</script>

<style lang="scss">
::v-deep .uni-page-head {
	background-color: #fff !important;
}

.refund-detail-container {
	min-height: 100vh;
	background-color: #F4F8FB;
}
.status-card-container{
	background: #FFFFFF;
	border-radius: 15rpx;
	padding: 30rpx;
	margin: 30rpx;
}
/* 顶部状态卡片 */
.status-card {
	background-color: #FFFFFF;
	padding: 30rpx;
	display: flex;
	align-items: center;
	position: relative;
	margin-bottom: 20rpx;
	border-bottom: 2rpx solid rgba(209, 209, 209, 0.5);
	
	.status-icon {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
		
		image {
			width: 100%;
			height: 100%;
		}
	}
	
	.status-info {
		flex: 1;
		
		.status-title {
			font-size: 34rpx;
			font-weight: 600;
			color: #000;
			margin-bottom: 10rpx;
		}
		
		.status-desc {
			font-size: 24rpx;
			color: #666;
			font-weight: 500;
		}
	}
	
	.refund-amount {
		font-weight: normal;
		font-size: 34rpx;
		color: #000000;
		position: absolute;
		right: 30rpx;
		top: 50%;
		transform: translateY(-50%);
	}
}

/* 退款进度时间线 */
.refund-timeline {
	background-color: #FFFFFF;
	padding: 30rpx;
	padding-left: 8rpx;
	margin-bottom: 20rpx;
	position: relative;
	.timeline-line1 {
		left: 16rpx;
		top: 40rpx;
		width: 2rpx;
		height: calc(100% - 120rpx); /* 修改高度，减去底部间距，使其不超出最后一个节点 */
		position: absolute;
		background-color: #D1D1D1;
	}
	.timeline-item {
		position: relative;
		padding-left: 45rpx;
		margin-bottom: 30rpx;
		&:last-child {
			margin-bottom: 0;
		}
		
		.timeline-dot {
			position: absolute;
			left: -4rpx;
			top: 0rpx;
			width: 28rpx;
			height: 28rpx;
			// background-color: #CCCCCC;
			image{
				width: 28rpx;
				height: 28rpx;
			}
		}

		
		.timeline-content {
			.timeline-title {
				font-weight: 500;
				font-size: 24rpx;
				color: #000000;
			}
			
			.timeline-time {
				font-weight: 500;
				font-size: 24rpx;
				color: #999999;
			}
		}
	}
}

/* 退款信息 */
.refund-info-section {
	background-color: #FFFFFF;
	padding: 30rpx;
	
	.section-title {
		font-size: 28rpx;
		color: #333333;
		font-weight: 600;
		margin-bottom: 20rpx;
	}
	
	.info-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.item-label {
			font-size: 26rpx;
			color: #666666;
		}
		
		.item-value {
			font-size: 26rpx;
			color: #333333;
		}
	}
}

/* 底部按钮 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #FFFFFF;
	padding: 20rpx 30rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	.contact-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #66D47E;
		color: #FFFFFF;
		font-size: 28rpx;
		border-radius: 40rpx;
		text-align: center;
	}
}

/* 测试按钮区域 */
.test-buttons {
	background-color: #FFFFFF;
	padding: 30rpx;
	margin-top: 20rpx;
	
	.test-title {
		font-size: 28rpx;
		color: #333333;
		font-weight: 600;
		margin-bottom: 20rpx;
	}
	
	.test-btn-group {
		display: flex;
		justify-content: space-around;
	}
	
	.test-btn {
		width: 200rpx;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #F0F0F0;
		color: #333333;
		font-size: 28rpx;
		border-radius: 40rpx;
		text-align: center;
		border: 1rpx solid #CCCCCC;
		
		&.active {
			background-color: #66D47E;
			color: #FFFFFF;
			border-color: #66D47E;
		}
	}
}
</style>