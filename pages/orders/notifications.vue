<template>
  <view class="order-notifications">
    <!-- Fixed header -->
    <view class="page-header">
      <view class="header-back" @click="goBack">
        <uni-icons type="left" size="20" color="#000"></uni-icons>
      </view>
      <text class="header-title">订单通知</text>
    </view>
    
    <!-- Scrollable content -->
    <scroll-view scroll-y class="notifications-container">
      <view class="notifications-list">
        <OrderNotificationCard
          v-for="(notification, index) in notifications"
          :key="index"
          :title="notification.title"
          :productName="notification.productName"
          :productSpec="notification.productSpec"
          :quantity="notification.quantity"
          :type="notification.type"
          @click="handleNotificationClick(notification)"
        />
      </view>
    </scroll-view>
  </view>
</template>

<script>
import OrderNotificationCard from './components/OrderNotificationCard.vue';

export default {
  components: {
    OrderNotificationCard
  },
  data() {
    return {
      notifications: [
        {
          title: '订单退款成功通知',
          productName: '话梅美式（标准杯）',
          productSpec: '冷/不另外加糖',
          quantity: '2',
          type: 'success',
          orderId: '123456789'
        },
        {
          title: '订单核销成功通知',
          productName: '话梅美式（标准杯）',
          productSpec: '冷/不另外加糖',
          quantity: '2',
          type: 'success',
          orderId: '987654321'
        },
        {
          title: '订单退款成功通知',
          productName: '话梅美式（标准杯）',
          productSpec: '冷/不另外加糖',
          quantity: '2',
          type: 'success',
          orderId: '456789123'
        },
        {
          title: '订单核销成功通知',
          productName: '话梅美式（标准杯）',
          productSpec: '冷/不另外加糖',
          quantity: '2',
          type: 'success',
          orderId: '789123456'
        }
      ]
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    handleNotificationClick(notification) {
      uni.navigateTo({
        url: `/pages/orders/details?orderId=${notification.orderId}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.order-notifications {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f8fb;
  
  .page-header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    position: relative;
    background-color: #fff;
    padding-top: var(--status-bar-height, 44px);
    flex-shrink: 0;
    
    .header-back {
      position: absolute;
      left: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 44rpx;
      height: 44rpx;
    }
    
    .header-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #000;
    }
  }
  
  .notifications-container {
    flex: 1;
    overflow: hidden;
    
    .notifications-list {
      padding: 32rpx;
    }
  }
}
</style> 