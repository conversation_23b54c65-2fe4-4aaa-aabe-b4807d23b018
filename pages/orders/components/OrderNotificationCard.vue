<template>
  <view class="notification-card" @click="handleClick">
    <view class="card-title">{{ title }}</view>
    <view class="card-content">
      <view class="content-row">
        <text class="label">商品名称</text>
        <text class="value">{{ productName }}</text>
      </view>
      <view class="content-row">
        <text class="label">商品规格</text>
        <text class="value">{{ productSpec }}</text>
      </view>
      <view class="content-row">
        <text class="label">商品数量</text>
        <text class="value">{{ quantity }}</text>
      </view>
    </view>
    <view class="card-footer">
      <view class="view-details">查看详情<uni-icons type="right" size="14" color="#999"></uni-icons></view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    },
    productName: {
      type: String,
      required: true
    },
    productSpec: {
      type: String,
      required: true
    },
    quantity: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'success' // 'success', 'cancelled'
    }
  },
  methods: {
    handleClick() {
      this.$emit('click');
    }
  }
}
</script>

<style lang="scss" scoped>
.notification-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 8rpx 0 rgba(0, 0, 0, 0.08);
  
  .card-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #000;
    margin-bottom: 32rpx;
  }
  
  .card-content {
    .content-row {
      display: flex;
      margin-bottom: 16rpx;
      align-items: center;
      
      .label {
        color: #999;
        font-size: 28rpx;
        width: 140rpx;
        flex-shrink: 0;
      }
      
      .value {
        color: #333;
        font-size: 28rpx;
        flex-grow: 1;
      }
    }
  }
  
  .card-footer {
    border-top: 1rpx solid rgba(0, 0, 0, 0.05);
    padding-top: 24rpx;
    margin-top: 24rpx;
    display: flex;
    justify-content: flex-end;
    
    .view-details {
      color: #999;
      font-size: 26rpx;
      display: flex;
      align-items: center;
    }
  }
}
</style> 