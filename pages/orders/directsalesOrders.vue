<template>
	<view class="container">
		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<uni-icons type="search" size="20" color="#999" />
				<input 
					class="search-input" 
					placeholder="请输入商品名称主关键字" 
					v-model="searchKeyword"
					@confirm="onSearch"
				/>
			</view>
		</view>

		<!-- 状态标签栏 -->
		<view class="tab-container">
			<view 
				v-for="(tab, index) in tabs" 
				:key="index"
				:class="['tab-item', currentTab === index ? 'active' : '']"
				@click="switchTab(index)"
			>
				<text class="tab-text">{{ tab.name }}</text>
				<text v-if="tab.count > 0">({{ tab.count }})</text>
			</view>
		</view>

		<!-- 订单列表 -->
		<scroll-view 
			class="order-list" 
			scroll-y 
			@scrolltolower="onReachBottom"
			lower-threshold="50"
		>
			<view v-if="orderList.length === 0" class="empty-state">
				<image class="empty-image" src="/static/images/order/nodata.png" mode="aspectFit"></image>
				<text class="empty-text">暂无相关订单</text>
			</view>
			
			<view v-for="order in orderList" :key="order.id" class="order-item">
				<!-- 订单头部：订单号和状态 -->
				<view class="order-header">
					<text class="order-number">订单号 {{ order.orderCode }}</text>
					<text class="order-status" :class="getStatusClass(order.orderStatus)">{{ order.orderStatusDesc }}</text>
				</view>

				<!-- 商家信息 -->
				<view class="shop-info">
					<uni-icons type="shop" size="16" color="#666" />
					<text class="shop-name">{{ order.merchantInfo.merchantName }}</text>
					<view class="settings-icon">
						<image @click="phoneCall(order.merchantInfo.merchantTel)" src="/static/images/shoping/kf.png" alt="" style="width: 30rpx;height: 30rpx;"></image>
					</view>
				</view>

				<!-- 商品信息 -->
				<view class="product-info" @click="goToOrderDetail(order)">
					<image 
						class="product-image" 
						:src="order.merchantInfo.merchantAvatar"
						mode="aspectFill"
					/>
					<view class="product-detail">
						<text class="product-name text-ellipsis">{{ order.goodsList[0].goodsName }}</text>
						<text class="product-spec">{{ order.goodsList[0].specName }}</text>
						<view class="product-quantity">
							<text>×{{ order.goodsList[0].quantity }}</text>
							<view class="price-info">
								<text class="price-label" v-if="order.orderStatus === '0'">剩余应付：</text>
								<text class="order-price" v-if="order.payMethod === '5'">{{ order.totalAmount / 10 }}贝壳币</text>
								<text class="order-price" v-else>¥ {{ order.totalAmount / 100 }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 价格信息 -->
				<!-- <view class="price-info">
					<text class="price-label">价格：</text>
					<text class="order-price">¥ {{ order.totalAmount / 100 }}</text>
				</view> -->

				<!-- 操作按钮 -->
				<view class="order-actions" v-if="['1', '0'].includes(order.orderStatus)">
					<view class="action-left">
						<text v-if="order.orderStatus === '0'" class="countdown-info">
							支付剩余 <text class="countdown"><text class="num">{{ orderCountdowns[order.id] ? orderCountdowns[order.id].minutes : '00' }}</text>分钟<text class="num">{{ orderCountdowns[order.id] ? orderCountdowns[order.id].seconds : '00' }}</text>秒</text>
						</text>
					</view>
					<view class="action-buttons">
						<button 
							v-if="order.orderStatus === '0'" 
							class="action-btn cancel-btn" 
							@click="cancelOrder(order, true)"
						>
							取消订单
						</button>
						<button 
							v-if="order.orderStatus === '1'" 
							class="action-btn cancel-btn" 
							@click="cancelOrder(order)"
						>
							申请退款
						</button>
						<button 
							v-if="order.orderStatus === '0'" 
							class="action-btn primary-btn" 
							@click="payOrder(order)"
						>
							立即支付
						</button>
						<!-- <button 
							v-if="order.orderStatus === '1'" 
							class="action-btn primary-btn" 
							@click="confirmReceive(order)"
						>
							去核销
						</button> -->
						<!-- <button 
							v-if="order.orderStatus === '2'" 
							class="action-btn primary-btn" 
							@click="evaluateOrder(order)"
						>
							评价
						</button>
						<button 
							v-if="order.orderStatus === '3'" 
							class="action-btn cancel-btn" 
							@click="viewRefundDetail(order)"
						>
							退款详情
						</button> -->
					</view>
				</view>
			</view>

			<!-- 加载状态 -->
			<view class="loading-state" v-if="orderList.length > 0">
				<view v-if="isLoading" class="loading-more">
					<text class="loading-text">加载中...</text>
				</view>
				<view v-else-if="!hasMore" class="no-more">
					<text class="no-more-text">没有更多数据了</text>
				</view>
			</view>
		</scroll-view>

		<!-- 自定义确认弹窗 -->
		<uni-popup ref="customModal" mode="center" :mask-click="false">
			<view class="confirm-popup-content">
				<view class="title">{{modalTitle}}</view>
				<view class="content">{{modalMessage}}</view>
				<view class="btn-box">
					<view class="cancel" @click="closeModal">取消</view>
					<view class="confirm" @click="confirmModal">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { orderApi, payApi } from '@/common/api';
export default {
	data() {
		return {
			// 搜索关键词
			searchKeyword: '',
			// 当前选中的标签
			currentTab: 0,
			// 标签配置
			tabs: [
				{ name: '全部', count: 0, status: '' },
				{ name: '待支付', count: 0, status: '0' },
				{ name: '待核销', count: 0, status: '1' },
				{ name: '已完成', count: 0, status: '3' },
				{ name: '售后/退款', count: 0, status: '99' }
			],
			// 订单列表
			orderList: [],
			// 分页参数
			pageNum: 1,
			pageSize: 10,
			hasMore: true,
			isLoading: false,
			// 倒计时相关
			countdownTimer: null,
			expiredOrders: new Set(), // 记录已过期的订单ID
			countdownTick: 0, // 用于强制更新倒计时显示
			// 页面状态
			isFirstLoad: true, // 标记是否首次加载
			// 页面配置
			pageOptions: {
				refreshOnShow: true, // 是否在页面显示时刷新数据
				forceRefresh: false // 是否强制刷新（忽略首次加载标记）
			},
			// 自定义弹窗相关
			modalTitle: '',
			modalMessage: '',
			confirmCallback: null,
		}
	},
	
	onLoad(options) {
		// 获取传入的状态参数
		if (options.status) {
			const tabIndex = this.tabs.findIndex(tab => tab.status === options.status);
			if (tabIndex !== -1) {
				this.currentTab = tabIndex;
			}
		}
		this.getOrderCount();
		// 处理页面配置参数
		if (options.refreshOnShow !== undefined) {
			this.pageOptions.refreshOnShow = options.refreshOnShow === 'true';
		}
		if (options.forceRefresh !== undefined) {
			this.pageOptions.forceRefresh = options.forceRefresh === 'true';
		}
		
		this.isFirstLoad = false; // 标记首次加载完成
	},
	
	onShow() {
		// 根据配置决定是否刷新数据
		if (this.pageOptions.refreshOnShow) {
			if (this.pageOptions.forceRefresh || !this.isFirstLoad) {
				this.refreshData();
			}
		}
		
		// 页面显示时启动倒计时
		this.startCountdown();
		
		// 延迟2秒后测试倒计时
		setTimeout(() => {
			this.testCountdown();
		}, 2000);
	},
	
	onHide() {
		// 页面隐藏时清除倒计时
		this.clearCountdown();
	},
	
	onUnload() {
		// 页面卸载时清除倒计时
		this.clearCountdown();
	},
	
	// 监听页面触底事件
	onReachBottom() {
		this.loadMore();
	},
	
	computed: {
		/**
		 * 获取订单倒计时（计算属性版本）
		 */
		orderCountdowns() {
			const countdowns = {};
			this.orderList.forEach(order => {
				if (order.orderStatus === '0') {
					countdowns[order.id] = this.getOrderCountdown(order);
				}
			});
			return countdowns;
		}
	},
 
	methods: {
		async getOrderCount() {
			const res = await orderApi.getOrderCount();
			this.tabs[0].count = res.data.allCount;
			this.tabs[1].count = res.data.waitPayCount;
			this.tabs[2].count = res.data.waitVerifyCount;
			this.tabs[3].count = res.data.doneCount;
			this.tabs[4].count = res.data.refundCount;
		},
		phoneCall(phone){
			uni.makePhoneCall({
				phoneNumber: phone
			});
		},
		/**
		 * 启动倒计时
		 */
		startCountdown() {
			// 清除之前的定时器
			this.clearCountdown();
			
			console.log('启动倒计时，当前订单数量:', this.orderList.length);
			
			// 每秒更新倒计时
			this.countdownTimer = setInterval(() => {
				this.updateCountdown();
			}, 1000);
		},
		
		/**
		 * 清除倒计时
		 */
		clearCountdown() {
			if (this.countdownTimer) {
				clearInterval(this.countdownTimer);
				this.countdownTimer = null;
			}
		},
		
		/**
		 * 更新倒计时
		 */
		updateCountdown() {
			const expiredOrderIds = [];
			
			// 检查每个待支付订单的倒计时
			this.orderList.forEach(order => {
				if (order.orderStatus === '0') {
					const countdown = this.getOrderCountdown(order);
					
					// 如果倒计时结束且订单未标记为过期
					if (countdown.minutes === '00' && countdown.seconds === '00' && !this.expiredOrders.has(order.id)) {
						this.expiredOrders.add(order.id);
						expiredOrderIds.push(order.id);
					}
				}
			});
			
			// 如果有过期订单，批量处理
			if (expiredOrderIds.length > 0) {
				this.handleExpiredOrders(expiredOrderIds);
			}
			
			// 强制更新倒计时显示
			this.countdownTick++;
		},
		
		/**
		 * 处理过期订单
		 */
		handleExpiredOrders(expiredOrderIds) {
			console.log('过期订单ID列表:', expiredOrderIds);
			
			// 可以在这里添加批量处理过期订单的逻辑
			// 例如：批量取消订单、发送通知等
			
			// 如果需要刷新列表，可以调用
			// this.refreshList();
			
			// 或者只更新特定订单的状态
			// this.updateOrderStatus(expiredOrderIds);
		},
		
		/**
		 * 更新特定订单的状态
		 */
		updateOrderStatus(orderIds) {
			// 更新本地订单列表中指定订单的状态
			this.orderList.forEach(order => {
				if (orderIds.includes(order.id)) {
					// 将订单状态更新为已过期或已取消
					order.orderStatus = '4'; // 假设4表示已取消
					order.orderStatusDesc = '已过期';
				}
			});
		},
		
		/**
		 * 刷新数据（类似onLoad的逻辑）
		 */
		refreshData() {
			console.log('页面显示，重新加载数据');
			
			// 重置分页参数
			this.pageNum = 1;
			this.orderList = [];
			this.hasMore = true;
			this.expiredOrders.clear();
			
			// 重新加载订单列表
			this.loadOrderList();
		},
		
		/**
		 * 强制刷新数据（忽略首次加载标记）
		 */
		forceRefreshData() {
			console.log('强制刷新数据');
			this.isFirstLoad = false; // 重置首次加载标记
			this.refreshData();
		},
		
		/**
		 * 测试倒计时功能
		 */
		testCountdown() {
			console.log('=== 倒计时测试 ===');
			console.log('当前订单列表:', this.orderList);
			console.log('倒计时定时器状态:', this.countdownTimer ? '运行中' : '已停止');
			console.log('倒计时Tick:', this.countdownTick);
			
			this.orderList.forEach(order => {
				if (order.orderStatus === '0') {
					const countdown = this.getOrderCountdown(order);
					console.log(`订单 ${order.orderCode}: ${countdown.minutes}:${countdown.seconds}`);
				}
			});
		},
		
		/**
		 * 批量取消过期订单
		 */
		async batchCancelExpiredOrders(orderIds) {
			try {
				uni.showLoading({ title: '处理中...' });
				
				// 批量调用取消订单API
				const cancelPromises = orderIds.map(orderId => {
					const order = this.orderList.find(o => o.id === orderId);
					if (order) {
						return orderApi.cancelOrder({
							orderNo: order.orderCode
						});
					}
					return Promise.resolve();
				});
				
				await Promise.all(cancelPromises);
				
				uni.hideLoading();
				uni.showToast({
					title: '过期订单已处理',
					icon: 'none'
				});
				
				// 刷新列表
				this.refreshList();
				
			} catch (error) {
				uni.hideLoading();
				console.error('批量取消订单失败:', error);
				uni.showToast({
					title: '处理失败',
					icon: 'none'
				});
			}
		},
		
		/**
		 * 获取订单倒计时
		 */
		getOrderCountdown(order) {
			if (order.orderStatus !== '0') {
				return { minutes: '00', seconds: '00' };
			}
			
			// 获取订单创建时间
			const createTime = new Date(order.createTime).getTime();
			const now = new Date().getTime();
			const orderTimeout = 15 * 60 * 1000; // 15分钟
			const endTime = createTime + orderTimeout;
			const remainingTime = endTime - now;
			
			// 如果已经超时，返回00:00
			if (remainingTime <= 0) {
				return { minutes: '00', seconds: '00' };
			}
			
			// 计算分钟和秒数
			const minutes = Math.floor(remainingTime / (1000 * 60));
			const seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);
			
			const result = {
				minutes: minutes < 10 ? '0' + minutes : minutes.toString(),
				seconds: seconds < 10 ? '0' + seconds : seconds.toString()
			};
			
			// 调试信息
			if (this.countdownTick % 10 === 0) { // 每10秒打印一次，避免日志过多
				console.log(`订单 ${order.orderCode} 倒计时: ${result.minutes}:${result.seconds}, createTime: ${order.createTime}`);
			}
			
			return result;
		},

		/**
		 * 搜索订单
		 */
		onSearch() {
			this.pageNum = 1;
			this.orderList = [];
			this.hasMore = true;
			this.expiredOrders.clear(); // 清空过期订单记录
			this.loadOrderList();
		},

		/**
		 * 切换标签
		 */
		switchTab(index) {
			if (this.currentTab === index) return;
			
			this.currentTab = index;
			this.pageNum = 1;
			this.orderList = [];
			this.hasMore = true;
			this.expiredOrders.clear(); // 清空过期订单记录
			this.loadOrderList();
		},

		/**
		 * 加载订单列表
		 */
		async loadOrderList() {
			this.getOrderCount();
			if (this.isLoading || !this.hasMore) return;
			
			this.isLoading = true;
			
			try {
				const params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					orderStatus: this.tabs[this.currentTab].status,
					goodsName: this.searchKeyword
				};
				
				const res = await orderApi.getOrderList(params);
				const { rows, total } = res;

				if (this.pageNum === 1) {
					this.orderList = rows;
				} else {
					this.orderList.push(...rows);
				}
				
				// 判断是否还有更多数据
				this.hasMore = this.orderList.length < total;
				
				// 加载完数据后启动倒计时
				if (this.pageNum === 1) {
					console.log('订单数据:', this.orderList);
					this.startCountdown();
				}
				
			} catch (error) {
				console.error('加载订单列表失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.isLoading = false;
			}
		},

		async getRefundList() {
			if (this.isLoading || !this.hasMore) return;
			
			this.isLoading = true;
			
			try {
				const params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					orderStatus: this.tabs[this.currentTab].status,
					goodsName: this.searchKeyword
				};
				
				const res = await orderApi.getRefundList(params);
				const { rows, total } = res;

				if (this.pageNum === 1) {
					this.orderList = rows;
				} else {
					this.orderList.push(...rows);
				}
				
				// 判断是否还有更多数据
				this.hasMore = this.orderList.length < total;
				
			} catch (error) {
				console.error('加载订单列表失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.isLoading = false;
			}
		},
		/**
		 * 刷新列表
		 */
		refreshList() {
			this.pageNum = 1;
			this.orderList = [];
			this.hasMore = true;
			this.expiredOrders.clear(); // 清空过期订单记录
			this.loadOrderList();
		},

		/**
		 * 触底加载更多
		 */
		onReachBottom() {
			this.loadMore();
		},

		/**
		 * 加载更多
		 */
		loadMore() {
			if (!this.hasMore || this.isLoading) return;
			
			this.pageNum++;
			this.loadOrderList();
		},

		/**
		 * 跳转到订单详情
		 */
		goToOrderDetail(order) {
			// 导航到详情页，同时传递订单ID作为参数
			uni.navigateTo({
				// url: `/pages/orders/details?orderNo=j7bssgNdzi7B&id=75`
				url: `/pages/orders/details?orderNo=${order.orderCode}&id=${order.id}&refundApplicationId=${order.refundApplicationId}`
			});
		},
		
		/**
		 * 支付订单
		 */
		async payOrder(order) {
			const res = await payApi.initiatePaymentById({
				// orderId: order.id,
				orderNo: order.orderCode,
				payMethod: 1
			});
			const payParams = res.data;
			uni.requestPayment({
					provider: 'wxpay',
					orderInfo: {
						"appid": payParams.appId,  // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
						"noncestr": payParams.nonceStr, // 随机字符串
						"package": "Sign=WXPay",        // 固定值
						"partnerid": payParams.mchId,      // 微信支付商户号
						"prepayid": payParams.prepayId, // 统一下单订单号
						"timestamp": payParams.timeStamp,        // 时间戳（单位：秒）
						"sign": payParams.sign // 签名，这里用的 MD5/RSA 签名
					}, // 微信支付订单信息
					success: () => {
						console.log('success');
						uni.showToast({ title: '支付成功', icon: 'success' });
						// 支付成功后刷新订单列表
						this.refreshList();
					},
					fail: (err) => {
						console.log('err',err);
						uni.showToast({ title: '支付失败', icon: 'none' });
					}
				});
		},

		/**
		 * 取消订单
		 */
		async cancelOrder(order, flag) {
			if(flag){
				uni.navigateTo({
					url: `/pages/orders/cancal?orderId=${order.orderCode}&amount=${order.totalAmount}&payMethod=${order.payMethod}`
				});
			} else {
				uni.navigateTo({
					url: `/pages/orders/refund?orderId=${order.orderCode}&amount=${order.totalAmount}&payMethod=${order.payMethod}`
				});
			}
			// const result = await this.showConfirmDialog('确定要取消这个订单吗？');
			// if (!result) return;
			
			// try {
			// 	uni.showLoading({ title: '取消中...' });
				
			// 	// 实际项目中调用取消API
			// 	const res = await orderApi.cancelOrder({
			// 		orderNo: order.orderCode,
			// 	});
			// 	if(res.code === 200){
			// 		uni.showToast({
			// 			title: '订单取消成功',
			// 			icon: 'none'
			// 		});
			// 		this.refreshList(); // 刷新列表
			// 	} else {
			// 		uni.showToast({
			// 			title: res.msg,
			// 			icon: 'none'
			// 		});
			// 	}
			// } catch (error) {
			// 	uni.hideLoading();
			// 	console.error('取消订单失败:', error);
			// 	uni.showToast({
			// 		title: '取消失败',
			// 		icon: 'none'
			// 	});
			// }
		},

		/**
		 * 确认核销
		 */
		async confirmReceive(order) {
			const result = await this.showConfirmDialog('确认要核销此订单？');
			if (!result) return;
			
			try {
				uni.showLoading({ title: '核销中...' });
				
				// 实际项目中调用确认核销API
				// await orderApi.confirmOrder(order.id);
				
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '核销成功',
						icon: 'success'
					});
					this.refreshList(); // 刷新列表
				}, 1000);
				
			} catch (error) {
				uni.hideLoading();
				console.error('确认核销失败:', error);
				uni.showToast({
					title: '核销失败',
					icon: 'none'
				});
			}
		},

		/**
		 * 删除订单
		 */
		async deleteOrder(order) {
			const result = await this.showConfirmDialog('确定要删除这个订单吗？删除后无法恢复。');
			if (!result) return;
			
			try {
				uni.showLoading({ title: '删除中...' });
				
				// 实际项目中调用删除API
				// await orderApi.deleteOrder(order.id);
				
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '删除成功',
						icon: 'none'
					});
					this.refreshList(); // 刷新列表
				}, 1000);
				
			} catch (error) {
				uni.hideLoading();
				console.error('删除订单失败:', error);
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				});
			}
		},

		/**
		 * 评价订单
		 */
		evaluateOrder(order) {
			uni.navigateTo({
				url: `/pages/shopping/evaluate?orderId=${order.id}`
			});
		},

		/**
		 * 查看退款详情
		 */
		viewRefundDetail(order) {
			uni.navigateTo({
				url: `/pages/orders/refundDetail?orderId=${order.id}`
			});
		},

		/**
		 * 获取状态样式类
		 */
		getStatusClass(status) {
			const statusMap = {
				'0': 'status-pending',
				'1': 'status-paid',
				'2': 'status-completed',
				'3': 'status-refund'
			};
			return statusMap[status] || '';
		},

		/**
		 * 获取状态文本
		 */
		getStatusText(status) {
			const statusMap = {
				'0': '待支付',
				'1': '待核销',
				'2': '已完成',
				'3': '售后/退款'
			};
			return statusMap[status] || '未知状态';
		},
						/**
		 * 显示确认对话框
		 */
		 showConfirmDialog(content, title = '提示') {
			this.modalTitle = title;
			this.modalMessage = content;
			// 使用 nextTick 确保 DOM 更新后再打开弹窗
			this.$nextTick(() => {
				if (this.$refs.customModal) {
					this.$refs.customModal.open();
				} else {
					console.warn('弹窗组件未找到，使用备用方案');
					// 备用方案：使用 uni.showModal
					uni.showModal({
						title: title,
						content: content,
						success: (res) => {
							if (this.confirmCallback) {
								this.confirmCallback(res.confirm);
							}
						}
					});
				}
			});
			this.confirmCallback = null; // 清空之前的回调
			return new Promise((resolve) => {
				this.confirmCallback = resolve;
			});
		},
				/**
		 * 关闭自定义弹窗
		 */
		 closeModal() {
			if (this.$refs.customModal) {
				this.$refs.customModal.close();
			}
			this.modalTitle = '';
			this.modalMessage = '';
			this.confirmCallback = null;
		},

		/**
		 * 确认自定义弹窗
		 */
		confirmModal() {
			if (this.confirmCallback) {
				this.confirmCallback(true);
			}
			this.closeModal();
		},
	}
}
</script>

<style lang="scss">
::v-deep .uni-page-head{
	background-color: #fff !important;
}
.container {
	background: #F4F8FB;
	min-height: 100vh;
	overflow-x: hidden; /* 防止横向滚动 */
	width: 100%;
	box-sizing: border-box;
}

/* 搜索框样式 */
.search-container {
	// background: #fff;
	padding: 20rpx;
	// border-bottom: 1rpx solid #eee;
	width: 100%;
	box-sizing: border-box;
}

.search-box {
	display: flex;
	align-items: center;
	background: #fff;
	border-radius: 30rpx;
	padding: 0 24rpx;
	height: 70rpx;
	width: 100%;
	box-sizing: border-box;
}

.search-input {
	flex: 1;
	margin-left: 16rpx;
	font-size: 28rpx;
	color: #333;
}

/* 标签栏样式 */
.tab-container {
	display: flex;
	// background: #fff;
	// border-bottom: 1rpx solid #eee;
	width: 100%;
	box-sizing: border-box;
	overflow-x: auto;
	overflow-y: hidden;
	-webkit-overflow-scrolling: touch;
}

.tab-item {
	position: relative;
	flex: none;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 88rpx;
	border-bottom: 4rpx solid transparent;
	white-space: nowrap;
	min-width: 120rpx;
	padding: 0 20rpx;
	
	&.active {
		border-bottom-color: #66D47E;
		
		.tab-text {
			color: #000;
			font-weight: bold;
		}
	}
}

.tab-text {
	font-size: 26rpx;
	color: #666;
	white-space: nowrap;
}

.tab-badge {
	position: absolute;
	top: 10rpx;
	right: 20rpx;
	background: #ff4757;
	color: #fff;
	font-size: 20rpx;
	border-radius: 20rpx;
	padding: 4rpx 12rpx;
	min-width: 32rpx;
	text-align: center;
	line-height: 1;
}

/* 订单列表样式 */
.order-list {
	flex: 1;
	padding: 20rpx;
	box-sizing: border-box;
	width: 100%;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
	padding: 40rpx 0;
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-weight: 600;
	font-size: 27rpx;
	color: #666666;
}

.order-item {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
	width: 100%;
	box-sizing: border-box;
}

/* 订单头部 */
.order-header {
	display: flex;
	border-bottom: 1px solid #f5f5f5;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
	width: 100%;
	padding-bottom: 16rpx;
}

.order-number {
	font-size: 26rpx;
	color: #333;
	max-width: 70%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.order-status {
	padding: 4rpx 0;
	border-radius: 4rpx;
	font-weight: 600;
	font-size: 28rpx;
	
	&.status-pending {
		color: #FF5F54;
	}
	
	&.status-paid {
		color: #66D47E;
	}
	
	&.status-completed {
		color: #666666;
	}
	
	&.status-refund {
		color: #F9AF25;
	}
}

/* 商家信息 */
.shop-info {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	width: 100%;
}

.shop-name {
	margin-left: 8rpx;
	font-size: 24rpx;
	color: #666;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.settings-icon {
	margin-left: auto;
}

/* 商品信息 */
.product-info {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	width: 100%;
	box-sizing: border-box;
}

.product-image {
	width: 100rpx;
	height: 100rpx;
	border-radius: 8rpx;
	background: #f0f0f0;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.product-detail {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.product-name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
	line-height: 1.4;
}

.text-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.product-spec {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 4rpx;
}

.product-quantity {
	font-size: 24rpx;
	color: #999;
	display: flex;
	justify-content: space-between;
}

/* 价格信息 */
.price-info {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	// margin-bottom: 24rpx;
	width: 100%;
}

.price-label {
	font-size: 24rpx;
	color: #666;
}

.order-price {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

/* 操作按钮 */
.order-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	flex-wrap: wrap;
	width: 100%;
	border-top: 1px solid #f5f5f5;
	padding-top: 16rpx;
}

.action-left {
	min-width: 0;
}

.countdown-info {
	font-weight: 500;
	font-size: 24rpx;
	color: #000000;
}

.countdown {
	font-weight: 500;
	font-size: 24rpx;
	color: #000000;
	.num{
		color: #FF5F54;
	}
}

.delivery-info {
	font-size: 24rpx;
	color: #999;
}

.action-buttons {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	justify-content: flex-end;
}

.action-btn {
	font-size: 24rpx;
	border-radius: 30rpx;
	padding: 0 24rpx;
	height: 60rpx;
	line-height: 60rpx;
	margin-left: 16rpx;
	// border: 1rpx solid #ddd;
	background: #fff;
	color: #666;
	
	&.primary-btn {
		background: #66D47E;
		color: #fff;
		border-color: #66D47E;
	}
	
	&.cancel-btn {
		border: 1px solid #ddd;
		color: #666;
	}
}

/* 加载状态 */
.loading-state {
	padding: 20rpx 0;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	width: 100%;
}

.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-text {
	font-size: 24rpx;
	color: #999;
}

.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}

.no-more-text {
	font-size: 24rpx;
	color: #999;
	position: relative;
	padding: 0 30rpx;
	
	&::before,
	&::after {
		content: '';
		position: absolute;
		top: 50%;
		width: 80rpx;
		height: 1px;
		background: #ddd;
	}
	
	&::before {
		left: -60rpx;
	}
	
	&::after {
		right: -60rpx;
	}
}
uni-button:after{
	display: none;
}
/* 自定义弹窗样式 */
.confirm-popup-content {
		width: 540rpx;
		background-color: #fff;
		border-radius: 30rpx;
		padding: 40rpx 60rpx;
		box-sizing: border-box;

		.title {
			font-family: 苹方-简, 苹方-简;
			font-weight: normal;
			font-size: 30rpx;
			color: #000000;
			line-height: 42rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
			margin-bottom: 30rpx;
		}

		.content {
			font-family: 苹方-简, 苹方-简;
			font-weight: normal;
			font-size: 24rpx;
			color: #666666;
			line-height: 36rpx;
			text-align: center;
		}

		.btn-box {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;

			.cancel,
			.confirm {
				width: 200rpx;
				height: 80rpx;
				border-radius: 40rpx;
			}
		}

		.cancel {
			width: 200rpx;
			height: 80rpx;
			background-color: #F5F5F5;
			border-radius: 40rpx;
			color: #666666;
			text-align: center;
			line-height: 80rpx;
		}

		.confirm {
			width: 200rpx;
			height: 80rpx;
			background: #34BC4D;
			border-radius: 40rpx;
			color: #fff;
			text-align: center;
			line-height: 80rpx;
		}
	}
</style>
