<template>
	<div>
		<div class="dialog-wrap">
			<div class="content">
				<text class="title">温馨提示</text>
				<text class="pop-tip">请确认您当前是否安全？</text>
				<div class="btn-group">
					<text class="pop-btn" @click="closeDialog">取消</text>
					<text class="pop-btn ok" @click="closeDialog">确定</text>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
		mounted() {
		  uni.$on('showDialog', () => {
				uni.getSubNVueById('popup').show('zoom-fade-out', 300)
			})	
		},
		beforeDestroy() {
			uni.$off('showDialog')
		},
		methods: {
			closeDialog() {
				uni.getSubNVueById('popup').hide('zoom-fade-in', 300)
			}
		}
	}
</script>

<style>
.dialog-wrap {
	/* position: fixed;
	bottom: 0;
	right: 0;
	left: 0;
	top: 0;
	background-color: rgba(0,0,0, .45); */
	display: flex;
	align-items: center;
	justify-content: center;
}
.content {
	width: 680rpx;
	height: 348rpx;
	border-radius: 16rpx;
	background-color: #fff;
}
.title {
	height: 48rpx;
	line-height: 48rpx;
	font-size: 32rpx;
	color: #000;
	text-align: center;
	margin: 52rpx 0 32rpx;
}
.pop-tip {
	height: 40rpx;
	line-height: 40rpx;
	color: #666;
	font-size: 28rpx;
	text-align: center;
	margin-bottom: 40rpx;
}
.btn-group {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	padding: 0 64rpx;
}

.pop-btn {
	width: 260rpx;
	height: 96rpx;
	line-height: 96rpx;
	background-color: #f5f5f5;
	border-radius: 48rpx;
	text-align: center;
	color: #666;
	font-weight: 500;
	font-size: 32rpx;
}

.pop-btn.ok {
	background-color: #66d47e;
	color: #fff;
}
</style>