<template>
	<div>
		<div class="during-wrap">
			<div class="status-wrap">
				<image class="status-icon" src="/static/images/expertOrder/ic_doing.png"></image>
				<div class="status-info">
					<div class="status-txt">
						<text>持续时间</text>
						<text class="remain"> 00:50:49</text>
					</div>
					<text class="tip">约会正在持续中</text>
				</div>
				<text class="btn-call" @click="openDialog">报警</text>
			</div>
			<div class="btns">
				<text class="btn">退款</text>
				<text class="btn renew" @click="gotoRenew">续费延时</text>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
		methods: {
			openDialog() {
				uni.$emit("showDialog")
			},
			gotoRenew() {
				uni.navigateTo({
					url: '/pages/orderRenew/orderRenew'
				})
			}
		}
	}
</script>

<style>
.during-wrap {
	/* position: absolute;
	bottom: 100rpx;
	left: 0;
	right: 0;
	width: 100%;
	background-color: #fff;
	box-sizing: border-box; */
	height: 340rpx;
	padding: 48rpx 32rpx;
	background-color: #fff;
	border-radius: 16rpx 16rpx 0 0;
}
.status-wrap {
	height: 96rpx;
	padding: 0 8rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
}

.status-icon {
	height: 72rpx;
	width:56rpx;
	margin-right: 24rpx;
}
.status-info {
	flex: 1;
}
.status-txt {
	display: flex;
	flex-direction: row;
	height: 56rpx;
	line-height: 56rpx;
	font-size: 36rpx;
	color: #000;
}
.remain {
	color: #ff5f54;
	padding-right: 40rpx;
}
.tip {
	color: #666;
	font-size: 24rpx;
	height: 40rpx;
	line-height: 40rpx;
}
.btn-call {
	height: 60rpx;
	line-height: 60rpx;
	text-align: center;
	width: 112rpx;
	background-color: #ff5f54;
	color: #fff;
	border-radius: 30rpx;
	font-size: 24rpx;
}
.btns {
	margin-top: 48rpx;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}

.btn {
	height: 92rpx;
	line-height: 92rpx;
	text-align: center;
	color: #333;
	font-size: 32rpx;
	width: 320rpx;
	border: 2rpx solid #d1d1d1;
	border-radius: 48rpx;
}
.renew {
	background-color: #66d47e;
	border-color: #66d47e;
	color: #fff;
}
</style>