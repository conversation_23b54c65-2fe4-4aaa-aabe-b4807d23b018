<template>
	<view class="appointment-detail-page">
		<map
			style="width: 100%;height: 100%"
			:longitude="location.longitude"
			:latitude="location.latitude"
			scale="16"
			v-if="location.latitude"
		></map>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				location: {
					longitude: '',
					latitude: ''
				},
			}
		},
		onLoad() {
			this.getCurrLocation()
		},
		methods: {
			getCurrLocation() {
				uni.getLocation({
					type: 'bcj02',
					success: res => {
						console.log('经纬度', res)
						this.location.longitude = res.longitude
						this.location.latitude = res.latitude
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.appointment-detail-page {
		position: relative;
		height: 100vh;
	}
</style>