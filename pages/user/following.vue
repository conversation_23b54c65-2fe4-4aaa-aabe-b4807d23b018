<template>
	<view class="container">
		<!-- 用户列表 -->
		<view class="user-list">
			<view 
				class="user-item"
				v-for="user in following"
				:key="user.id"
			>
				<view class="user-info" @tap="navigateToUser(user)">
					<image class="avatar" :src="user.avatar" mode="aspectFill"></image>
					<view class="info-content">
						<text class="nickname">{{user.nickname}}</text>
						<text class="bio">{{user.bio || '这个人很懒，什么都没写~'}}</text>
					</view>
				</view>
				
				<button 
					class="follow-btn active"
					@tap="unfollow(user)"
				>已关注</button>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<uni-load-more :status="loadMoreStatus"></uni-load-more>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="following.length === 0 && !isLoading">
			<image class="empty-image" src="/static/images/empty-following.png" mode="aspectFit"></image>
			<text class="empty-text">还没有关注任何人哦~</text>
			<button class="discover-btn" @tap="navigateToDiscover">去发现有趣的人</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userId: '',
			following: [],
			page: 1,
			loadMoreStatus: 'more',
			isLoading: false
		}
	},
	
	onLoad(options) {
		this.userId = options.userId;
		this.loadFollowing();
	},
	
	onPullDownRefresh() {
		this.page = 1;
		this.loadFollowing().then(() => {
			uni.stopPullDownRefresh();
		});
	},
	
	onReachBottom() {
		if (this.loadMoreStatus === 'more') {
			this.loadFollowing();
		}
	},
	
	methods: {
		// 加载关注列表
		async loadFollowing() {
			if (this.isLoading) return;
			
			this.isLoading = true;
			this.loadMoreStatus = 'loading';
			
			try {
				const { list, total } = await this.$api.getFollowing({
					userId: this.userId,
					page: this.page
				});
				
				if (this.page === 1) {
					this.following = list;
				} else {
					this.following = [...this.following, ...list];
				}
				
				this.loadMoreStatus = this.following.length >= total ? 'noMore' : 'more';
				this.page++;
			} catch (error) {
				this.loadMoreStatus = 'more';
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.isLoading = false;
			}
		},
		
		// 取消关注
		async unfollow(user) {
			uni.showModal({
				title: '提示',
				content: `确定不再关注 ${user.nickname} 吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							await this.$api.unfollowUser(user.id);
							
							// 从列表中移除
							const index = this.following.findIndex(item => item.id === user.id);
							if (index !== -1) {
								this.following.splice(index, 1);
							}
						} catch (error) {
							uni.showToast({
								title: '操作失败',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		
		// 跳转到用户主页
		navigateToUser(user) {
			uni.navigateTo({
				url: `/pages/user/profile?id=${user.id}`
			});
		},
		
		// 跳转到发现页
		navigateToDiscover() {
			uni.switchTab({
				url: '/pages/community/community'
			});
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.user-list {
	.user-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
		
		.user-info {
			flex: 1;
			display: flex;
			align-items: center;
			margin-right: 30rpx;
			
			.avatar {
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}
			
			.info-content {
				flex: 1;
				
				.nickname {
					font-size: 30rpx;
					color: #333;
					font-weight: 500;
					margin-bottom: 6rpx;
				}
				
				.bio {
					font-size: 26rpx;
					color: #999;
					@include text-ellipsis;
				}
			}
		}
		
		.follow-btn {
			width: 140rpx;
			height: 56rpx;
			line-height: 56rpx;
			font-size: 26rpx;
			color: $color-primary;
			background: rgba($color-primary, 0.1);
			border-radius: 28rpx;
			padding: 0;
			
			&.active {
				color: #fff;
				background: $color-primary;
			}
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 120rpx 60rpx;
	
	.empty-image {
		width: 320rpx;
		height: 320rpx;
		margin-bottom: 40rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 60rpx;
	}
	
	.discover-btn {
		width: 320rpx;
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		color: #fff;
		background: $color-primary;
		border-radius: 40rpx;
	}
}
</style> 