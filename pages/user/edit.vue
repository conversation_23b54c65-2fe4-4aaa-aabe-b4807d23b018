<template>
	<view class="container">
    <NavigationCustorm>
      <template #title>
        编辑资料
      </template>
    </NavigationCustorm>
		<!-- 头像 -->
		<view class="avatar-section">
      <view class="avatar-box" @tap="chooseAvatar">
        <image 
				class="avatar" 
				:src="`${HTTP_IMG}${userStore?.userInfo.avatar}`" 
        ></image>
        <text class="tip">更换</text>
      </view>
		</view>
		<!-- 个人相册 -->
		<!-- <FilePicker></FilePicker> -->
    <!-- <view class="line"></view> -->
		<!-- 表单 -->
		<view class="form-section">
			<view class="form-item">
				<text class="label">个人相册</text>
				<view class="value" @click="goToPage('/pages/user/MyAlbum')">
					<uni-view  class="is-add"><uni-view class="icon-add"></uni-view><uni-view class="icon-add rotate"></uni-view></uni-view>
					<uni-icons type="arrowright" size="20" color="#999"></uni-icons>
				</view>
			</view>
			<view class="line"></view>
			<view class="form-item">
				<text class="label">昵称</text>
				<view class="value" @click="goToPage('/pages/user/components/NickName')">
					<text>{{ userStore?.userInfo?.nickname}}</text>
					<uni-icons type="arrowright" size="20" color="#999"></uni-icons>
				</view>
			</view>
			<view class="form-item">
				<text class="label">用户ID</text>
				<text>{{ userStore?.userInfo?.accountCode}}</text>
			</view>
			<!-- 生日 -->
			<view class="form-item">
				<text class="label">生日</text>
				<picker
					mode="date"
					:value="userStore?.userInfo?.birthday"
					start="1900-01-01"
					:end="today"
					@change="setBirthday"
				>
					<view class="picker">
						<text>{{userStore?.userInfo.birthday || '保密'}}</text>
						<uni-icons type="arrowright" size="20" color="#999"></uni-icons>
					</view>
				</picker>
			</view>
			<!-- 性别 -->
			<view class="form-item">
				<text class="label">性别</text>
				<view>
					<radio-group  @change="radioChange">
					<label class="radio"><radio color="#66D47E" value="1" :checked="userStore?.userInfo?.sex === 1" />男</label>
					<label class="radio"><radio color="#66D47E" value="2" :checked="userStore?.userInfo?.sex === 2" />女</label>
				</radio-group>
				</view>
			</view>
			<!-- 所在地 -->
			<CityPicker></CityPicker>
			<!-- 身高	 -->
			 <HightPicker></HightPicker>
			 <!-- 体重	 -->
			 <WeightPicker></WeightPicker>
			 <!-- 职业	 -->
			 <OccupationPicker></OccupationPicker>
       <view class="line"></view>
			 <!-- 个性签名 -->
			 <view class="form-item">
				<text class="label">个性签名</text>
				<view class="value" @click="goToPage('/pages/user/components/SignaturePage')">
					<text>{{ userStore?.userInfo?.signature || '添加个性签名' }}</text>
					<uni-icons type="arrowright" size="20" color="#999"></uni-icons>
				</view>
			</view>
      <view class="line"></view>
			<!-- 邀请码	 -->
			<ReferralCode></ReferralCode>
			<view class="line"></view>
			<!-- 紧急联系人 -->
			<view class="form-item">
				<text class="label">紧急联系人</text>
				<view class="value" @click="goToPage('/pages/user/components/lianxiren')">
					<text>未设置</text>
					<uni-icons type="arrowright" size="20" color="#999"></uni-icons>
				</view>
			</view>
			<view class="line"></view>
			<view class="line"></view>
			<view class="line"></view>
		</view>
		<!-- 保存按钮 -->
		<view class="btn-section">
			<button 
				class="save-btn"
				@click="goToPage('/pages/user/tag')"
			>编辑标签</button>
		</view>
	</view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import CityPicker from './components/CityPicker.vue'
import FilePicker from './components/FilePicker.vue'  
import HightPicker from './components/HightPicker.vue'  
import WeightPicker from './components/WeightPicker.vue'  
import OccupationPicker from './components/OccupationPicker.vue'  
import NickName from './components/NickName.vue'
import ReferralCode from './components/ReferralCode.vue'
import { userApi } from '../../common/api'
import { HTTP_IMG } from '@/common/constant'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

export default {
	components:{NickName,CityPicker,FilePicker,HightPicker,WeightPicker,OccupationPicker,NickName,NavigationCustorm,ReferralCode},
	setup() {
		const userStore = useUserStore()
		const userInfo = ref({
			avatar: '',
			nickname: '',
			bio: '',
			gender: 0,
			birthday: '',
			region: ''
		})
		const region = ref([])
		const today = ref(new Date().toISOString().split('T')[0])
		const isSaving = ref(false)

		const isFormValid = computed(() => {
			return userInfo.value.nickname.trim().length > 0
		})


		// 选择头像
		const chooseAvatar = () => {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: async (res) => {
					const tempFilePath = res.tempFilePaths[0]
					debugger
					try {
						uni.showLoading({
							title: '上传中...',
							mask: true
						})

						// 上传头像
						const { url } = await userApi.updateAvatar(tempFilePath)

						userStore.getUserInfo()
					} catch (error) {
						debugger
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						})
					} finally {
						uni.hideLoading()
					}
				}
			})
		}
		const radioChange = async(e)=>{
			await userStore.updateUserInfo({
				sex: Number(e.detail?.value)
			})
		}
		// 设置性别
		const setGender = (gender) => {
			userInfo.value.gender = gender
		}

		// 设置生日
		const setBirthday = async(e) => {
      try {
        await userStore.updateUserInfo({
          birthday:e.detail?.value
        })
        userInfo.value.birthday = e.detail.value
      } catch (error) {
        debugger
      }
			
		}

		// 设置地区
		const setRegion = (e) => {
			region.value = e.detail.value
			userInfo.value.region = region.value.join(' ')
		}
		// 跳转页面
		const goToPage =(url)=>{
			uni.navigateTo({
					url
				})
		}
		// 保存资料
		const saveProfile = async () => {
			if (!isFormValid.value) {
				uni.showToast({
					title: '请填写昵称',
					icon: 'none'
				})
				return
			}

			if (isSaving.value) return

			isSaving.value = true

			try {
				await this.$api.updateUserInfo(userInfo.value)
				userStore.setUserInfo(userInfo.value)
				uni.showToast({
					title: '保存成功'
				})
				uni.navigateBack()
			} catch (error) {
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			} finally {
				isSaving.value = false
			}
		}

		onMounted(() => {
			// loadUserInfo()
		})

		return {
			userStore,
			userInfo,
			region,
			today,
			isSaving,
			isFormValid,
			chooseAvatar,
			setGender,
			setBirthday,
			setRegion,
			saveProfile,
			radioChange,
			goToPage,
			HTTP_IMG
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: #f4f8fb;
	padding: 192rpx 0 120rpx 0;
	box-sizing: border-box;
}
.avatar-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 36rpx 0;
	background-color: #f4f8fb;
	
	.avatar {
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
	}
	.avatar-box{
    height: 160rpx;
    width: 160rpx;
		position: relative;
    border-radius: 50%;
    overflow: hidden;
	}
	.tip {
    height: 52rpx;
		font-size: 26rpx;
		color: #fff;
    background: rgba(0,0,0,0.7);
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 0;
    left: 0;
    border-radius: 10rpx;
	}
}

.form-section {
	background-color: #f4f8fb;
	
	.form-item {
    height: 104rpx;
		position: relative;
		display: flex;
    font-size: 32rpx;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
    color: #999;
    background-color: #fff;
    .picker{
      uni-text{
      color: #999 !important;
    }
    }
		
		&:last-child {
			border-bottom: none;
		}
		
		.label {
			width: 200rpx;
			font-size: 32rpx;
			color: #000;
			padding-top: 4rpx;
		}
		.value{
			display: flex;
			align-items: center;
		}
		.input {
			flex: 1;
			height: 44rpx;
			font-size: 30rpx;
		}
		
		.textarea {
			flex: 1;
			height: 160rpx;
			font-size: 30rpx;
		}
		
		.count {
			position: absolute;
			right: 30rpx;
			bottom: 30rpx;
			font-size: 24rpx;
			color: #999;
		}
		
		.gender-options {
			flex: 1;
			display: flex;
			
			.gender-item {
				display: flex;
				align-items: center;
				margin-right: 60rpx;
				
				.iconfont {
					font-size: 36rpx;
					color: #999;
					margin-right: 10rpx;
				}
				
				text {
					font-size: 30rpx;
					color: #333;
				}
				
				&.active {
					.iconfont {
						color: $color-primary;
					}
					
					text {
						color: $color-primary;
					}
				}
			}
		}
		
		.picker {
			flex: 1;
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			text {
				font-size: 30rpx;
				color: #333;
				
				&.iconfont {
					color: #999;
				}
			}
		}
	}
}

.btn-section {
	height: 160rpx;
	box-sizing: border-box;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 16rpx 30rpx;
	background-color: #fff;
	z-index: 9;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	.save-btn {
		height: 88rpx;
		line-height: 88rpx;
		background:#66D47E;
		color: #fff;
		font-size: 32rpx;
		border-radius: 44rpx;
		
		&[disabled] {
			opacity: 0.6;
		}
	}
}
.line{
  height: 24rpx;
  background-color: #f4f8fb;
}
.is-add{
  width: 90rpx;
  height: 90rpx;
  border-radius: 16rpx;
	display: flex;
    align-items: center;
    justify-content: center;
		background-color: #F3F3F3;
}
.icon-add{	
	display: flex;
    align-items: center;
    justify-content: center;
	width: 4rpx;
    height: 40rpx;
    background: #ADADAD;
    border-radius: 0.5rem !important;
    border: none !important;

		&.rotate {
			position: absolute;
			transform: rotate(90deg);
	}
}
.icon-add-rotate{
  transform: rotate(45deg);
}
.uni-button:after{
  border: none;
}
</style> 