<template>
	<view class="container">
		<!-- 用户列表 -->
		<view class="user-list">
			<view 
				class="user-item"
				v-for="user in followers"
				:key="user.id"
			>
				<view class="user-info" @tap="navigateToUser(user)">
					<image class="avatar" :src="user.avatar" mode="aspectFill"></image>
					<view class="info-content">
						<text class="nickname">{{user.nickname}}</text>
						<text class="bio">{{user.bio || '这个人很懒，什么都没写~'}}</text>
					</view>
				</view>
				
				<button 
					class="follow-btn"
					:class="{active: user.isFollowing}"
					@tap="toggleFollow(user)"
				>{{user.isFollowing ? '已关注' : '关注'}}</button>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<uni-load-more :status="loadMoreStatus"></uni-load-more>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userId: '',
			followers: [],
			page: 1,
			loadMoreStatus: 'more',
			isLoading: false
		}
	},
	
	onLoad(options) {
		this.userId = options.userId;
		this.loadFollowers();
	},
	
	onPullDownRefresh() {
		this.page = 1;
		this.loadFollowers().then(() => {
			uni.stopPullDownRefresh();
		});
	},
	
	onReachBottom() {
		if (this.loadMoreStatus === 'more') {
			this.loadFollowers();
		}
	},
	
	methods: {
		// 加载粉丝列表
		async loadFollowers() {
			if (this.isLoading) return;
			
			this.isLoading = true;
			this.loadMoreStatus = 'loading';
			
			try {
				const { list, total } = await this.$api.getFollowers({
					userId: this.userId,
					page: this.page
				});
				
				if (this.page === 1) {
					this.followers = list;
				} else {
					this.followers = [...this.followers, ...list];
				}
				
				this.loadMoreStatus = this.followers.length >= total ? 'noMore' : 'more';
				this.page++;
			} catch (error) {
				this.loadMoreStatus = 'more';
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.isLoading = false;
			}
		},
		
		// 关注/取消关注
		async toggleFollow(user) {
			try {
				if (user.isFollowing) {
					await this.$api.unfollowUser(user.id);
				} else {
					await this.$api.followUser(user.id);
				}
				user.isFollowing = !user.isFollowing;
			} catch (error) {
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				});
			}
		},
		
		// 跳转到用户主页
		navigateToUser(user) {
			uni.navigateTo({
				url: `/pages/user/profile?id=${user.id}`
			});
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.user-list {
	.user-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
		
		.user-info {
			flex: 1;
			display: flex;
			align-items: center;
			margin-right: 30rpx;
			
			.avatar {
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}
			
			.info-content {
				flex: 1;
				
				.nickname {
					font-size: 30rpx;
					color: #333;
					font-weight: 500;
					margin-bottom: 6rpx;
				}
				
				.bio {
					font-size: 26rpx;
					color: #999;
					@include text-ellipsis;
				}
			}
		}
		
		.follow-btn {
			width: 140rpx;
			height: 56rpx;
			line-height: 56rpx;
			font-size: 26rpx;
			color: $color-primary;
			background: rgba($color-primary, 0.1);
			border-radius: 28rpx;
			padding: 0;
			
			&.active {
				color: #fff;
				background: $color-primary;
			}
		}
	}
}
</style> 