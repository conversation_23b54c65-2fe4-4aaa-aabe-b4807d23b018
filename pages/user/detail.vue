<template>
    <view class="bg-img-container">
        <uni-icons type="back" size="20" color="#fff" @click="handleBack" class="back-icon"></uni-icons>
      <image src="@/static/images/profile/image.png" mode="widthFix" class="bg-img"></image>
      <!-- start -->
      <view class="user-info">
        <view class="user-header">
          <text class="user-name">彩虹堂</text>
          <text class="user-badge">已实名</text>
        </view>
        <view class="user-meta">
          <text class="user-tag">♀ 19</text>
          <text class="user-location">南京市</text>
          <text class="user-distance">9.34km</text>
          <text class="user-online">在线</text>
          <text class="user-likes">1416获赞</text>
          <text class="user-fans">416粉丝</text>
        </view>
        <view class="user-tabs">
          <view class="tab" :class="{active: activeTab === 0}" @click="handleTab(0)">关于她</view>
          <view class="tab" :class="{active: activeTab === 1}" @click="handleTab(1)">动态149</view>
          <view class="tab" :class="{active: activeTab === 2}" @click="handleTab(2)">技能</view>
          <view class="tab" :class="{active: activeTab === 3}" @click="handleTab(3)">橱窗</view>
        </view>
      </view>
      <view class="tab-content">
        <view v-if="activeTab === 0">关于她内容</view>
        <view v-else-if="activeTab === 1">动态内容</view>
        <view v-else-if="activeTab === 2">技能内容</view>
        <view v-else-if="activeTab === 3">橱窗内容</view>
      </view>
      <!-- end -->
      <view class="bottom-btns">
        <button class="btn-outline" @click="handleEdit">编辑资料</button>
        <button class="btn-solid" @click="handlePublish">发动态</button>
      </view>
    </view>
</template>
<script setup>
import { ref } from 'vue'
const activeTab = ref(0)
const handleTab = (idx) => {
  activeTab.value = idx
}
const handleBack = () => {
  uni.navigateBack()
}
const handleEdit = () => {
  uni.navigateTo({
    url: '/pages/user/edit'
  })
}
const handlePublish = () => {
    //导航到发布选择页面
    uni.switchTab({
        url: '/pages/post/choose'
  })
}
</script>
<style lang="scss" scoped>
.bg-img-container {
  width: 100%;
  height: 100%;
}
.back-icon {
  position: absolute;
  left: 32rpx;
  top: 32rpx;
  z-index: 10;
}
.bg-img {
  width: 100%;
  height: 100%;
}
.bottom-btns {
  position: fixed;
  left: 0;
  bottom: 32rpx;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 10;
  
  .btn-outline {
    margin-right: 16rpx;
  }
  
  .btn-solid {
    margin-left: 16rpx;
  }
}
.btn-outline {
  background: #fff;
  color: #222;
  border: 2rpx solid #222;
  border-radius: 50rpx;
  padding: 0 48rpx;
  height: 80rpx;
  font-size: 32rpx;
  line-height: 80rpx;
  box-sizing: border-box;
}
.btn-solid {
  background: #000;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 0 48rpx;
  height: 80rpx;
  font-size: 32rpx;
  line-height: 80rpx;
  box-sizing: border-box;
}
.user-info {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx 24rpx 0 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  position: relative;
  z-index: 2;
}
.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  
  .user-name {
    margin-right: 16rpx;
  }
}
.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
}
.user-badge {
  font-size: 22rpx;
  background: #a98cff;
  color: #fff;
  border-radius: 16rpx;
  padding: 2rpx 16rpx;
  margin-left: 8rpx;
}
.user-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  
  .user-tag {
    margin-right: 18rpx;
    margin-bottom: 6rpx;
  }
  
  .user-location, .user-distance, .user-online, .user-likes, .user-fans {
    font-size: 22rpx;
    margin-right: 18rpx;
    margin-bottom: 6rpx;
  }
}
.user-tag {
  background: #eaf7f0;
  color: #3bb368;
  border-radius: 12rpx;
  padding: 2rpx 12rpx;
  font-size: 22rpx;
}
.user-location, .user-distance, .user-online, .user-likes, .user-fans {
  font-size: 22rpx;
}
.user-online {
  color: #3bb368;
}
.user-tabs {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #eee;
  margin-top: 12rpx;
}
.tab {
  font-size: 28rpx;
  color: #888;
  padding: 0 18rpx 12rpx 18rpx;
  position: relative;
  cursor: pointer;
}
.tab.active {
  color: #3bb368;
  font-weight: bold;
}
.tab.active::after {
  content: '';
  display: block;
  position: absolute;
  left: 18rpx;
  right: 18rpx;
  bottom: -2rpx;
  height: 4rpx;
  background: #3bb368;
  border-radius: 2rpx;
}
.tab-content {
  min-height: 200rpx;
  padding: 32rpx ;
  color: #333;
  font-size: 28rpx;
}
</style>