<template>
  <view class="my-album">
    <NavigationCustorm >
      <template #title>
        个人相册
      </template>
    </NavigationCustorm>
    <view class="file-picker">

      <uni-file-picker v-model="imageValue" fileMediatype="image" mode="grid" @select="select" @progress="progress"
        @success="success" @fail="fail" @delete="deleteFile" />
    </view>
  </view>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { userApi, profileApi } from '@/common/api'
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()
import { HTTP_IMG } from '@/common/constant'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

const imageValue = ref([])
const select = async res => {
  try {
    uni.showLoading({
      title: '上传中...',
      mask: true
    })
    res?.tempFilePaths?.map(async (item) => {
      const { url } = await userApi.addAlbum(item)
    })

  } catch (error) {
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}
const deleteFile = async (e) => {
  if (e.tempFile?.orgin) {
    await profileApi.deleteAlbum({
      dataId: e.tempFile?.orgin
    })
  }
}
onMounted(() => {
  imageValue.value = userStore?.userInfo?.albumPhotos?.filter(item => item).map(item => {
    return {
      url: HTTP_IMG + item,
      orgin: item
    }
  })
})
const progress = () => { }
const success = () => { }
const fail = () => { }
</script>
<style lang="scss" scoped>
.my-album{
  height: 100vh;
  background: #f4f8fb;
  padding: 192rpx 0 120rpx 0;
  box-sizing: border-box;
}
.file-picker {
  padding: 40rpx 30rpx;

}

.title {
  font-size: 32rpx;
  color: #000;
  margin-bottom: 20rpx;
}



:deep(.is-add) {
  background: #F3F3F3;
  border-radius: 32rpx !important;
  border: none !important;
}

:deep(.icon-add) {
  width: 4rpx;
  height: 40rpx;
  background-color: #ADADAD;
}
</style>