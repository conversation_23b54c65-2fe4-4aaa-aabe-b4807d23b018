<template>
  <div class="tag-page">
    <NavigationCustorm>
      <template #title>
        选择标签
      </template>
    </NavigationCustorm>
    <div class="tag-content">
      <div class="category" v-for="cat in categories" :key="cat.name">
        <div class="cat-title">{{ cat.groupName }}</div>
        <div class="tags">
          <div v-for="tag in cat.options" :key="tag.option" :class="['tag', { selected: tag.selected }]"
            @click="toggleTag(cat, tag)">{{ tag.option }}
          </div>
        </div>
      </div>
    </div>
    <div class="btn-box">
      <button :class="{
        'active': isDisabled
      }" class="confirm-btn" @click="onConfirm">确定</button>
    </div>
  </div>
</template>

<script setup>
import { reactive, computed } from 'vue';
import { userApi, profileApi, tagApi } from '../../common/api';
import { useUserStore } from '@/stores/user'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

const categories = reactive([]);
const userStore = useUserStore()
const isDisabled = computed(() => {
  const selected = categories.flatMap(cat => cat.options?.filter(t => t.selected)?.map(t => t.option));
  return selected.length > 0
})
const goBack = () => {
  uni.navigateBack()
}
function toggleTag(cat, tag) {
  tag.selected = !tag.selected;
}
const queryTags = async () => {
  try {
    const res = await profileApi.queryAllTags()
    Object.assign(categories, res.data)
    debugger

  } catch (error) {
    console.log(error)
  }
}
queryTags()
async function onConfirm() {
  if (!isDisabled.value) return
  // 这里可以处理选中的标签
  const selected = categories.flatMap(cat => cat.options?.filter(t => t.selected).map(t => t.option));
  try {
    await tagApi.commitTags(selected)
    userStore.getUserInfo()
  } catch (error) {
    console.log(error)
  }
}
</script>

<style scoped>
.tag-page {
  background: #F4F8FB;
  min-height: 100vh;
  overflow-y: auto;
  padding: 0;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;

  .tag-page-header {
    padding-top: 100rpx;
  }

  .tag-page-header-title {
    width: 100%;
    height: 92rpx;
    line-height: 92rpx;
    font-size: 34rpx;
    color: #000000;
    font-weight: 600;
    text-align: center;
    position: relative;

    .icon {
      position: absolute;
      left: 32rpx;
    }
  }
}

.category {
  margin-top: 30rpx;
  margin-bottom: 18px;
  padding: 0 20px;
}

.cat-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #222;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px 12px;
}

.tag {
  background: #FFF;
  color: #333333;
  border-radius: 8px;
  padding: 6px 18px;
  font-size: 15px;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  transition: background 0.2s, color 0.2s;
  font-weight: 500;
}

.tag.selected {
  background: #66D47E;
  color: #fff;
}

.icon {
  margin-right: 4px;
}

.btn-box{
  height: 160rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  padding: 16rpx 30rpx;
  box-sizing: border-box;
}
.confirm-btn {
  width: 100%;
  height: 96rpx;

  background: #BEEDC5;
  color: #fff;
  border: none;
  border-radius: 48rpx;
  font-size: 36rpx;
  font-weight: 500;
  cursor: pointer;

  &.active {
    background: #66D47E;
  }
}

.tag-content {
  padding: 192rpx 0 120rpx 0;
  box-sizing: border-box;
  margin-bottom: 200rpx;
  height: calc(100vh - 100rpx);
  overflow-y: auto;
}
</style>
