<template>
    <view class="signature-page">
      <NavigationCustorm>
        <template #title>
          个性签名
        </template>
      </NavigationCustorm>
      <view class="signature-content">
        <textarea
            class="textarea"
            v-model="signature"
            placeholder="介绍一下自己吧"
            maxlength="200"
        ></textarea>
        <div class="count">{{ signature?.length }}/200</div>
      </view>
      <view class="btn-box">
        <button class="btn-confirm" @click="handleConfirm">确定</button>
      </view>
    </view>
</template>
<script setup>
import { ref } from 'vue';
import { useUserStore } from '@/stores/user'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
const userStore = useUserStore() 
const signature = ref(userStore.userInfo?.signature)
const handleConfirm = async()=>{
  try {
    await userStore.updateUserInfo({
      signature:signature.value
    })
    uni.navigateBack()
  } catch (error) {
    console.log(error)
  }
}
</script>
<style lang="scss">
.signature-page{
  background: #F4F8FB;
  height: 100vh;
  padding: 192rpx 0 120rpx 0;
  box-sizing: border-box;
}
.signature-content{
  padding: 30rpx;
  box-sizing: border-box;
  position: relative;
}
.count{
  position: absolute;
  right: 50rpx;
  bottom: 50rpx;
  font-size: 24rpx;
  color: #999;
}
.textarea{
  width: 100%;
  height: 288rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  border: 2rpx solid #D1D1D1;
  padding: 20rpx;
  box-sizing: border-box;
}
.btn-confirm{
  width: 100%;
  height: 96rpx;
  background: #66D47E;
  border-radius: 48rpx;
  color: #fff !important;
}
.btn-box{

  padding: 30rpx;
  box-sizing: border-box;
}
</style>