<template>
  <view class="lianxiren-container">
    <NavigationCustorm @goBack="goBack">
      <template #title>
        添加紧急联系人
      </template>
    </NavigationCustorm>
    <view class="lianxiren-content">
      <!-- 联系人信息输入区域 -->
      <view class="contact-info-section">
        <view class="input-item">
          <text class="label">联系人姓名</text>
          <input 
            class="input-field" 
            placeholder="请输入" 
            v-model="contactName"
            placeholder-class="placeholder"
          />
        </view>
        <view class="input-item">
          <text class="label">手机号码</text>
          <input 
            class="input-field" 
            placeholder="请输入" 
            v-model="phoneNumber"
            type="number"
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <!-- 优先联系人设置区域 -->
      <view class="priority-section">
        <view class="priority-header">
          <view class="priority-info">
            <text class="priority-title">优先联系人</text>
            <text class="priority-desc">如遇紧急情况平台无法联系到您时,会给TA打电话</text>
          </view>
          <switch 
            :checked="isPriorityContact" 
            @change="handlePriorityChange"
            color="#007AFF"
          />
        </view>
      </view>

      <!-- 保存按钮 -->
      <!-- <view class="save-section">
        <button class="save-btn" @click="handleSave">保存</button>
      </view> -->
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onShow, onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user'
import { settingApi } from '@/common/api'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

const emit = defineEmits(['goBack'])  

const userStore = useUserStore()

const contactName = ref('');
const phoneNumber = ref('');
const isPriorityContact = ref(false);

const handlePriorityChange = (e) => {
  isPriorityContact.value = e.detail.value;
}

const handleSave = async () => {
  if (!contactName.value.trim()) {
    return;
  }
  
  if (!phoneNumber.value.trim()) {
    return;
  }

  // 简单的手机号验证
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(phoneNumber.value)) {
    return;
  }

  try {
    const res = await settingApi.saveEmergencyContact({
        name: contactName.value,
        phone: phoneNumber.value,
        first: isPriorityContact.value?1:0
      });
    
  } catch (error) {
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    });
  }
}
const goBack = () => {
  console.log('goBack')
  handleSave()
}
onLoad((options) => {
  contactName.value = options.name || ''
  phoneNumber.value = options.phone || ''
  isPriorityContact.value = options.first==1 ? true : false
})
</script>
<style lang="scss" scoped>
.lianxiren-container {
  background: #F4F8FB;
  min-height: 100vh;
  padding: 192rpx 0 40rpx 0;
  box-sizing: border-box;
}

.lianxiren-content {
  margin: 16rpx 0rpx;
}

.contact-info-section {
  background: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.input-item {
  display: flex;
  align-items: center;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #F5F5F5;
  
  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 30rpx;
  color: #000;
  min-width: 160rpx;
}

.input-field {
  flex: 1;
  font-size: 30rpx;
  color: #999;
  text-align: right;
}

.placeholder {
  color: #999999;
}

.priority-section {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 48rpx;
}

.priority-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.priority-info {
  flex: 1;
  margin-right: 24rpx;
}

.priority-title {
  display: block;
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.priority-desc {
  display: block;
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}

.save-section {
  padding: 0 32rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #66D47E;
  color: #FFFFFF;
  border-radius: 44rpx;
  font-size: 32rpx;
  border: none;
  
  &:active {
    background: #66D47E;
  }
}
:deep(.uni-switch-input.uni-switch-input-checked){
  background: #66D47E !important;
  border-color: #66D47E !important;
} 
</style>