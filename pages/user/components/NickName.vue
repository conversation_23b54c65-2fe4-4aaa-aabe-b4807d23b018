<template>
  <view class="nickname-container">
    <NavigationCustorm>
      <template #title>
        昵称
      </template>
    </NavigationCustorm>
    <view class="nickname-content">
      <view class="nickname-input">
        <input class="uni-input" v-model="nickname" focus placeholder="请输入昵称" />
      </view>
      <view class="nickname-button">
        <button type="primary" class="primary-button-btn" @click="handleNicknameChange">保存</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { useUserStore } from '@/stores/user'
import { userApi } from '@/common/api'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

const userStore = useUserStore()


const nickname = ref('');

const handleNicknameChange = async () => {
  console.log(nickname.value);
  const res = await userStore.updateUserInfo({
    nickname: nickname.value
  })
  if (res) {
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
  }
}
</script>
<style lang="scss" scoped>
.nickname-container {
  background: #F4F8FB;
  height: 100vh;
  padding: 192rpx 0 120rpx 0;
  box-sizing: border-box;
}

.nickname-content {
  padding: 30rpx;
}

.nickname-input {
  height: 96rpx;
  line-height: 96rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  border: 2rpx solid #D1D1D1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.primary-button-btn {
  width: 100%;
  height: 96rpx;
  background: #66D47E;
  border-radius: 48rpx;
  color: #fff !important;
  margin-top: 30rpx;
}
</style>