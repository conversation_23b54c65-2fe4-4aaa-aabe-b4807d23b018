<template>
    <view class="form-item">
        <text class="label">所在地</text>
        <uni-data-picker v-slot:default="{ data, error, options }" :localdata="citydata" popup-title="请选择省市区"
            @change="onchange" @nodeclick="onnodeclick">
            <view class="selectedAddress">
                <view v-if="data.length" class="selected">
                    <view v-for="(item, index) in data" :key="index" class="selected-item">
                        <text>{{ item.text }}<text v-if="index<2">-</text></text>
                    </view>
                    <uni-icons type="arrowright" size="20" color="#999"></uni-icons>
                </view>
                <view v-if="data.length===0" class="addrlocation">
                    {{userStore.userInfo?.location || '保密'}}
                    <uni-icons type="arrowright" size="20" color="#999"></uni-icons>
                </view>
                
            </view>
        </uni-data-picker>

    </view>
</template>
<script setup>
import citydata from './citydata.json'
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()


const onchange = async(e)=>{
  try {
    const region = e.detail.value.map(item=>{
        return item.text
    }).join('-')
    userStore.updateUserInfo({
      location:region
    })
  } catch (error) {
    
  }
}
const onnodeclick = ()=>{
    
}

</script>
<style lang="scss" scoped>
.form-item{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.label {
    width: 140rpx;
    font-size: 30rpx;
    color: #333;
    padding-top: 4rpx;
}
.addrlocation{
    height: 30rpx;
    text-align: right;
}
.selected{
    display: flex;
    justify-content: end;
}
</style>