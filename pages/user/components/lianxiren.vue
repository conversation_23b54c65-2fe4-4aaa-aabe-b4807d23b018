<template>
  <view class="lianxiren-container">
    <NavigationCustorm>
      <template #title>
        紧急联系人
      </template>
    </NavigationCustorm>
    <view class="lianxiren-content">
      <view class="header">
        <view class="left">
          <view class="lianxiren-title">
            管理紧急联系人
          </view>
          <view>添加或修改紧急联系人后请及时告知对方</view>
        </view>
        <view class="right">
          <image src="/static/images/profile/lianxiren.png" mode="widthFix" style="width: 152rpx;height: 134rpx;">
          </image>
        </view>
      </view>
      <view v-if="lianxiren.length > 0" >
        <view class="lianxiren-card-data" v-for="item in lianxiren" :key="item.phone" @click="goToPage(`/pages/user/components/lianxiren-edit?name=${item.name}&phone=${item.phone}&first=${item.first}`)">
            <view class="lianxiren-card-content-left">
              <view class="lianxiren-card-content-left-name">{{ item.name }}</view>
            </view>
            <view class="lianxiren-card-content-right ">
              <view class="lianxiren-card-content-right-phone">{{ maskPhone(item.phone) }}</view>
              <uni-icons type="right" size="20" color="#666"></uni-icons>
            </view>
        </view>
      </view>
      <view class="lianxiren-card" @click="goToPage('/pages/user/components/lianxiren-edit')">
        <image src="/static/images/profile/icon-add-2.png" mode="widthFix"
          style="width: 40rpx;height: 40rpx;margin-right: 20rpx;"></image>
        紧急联系人
      </view>

      <view class="lianxiren-card function-list-card">
        <view class="function-list">
          <view class="function-item">

            <view class="text">
              <view class="bullet"></view>用户开启行程分享后,紧急联系人会收到包含接单信息、接单人、实时位置及行驶路线的短信或APP通知。
            </view>
          </view>
          <view class="function-item">
            <view class="text">
              <view class="bullet"></view>仅在使用110报警或紧急情况下,会主动通知紧急联系人
            </view>
          </view>
          <view class="function-item">
            <view class="text">
              <view class="bullet"></view>您可针对不同的紧急联系人开启或关闭亲友守护
            </view>
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { useUserStore } from '@/stores/user'
import { settingApi } from '@/common/api'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

const userStore = useUserStore()


const lianxiren = ref([]);

const handlelianxirenChange = async () => {
  console.log(lianxiren.value);
  const res = await userStore.updateUserInfo({
    lianxiren: lianxiren.value
  })
  if (res) {
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
  }
}
const goToPage = (url) => {
  uni.navigateTo({
    url: url
  })
}
const maskPhone = (phone) => {
  if (!phone || phone.length !== 11) {
    return phone
  }
  return phone.substring(0, 3) + '****' + phone.substring(7)
}

const getEmergencyContact = async () => {
  const res = await settingApi.getEmergencyContact()
  if (res) {
    lianxiren.value = res.data
  }
}
getEmergencyContact()
</script>
<style lang="scss" scoped>
.lianxiren-container {
  background: #F4F8FB;
  min-height: 100vh;
  padding: 192rpx 0 40rpx 0;
  box-sizing: border-box;
}

.lianxiren-content {
  height: 100%;
  padding: 40rpx 30rpx;
  background: linear-gradient(180deg, #66D47E 0%, #F4F8FB 572rpx, #F4F8FB 100%);
  border-radius: 0px 0px 0px 0px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left {
  display: flex;
  flex-direction: column;
  color: #fff;

  .lianxiren-title {
    font-size: 50rpx;
    font-weight: 500;
    color: #FFFFFF;
    line-height: 76rpx;
    margin-bottom: 16rpx;
  }

}

.right {
  width: 152rpx;
  height: 134rpx;
}

.lianxiren-card {
  background: #FFFFFF;
  color: #666;
  border-radius: 16rpx;
  padding: 48rpx;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 46rpx;
  &.function-list-card {
    padding: 30rpx;
    background: #F6F7FB;
  }
}
.lianxiren-card-data{
  background: #FFFFFF;
  color: #000;
  border-radius: 16rpx;
  padding: 48rpx 20rpx 48rpx 48rpx;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 46rpx;
}
.function-list {
  .function-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .bullet {
      color: #666;
      width: 12rpx;
      height: 12rpx;
      background: #666;
      border-radius: 50%;
      margin-right: 16rpx;
      margin-top: 4rpx;
      flex-shrink: 0;
      display: inline-block;
    }

    .text {
      color: #666;
      font-size: 24rpx;
      line-height: 38rpx;
      flex: 1;
    }
  }

}
.lianxiren-card-content-right{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>