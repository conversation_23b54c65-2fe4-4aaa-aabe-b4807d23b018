<script setup>
import { ref } from 'vue'; // 假设使用 Vue 3，需要导入 ref
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()

const popup = ref(null)
const referralCodeInput = ref('')

const handleClick = () => {
  if(userStore.userInfo?.referralCode){
    return
  }
  popup.value.open('center')
}

const handleConfirm = async () => {
  try {
    if (!referralCodeInput.value.trim()) {
      uni.showToast({
        title: '请输入邀请码',
        icon: 'none'
      })
      return
    }
    
    await userStore.updateUserInfo({
      referralCode: referralCodeInput.value.trim()
    })
    
    uni.showToast({
      title: '绑定成功',
      icon: 'success'
    })
    
    popup.value.close()
    referralCodeInput.value = ''
  } catch (error) {
    console.log(error)
    uni.showToast({
      title: '绑定失败，请重试',
      icon: 'none'
    })
  }
}

const handleCancel = () => {
  popup.value.close()
  referralCodeInput.value = ''
}
</script>

<template>
  <view class="uni-list">
    <view class="uni-list-cell">
      <view class="uni-list-cell-left">
        推荐人
      </view>
      <view @click="handleClick">{{userStore.userInfo?.referralCode || '去绑定'}}<uni-icons v-if="!userStore.userInfo?.referralCode" type="arrowright" size="20" color="#999"></uni-icons></view>
    </view>
    
    <uni-popup ref="popup" style="width: 660rpx;" class="referral-popup">
      <view class="popup-content">
        <view class="popup-top">
          <image src="/static/images/profile/popup-top.png" class="popup-top-image" mode="widthFix"></image>
        </view>
        <view class="popup-main">
          <view class="popup-title">输入邀请码</view>
          <view class="input-container">
            <input 
              v-model="referralCodeInput"
              placeholder="输入Ta的邀请码"
              placeholder-class="input-placeholder"
              class="referral-input"
              maxlength="20"
            />
          </view>
          <view class="btn-row">
            <view class="btn-cancel" @click="handleCancel">取消</view>
            <view class="btn-confirm" @click="handleConfirm">绑定</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<style lang="scss" scoped>
.referral-popup{
  z-index: 100;
}
.uni-list:after{
  background: none;
}

.uni-list-cell{
  height: 48rpx;
  padding: 30rpx;
  font-size: 32rpx;
  color: #999;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .uni-list-cell-left{
    padding: 0;
    font-size: 32rpx;
    color: #000;
  }
}

.popup-content{
  width: 660rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 200;
}

.popup-top{
  position: relative;
  top:130rpx;
  z-index: 2;
  
  .popup-top-image{
    width: 508rpx;
    height: auto;
    display: block;
  }
}

.popup-main{
  width: 708rpx;
  min-height: 580rpx;
  background-image: url('/static/images/profile/referral-code-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 0 0 32rpx 32rpx;
  padding: 200rpx 80rpx 80rpx 80rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -10px;
  position: relative;
  z-index: 1;
}

.popup-title{
  font-family: 苹方-简-中粗体, 苹方-简;
font-weight: normal;
font-size: 46rpx;
color: #000000;
line-height: 46rpx;
  margin-bottom: 60rpx;
  text-align: center;
}

.input-container{
  width: 100%;
  margin-bottom: 40px;
}

.referral-input{
  width: 100%;
  height: 100rpx;
  background: #FFFFFF;
  border: 1px solid #CCCCCC;
  border-radius: 16rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  
  &:focus{
    border-color: #4CAF50;
    background: rgba(255, 255, 255, 1);
  }
}

.input-placeholder{
  color: #bbb;
  font-size: 16px;
}

.btn-row{
  display: flex;
  width: 100%;
  justify-content: space-between;
  gap: 15px;
}

.btn-cancel{
  width: 260rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #F5F5F5;
  border-radius: 40rpx;
  font-size: 32rpx;
  color: #000;
  text-align: center;
}

.btn-confirm{
  width: 260rpx;
  height: 80rpx;
  background: #34BC4D;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
}

.btn-cancel:active{
  background: rgba(255, 255, 255, 0.5);
}

.btn-confirm:active{
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}
</style>