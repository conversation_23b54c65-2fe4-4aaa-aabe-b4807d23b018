<script setup>
import { ref } from 'vue'; // 假设使用 Vue 3，需要导入 ref
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()
const popup = ref(null)

const buildData = () => {
  // 构建一个数字从125到150的数组
  const list = [];
  for (let i = 40; i <= 100; i++) {
    list.push(i);
  }
  return list;
}
const weightList = buildData()

const handleClick = () => {
  popup.value.open('center')
}

const bindChange = (e) => {
  weightIndex.value = e.detail.value
  weightValue.value = weightList[e.detail.value[0]]
}
const weightIndex = ref([1])
const weightValue = ref(130)

const handleConfirm = async () => {
  try {
    await userStore.updateUserInfo({
      weight: weightValue.value
    })
    popup.value.close()
  } catch (error) {
    console.log(error)
  }
}
</script>

<template>
  <view class="uni-list">
    <view class="uni-list-cell">
      <view class="uni-list-cell-left">
        体重
      </view>
      <view @click="handleClick">{{ userStore.userInfo.weight ? userStore.userInfo.weight + 'kg' : '保密' }}<uni-icons
          type="arrowright" size="20" color="#999"></uni-icons></view>

    </view>
    <uni-popup ref="popup">
      <view class="popup-content">
        <view class="popup-title">选择体重</view>
        <view class="flex picker-row">
          <picker-view indicator-style="height: 48px;" :value="weightIndex" @change="bindChange" class="picker-view">
            <picker-view-column>
              <view class="item" :class="{ 'active': weightIndex[0] === index }" v-for="(item, index) in weightList"
                :key="index">{{ item }}</view>
            </picker-view-column>
          </picker-view>
          <text class="unit">kg</text>
        </view>
        <view class="btn-box">
          <button class="cancel" @click="popup.close()">取消</button>
          <button class="confirm" @click="handleConfirm">确定</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<style lang="scss" scoped>
.uni-list:after {
  background: none;
}

.uni-list-cell {
  height: 48rpx;
  padding: 30rpx;
  font-size: 32rpx;
  color: #999;

  .uni-list-cell-left {
    padding: 0;
    font-size: 32rpx;
    color: #000;
  }
}

.popup-content {
  width: 640rpx;
  background-color: #fff;
  border-radius: 32px;
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.12);
  text-align: center;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.popup-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #222;
}

.picker-row {
  display: flex;
  align-items: center;
  justify-content: center;
}

.picker-view {
  width: 160rpx;
  height: 288rpx;
  margin-right: 16rpx;
  background: transparent;

  .item {
    line-height: 96rpx;
    font-size: 36rpx;
    color: #bbb;

    &.active {
      color: #222;
      font-size: 48rpx;
      font-weight: bold;
    }
  }
}

.unit {
  font-size: 36rpx;
  color: #888;
  margin-left: 8rpx;
}

.btn-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30rpx;

  .cancel,
  .confirm {
    width: 200rpx;
    height: 80rpx;
    border-radius: 40rpx;

    &::after {
      border: none !important;
    }
  }
}

.cancel {
  width: 200rpx;
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 40rpx;
  color: #666666;
  text-align: center;
  line-height: 80rpx;

  &::after {
    border: none !important;
  }
}


.confirm {
  width: 200rpx;
  height: 80rpx;
  background: #34BC4D;
  border-radius: 40rpx;
  color: #fff;
  text-align: center;
  line-height: 80rpx;
}

:deep(.uni-button::after) {
  border: none !important;
}
</style>