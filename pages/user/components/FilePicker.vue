<template>
  <view class="file-picker">
    <text class="title">个人相册</text>
    <uni-file-picker
      v-model="imageValue"
      fileMediatype="image"
      mode="grid"
      @select="select"
      @progress="progress"
      @success="success"
      @fail="fail"
      @delete="deleteFile"
    />
  </view>
</template>
<script setup>
import { ref ,onMounted} from 'vue'
import { userApi ,profileApi} from '@/common/api'
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()
import { HTTP_IMG } from '@/common/constant'

const imageValue = ref([])
const select = async res => {
  try {
    uni.showLoading({
      title: '上传中...',
      mask: true
    })
    res?.tempFilePaths?.map(async (item)=>{
      const { url } = await userApi.addAlbum(item)
    })

  } catch (error) {
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}
const deleteFile = async(e)=>{
  if(e.tempFile?.orgin){
      await profileApi.deleteAlbum({
        dataId:e.tempFile?.orgin
    })
  }
}
onMounted(()=>{
  imageValue.value = userStore?.userInfo?.albumPhotos?.filter(item=>item).map(item=>{
    return{
      url:HTTP_IMG+item,
      orgin:item
    }
  })
})
const progress = () => {}
const success = () => {}
const fail = () => {}
</script>
<style lang="scss" scoped>
.file-picker {
  height: 144rpx;
  margin-top: 20rpx;
  padding:0 30rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title{
  font-size: 32rpx;
  color: #000;
  margin-bottom: 20rpx;
}
:deep(.file-picker__box) {
  height: 120rpx !important;
  width: 120rpx !important;
  padding-top: 100rpx !important;
}
.uni-file-picker{
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
:deep(.is-add){
  width: 90rpx ;
height: 90rpx;
background: #F3F3F3;
border-radius: 16rpx !important;
border: none !important;
}
:deep(.icon-add){
  width: 4rpx;
  height: 40rpx;
  background-color: #ADADAD;
}
</style>