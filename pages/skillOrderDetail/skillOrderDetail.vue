<template>
	<view class="order-detail-wrap">
		<view class="status-wrap">
			<image class="status-icon" :src="`/static/images/expertOrder/${getStatus}.svg`"></image>
			<view class="status-info">
				<view v-if="orderInfo.orderStatus === 10" class="status-txt">
					<text>待同意，剩余</text>
					<text class="remain">23小时15分钟10秒</text>
					<text>后</text>
				</view>
				<view v-else>{{ getStatusText }}</view>
				<text class="tip">{{ getTips }}</text>
			</view>
		</view>
		<view class="warning" v-if="orderInfo.orderStatus === 10">
			若时间到期后未接单，订单将自动关闭
		</view>
		<view class="order-theme">
			<view :class="['icon', getIconBg]">{{ getStatusText }}</view>
			<image class="pic" :src="orderInfo.skillImg || '/static/images/expertOrder/foodGuide.png'"></image>
			<view class="skill-info">
				<text class="txt1">{{ orderInfo.skillName || '技能服务' }}</text>
				<text class="txt2">{{ orderInfo.skillAmountYuan || (orderInfo.skillAmount ? orderInfo.skillAmount / 100 : '300') }} 贝壳币/小时</text>
				<text class="txt3">共 {{ orderInfo.serviceDurationHours || '1' }} 小时</text>
			</view>
			<view class="cost"><text class="ita">{{ orderInfo.totalAmountYuan || (orderInfo.totalAmount ? orderInfo.totalAmount / 100 : '300') }}</text> 贝壳币</view>
		</view>
		<view class="order-info panel-style mb32" v-if="orderInfo.orderStatus === 50 || orderInfo.orderStatus === 60">
			<view class="title">退款信息</view>
			<view class="line">
				<text class="txt1">退款进度</text>
				<text class="txt2">{{ orderInfo.orderStatus === 60 ? '已退款' : '退款中' }}</text>
			</view>
			<view class="line">
				<text class="txt1">退款原因</text>
				<text class="txt2">{{ orderInfo.refundReason || '计划有变' }}</text>
			</view>
			<view class="line">
				<text class="txt1">退款时间</text>
				<text class="txt2">{{ formatDate(orderInfo.refundTime) || formatDate(orderInfo.updateTime) || '2025-07-12 12:00:39' }}</text>
			</view>
			<view class="line">
				<text class="txt1">退款金额</text>
				<text class="txt2">{{ orderInfo.refundAmountYuan || (orderInfo.refundAmount ? orderInfo.refundAmount / 100 : '0') }} 贝壳币</text>
			</view>
		</view>
		<view class="order-info panel-style service-info" v-if="showServiceInfo">
			<view class="title">服务信息</view>
			<view v-for="(item, idx) in serviceList" class="item-wrap">
				<view v-for="(it, index) in item" :class="['txt-line', it.isFinished ? 'finish' : '']">
					<template v-if="index === 0">
						<image
							class="icon-status"
							v-if="it.isFinished && idx === 0"
							src="/static/images/expertOrder/ic_success.svg"
						></image>
						<view class="icon-status circle" v-else-if="idx === 0"></view>
						<image v-else class="icon-status" src="/static/images/expertOrder/ic_success-gray.svg"></image>
					</template>
				  <text :class="[{pl60: index !== 0}]">{{ it.text }}</text>
				</view>
			</view>
		</view>
		<!-- 预约信息 -->
		<OrderPanel :order-info="orderInfo" :is-show-order-info="Boolean(1)" class="panel-style">
			<template v-slot:header>
				<view class="title">预约信息</view>
			</template>
			<template v-if="showSendMsgBtn" v-slot:btn>
				<button class="send-msg">发消息</button>
			</template>
		</OrderPanel>
		<view class="order-info panel-style">
			<view class="title">订单信息</view>
			<view class="line">
				<text class="txt1">订单编号</text>
				<text class="txt2" id="orderId">{{ orderInfo.orderNo || 'E00909090909090909XXXXX' }}</text>
				<button class="btn" @click="copyOrderId">复制</button>
			</view>
			<view class="line">
				<text class="txt1">下单时间</text>
				<text class="txt2">{{ formatDate(orderInfo.createTime) || '2025-07-12 12:00:09' }}</text>
			</view>
			<view class="line">
				<text class="txt1">支付时间</text>
				<text class="txt2">{{ formatDate(orderInfo.payTime) || '2025-07-12 12:00:39' }}</text>
			</view>
		</view>
		<view class="footer" v-if="orderInfo.status !== 4">
			<image class="icon-kf" src="/static/images/shoping/kf.png"></image>
			<view class="btns" v-if="showBtnGroup1">
				<button class="btn modify" @click="modDateTime">修改时间</button>
				<button class="btn cancel" @click="cancelOrder">取消订单</button>
			</view>
			<view class="btns" v-if="showBtnGroup2">
				<button class="btn cancel" @click="cancelOrder">取消订单</button>
				<button class="btn appointment" @click="gotoAppointmentDetail">约会详情</button>
			</view>
			<view class="btns" v-if="orderInfo.status === 3">
				<button class="btn cancel">申请退款</button>
			</view>
		</view>
		
		<uni-popup type="bottom" ref="dateTime">
			<Dt @close="closeBottomPop" @sendDateTime="sendDateTime"></Dt>
		</uni-popup>
	</view>
</template>

<script>
	import OrderPanel from '../myOrder/components/OrderPanel.vue';
	import Dt from '../expertOrder/components/DateTime.vue';
	import { skillOrderApi } from '@/common/api';
	
	export default {
		components: {
			OrderPanel,
			Dt,
		},
		data() {
			return {
				orderNo: '', // 订单编号
				orderInfo: {
					orderStatus: 10,
					buyerGender: 1,
					buyerAge: 18
				},
				serviceList: [],
				isLoading: false,
				// 可用操作列表
				availableActions: [],
				// 状态节点
				statusNodes: []
			}
		},
		computed: {
			getStatus() {
				// 状态码映射到图标
				const statusIconMap = {
					10: 'ic_waiting',
					20: 'ic_beforeStart',
					30: 'ic_doing',
					40: 'ic_done',
					50: 'ic_refunding',
					60: 'ic_refunding'
				};
				
				const status = this.orderInfo.orderStatus || 10;
				return statusIconMap[status] || 'ic_waiting';
			},
			getIconBg() {
				// 状态码映射到样式类名
				const statusClassMap = {
					10: 'waiting',
					20: 'before-start',
					30: 'doing',
					40: 'done',
					50: 'refunding',
					60: 'refunding'
				};
				
				const status = this.orderInfo.orderStatus || 10;
				return statusClassMap[status] || 'waiting';
			},
			getStatusText() {
				// 优先使用接口返回的状态描述
				if (this.orderInfo.orderStatusDesc) {
					return this.orderInfo.orderStatusDesc;
				}
				
				// 状态码映射到文本
				const statusTextMap = {
					10: '待同意',
					20: '待开始',
					30: '进行中',
					40: '已完成',
					50: '退款中',
					60: '已退款'
				};
				
				const status = this.orderInfo.orderStatus || 10;
				return statusTextMap[status] || '待同意';
			},
			getTips() {
				// 状态码映射到提示文本
				const statusTipsMap = {
					10: '正在等待接单，请保持关注',
					20: '即将开始，请耐心等待~',
					30: '正在进行中，请保持联系',
					40: '活动已完成，再来一单吧~',
					50: '退款处理中，请耐心等待',
					60: '退款处理已完成，请留意账户变动'
				};
				
				const status = this.orderInfo.orderStatus || 10;
				return statusTipsMap[status] || '正在等待接单，请保持关注';
			},
			showServiceInfo() {
				// 进行中、已完成、退款状态显示服务信息
				const status = this.orderInfo.orderStatus || 10;
				return status === 30 || status === 40 || status === 50 || status === 60;
			},
			showSendMsgBtn() {
				// 待开始、进行中、已完成、退款状态显示发消息按钮
				const status = this.orderInfo.orderStatus || 10;
				return status === 20 || status === 30 || status === 40 || status === 50 || status === 60;
			},
			showBtnGroup1() {
				// 待同意、待开始状态显示按钮组1
				const status = this.orderInfo.orderStatus || 10;
				return status === 10 || status === 20;
			},
			showBtnGroup2() {
				// 进行中状态显示按钮组2
				const status = this.orderInfo.orderStatus || 10;
				return status === 30;
			},
			// 是否显示开始服务按钮
			showStartServiceBtn() {
				return this.availableActions.includes('START_SERVICE');
			},
			// 是否显示联系买家按钮
			showContactBuyerBtn() {
				return this.availableActions.includes('CONTACT_BUYER');
			}
		},
		onLoad(options) {
			console.log(options);
			if (options.id) {
				this.orderNo = options.id;
				this.fetchOrderDetail();
			}
		},
		
		methods: {
			// 获取订单详情
			async fetchOrderDetail() {
				if (!this.orderNo) return;
				
				try {
					this.isLoading = true;
					uni.showLoading({
						title: '加载中...'
					});
					
					const res = await skillOrderApi.getOrderDetailByOrderNo(this.orderNo);
					console.log('订单详情:', res);
					
					if (res && res.code === 200 && res.data) {
						// 更新订单信息
						this.updateOrderInfo(res.data);
					} else {
						uni.showToast({
							title: res?.message || '获取订单详情失败',
							icon: 'none'
						});
					}
				} catch (err) {
					console.error('获取订单详情失败:', err);
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					});
				} finally {
					this.isLoading = false;
					uni.hideLoading();
				}
			},
			
			// 更新订单信息
			updateOrderInfo(data) {
				console.log('更新订单信息:', data);
				
				// 直接使用接口返回的数据
				this.orderInfo = data;
				
				// 保存可用操作
				this.availableActions = data.availableActions || [];
				
				// 保存状态节点
				this.statusNodes = data.statusNodes || [];
				
				// 更新服务信息列表
				this.updateServiceList(data);
			},
			
			// 更新服务信息列表
			updateServiceList(data) {
				// 如果有状态节点，使用状态节点生成服务信息列表
				if (data.statusNodes && data.statusNodes.length > 0) {
					const serviceList = [];
					
					// 按时间倒序排列状态节点
					const sortedNodes = [...data.statusNodes].sort((a, b) => {
						return new Date(b.statusTime) - new Date(a.statusTime);
					});
					
					// 将状态节点转换为服务信息列表
					sortedNodes.forEach(node => {
						serviceList.push([
							{ 
								text: node.statusName,
								isFinished: node.statusCode === data.orderStatus
							}, 
							{ text: this.formatDate(node.statusTime) }
						]);
					});
					
					// 更新服务列表
					this.serviceList = serviceList;
					return;
				}
				
				// 如果没有状态节点，使用传统方式生成服务信息列表
				const serviceList = [];
				
				// 添加下单成功记录
				serviceList.push([
					{ text: '下单成功' }, 
					{ text: this.formatDate(data.createDate) || '2025-07-12 13:00:00' }
				]);
				
				// 如果有支付时间，添加支付记录
				if (data.payTime) {
					serviceList.unshift([
						{ text: '支付完成' }, 
						{ text: this.formatDate(data.payTime) }
					]);
				}
				
				// 如果有接单时间，添加接单记录
				if (data.acceptTime) {
					serviceList.unshift([
						{ text: '达人接单' }, 
						{ text: this.formatDate(data.acceptTime) }
					]);
				}
				
				// 如果有开始服务时间，添加服务开始记录
				if (data.actualStartTime) {
					serviceList.unshift([
						{ text: '服务开始' }, 
						{ text: this.formatDate(data.actualStartTime) }
					]);
				}
				
				// 如果有结束时间，添加服务完成记录
				if (data.actualEndTime) {
					serviceList.unshift([
						{ 
							text: '服务完成',
							isFinished: true
						}, 
						{ text: this.formatDate(data.actualEndTime) }
					]);
				}
				
				// 如果有完成时间，添加订单完成记录
				if (data.doneDate) {
					serviceList.unshift([
						{ 
							text: '订单完成',
							isFinished: true
						}, 
						{ text: this.formatDate(data.doneDate) }
					]);
				}
				
				// 更新服务列表
				if (serviceList.length > 0) {
					this.serviceList = serviceList;
				}
			},
			
			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return '';
				try {
					const date = new Date(dateStr);
					return date.toLocaleString('zh-CN', {
						year: 'numeric',
						month: '2-digit',
						day: '2-digit',
						hour: '2-digit',
						minute: '2-digit',
						second: '2-digit',
						hour12: false
					}).replace(/\//g, '-');
				} catch (e) {
					return dateStr;
				}
			},
			
			// 将状态文本转换为状态码
			getStatusCode(statusText) {
				const statusMap = {
					'待接单': 0,
					'待开始': 1,
					'进行中': 2,
					'已完成': 3,
					'退款/售后': 4,
					'已退款': 4
				};
				return statusMap[statusText] || 0;
			},
			async copyOrderId() {
				const orderId = document.querySelector('#orderId').textContent
				try {
					await navigator.clipboard.writeText(orderId)
					uni.showToast({
						title: '复制成功',
						icon: 'success'
					});
				} catch(err) {
					// console.log('复制失败', err)
					const ele = document.createElement('input');
					ele.setAttribute('readonly', 'readonly');
					ele.value = orderId;
					document.body.appendChild(ele);
					ele.select();
					document.execCommand('copy');
					ele.remove();
					uni.showToast({
						title: '复制成功',
						icon: 'success'
					});
				}
			},
			modDateTime() {
				this.$refs.dateTime.open()
			},
			closeBottomPop() {
				this.$refs.dateTime.close()
			},
			sendDateTime(payload) {
				console.log(payload)
				this.closeBottomPop()
			},
			cancelOrder() {
				uni.navigateTo({
					url: '/pages/cancelOrder/cancelOrder'
				})
			},
			gotoAppointmentDetail() {
				uni.navigateTo({
					url: '/pages/appointmentDetail/appointmentDetail'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-detail-wrap {
		height: 100vh;
		overflow-y: auto;
		background-color: #f4f8fb;
		padding: 32rpx 32rpx 168rpx;
		.status-wrap {
			height: 96rpx;
			padding: 0 8rpx;
			display: flex;
			align-items: center;
			.status-icon {
				height: 72rpx;
				flex-shrink: 0;
				width:56rpx;
				margin-right: 24rpx;
			}
			.status-info {
				flex-grow: 1;
				.status-txt {
					height: 56rpx;
					line-height: 56rpx;
					font-size: 36rpx;
					color: #000;
					.remain {
						color: #ff5f54;
					}
				}
				.tip {
					color: #666;
					font-size: 24rpx;
					height: 40rpx;
					line-height: 40rpx;
				}
			}
		}
	  .warning {
			border-radius: 16rpx;
			margin-top: 24rpx;
			height: 88rpx;
			line-height: 88rpx;
			background-color: #fff;
			box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
			color: #000;
			font-size: 24rpx;
			padding-left: 72rpx;
			background: #fff url('/static/images/expertOrder/voice.png') no-repeat 24rpx center / 30rpx 28rpx;
		}
		.order-theme {
			position: relative;
			height: 152rpx;
			box-sizing: border-box;
			border-radius: 16rpx;
			background-color: #fff;
			box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
			padding: 24rpx;
			margin-top: 32rpx;
			display: flex;
			align-items: flex-end;
			.icon {
				position: absolute;
				top: -8rpx;
				right: 0;
				width:124rpx;
				line-height: 48rpx;
				height: 48rpx;
				text-indent: 36rpx;
				font-size: 24rpx;
				background-size: 100% 100%;
				&.before-start {
					background-image: url('/static/images/expertOrder/beforeStart.svg');
					color: #f9af25;
				}
				&.waiting {
					background-image: url('/static/images/expertOrder/waiting.svg');
					color: #ff5f54;
				}
				&.doing {
					background-image: url('/static/images/expertOrder/doing.svg');
					color: #6fba1a;
				}
				&.done {
					background-image: url('/static/images/expertOrder/done.svg');
					color: #666;
				}
				&.refunding {
					background-image: url('/static/images/expertOrder/refunding.svg');
					color: #f9af25;
				}
			}
			.pic {
				height: 96rpx;
				width: 96rpx;
				margin-right: 16rpx;
				flex-shrink: 0;
			}
			.skill-info {
				flex-grow: 1;
				display: flex;
				flex-direction: column;
				justify-content: flex-end;
				.txt1 {
					height: 40rpx;
					line-height: 40rpx;
					color: #000;
					font-size: 28rpx;
					font-weight: 600;
				}
				.txt2, .txt3 {
					height: 24rpx;
					line-height: 28rpx;
					font-size: 24rpx;
					color: #999;
				}
				.txt2 {
					margin: 4rpx 0;
				}
			}
			.cost {
				flex-shrink: 0;
				color: #000;
				font-size: 24rpx;
				height: 40rpx;
				line-height: 40rpx;
				.ita {
					font-style: italic;
				}
			}
		}
		.panel-style {
			margin-top: 24rpx;
			.title {
				height: 44rpx;
				color: #000;
				font-size: 28rpx;
				font-weight: 600;
				line-height: 44rpx;
				padding-bottom: 24rpx;
				border-bottom: 2rpx solid rgba(209,209,209, .5);
				margin-bottom: 32rpx;
			}
			::v-deep {
				.order-theme {
					display: none;
				}
				.status {
					display: none;
				}
				.during, .startTime {
					display: none;
				}
			}
			.send-msg {
				position: absolute;
				width: 136rpx;
				height: 56rpx;
				line-height: 54rpx;
				border: 2rpx solid #d1d1d1;
				border-radius: 30rpx;
				background-color: #fff;
				bottom: 40rpx;
				right: 32rpx;
				color: #000;
				font-size: 24rpx;
			}
		}
		.order-info {
			padding: 24rpx 32rpx 32rpx;
			background-color: #fff;
			border-radius: 16rpx;
			box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
			margin-bottom: 300rpx;
			&.mb32 {
				margin-bottom: 32rpx;
			}
			.line {
				line-height: 40rpx;
				margin-bottom: 16rpx;
				display: flex;
				.txt1 {
					font-size: 24rpx;
					line-height: 40rpx;
					color: #999;
					flex-shrink: 0;
				}
				.txt2 {
					font-size: 24rpx;
					padding-left: 32rpx;
					line-height: 40rpx;
					color: #000;
				}
				.btn {
					border: 2rpx solid #d1d1d1;
					border-radius: 20rpx;
					height: 40rpx;
					line-height: 36rpx;
					// width: 80rpx;
					color: #000;
					font-size: 24rpx;
					padding: 0 16rpx;
					margin-left: 16rpx;
				}
				&:last-child {
					margin-bottom: 0;
				}
			}
		}
		.service-info {
			margin-bottom: 32rpx;
			.item-wrap {
				position: relative;
				padding-bottom: 40rpx;
				// border-left: 6rpx solid #d1d1d1;
				&::before {
					content: '';
					position: absolute;
					left: 10rpx;
					top: 26rpx;
					width: 6rpx;
					bottom: -6rpx;
					background-color: #d1d1d1;
				}
				&:nth-child(2) {
					
				}
				&:nth-child(n+3)::before {
					top: 26rpx;
				}
				&:last-child {
					padding-bottom: 0;
				}
				&:last-child::before {
					display: none;
				}
				.txt-line {
					height: 40rpx;
					line-height: 40rpx;
					color: #000;
					font-size: 24rpx;
					font-weight: 500;
					text-indent: 32rpx;
					display: flex;
					align-items: center;
					&:last-child {
						color: #999;
					}
					.icon-status {
						width: 28rpx;
						height: 28rpx;
						box-sizing: border-box;
						&.circle {
							z-index: 1;
							background-color: #fff;
							border-radius: 28rpx;
							border: 6rpx solid #66d47e;
						}
					}
					.pl60 {
						text-indent: 0;
						padding-left: 60rpx;
					}
				}
			}
		}
		.footer {
			position: fixed;
			background-color: #fff;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			height: 152rpx;
			padding: 16rpx 32rpx 0;
			justify-content: space-between;
			.icon-kf {
				width: 48rpx;
				height: 48rpx;
				margin-top: 14rpx;
			}
			.btns {
				height: 76rpx;
				display: flex;
				justify-content: flex-end;
				.btn {
					width: 192rpx;
					height: 72rpx;
					line-height: 72rpx;
					border: 2rpx solid #d1d1d1;
					color: #333;
					font-size: 32rpx;
					border-radius: 38rpx;
					background-color: #fff;
					margin-left: 16rpx;
					&::after {
						border: none !important;
					}
				}
				.appointment {
					color: #fff;
					background-color: #66d47e;
					border-color: #66d47e;
				}
			}
		}
	}
</style>