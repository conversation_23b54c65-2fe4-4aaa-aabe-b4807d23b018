<template>
  <view class="search-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="back-button" @click="goBack">
          <text class="back-arrow">←</text>
        </view>
        <view class="search-input-container">
          <uni-icons type="search" size="18" color="#999" class="search-icon"></uni-icons>
          <input
            type="text"
            :placeholder="getSearchPlaceholder()"
            class="search-input"
            v-model="searchKeyword"
            @input="onInput"
            @confirm="onSearch"
            :focus="true"
          />
        </view>
        <text class="search-button" @click="onSearch">搜索</text>
      </view>
    </view>

    <!-- 搜索内容区域 -->
    <view class="search-content">
      <!-- 快捷搜索 -->
      <view class="quick-search-section" v-if="!hasSearched">
        <view class="section-title">分类搜索</view>
        <view class="quick-search-tags">
          <view
            v-for="tag in quickSearchTags"
            :key="tag"
            class="search-tag"
            @click="quickSearch(tag)"
          >
            {{ tag }}
          </view>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view class="search-results" v-if="hasSearched">
        <!-- 加载状态 -->
        <view class="loading-container" v-if="isLoading">
          <uni-icons
            type="spinner-cycle"
            size="32"
            color="#66d47e"
            class="loading-icon"
          ></uni-icons>
          <text class="loading-text">搜索中...</text>
        </view>

        <view class="result-header" v-if="!isLoading">
          <text class="result-count">找到 {{ searchResults.length }} 个相关结果</text>
        </view>
        <scroll-view 
          class="result-list" 
          v-if="!isLoading" 
          scroll-y 
          @scrolltolower="onReachBottom"
          lower-threshold="50"
        >
          <view
            v-for="item in searchResults"
            :key="item.id"
            class="result-item"
            @click="viewOrderDetail(item)"
          >
            <view class="result-content">
              <view class="order-info">
                <text class="order-title">{{ item.title }}</text>
                <text class="order-desc">{{ item.description }}</text>
              </view>
              <view class="order-meta">
                <text class="order-type">{{ item.type }}</text>
                <text class="order-time">{{ item.time }}</text>
              </view>
            </view>
            <view class="order-status" :class="item.statusClass">
              {{ item.status }}
            </view>
          </view>
          
          <!-- 加载状态 -->
          <view class="loading-state" v-if="searchResults.length > 0">
            <view v-if="isLoading" class="loading-more">
              <text class="loading-text">加载中...</text>
            </view>
            <view v-else-if="!hasMore" class="no-more">
              <text class="no-more-text">没有更多数据了</text>
            </view>
          </view>
        </scroll-view>
        <view class="empty-result" v-if="!isLoading && searchResults.length === 0">
          <image src="/static/images/order/nodata.png" class="empty-image"></image>
          <text class="empty-text">未找到相关订单</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { skillOrderApi, recruitOrderApi, orderApi } from '@/common/api'
  import { useUserStore } from '@/stores/user'

  export default {
    data() {
      return {
        statusBarHeight: 0,
        searchKeyword: '',
        hasSearched: false,
        // 从父页面传递过来的tab标识
        mainTabType: 'skill', // 一级tab类型：skill/recruit/shop
        subTabType: 'all', // 二级tab类型
        mainTabIndex: 0, // 一级tab索引
        subTabIndex: 0, // 二级tab索引
        quickSearchTags: [
          '美食',
          '旅游向导',
          '生活助理',
          '运动陪练',
          '学习辅导',
          '摄影服务',
          '购物陪同',
          '语言交换',
          '宠物照看',
          '登山活动',
          '读书分享',
          '电影观影'
        ],
        searchResults: [],
        isLoading: false, // 搜索加载状态
        merchantId: '5',
        // 分页相关
        pageNum: 1,
        pageSize: 10,
        hasMore: true,
        currentKeyword: '' // 当前搜索关键词
      }
    },
    onLoad(options) {
      // 获取状态栏高度
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight

      // 接收从订单页面传递过来的tab标识
      if (options.mainTab) {
        this.mainTabType = options.mainTab
      }
      if (options.subTab) {
        this.subTabType = options.subTab
      }
      if (options.mainTabIndex) {
        this.mainTabIndex = parseInt(options.mainTabIndex)
      }
      if (options.subTabIndex) {
        this.subTabIndex = parseInt(options.subTabIndex)
      }

      console.log('搜索页面接收到的tab信息:', {
        mainTabType: this.mainTabType,
        subTabType: this.subTabType,
        mainTabIndex: this.mainTabIndex,
        subTabIndex: this.subTabIndex
      })
    },
    methods: {
      goBack() {
        uni.navigateBack()
      },
      onInput(e) {
        this.searchKeyword = e.detail.value
      },
      onSearch() {
        if (!this.searchKeyword.trim()) {
          uni.showToast({
            title: '请输入搜索关键词',
            icon: 'none'
          })
          return
        }
        this.performSearch(this.searchKeyword)
      },
      quickSearch(keyword) {
        this.searchKeyword = keyword
        this.performSearch(keyword)
      },
      performSearch(keyword) {
        this.hasSearched = true
        this.currentKeyword = keyword
        this.pageNum = 1
        this.searchResults = []
        this.hasMore = true
        this.isLoading = true

        // 根据当前tab信息调用不同的搜索接口
        this.callSearchAPI(keyword)
      },
      
      // 触底加载更多
      onReachBottom() {
        if (!this.hasMore || this.isLoading || !this.hasSearched) return
        
        this.pageNum++
        this.isLoading = true
        this.callSearchAPI(this.currentKeyword)
      },

      // 根据tab标识调用对应的搜索接口
      callSearchAPI(keyword) {
        console.log('调用搜索接口，参数:', {
          keyword,
          mainTabType: this.mainTabType,
          subTabType: this.subTabType
        })

        // 根据一级tab类型调用不同的搜索接口
        switch (this.mainTabType) {
          case 'skill':
            this.searchSkillOrders(keyword)
            break
          case 'recruit':
            this.searchRecruitOrders(keyword)
            break
          case 'shop':
            this.searchShopOrders(keyword)
            break
          default:
            this.searchAllOrders(keyword)
        }
      },

      // 搜索技能订单
      async searchSkillOrders(keyword) {
        try {
          // 构建请求参数
          let params = {
            skillName: keyword,
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }

          // 根据二级tab添加状态筛选
          // 全部tab时只查名称，其他tab携带状态查询
          if (this.subTabType !== 'all') {
            const statusMap = {
              waiting: '待接单',
              beforeStart: '待开始',
              doing: '进行中',
              done: '已完成',
              refunding: '退款/售后'
            }
            params.statusFilter = statusMap[this.subTabType]
          }

          console.log('技能订单搜索参数:', params)

          // 调用技能订单搜索接口
          const res = await skillOrderApi.searchOrders(params)
          this.isLoading = false

          console.log('技能订单搜索结果:', res)
          if (res && res.code === 200) {
            const newResults = this.processSkillOrdersResult(res.data || [])
            
            if (this.pageNum === 1) {
              this.searchResults = newResults
            } else {
              this.searchResults.push(...newResults)
            }
            
            // 假设没有total字段，根据返回数据量判断
            this.hasMore = (res.data || []).length >= this.pageSize
          } else {
            if (this.pageNum === 1) {
              this.searchResults = []
              uni.showToast({
                title: res?.message || '搜索失败',
                icon: 'none'
              })
            }
            this.hasMore = false
          }
        } catch (err) {
          this.isLoading = false
          console.error('技能订单搜索失败:', err)
          if (this.pageNum === 1) {
            this.searchResults = []
            uni.showToast({
              title: '网络请求失败',
              icon: 'none'
            })
          }
          this.hasMore = false
        }
      },
      
      // 处理技能订单搜索结果
      processSkillOrdersResult(data) {
        return data.map(item => ({
          id: item.id,
          title: item.skillName || item.title || '技能服务',
          description: `订单号: ${item.orderNo || item.id}`,
          type: '技能订单',
          time: item.createTime || item.updateTime || '',
          status: item.statusText || this.getStatusText('skill', this.subTabType),
          statusClass:
            this.getStatusClassByStatus(item.status) ||
            this.getStatusClass('skill', this.subTabType),
          originalData: item // 保存原始数据用于详情页跳转
        }))
      },

      // 处理招募订单搜索结果
      handleRecruitOrdersResult(data) {
        this.searchResults = data.map(item => ({
          id: item.id,
          title: item.activityName || item.title || '招募活动',
          description: `订单号: ${item.orderNo || item.activityCode || item.id}`,
          type: '招募订单',
          time: item.createTime || item.updateTime || '',
          status: item.statusText || this.getStatusText('recruit', this.subTabType),
          statusClass:
            this.getStatusClassByStatus(item.status) ||
            this.getStatusClass('recruit', this.subTabType),
          originalData: item
        }))
      },

      // 处理商城订单搜索结果新方法（不直接赋值，返回结果）
      processShopOrdersResult(data) {
        return data.map(item => ({
          id: item.orderCode || item.id,
          title: item.goodsList && item.goodsList[0] ? item.goodsList[0].goodsName : (item.goodName || '商品'),
          description: `订单号: ${item.orderCode || item.orderNo || item.id}`,
          type: '商城订单',
          time: item.orderDate || item.createTime || item.updateTime || '',
          status: item.orderStatusDesc || this.getStatusText('shop', this.subTabType),
          statusClass: this.getShopOrderStatusClass(item.orderStatus) || this.getStatusClass('shop', this.subTabType),
          originalData: item
        }))
      },
      
      // 处理商城订单搜索结果（兼容旧的方法）
      handleShopOrdersResult(data) {
        this.searchResults = this.processShopOrdersResult(data)
      },
      
      // 获取商城订单状态样式类（与商家订单页面保持一致）
      getShopOrderStatusClass(status) {
        const statusMap = {
          '0': 'status-pending',   // 待支付
          '1': 'status-paid',      // 待核销
          '3': 'status-completed', // 已完成
          '99': 'status-refund'    // 售后/退款
        }
        return statusMap[status] || 'default'
      },

      // 根据订单状态获取样式类
      getStatusClassByStatus(status) {
        const statusClassMap = {
          待接单: 'pending',
          待开始: 'pending',
          进行中: 'processing',
          已完成: 'completed',
          '退款/售后': 'refunding'
        }
        return statusClassMap[status] || 'default'
      },

      // 搜索招募订单
      async searchRecruitOrders(keyword) {
        try {
          let params = { 
            name: keyword,
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
          // 根据二级tab添加状态筛选
          if (this.subTabType !== 'all') {
            const statusMap = {
              recruiting: 1,
              success: 2,
              failed: 3
            }
            params.status = statusMap[this.subTabType]
          }
          const userStore = useUserStore()
          const res = await userStore.getRecruitListMy(params)
          this.isLoading = false
          
          if (res && res.code === 200 && res.data && res.data.list) {
            const newResults = this.processRecruitOrdersResult(res.data.list)
            
            if (this.pageNum === 1) {
              this.searchResults = newResults
            } else {
              this.searchResults.push(...newResults)
            }
            
            // 假设没有total字段，根据返回数据量判断
            this.hasMore = (res.data.list || []).length >= this.pageSize
          } else {
            if (this.pageNum === 1) {
              this.searchResults = []
              uni.showToast({
                title: res?.message || '搜索失败',
                icon: 'none'
              })
            }
            this.hasMore = false
          }
        } catch (err) {
          this.isLoading = false
          console.error('招募订单搜索失败:', err)
          if (this.pageNum === 1) {
            this.searchResults = []
            uni.showToast({
              title: '网络请求失败',
              icon: 'none'
            })
          }
          this.hasMore = false
        }
      },
      
      // 处理招募订单搜索结果
      processRecruitOrdersResult(data) {
        return data.map(item => ({
          id: item.id,
          title: item.activityName || item.title || '招募活动',
          description: `订单号: ${item.orderNo || item.activityCode || item.id}`,
          type: '招募订单',
          time: item.createTime || item.updateTime || '',
          status: item.statusText || this.getStatusText('recruit', this.subTabType),
          statusClass:
            this.getStatusClassByStatus(item.status) ||
            this.getStatusClass('recruit', this.subTabType),
          originalData: item
        }))
      },

      // 搜索商城订单
      async searchShopOrders(keyword) {
        try {
          const params = {
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            goodsName: keyword,
            merchantId: this.merchantId
          }
          
          // 根据二级tab添加状态筛选，与商家订单页面保持一致
          if (this.subTabType !== 'all') {
            const statusMap = {
              pending: '0',    // 待支付
              toVerify: '1',   // 待核销
              completed: '3',  // 已完成
              refunding: '99'  // 售后/退款
            }
            params.orderStatus = statusMap[this.subTabType]
          }
          
          console.log('商城订单搜索参数:', params)
          
          const res = await orderApi.getOrderList(params)
          this.isLoading = false
          
          console.log('商城订单搜索结果:', res)
          
          if (res && res.rows) {
            const newResults = this.processShopOrdersResult(res.rows)
            
            if (this.pageNum === 1) {
              this.searchResults = newResults
            } else {
              this.searchResults.push(...newResults)
            }
            
            // 判断是否还有更多数据
            this.hasMore = this.searchResults.length < (res.total || 0)
          } else {
            if (this.pageNum === 1) {
              this.searchResults = []
            }
            this.hasMore = false
            if (this.pageNum === 1) {
              uni.showToast({
                title: '未找到相关订单',
                icon: 'none'
              })
            }
          }
        } catch (err) {
          this.isLoading = false
          console.error('商城订单搜索失败:', err)
          if (this.pageNum === 1) {
            this.searchResults = []
          }
          this.hasMore = false
          uni.showToast({
            title: '搜索失败，请重试',
            icon: 'none'
          })
        }
      },

      // 搜索全部订单 (暂时保留模拟实现)
      async searchAllOrders(keyword) {
        try {
          // TODO: 实现全部订单搜索接口
          // const res = await orderApi.searchAllOrders({ keyword })
          this.isLoading = false
          this.mockSearchAPI('/api/orders/search', { keyword }, keyword)
        } catch (err) {
          this.isLoading = false
          console.error('全部订单搜索失败:', err)
          this.searchResults = []
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
        }
      },

      // 模拟API调用
      mockSearchAPI(apiUrl, searchParams, keyword) {
        // 根据不同的tab类型返回不同的模拟数据
        let mockResults = []

        switch (this.mainTabType) {
          case 'skill':
            mockResults = [
              {
                id: 1,
                title: '旅游向导服务',
                description: '订单号: SP202507230001',
                type: '技能订单',
                time: '2025-07-23 14:30',
                status: this.getStatusText('skill', this.subTabType),
                statusClass: this.getStatusClass('skill', this.subTabType)
              },
              {
                id: 2,
                title: '摄影服务',
                description: '订单号: SP202507230002',
                type: '技能订单',
                time: '2025-07-22 16:00',
                status: this.getStatusText('skill', this.subTabType),
                statusClass: this.getStatusClass('skill', this.subTabType)
              }
            ]
            break
          case 'recruit':
            mockResults = [
              {
                id: 3,
                title: '周末户外登山',
                description: '订单号: ZM202507230003',
                type: '招募订单',
                time: '2025-07-22 16:00',
                status: this.getStatusText('recruit', this.subTabType),
                statusClass: this.getStatusClass('recruit', this.subTabType)
              }
            ]
            break
          case 'shop':
            mockResults = [
              {
                id: 4,
                title: '有机水果礼盒',
                description: '订单号: SC202507230004',
                type: '商城订单',
                time: '2025-07-21 10:15',
                status: this.getStatusText('shop', this.subTabType),
                statusClass: this.getStatusClass('shop', this.subTabType)
              }
            ]
            break
        }

        // 根据关键词筛选结果
        this.searchResults = mockResults.filter(item => {
          return item.title.includes(keyword) || item.description.includes(keyword)
        })
      },

      // 根据tab类型和状态获取状态文本
      getStatusText(mainType, subType) {
        const statusMap = {
          skill: {
            all: '全部',
            waiting: '待接单',
            beforeStart: '待开始',
            doing: '进行中',
            done: '已完成',
            refunding: '退款/售后'
          },
          recruit: {
            all: '全部',
            recruiting: '招募中',
            success: '招募成功',
            failed: '招募失败'
          },
          shop: {
            all: '全部',
            pending: '待支付',
            toVerify: '待核销', 
            completed: '已完成',
            refunding: '售后/退款'
          }
        }
        return statusMap[mainType]?.[subType] || '未知状态'
      },

      // 根据tab类型和状态获取状态样式类
      getStatusClass(mainType, subType) {
        const classMap = {
          skill: {
            waiting: 'pending',
            beforeStart: 'pending',
            doing: 'processing',
            done: 'completed',
            refunding: 'refunding'
          },
          recruit: {
            recruiting: 'processing',
            success: 'completed',
            failed: 'failed'
          },
          shop: {
            pending: 'status-pending',
            toVerify: 'status-paid',
            completed: 'status-completed',
            refunding: 'status-refund'
          }
        }
        return classMap[mainType]?.[subType] || 'default'
      },

// 获取搜索框占位符文本
      getSearchPlaceholder() {
        const placeholderMap = {
          skill: '输入技能名称或订单号',
          recruit: '输入活动名称或订单号',
          shop: '输入商品名称或订单号'
        }
        return placeholderMap[this.mainTabType] || '输入订单号或名称'
      },

      viewOrderDetail(item) {
        // 根据订单类型跳转到不同的详情页
        let url = ''
        switch (item.type) {
          case '技能订单':
            url = `/pages/reservationOrderDetail/reservationOrderDetail?id=${item.id}`
            break
          case '招募订单':
            url = `/pages/recruit/detail?id=${item.id}`
            break
          case '商城订单':
            // 使用orderCode作为订单详情页参数
            url = `/pages/orders/details?id=${item.originalData?.orderCode || item.id}`
            break
          default:
            url = `/pages/reservationOrderDetail/reservationOrderDetail?id=${item.id}`
        }
        uni.navigateTo({ url })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .search-page {
    height: 100vh;
    background-color: #f5f5f5;
  }

  .custom-navbar {
    background-color: #fff;
    border-bottom: 1px solid #eee;

    .navbar-content {
      height: 88rpx;
      display: flex;
      align-items: center;
      padding: 0 32rpx;

      .back-button {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.05);

        .back-arrow {
          font-size: 36rpx;
          color: #333;
          font-weight: bold;
          line-height: 1;
        }

        &:active {
          background-color: rgba(0, 0, 0, 0.1);
        }
      }

      .search-input-container {
        flex: 1;
        height: 64rpx;
        background-color: #f5f5f5;
        border-radius: 32rpx;
        display: flex;
        align-items: center;
        margin: 0 24rpx;
        padding: 0 24rpx;

        .search-icon {
          margin-right: 16rpx;
        }

        .search-input {
          flex: 1;
          font-size: 28rpx;
          color: #333;

          &::placeholder {
            color: #999;
          }
        }
      }

      .search-button {
        color: #66d47e;
        font-size: 28rpx;
        font-weight: 500;
        padding: 0 8rpx;

        &:active {
          opacity: 0.6;
        }
      }
    }
  }

  .search-content {
    flex: 1;
    padding: 32rpx;
  }

.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 32rpx;
  }

  .quick-search-section {
    .quick-search-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .search-tag {
        padding: 16rpx 32rpx;
        background-color: #fff;
        color: #666;
        font-size: 26rpx;
        border-radius: 32rpx;
        border: 1px solid #eee;

        &:active {
          background-color: #66d47e;
          color: #fff;
          border-color: #66d47e;
        }
      }
    }
  }

  .search-results {
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 120rpx 0;

      .loading-icon {
        animation: spin 1s linear infinite;
        margin-bottom: 24rpx;
      }

      .loading-text {
        font-size: 28rpx;
        color: #666;
      }
    }

    .result-header {
      margin-bottom: 32rpx;

      .result-count {
        font-size: 26rpx;
        color: #666;
      }
    }

    .result-list {
      flex: 1;
      overflow-y: auto;
      
      .result-item {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 32rpx;
        margin-bottom: 16rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

        .result-content {
          flex: 1;

          .order-info {
            margin-bottom: 16rpx;

            .order-title {
              font-size: 30rpx;
              font-weight: 600;
              color: #333;
              display: block;
              margin-bottom: 8rpx;
            }

            .order-desc {
              font-size: 24rpx;
              color: #666;
            }
          }

          .order-meta {
            display: flex;
            gap: 24rpx;

            .order-type,
            .order-time {
              font-size: 22rpx;
              color: #999;
            }
          }
        }

        .order-status {
          padding: 8rpx 16rpx;
          border-radius: 8rpx;
          font-size: 22rpx;

          &.completed {
            background-color: #f6ffed;
            color: #52c41a;
          }

          &.success {
            background-color: #f6ffed;
            color: #52c41a;
          }

          &.pending {
            background-color: #fff2e8;
            color: #fa8c16;
          }

          &.processing {
            background-color: #e6f7ff;
            color: #1890ff;
          }

          &.failed {
            background-color: #fff2f0;
            color: #ff4d4f;
          }

          &.refunding {
            background-color: #f9f0ff;
            color: #722ed1;
          }

          &.default {
            background-color: #f5f5f5;
            color: #666;
          }
          
          // 商城订单状态样式（与商家订单页面保持一致）
          &.status-pending {
            color: #FF5F54;
          }
          
          &.status-paid {
            color: #66D47E;
          }
          
          &.status-completed {
            color: #666666;
          }
          
          &.status-refund {
            color: #F9AF25;
          }
        }

        &:active {
          opacity: 0.8;
        }
      }
      
      // 加载状态样式
      .loading-state {
        text-align: center;
        padding: 32rpx 0;
        
        .loading-more {
          color: #666;
          font-size: 26rpx;
        }
        
        .no-more {
          color: #999;
          font-size: 26rpx;
          position: relative;
          
          .no-more-text {
            position: relative;
            padding: 0 30rpx;
            
            &::before,
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              width: 80rpx;
              height: 1px;
              background: #ddd;
            }
            
            &::before {
              left: -60rpx;
            }
            
            &::after {
              right: -60rpx;
            }
          }
        }
      }
    }

    .empty-result {
      text-align: center;
      padding: 120rpx 0;

      .empty-image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 32rpx;
      }

      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
