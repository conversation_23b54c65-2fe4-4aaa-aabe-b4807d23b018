<template>
  <view class="my-order-page">
    <view class="search-bar" @click="goToSearch">
      <uni-icons type="search" size="20" color="#000" class="icon"></uni-icons>
      <view class="fg search-input"></view>
    </view>

    <!-- 主要Tab -->
    <view class="main-tabs">
      <view
        v-for="(tab, index) in mainTabs"
        :key="index"
        :class="['main-tab', { active: currentMainTab === index }]"
        @click="switchMainTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>

    <!-- 子Tab和内容 -->
    <component
      :is="currentTabComponent"
      class="tab-content"
      @subTabChange="onSubTabChange"
    ></component>
  </view>
</template>

<script>
  import SkillOrders from './components/SkillOrders.vue'
  import RecruitOrders from './components/RecruitOrders.vue'
  import ShopOrders from './components/ShopOrders.vue'

  export default {
    components: {
      SkillOrders,
      RecruitOrders,
      ShopOrders
    },
    data() {
      return {
        currentMainTab: 0,
        currentSubTab: 0, // 当前二级tab索引
        mainTabs: [
          { name: '技能订单', component: 'SkillOrders', type: 'skill' },
          { name: '招募订单', component: 'RecruitOrders', type: 'recruit' },
          { name: '商城订单', component: 'ShopOrders', type: 'shop' }
        ]
      }
    },
    computed: {
      currentTabComponent() {
        return this.mainTabs[this.currentMainTab].component
      }
    },
    methods: {
      goToSearch() {
        // 获取当前二级tab的标识
        const currentSubTabType = this.getCurrentSubTabType()

        uni.navigateTo({
          url: `/pages/myOrder/search?mainTab=${this.mainTabs[this.currentMainTab].type}&subTab=${currentSubTabType}&mainTabIndex=${this.currentMainTab}&subTabIndex=${this.currentSubTab}`
        })
      },
      switchMainTab(index) {
        this.currentMainTab = index
        this.currentSubTab = 0 // 切换主tab时重置子tab
      },
      // 监听子组件的tab切换事件
      onSubTabChange(subTabIndex) {
        this.currentSubTab = subTabIndex
      },
      // 获取当前二级tab的类型标识
      getCurrentSubTabType() {
        const subTabTypes = {
          0: {
            // 技能订单
            0: 'all',
            1: 'waiting',
            2: 'beforeStart',
            3: 'doing',
            4: 'done',
            5: 'cancel',
            6: 'refunding'
          },
          1: {
            // 招募订单
            0: 'all',
            1: 'recruiting',
            2: 'success',
            3: 'failed'
          },
          2: {
            // 商城订单
            0: 'all',
            1: 'pending',
            2: 'toVerify',
            3: 'completed',
            4: 'refunding'
          }
        }
        return subTabTypes[this.currentMainTab]?.[this.currentSubTab] || 'all'
      }
    }
  }
</script>

<style lang="scss" scoped>
  .my-order-page {
    height: 100%;
    box-sizing: border-box;
    background-color: #f4f8fb;
    padding-top: 32rpx;
    display: flex;
    flex-direction: column;

    .fg {
      flex-grow: 1;
    }

    .search-bar {
      margin: 0 32rpx 8rpx;
      background-color: #fff;
      height: 64rpx;
      border-radius: 32rpx;
      padding-right: 24rpx;
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .icon {
        margin: 0 16rpx 0 24rpx;
      }

      .search-input {
        font-size: 28rpx;
        color: #999;
      }
    }

    .main-tabs {
      display: flex;
      background-color: #fff;
      margin: 0 32rpx 16rpx;
      border-radius: 16rpx;
      padding: 8rpx;
      flex-shrink: 0;

      .main-tab {
        flex: 1;
        height: 72rpx;
        line-height: 72rpx;
        text-align: center;
        font-size: 28rpx;
        color: #666;
        border-radius: 12rpx;
        transition: all 0.3s;

        &.active {
          background-color: #66d47e;
          color: #fff;
          font-weight: 600;
        }
      }
    }

    .tab-content {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
