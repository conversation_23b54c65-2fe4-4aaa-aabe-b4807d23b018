<template>
	<view>
		<!-- 加载状态 -->
		<view class="loading-container" v-if="isLoading">
			<uni-icons type="spinner-cycle" size="32" color="#66d47e" class="loading-icon"></uni-icons>
			<text class="loading-text">加载中...</text>
		</view>
		
		<OrderPanel v-for="item in orderInfo" :key="item.id" :order-info="item"></OrderPanel>
		<template v-if="!isLoading && !orderInfo.length">
		  <image class="no-data" src="/static/images/order/nodata.png"></image>
			<view class="txt">暂无已取消技能订单</view>
		</template>
	</view>
</template>

<script>
	import OrderPanel from "./OrderPanel.vue";
	import { skillOrderApi } from '@/common/api'
	
	export default {
		components: {
			OrderPanel
		},
		data() {
			return {
				orderInfo: [],
				isLoading: false,
				pageNum: 1,
				pageSize: 20,
				hasMore: true,
				hasLoaded: false // 添加加载标记
			}
		},
		methods: {
			async loadOrderList(refresh = false) {
				if (this.isLoading) return
				
				try {
					this.isLoading = true
					
					if (refresh) {
						this.pageNum = 1
						this.orderInfo = []
						this.hasMore = true
					}
					
					const params = {
						pageNum: this.pageNum,
						pageSize: this.pageSize
					}
					
					const res = await skillOrderApi.getCancelOrders(params)
					
					if (res && res.code === 200) {
						const newOrders = this.formatOrderData(res.data?.rows || [])
						
						if (refresh) {
							this.orderInfo = newOrders
						} else {
							this.orderInfo = [...this.orderInfo, ...newOrders]
						}
						
						this.hasMore = newOrders.length === this.pageSize
						if (this.hasMore) {
							this.pageNum++
						}
					} else {
						uni.showToast({
							title: res?.message || '获取订单失败',
							icon: 'none'
						})
					}
				} catch (err) {
					console.error('获取已取消订单失败:', err)
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					})
				} finally {
					this.isLoading = false
				}
			},
			
			formatOrderData(data) {
				// 直接返回原始数据，让 OrderPanel 组件处理
				return data;
			},
			

			
			// 初始化数据加载
			initData() {
				if (!this.hasLoaded) {
					this.hasLoaded = true
					this.loadOrderList()
				}
			},
			
			refresh() {
				this.loadOrderList(true)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 0;
		
		.loading-icon {
			animation: spin 1s linear infinite;
			margin-bottom: 24rpx;
		}
		
		.loading-text {
			font-size: 28rpx;
			color: #666;
		}
	}
	
	.no-data {
		width: 246rpx;
		height: 254rpx;
		margin-top: 200rpx;
		margin-left: 50%;
		transform: translate(-50%);
	}
	.txt {
		color: #666;
		font-size: 28rpx;
		text-align: center;
		margin-top: 40rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
</style>