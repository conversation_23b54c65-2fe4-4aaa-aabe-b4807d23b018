<template>
  <view class="shop-pending">
    <scroll-view class="order-list" scroll-y @scrolltolower="onReachBottom" lower-threshold="50">
      <view v-if="orderList.length === 0" class="empty-state">
        <image class="no-data" src="/static/images/order/nodata.png"></image>
        <view class="txt">暂无待支付订单</view>
      </view>
      <view
        v-for="order in orderList"
        :key="order.id"
        class="order-item"
        @click="goToOrderDetail(order)"
      >
        <view class="order-header">
          <text class="order-number">
            <image
              class="ztIMG"
              src="/static/images/order/zt.png"
              mode="aspectFill"
              v-if="order.deliveryType === 2"
            ></image>
            <image class="psIMG" src="/static/images/order/ps.png" mode="aspectFill" v-else></image>
            {{ order.deliveryType === 2 ? '自提订单' : '配送订单' }}
          </text>
          <text class="order-status" :class="getStatusClass(order.orderStatus)">{{
            order.orderStatusDesc
          }}</text>
        </view>
        <view class="shop-info">
          <uni-icons type="shop" size="16" color="#666" />
          <text class="shop-name">{{ order.merchantInfo.merchantName }}</text>
        </view>
        <view class="product-info">
          <image class="product-image" :src="order.merchantInfo.merchantAvatar" mode="aspectFill" />
          <view class="product-detail">
            <text class="product-name text-ellipsis">{{ order.goodsList[0].goodsName }}</text>
            <text class="product-spec">{{ order.goodsList[0].specName }}</text>
            <view class="product-quantity">
              <text>×{{ order.goodsList[0].quantity }}</text>
              <text class="order-price" v-if="order.payMethod === '5'"
                >{{ order.totalAmount / 10 }}贝壳币</text
              >
              <text class="order-price" v-else>¥ {{ order.totalAmount / 100 }}</text>
            </view>
          </view>
        </view>
        <view class="order-info">
          <text class="order-info-label">留言</text>
          <text class="order-info-value">{{ order.orderMessage }}</text>
        </view>
        <view class="order-info" v-if="order.deliveryType === 1">
          <text class="order-info-label">配送地点</text>
          <text class="order-info-value">{{ order.deliveryAddress }}</text>
        </view>
        <view class="order-info">
          <text class="order-info-label">下单时间</text>
          <text class="order-info-value">{{ order.orderDate }}</text>
        </view>
        <view class="order-info" v-if="order.refundApplicationId">
          <text class="order-info-label">申请时间</text>
          <text class="order-info-value">{{ order.updateTime }}</text>
        </view>
        <view class="order-info" v-if="order.orderStatus === '3'">
          <text class="order-info-label">核销时间</text>
          <text class="order-info-value">{{ order.updateTime }}</text>
        </view>
      </view>
      <view class="loading-state" v-if="orderList.length > 0">
        <view v-if="isLoading" class="loading-more">
          <text class="loading-text">加载中...</text>
        </view>
        <view v-else-if="!hasMore" class="no-more">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
  import { orderApi } from '@/common/api'
  export default {
    data() {
      return {
        orderList: [],
        pageNum: 1,
        pageSize: 10,
        hasMore: true,
        isLoading: false,
        merchantId: '5',
        hasLoaded: false // 记录是否已加载过数据
      }
    },
    // 移除mounted，改为activated时加载
    activated() {
      // 只在第一次显示时加载数据
      if (!this.hasLoaded) {
        this.loadOrderList()
        this.hasLoaded = true
      }
    },
    methods: {
      // 提供给父组件调用的初始化方法
      initData() {
        if (!this.hasLoaded) {
          this.loadOrderList()
          this.hasLoaded = true
        }
      },
      onReachBottom() {
        this.loadMore()
      },
      loadMore() {
        if (!this.hasMore || this.isLoading) return
        this.pageNum++
        this.loadOrderList()
      },
      async loadOrderList() {
        if (this.isLoading || !this.hasMore) return
        this.isLoading = true
        try {
          const params = {
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            orderStatus: '0',
            merchantId: this.merchantId
          }
          const res = await orderApi.getOrderList(params)
          const { rows = [], total = 0 } = res || {}
          if (this.pageNum === 1) {
            this.orderList = rows
            // 向父组件传递total数据，用于更新tab角标
            this.$emit('updateTotal', { type: 'pending', total })
          } else {
            this.orderList.push(...rows)
          }
          this.hasMore = this.orderList.length < total
        } catch (error) {
          uni.showToast({ title: '加载失败', icon: 'none' })
        } finally {
          this.isLoading = false
        }
      },
      goToOrderDetail(order) {
        uni.navigateTo({ url: `/pages/orders/details?id=${order.orderCode}` })
      },
      getStatusClass(status) {
        switch (status) {
          case '1':
            return 'pending'
          case '3':
            return 'completed'
          case '99':
            return 'refunding'
          default:
            return 'default'
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .shop-pending {
    height: 100%;
    display: flex;
    flex-direction: column;
    .order-list {
      flex: 1;
      overflow-y: auto;
    }
    .order-item {
      background: #fff;
      border-radius: 16rpx;
      margin-bottom: 24rpx;
      padding: 32rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
        .order-number {
          font-size: 26rpx;
          color: #666;
          display: flex;
          align-items: center;
          .ztIMG,
          .psIMG {
            width: 36rpx;
            height: 36rpx;
            margin-right: 8rpx;
          }
        }
        .order-status {
          font-size: 26rpx;
          &.pending {
            color: #fa8c16;
          }
          &.completed {
            color: #52c41a;
          }
          &.refunding {
            color: #722ed1;
          }
          &.default {
            color: #999;
          }
        }
      }
      .shop-info {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        .shop-name {
          font-size: 24rpx;
          color: #333;
          margin-left: 8rpx;
        }
      }
      .product-info {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        .product-image {
          width: 80rpx;
          height: 80rpx;
          border-radius: 8rpx;
          margin-right: 16rpx;
        }
        .product-detail {
          flex: 1;
          .product-name {
            font-size: 26rpx;
            color: #222;
            font-weight: 500;
          }
          .product-spec {
            font-size: 22rpx;
            color: #999;
            margin: 8rpx 0;
          }
          .product-quantity {
            display: flex;
            align-items: center;
            .order-price {
              margin-left: 16rpx;
              color: #fa8c16;
              font-size: 24rpx;
            }
          }
        }
      }
      .order-info {
        display: flex;
        align-items: center;
        font-size: 22rpx;
        color: #666;
        margin-bottom: 8rpx;
        .order-info-label {
          width: 120rpx;
          color: #999;
        }
        .order-info-value {
          flex: 1;
        }
      }
    }
    .empty-state {
      text-align: center;
      padding: 120rpx 0;
      .no-data {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 32rpx;
      }
      .txt {
        font-size: 28rpx;
        color: #999;
      }
    }
    .loading-state {
      text-align: center;
      padding: 32rpx 0;
      .loading-more {
        color: #666;
        font-size: 26rpx;
      }
      .no-more {
        color: #999;
        font-size: 26rpx;
      }
    }
  }
</style>
