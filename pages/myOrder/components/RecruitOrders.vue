<template>
	<view class="recruit-orders">
		<RecruitTabs @switchTab="switchTab" :tabTotalData="tabTotalData"></RecruitTabs>
		<component
			v-for="(compName, idx) in compList"
			:key="compName"
			:is="compName"
			v-show="currTabIdx === idx"
			class="order-wrap"
			@updateTotal="onUpdateTotal"
			:ref="`recruitComponent${idx}`"
		></component>
	</view>
</template>

<script>
	import RecruitTabs from './RecruitTabs.vue'
	import RecruitAll from './RecruitAll.vue'
	import RecruitRecruiting from './RecruitRecruiting.vue'
	import RecruitSuccess from './RecruitSuccess.vue'
	import RecruitFailed from './RecruitFailed.vue'
	
	export default {
		components: {
			RecruitTabs,
			RecruitAll,
			RecruitRecruiting,
			RecruitSuccess,
			RecruitFailed
		},
		data() {
			return {
				currTabIdx: 0,
				compList: ['RecruitAll', 'RecruitRecruiting', 'RecruitSuccess', 'RecruitFailed'],
				// 存储各个tab的角标数据
				tabTotalData: {
					all: 0,
					recruiting: 0,
					success: 0,
					failed: 0
				}
			}
		},
		mounted() {
			// 组件加载完成后，初始化第一个tab（全部）的数据
			this.$nextTick(() => {
				const firstComponent = this.$refs[`recruitComponent0`]
				if (firstComponent && firstComponent[0] && firstComponent[0].initData) {
					firstComponent[0].initData()
				}
			})
		},
		methods: {
			switchTab(currTabIdx) {
				this.currTabIdx = currTabIdx
				// 向父组件传递二级tab变化事件
				this.$emit('subTabChange', currTabIdx)
				
				// 触发当前组件的数据加载
				this.$nextTick(() => {
					const currentComponent = this.$refs[`recruitComponent${currTabIdx}`]
					if (currentComponent && currentComponent[0] && currentComponent[0].initData) {
						currentComponent[0].initData()
					}
				})
			},
			// 处理子组件传递的total数据
			onUpdateTotal({ type, total }) {
				this.tabTotalData[type] = total
				// 强制触发响应式更新
				this.$set(this.tabTotalData, type, total)
			}
		}
	}
</script>

<style lang="scss" scoped>
.recruit-orders {
	height: 100%;
	display: flex;
	flex-direction: column;
	
	.order-wrap {
		flex: 1;
		overflow-y: auto;
		margin: 14rpx 32rpx 0;
	}
}
</style>