<template>
  <view class="container">
    <scroll-view
      class="tab-content"
      scroll-y
      :scroll-with-animation="false"
      @scroll="onScroll"
      :scroll-top="scrollTop"
    >
      <listItem :list="recruitList" :status="recruitStatus"></listItem>
      <!-- 加载状态 -->
      <view v-if="recruitStatus === 'loading'" class="loading-indicator">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <!-- 空状态 -->
      <view v-if="!recruitList.length && recruitStatus === 'noMore'" class="no-more-indicator">
        <text class="empty-text">暂无活动</text>
      </view>
      <!-- 没有更多数据 -->
      <view v-if="recruitList.length && recruitStatus === 'noMore'" class="no-more-indicator">
        <text class="no-more-text">已经到底了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
  import { useUserStore } from '@/stores/user'
  import listItem from '@/pages/recruit/listItem.vue'
  export default {
    components: { listItem },
    data() {
      return {
        scrollTimer: null,
        scrollTop: 0,
        recruitList: [],
        recruitStatus: 'more',
        nextCursor: null,
        isLoadingMore: false,
        hasLoaded: false // 添加加载标记
      }
    },
    setup() {
      const userStore = useUserStore()
      return { userStore }
    },
    methods: {
      // 初始化数据加载
      initData() {
        if (!this.hasLoaded) {
          this.hasLoaded = true
          this.fetchRecruitList()
        }
      },
      
      async fetchRecruitList(isRefresh = false) {
        if (isRefresh) {
          this.recruitList = []
          this.recruitStatus = 'more'
          this.nextCursor = null
        }
        if (this.recruitStatus === 'noMore' || this.recruitStatus === 'loading') return
        this.recruitStatus = 'loading'
        try {
          const params = {
            pageSize: 20,
            status: 3 // 招募失败
          }
          if (!isRefresh && this.nextCursor) {
            params.cursor = this.nextCursor
          }
          const res = await this.userStore.getRecruitListMy(params)
          if (res.code === 200 && res.data && res.data.list) {
            const newList = res.data.list
            this.recruitList = isRefresh ? newList : [...this.recruitList, ...newList]
            this.nextCursor = res.data.nextCursor
            this.recruitStatus = res.data.nextCursor ? 'more' : 'noMore'
          } else {
            this.recruitStatus = 'noMore'
          }
        } catch (e) {
          this.recruitStatus = 'noMore'
        }
      },
      onScroll(e) {
        if (this.scrollTimer) clearTimeout(this.scrollTimer)
        this.scrollTimer = setTimeout(() => {
          const { scrollTop, scrollHeight } = e.detail
          this.scrollTop = scrollTop
          const query = uni.createSelectorQuery().in(this)
          query
            .select('.tab-content')
            .boundingClientRect(data => {
              if (data) {
                const clientHeight = data.height
                const distanceToBottom = scrollHeight - scrollTop - clientHeight
                if (distanceToBottom <= 100) {
                  if (this.recruitStatus === 'more' && !this.isLoadingMore) {
                    this.isLoadingMore = true
                    this.fetchRecruitList().finally(() => {
                      this.isLoadingMore = false
                    })
                  }
                }
              }
            })
            .exec()
        }, 100)
      }
    }
  }
</script>

<style scoped lang="scss">
  .container {
    background: #f4f8fb;
    height: 100vh;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
  }
  .nav-title {
    font-weight: bold;
  }

  .search-container {
    padding: 20rpx;
  }

  .search-input {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-radius: 50rpx;
    padding: 0 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  .search-input input {
    flex: 1;
    height: 60rpx;
    margin-left: 15rpx;
    font-size: 28rpx;
    color: #333;
  }

  .search-input uni-icons {
    margin-right: 0;
  }
  /* 标签栏样式 */
  .tab-container {
    display: flex;
    // background: #fff;
    // border-bottom: 1rpx solid #eee;
    width: 100%;
    box-sizing: border-box;
    padding: 0 30rpx;
  }
  .tab-content {
    flex: 1;
    height: 0; /* 确保flex子元素能正确计算高度 */
    overflow: hidden;
    // background:
    //   url('@/static/index/bg.png') no-repeat top center,
    //   transparent;
    // background-size: cover;
    // background-position-y: -3px;
  }
  .tab-item {
    position: relative;
    // flex: 1;
    // width:20%;
    display: flex;
    margin-right: 60rpx;
    align-items: center;
    justify-content: center;
    height: 60rpx;
    // border-bottom: 4rpx solid transparent;
    color: rgba(0, 0, 0, 0.5);

    &.active {
      border-bottom: 4rpx solid rgba(102, 212, 126, 1);

      .tab-text {
        color: rgba(0, 0, 0, 1);
        font-weight: bold;
      }
    }
  }

  .tab-text {
    font-size: 26rpx;
    color: #666;
  }

  .tab-badge {
    position: absolute;
    top: 10rpx;
    right: 20rpx;
    background: #ff4757;
    color: #fff;
    font-size: 20rpx;
    border-radius: 20rpx;
    padding: 4rpx 12rpx;
    min-width: 32rpx;
    text-align: center;
    line-height: 1;
  }
  .loading-text {
    font-size: 28rpx;
    color: #666;
  }

  /* 没有更多数据提示样式 */
  .no-more-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    margin: 20rpx;
    border-radius: 12rpx;
  }

  .no-more-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60rpx;
  }
  /* 加载状态指示器样式 */
  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    background-color: #f8f9fa;
    margin: 20rpx;
    border-radius: 12rpx;
  }

  .loading-spinner {
    border: 4rpx solid rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
    border-radius: 50%;
    width: 60rpx;
    height: 60rpx;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
  }
</style>
