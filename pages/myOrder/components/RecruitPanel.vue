<template>
	<view :class="['panel-wrap', getOrderStatus('className')]">
		<view class="status">{{ getOrderStatus('statusName') }}</view>
		<view class="order-theme">{{ orderInfo.title || '招募活动' }}</view>
		<view class="line">
			<text class="txt1">活动描述</text>
			<text class="txt2">{{ orderInfo.description || '精彩活动等你参与' }}</text>
		</view>
		<view class="line">
			<text class="txt1">招募人数</text>
			<text class="txt2">{{ orderInfo.currentCount || 0 }}/{{ orderInfo.targetCount || 0 }}人</text>
			<view class="progress-bar">
				<view class="progress" :style="{width: progressWidth}"></view>
			</view>
		</view>
		<view class="line">
			<text class="txt1">截止时间</text>
			<text class="txt2">{{ orderInfo.deadline || '2025-07-25 18:00' }}</text>
		</view>
		<view class="line" v-if="orderInfo.status === 1">
			<text class="txt1">活动时间</text>
			<text class="txt2">2025-07-26 09:00~18:00</text>
		</view>
		<view class="line" v-if="orderInfo.status === 1">
			<text class="txt1">集合地点</text>
			<text class="txt2">市中心广场东门</text>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			orderInfo: {
				type: Object,
				default: () => {}
			}
		},
		computed: {
			getOrderStatus() {
				return type => {
					let classList = [{
							className: 'recruiting',
							statusName: '招募中'
						}, {
							className: 'success',
							statusName: '招募成功'
						},{
							className: 'failed',
							statusName: '招募失败'
					}];
					return classList[this.orderInfo['status']][type];
				}
			},
			progressWidth() {
				const current = this.orderInfo.currentCount || 0;
				const target = this.orderInfo.targetCount || 1;
				return Math.min((current / target) * 100, 100) + '%';
			}
		}
	}
</script>

<style scoped lang="scss">
.panel-wrap {
	position: relative;
	padding: 24rpx 32rpx 32rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
	margin-bottom: 32rpx;
	.status {
		position: absolute;
		top: 0;
		right: 0;
		width: 124rpx;
		height: 48rpx;
		font-size: 24rpx;
		line-height: 48rpx;
		text-align: right;
		padding-right: 16rpx;
	}
	&.recruiting .status {
		color: #1ba2fc;
		background: url('/static/images/expertOrder/beforeStart.svg') no-repeat 100% / 100%;
	}
	&.success .status {
		color: #6fba1a;
		background: url('/static/images/expertOrder/done.svg') no-repeat 100% / 100%;
	}
	&.failed .status {
		color: #ff5f54;
		background: url('/static/images/expertOrder/waiting.svg') no-repeat 100% / 100%;
	}
	.order-theme {
		height: 40rpx;
		font-weight: 600;
		margin-bottom: 30rpx;
		color: #66d47e;
		font-size: 28rpx;
		line-height: 40rpx;
		padding-left: 36rpx;
		background: url('/static/images/expertOrder/assemble.png') no-repeat left center / 28rpx 26rpx;
	}
	.line {
		line-height: 40rpx;
		margin-bottom: 16rpx;
		display: flex;
		align-items: center;
		.txt1 {
			font-size: 24rpx;
			line-height: 40rpx;
			color: #999;
			flex-shrink: 0;
			width: 120rpx;
		}
		.txt2 {
			font-size: 24rpx;
			padding-left: 32rpx;
			line-height: 40rpx;
			color: #000;
			flex-grow: 1;
		}
		.progress-bar {
			width: 100rpx;
			height: 8rpx;
			background-color: #f0f0f0;
			border-radius: 4rpx;
			margin-left: 16rpx;
			overflow: hidden;
			.progress {
				height: 100%;
				background-color: #66d47e;
				transition: width 0.3s;
			}
		}
		&:last-child {
			margin-bottom: 0;
		}
	}
}
</style>