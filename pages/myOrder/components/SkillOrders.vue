<!--
 * @Author: tb0912 <EMAIL>
 * @Date: 2025-07-23 16:19:11
 * @LastEditors: tb0912 <EMAIL>
 * @LastEditTime: 2025-07-23 17:57:29
 * @FilePath: /small-Islan/pages/myOrder/components/SkillOrders.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
	<view class="skill-orders">
		<SkillTabs @switchTab="switchTab" :tabTotalData="tabTotalData"></SkillTabs>
		<component
			v-for="(compName, idx) in compList"
			:key="compName"
			:is="compName"
			v-show="currTabIdx === idx"
			class="order-wrap"
			@updateTotal="onUpdateTotal"
			:ref="`skillComponent${idx}`"
		></component>
	</view>
</template>

<script>
	import SkillTabs from './SkillTabs.vue'
	import SkillAll from './SkillAll.vue'
	import SkillWaiting from './SkillWaiting.vue'
	import SkillBeforeStart from './SkillBeforeStart.vue'
	import SkillDoing from './SkillDoing.vue'
	import SkillDone from './SkillDone.vue'
	import SkillRefunding from './SkillRefunding.vue'
	
	export default {
		components: {
			SkillTabs,
			SkillAll,
			SkillWaiting,
			SkillBeforeStart,
			SkillDoing,
			SkillDone,
			SkillRefunding
		},
		data() {
			return {
				currTabIdx: 0,
				compList: ['SkillAll', 'SkillWaiting', 'SkillBeforeStart', 'SkillDoing', 'SkillDone', 'SkillRefunding'],
				// 存储各个tab的角标数据
				tabTotalData: {
					all: 0,
					waiting: 0,
					beforeStart: 0,
					doing: 0,
					done: 0,
					refunding: 0
				}
			}
		},
		mounted() {
			// 组件加载完成后，初始化第一个tab（全部）的数据
			this.$nextTick(() => {
				const firstComponent = this.$refs[`skillComponent0`]
				if (firstComponent && firstComponent[0] && firstComponent[0].initData) {
					firstComponent[0].initData()
				}
			})
		},
		methods: {
			switchTab(currTabIdx) {
				this.currTabIdx = currTabIdx
				// 向父组件传递二级tab变化事件
				this.$emit('subTabChange', currTabIdx)
				
				// 触发当前组件的数据加载
				this.$nextTick(() => {
					const currentComponent = this.$refs[`skillComponent${currTabIdx}`]
					if (currentComponent && currentComponent[0] && currentComponent[0].initData) {
						currentComponent[0].initData()
					}
				})
			},
			// 处理子组件传递的total数据
			onUpdateTotal({ type, total }) {
				this.tabTotalData[type] = total
				// 强制触发响应式更新
				this.$set(this.tabTotalData, type, total)
			}
		}
	}
</script>

<style lang="scss" scoped>
.skill-orders {
	height: 100%;
	display: flex;
	flex-direction: column;
	
	.order-wrap {
		flex: 1;
		overflow-y: auto;
		margin: 14rpx 32rpx 0;
	}
}
</style>