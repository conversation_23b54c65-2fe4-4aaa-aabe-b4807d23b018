<template>
	<view :class="['panel-wrap', getOrderStatus('className')]">
		<view class="status">{{ getOrderStatus('statusName') }}</view>
		<view class="order-content">
			<view class="product-info">
				<image class="product-image" :src="orderInfo.image || '/static/images/shoping/spact.png'" mode="aspectFill"></image>
				<view class="product-details">
					<view class="product-name">{{ orderInfo.productName || '商品名称' }}</view>
					<view class="product-spec">
						<text class="quantity">数量: {{ orderInfo.quantity || 1 }}</text>
					</view>
					<view class="product-price">
						<text class="price">¥{{ orderInfo.price || '0.00' }}</text>
					</view>
				</view>
			</view>
			<view class="order-info">
				<view class="line">
					<text class="txt1">订单编号</text>
					<text class="txt2">{{ orderInfo.orderNo || generateOrderNo() }}</text>
				</view>
				<view class="line">
					<text class="txt1">下单时间</text>
					<text class="txt2">{{ orderInfo.createTime || '2025-07-23 14:30:00' }}</text>
				</view>
				<view class="line" v-if="orderInfo.status === 1">
					<text class="txt1">支付时间</text>
					<text class="txt2">{{ orderInfo.payTime || '2025-07-23 14:35:00' }}</text>
				</view>
				<view class="line" v-if="orderInfo.status === 2">
					<text class="txt1">完成时间</text>
					<text class="txt2">{{ orderInfo.finishTime || '2025-07-23 16:00:00' }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			orderInfo: {
				type: Object,
				default: () => {}
			}
		},
		computed: {
			getOrderStatus() {
				return type => {
					let classList = [{
							className: 'pending',
							statusName: '待支付'
						}, {
							className: 'to-verify',
							statusName: '待核销'
						},{
							className: 'completed',
							statusName: '已完成'
						},{
							className: 'refunding',
							statusName: '退款/售后'
					}];
					return classList[this.orderInfo['status']][type];
				}
			}
		},
		methods: {
			generateOrderNo() {
				return 'SP' + Date.now().toString().slice(-8) + Math.floor(Math.random() * 100);
			}
		}
	}
</script>

<style scoped lang="scss">
.panel-wrap {
	position: relative;
	padding: 24rpx 32rpx 32rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
	margin-bottom: 32rpx;
	.status {
		position: absolute;
		top: 0;
		right: 0;
		width: 124rpx;
		height: 48rpx;
		font-size: 24rpx;
		line-height: 48rpx;
		text-align: right;
		padding-right: 16rpx;
	}
	&.pending .status {
		color: #ff5f54;
		background: url('/static/images/expertOrder/waiting.svg') no-repeat 100% / 100%;
	}
	&.to-verify .status {
		color: #f9af25;
		background: url('/static/images/expertOrder/beforeStart.svg') no-repeat 100% / 100%;
	}
	&.completed .status {
		color: #6fba1a;
		background: url('/static/images/expertOrder/done.svg') no-repeat 100% / 100%;
	}
	&.refunding .status {
		color: #f9af25;
		background: url('/static/images/expertOrder/refunding.svg') no-repeat 100% / 100%;
	}
	.order-content {
		.product-info {
			display: flex;
			margin-bottom: 24rpx;
			.product-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 12rpx;
				flex-shrink: 0;
			}
			.product-details {
				flex: 1;
				margin-left: 24rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				.product-name {
					font-size: 28rpx;
					color: #000;
					font-weight: 600;
					line-height: 40rpx;
				}
				.product-spec {
					font-size: 24rpx;
					color: #666;
					line-height: 34rpx;
				}
				.product-price {
					.price {
						font-size: 32rpx;
						color: #ff5f54;
						font-weight: 600;
					}
				}
			}
		}
		.order-info {
			.line {
				line-height: 40rpx;
				margin-bottom: 12rpx;
				display: flex;
				.txt1 {
					font-size: 24rpx;
					line-height: 40rpx;
					color: #999;
					flex-shrink: 0;
					width: 120rpx;
				}
				.txt2 {
					font-size: 24rpx;
					padding-left: 32rpx;
					line-height: 40rpx;
					color: #000;
					flex-grow: 1;
				}
				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}
}
</style>