<template>
	<view :class="['panel-wrap', getOrderStatus('className')]" @click="handleClick">
		<slot name="header"></slot>
		<slot name="btn"></slot>
		<view class="status">{{ getOrderStatus('statusName') }}</view>
		<view class="order-theme">{{ orderInfo.skillName || '技能服务' }}</view>
		<view class="line during">
			<text class="txt1">预约时长</text>
			<text class="txt2">{{ orderInfo.serviceDurationHours ? orderInfo.serviceDurationHours + '小时' : '1小时' }}</text>
			<text class="txt3">{{ formatAmount(orderInfo.totalAmount) }}</text>
		</view>
		<view class="line startTime" v-if="showStartTime">
			<text class="txt1">开始时间</text>
			<text class="txt2">{{ orderInfo.serviceStartTime }}</text>
		</view>
		<view class="line during" v-if="showDuringTime">
			<text class="txt1">持续时间</text>
			<view class="txt2">
				<text class="minutes">52</text>
				<text class="txt4">分钟</text>
				<text class="seconds">10</text>
				<text class="txt4">秒</text>
			</view>
		</view>
		<view class="line startTime" v-if="showEndTime">
			<text class="txt1">结束时间</text>
			<text class="txt2">2025-07-12 16:00:08</text>
		</view>
		<template v-if="showOrderInfo">
			<view class="line time">
				<text class="txt1">预约时间</text>
				<text class="txt2">{{ orderInfo.serviceStartTime }}</text>
			</view>
			<view class="line info">
				<text class="txt1">预约信息</text>
				<text class="txt2">{{ orderInfo.buyerName || orderInfo.buyerNickname }} {{ orderInfo.buyerPhone ? orderInfo.buyerPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '' }}</text>
			</view>
			<view class="line addr">
				<text class="txt1">预约地址</text>
				<text class="txt2">{{ getServiceAddress(orderInfo.serviceAddress) }}</text>
			</view>
			<view class="line remark" v-if="orderInfo.buyerMessage">
				<text class="txt1">预约备注</text>
				<text class="txt2">{{ orderInfo.buyerMessage }}</text>
			</view>
		</template>
		
		<view class="line partner" v-if="showPartner">
			<text class="txt1">下&nbsp;单&nbsp;人</text>
			<view class="userinfo">
				<!-- 头像 -->
				<view class="avatar" :style="orderInfo.buyerAvatar ? `background-image: url(${orderInfo.buyerAvatar}); background-size: cover; background-position: center;` : ''"></view>
				<view class="name-age">
					<view class="name">
						<text>{{ orderInfo.buyerName || orderInfo.buyerNickname || 'Ada' }}</text>
						<view class="icon-v">V</view>
					</view>
					<view class="age-wrap">
						<view :class="['age', orderInfo.buyerGender === 1 ? 'female' : 'male']">{{ orderInfo.buyerAge || '18' }}</view>
						<view class="xz"></view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			orderInfo: {
				type: Object,
				default: () => {}
			},
			isShowOrderInfo: {
				type: Boolean,
				default: false
			}
		},
		methods: {
			handleClick() {
				if (this.orderInfo && this.orderInfo.orderNo) {
					uni.navigateTo({
						url: `/pages/skillOrderDetail/skillOrderDetail?id=${this.orderInfo.orderNo}`
					})
				}
			},
			// 格式化金额，将分转为元并添加贝壳币单位
			formatAmount(amount) {
				if (!amount && amount !== 0) return '';
				// 将分转为元
				const yuan = (amount / 100).toFixed(2);
				// 去掉小数点后的零
				const formattedYuan = yuan.replace(/\.00$/, '');
				return `${formattedYuan}贝壳币`;
			},
			// 解析服务地址JSON字符串
			getServiceAddress(addressStr) {
				if (!addressStr) return '未设置地址';
				try {
					const addressObj = typeof addressStr === 'string' ? JSON.parse(addressStr) : addressStr;
					return addressObj.areaDetail + ' ' + addressObj.detailAddress;
				} catch (e) {
					return addressStr;
				}
			}
		},
		computed: {
			getOrderStatus() {
				return type => {
					// 状态映射表
					const statusMap = {
						// 旧状态码映射
						0: { className: 'waiting', statusName: '待接单' },
						1: { className: 'before-start', statusName: '待开始' },
						2: { className: 'doing', statusName: '进行中' },
						3: { className: 'done', statusName: '已完成' },
						4: { className: 'refunding', statusName: '退款/售后' },
						// 新状态码映射
						10: { className: 'waiting', statusName: '待同意' },
						20: { className: 'before-start', statusName: '待开始' },
						30: { className: 'doing', statusName: '进行中' },
						40: { className: 'done', statusName: '已完成' },
						50: { className: 'refunding', statusName: '退款中' },
						60: { className: 'refunding', statusName: '已退款' }
					};
					
					// 获取状态
					const status = this.orderInfo.orderStatus || this.orderInfo.status || 0;
					// 获取状态描述
					const statusDesc = this.orderInfo.orderStatusDesc;
					
					// 如果有状态描述，优先使用状态描述
					if (statusDesc && type === 'statusName') {
						return statusDesc;
					}
					
					// 否则使用状态码映射
					if (statusMap[status] && statusMap[status][type]) {
						return statusMap[status][type];
					}
					
					// 如果没有找到对应的状态，返回默认值
					return type === 'className' ? 'waiting' : '未知状态';
				}
			},
			showOrderInfo() {
				const status = this.orderInfo.orderStatus || this.orderInfo.status || 0;
				return status === 0 || status === 10 || status === 1 || status === 20 || this.isShowOrderInfo;
			},
			showDuringTime() {
				const status = this.orderInfo.orderStatus || this.orderInfo.status || 0;
				return status === 2 || status === 30;
			},
			showEndTime() {
				const status = this.orderInfo.orderStatus || this.orderInfo.status || 0;
				return status === 3 || status === 40 || status === 4 || status === 50 || status === 60;
			},
			showStartTime() {
				const status = this.orderInfo.orderStatus || this.orderInfo.status || 0;
				return status === 2 || status === 30 || status === 3 || status === 40 || status === 4 || status === 50 || status === 60;
			},
			showPartner() {
				const status = this.orderInfo.orderStatus || this.orderInfo.status || 0;
				return status !== 0 && status !== 10;
			}
		}
	}
</script>

<style scoped lang="scss">
.panel-wrap {
	position: relative;
	padding: 24rpx 32rpx 32rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
	margin-bottom: 32rpx;
	.status {
		position: absolute;
		top: 0;
		right: 0;
		width: 124rpx;
		height: 48rpx;
		font-size: 24rpx;
		line-height: 48rpx;
		text-align: right;
		padding-right: 16rpx;
	}
	&.before-start .status {
		color: #f9af25;
		background: url('/static/images/expertOrder/beforeStart.svg') no-repeat 100% / 100%;
	}
	&.waiting .status {
		color: #ff5f54;
		background: url('/static/images/expertOrder/waiting.svg') no-repeat 100% / 100%;
	}
	&.doing .status {
		color: #6fba1a;
		background: url('/static/images/expertOrder/doing.svg') no-repeat 100% / 100%;
	}
	&.done .status {
		color: #666;
		background: url('/static/images/expertOrder/done.svg') no-repeat 100% / 100%;
	}
	&.refunding .status {
		color: #f9af25;
		background: url('/static/images/expertOrder/refunding.svg') no-repeat 100% / 100%;
	}
	.order-theme {
		height: 40rpx;
		font-weight: 600;
		margin-bottom: 30rpx;
		color: #66d47e;
		font-size: 28rpx;
		line-height: 40rpx;
		padding-left: 36rpx;
		background: url('/static/images/expertOrder/assemble.png') no-repeat left center / 28rpx 26rpx;
	}
	.line {
		line-height: 40rpx;
		margin-bottom: 16rpx;
		display: flex;
		.txt1 {
			font-size: 24rpx;
			line-height: 40rpx;
			color: #999;
			flex-shrink: 0;
		}
		.txt2 {
			font-size: 24rpx;
			padding-left: 32rpx;
			line-height: 40rpx;
			color: #000;
			flex-grow: 1;
			.minutes, .seconds {
			  color: #66d47e
			}
			.txt4 {
				padding: 0 12rpx;
			}
		}
		.txt3 {
			line-height: 40rpx;
			font-size: 28rpx;
			color: #66d47e
		}
		&:last-child {
			margin-bottom: 0;
		}
	}
	.partner {
		.userinfo {
			height: 80rpx;
			display: flex;
			.avatar {
				height: 80rpx;
				width: 80rpx;
				background-color: #ccc;
				border-radius: 40rpx;
				margin-left: 36rpx;
				margin-right: 16rpx;
			}
			.name-age {
				height: 80rpx;
				.name {
					height: 32rpx;
					line-height: 32rpx;
					color: #000;
					font-size: 26rpx;
					font-weight: 600;
					display: flex;
					.icon-v {
						height: 28rpx;
						width: 28rpx;
						border-radius: 14rpx;
						background-color: #ff4242;
						font-size: 20rpx;
						text-align: center;
						margin-left: 8rpx;
						color: #fff;
					}
				}
				.age-wrap {
					display: flex;
					height: 40rpx;
					margin-top: 8rpx;
					.age {
						height: 40rpx;
						width: 72rpx;
						margin-right: 8rpx;
						line-height: 40rpx;
						padding-left: 32rpx;
						box-sizing: border-box;
						font-size: 24rpx;
						border-radius: 4rpx;
						background-repeat: no-repeat;
						background-position: 8rpx center;
						background-size: 20rpx 20rpx;
						&.male {
							color: #ff5f54;
							background-color: #fff2f0;
							background-image: url('/static/images/profile/woman.png');
						}
						&.female {
							color: #1ba2fc;
							background-color: #e8faff;
							background-image: url('/static/images/profile/man.png');
						}
					}
					.xz {
						border-radius: 4rpx;
						height: 40rpx;
						width: 40rpx;
						border-radius: 4rpx;
						background-color: rgba(0, 0, 0, .05);
					}
				}
			}
		}
	}
}
</style>