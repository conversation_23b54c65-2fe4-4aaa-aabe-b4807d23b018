<template>
	<view class="shop-orders">
		<ShopTabs @switchTab="switchTab" :tabTotalData="tabTotalData"></ShopTabs>
		<component
			v-for="(compName, idx) in compList"
			:is="compName"
			v-show="currTabIdx === idx"
			class="order-wrap"
			@updateTotal="onUpdateTotal"
			:ref="`shopComponent${idx}`"
		></component>
	</view>
</template>

<script>
	import ShopTabs from './ShopTabs.vue'
	import ShopAll from './ShopAll.vue'
	import ShopPending from './ShopPending.vue'
	import ShopToVerify from './ShopToVerify.vue'
	import ShopCompleted from './ShopCompleted.vue'
	import ShopRefunding from './ShopRefunding.vue'
	
	export default {
		components: {
			ShopTabs,
			ShopAll,
			ShopPending,
			ShopToVerify,
			ShopCompleted,
			ShopRefunding
		},
		data() {
			return {
				currTabIdx: 0,
				compList: ['ShopAll', 'ShopPending', 'ShopToVerify', 'ShopCompleted', 'ShopRefunding'],
				// 存储各个tab的角标数据
				tabTotalData: {
					all: 0,
					pending: 0,
					toVerify: 0,
					completed: 0,
					refunding: 0
				}
			}
		},
		mounted() {
			// 组件加载完成后，初始化第一个tab（全部）的数据
			this.$nextTick(() => {
				const firstComponent = this.$refs[`shopComponent0`]
				if (firstComponent && firstComponent[0] && firstComponent[0].initData) {
					firstComponent[0].initData()
				}
			})
		},
		methods: {
			switchTab(currTabIdx) {
				this.currTabIdx = currTabIdx
				// 向父组件传递二级tab变化事件
				this.$emit('subTabChange', currTabIdx)
				
				// 触发当前组件的数据加载
				this.$nextTick(() => {
					const currentComponent = this.$refs[`shopComponent${currTabIdx}`]
					if (currentComponent && currentComponent[0] && currentComponent[0].initData) {
						currentComponent[0].initData()
					}
				})
			},
			// 处理子组件传递的total数据
			onUpdateTotal({ type, total }) {
				this.tabTotalData[type] = total
				// 强制触发响应式更新
				this.$set(this.tabTotalData, type, total)
			}
		}
	}
</script>

<style lang="scss" scoped>
.shop-orders {
	height: 100%;
	display: flex;
	flex-direction: column;
	
	.order-wrap {
		flex: 1;
		overflow-y: auto;
		margin: 14rpx 32rpx 0;
	}
}
</style>