<template>
	<scroll-view scroll-x="true" class="scroll-x">
		<view class="tabs-wrap">
			<view
				v-for="(item, idx) in tabList"
				:key="item.type"
				:class="['tab', {curr: idx === currIdx}]"
				@click="switchTab(idx)"
			>
				<text>{{ item.txt }}</text>
				<text v-show="item.total > 0">({{ item.total }})</text>
			</view>
		</view>
	</scroll-view>
</template>

<script>
	export default {
		props: {
			// 接收父组件传递的角标数据
			tabTotalData: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				currIdx: 0,
				tabList: [
					{
						type: 'all',
						txt: '全部',
						total: 0,
					},
					{
						type: 'recruiting',
						txt: '招募中',
						total: 0,
					},
					{
						type: 'success',
						txt: '招募成功',
						total: 0,
					},
					{
						type: 'failed',
						txt: '招募失败',
						total: 0,
					}
				]
			}
		},
		watch: {
			// 监听父组件传递的角标数据变化
			tabTotalData: {
				handler(newData) {
					// 更新对应tab的角标数量
					this.tabList.forEach(tab => {
						if (newData[tab.type] !== undefined) {
							tab.total = newData[tab.type]
						}
					})
				},
				deep: true,
				immediate: true
			}
		},
		methods: {
			switchTab(idx) {
				this.currIdx = idx;
				this.$emit('switchTab', idx)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.scroll-x {
		white-space: nowrap;
		padding-right: 32rpx;
	}
	.tabs-wrap {
		height: 88rpx;
		padding-left: 32rpx;
		margin-right: 32rpx;
		padding-right: 32rpx;
		display: flex;
    flex-wrap: nowrap;
		.tab {
			height: 88rpx;
			line-height: 88rpx;
			margin-right: 64rpx;
			color: rgba(0,0,0,.5);
			font-size: 28rpx;
			flex: 0 0 auto;
			&.curr {
				position: relative;
				color: #000;
				&::before {
					content: '';
					bottom: 10rpx;
					position: absolute;
					left: 0;
					right: 0;
					border-radius: 3rpx;
					height: 6rpx;
					background-color: #66d47e;
				}
			}
			&:last-child {
				padding-right: 32rpx;
				&::before {
					right: 32rpx;
				}
			}
		}
	}
</style>