<template>
	<scroll-view scroll-y="true" @scrolltolower="handleToLower" class="scroll-view-wrap">
		<slot></slot>
		<view class="no-more" v-if="isNoMore">-- 没有了 --</view>
		<view class="bt"></view>
	</scroll-view>
</template>

<script>
	import { skillOrderApi } from '@/common/api.js'
	export default {
		props: {
			orderStatus: {
				type: [String, Number],
				default: ''
			}
		},
		data() {
			return {
				isLoading: false, // 接口是否正在请求 防止多次调用
				orderList: [],
				pageNum: 1,
				pageSize: 10,
				totalPage: 0,
				isNoMore: false
			}
		},
		created() {
			// 获取技能订单列表
			this.getSkillOrderList()
		},
		methods: {
			handleToLower() {
				// console.log('到底啦！！！')
				if (this.pageNum === this.totalPage) {
					return
				}
				this.pageNum++
				this.getSkillOrderList()
			},
			getSkillOrderList() {
				let params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}
				if (this.orderStatus !== '') {
					params.statusFilter = this.orderStatus
				}
				skillOrderApi.getSkillOrderList(params).then(res => {
					// console.log(res)
					if (res && res.data && Array.isArray(res.data.rows)) {
						this.$emit('pushOrderList', res.data.rows)
						this.orderList.push(res.data.rows)
						this.totalPage = Math.ceil(res.data.total / this.pageSize)
						if (this.totalPage === this.pageNum) {
							this.isNoMore = true
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.scroll-view-wrap {
		height: 100%;
		// overflow: auto;
		.no-more {
			height: 40rpx;
			line-height: 40rpx;
			font-size: 24rpx;
			color: #999;
			text-align: center;
			margin-bottom: 32rpx;
		}
		.bt {
			height: 1rpx;
		}
	}
</style>