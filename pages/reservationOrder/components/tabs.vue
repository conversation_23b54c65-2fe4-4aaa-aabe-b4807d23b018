<template>
	<scroll-view scroll-x="true" class="scroll-x">
		<view class="tabs-wrap">
			<view
				v-for="(item, idx) in tabList"
				:class="['tab', {curr: idx === currIdx}]"
				@click="switchTab(idx)"
			>
				<text>{{ item.txt }}</text>
				<text v-show="item.total">({{ item.total }})</text>
			</view>
		</view>
	</scroll-view>
</template>

<script>
	import { skillOrderApi } from '@/common/api.js'
	export default {
		data() {
			return {
				currIdx: 0,
				tabList: [
					{
						type: 'all',
						txt: '全部',
						total: 0,
					},
					{
						type: 'waiting',
						txt: '待接单',
						total: 0,
					},
					{
						type: 'beforeStart',
						txt: '待开始',
						total: 0,
					},
					{
						type: 'doing',
						txt: '进行中',
						total: 0,
					},
					{
						type: 'done',
						txt: '已完成',
						total: 0,
					},
					{
						type: 'refunding',
						txt: '退款中',
						total: 0,
					},
				]
			}
		},
		created() {
			this.getSkillOrderCountByStatus()
		},
		methods: {
			getSkillOrderCountByStatus() {
				skillOrderApi.getSkillOrderNumByStatus().then(res => {
					if (res && res.data) {
						// console.log(res.data)
						this.tabList[0].total = res.data.totalCount;
						this.tabList[1].total = res.data.pendingAcceptCount;
						this.tabList[2].total = res.data.pendingStartCount;
						this.tabList[3].total = res.data.inProgressCount;
						this.tabList[4].total = res.data.completedCount;
						this.tabList[5].total = res.data.cancelledCount;
					}
				})
			},
			switchTab(idx) {
				this.currIdx = idx;
				this.$emit('switchTab', idx)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.scroll-x {
		white-space: nowrap;
		padding-right: 32rpx;
	}
	.tabs-wrap {
		height: 88rpx;
		padding-left: 32rpx;
		margin-right: 32rpx;
		padding-right: 32rpx;
		display: flex;
    flex-wrap: nowrap;
		.tab {
			height: 88rpx;
			line-height: 88rpx;
			margin-right: 64rpx;
			color: rgba(0,0,0,.5);
			font-size: 28rpx;
			flex: 0 0 auto;
			&.curr {
				position: relative;
				color: #000;
				&::before {
					content: '';
					bottom: 10rpx;
					position: absolute;
					left: 0;
					right: 0;
					border-radius: 3rpx;
					height: 6rpx;
					background-color: #66d47e;
				}
			}
			&:last-child {
				padding-right: 32rpx;
				&::before {
					right: 32rpx;
				}
			}
		}
	}
</style>