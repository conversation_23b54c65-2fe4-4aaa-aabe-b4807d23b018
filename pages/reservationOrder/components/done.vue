<template>
	<view>
		<Waterfall order-status="已完成" @pushOrderList="pushOrderList">
			<PanelVue v-for="item in orderInfo" :order-info="item" @click="gotoDetail(item.orderNo)"></PanelVue>
			<template v-if="!orderInfo.length">
				<image class="no-data" src="/static/images/order/nodata.png"></image>
				<view class="txt">暂无相关订单</view>
			</template>
		</Waterfall>
	</view>
</template>

<script>
	import { SKILL_ORDER_STATUS } from '@/common/constants.js'
	import PanelVue from "./panel.vue";
	import Waterfall from "./waterfall.vue";
	export default {
		components: {
			PanelVue,
			Waterfall
		},
		data() {
			return {
				orderInfo: [],
				// orderStatus: SKILL_ORDER_STATUS.COMPLETE
			}
		},
		methods: {
			pushOrderList(orderList) {
				this.orderInfo.push(...orderList)
			},
			gotoDetail(orderNo) {
				uni.navigateTo({
					url: '/pages/reservationOrderDetail/reservationOrderDetail?orderNo=' + orderNo
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.no-data {
		width: 246rpx;
		height: 254rpx;
		margin-top: 200rpx;
		margin-left: 50%;
		transform: translate(-50%);
	}
	.txt {
		color: #666;
		font-size: 28rpx;
		text-align: center;
		margin-top: 40rpx;
	}
</style>