<template>
	<view>
		<Waterfall @pushOrderList="pushOrderList">
			<PanelVue
			  v-for="item in orderInfo"
				:order-info="item"
				:timer-list="timerList"
				@click="gotoDetail(item.orderNo)"
				@setDurationTime="setDurationTime"
			></PanelVue>
			<template v-if="!orderInfo.length">
				<image class="no-data" src="/static/images/order/nodata.png"></image>
				<view class="txt">暂无相关订单</view>
			</template>
		</Waterfall>
	</view>
</template>

<script>
	import Waterfall from "./waterfall.vue";
	import PanelVue from "./panel.vue";
	// import { skillOrderApi } from '@/common/api.js'
	export default {
		components: {
			PanelVue,
			Waterfall
		},
		data() {
			return {
				orderInfo: [],
				timeout: null,
				timerList: {}
			}
		},
		created() {
		},
		methods: {
			pushOrderList(orderList) {
				this.orderInfo.push(...orderList)
			},
			setDurationTime(payload) {
				this.$set(this.timerList, [payload.orderNo], {startTime: payload.startTime})
				if (this.timeout) {
					return
				}
				this.timeout = setInterval(() => {
					Object.keys(this.timerList).map(key => {
						
						const timeDiff = Date.parse(new Date()) - Date.parse(new Date(this.timerList[key].startTime))
						let seconds = timeDiff / 1000
						let minutes = Math.floor(seconds / 60)
						let hours = Math.floor(minutes / 60)
						
						seconds %= 60
						minutes %= 60
						this.$set(this.timerList[key], 'durationHours', hours)
						this.$set(this.timerList[key], 'durationMinutes', minutes)
						this.$set(this.timerList[key], 'durationSeconds', seconds)
						// console.log('-----------', this.orderInfo.orderNo, timeDiff, hours, minutes, seconds)
						// console.log('-----------', this.timerList)
					})
				}, 1000)
			},
			gotoDetail(orderNo) {
				uni.navigateTo({
					url: '/pages/reservationOrderDetail/reservationOrderDetail?orderNo=' + orderNo
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.no-data {
		width: 246rpx;
		height: 254rpx;
		margin-top: 200rpx;
		margin-left: 50%;
		transform: translate(-50%);
	}
	.txt {
		color: #666;
		font-size: 28rpx;
		text-align: center;
		margin-top: 40rpx;
	}
</style>