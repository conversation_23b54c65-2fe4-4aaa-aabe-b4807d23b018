<template>
	<view :class="['panel-wrap', getOrderStatus('className')]">
		<slot name="header"></slot>
		<slot name="btn"></slot>
		<view class="status">{{ getOrderStatus('statusName') }}</view>
		<!-- <view class="status">{{ orderInfo.orderStatusDesc }}</view> -->
		<view class="order-theme">{{ orderInfo.skillName }}</view>
		<view class="line during">
			<text class="txt1">预约时长</text>
			<text class="txt2">{{ orderInfo.serviceDurationHours }}小时</text>
			<text class="txt3">{{ orderInfo.skillAmount }}贝壳币</text>
		</view>
		<view class="line startTime" v-if="showStartTime">
			<text class="txt1">开始时间</text>
			<text class="txt2">{{ orderInfo.serviceStartTime }}</text>
		</view>
		<view class="line during" v-if="showDuringTime">
			<text class="txt1">持续时间</text>
			<view class="txt2">
				<template v-if="timerList && timerList[orderInfo.orderNo] && timerList[orderInfo.orderNo].durationHours">
					<text class="minutes">{{ timerList[orderInfo.orderNo].durationHours }}</text>
					<text class="txt4">小时</text>
				</template>
				<template v-if="timerList && timerList[orderInfo.orderNo] && timerList[orderInfo.orderNo].durationMinutes">
					<text class="minutes">{{ timerList[orderInfo.orderNo].durationMinutes }}</text>
					<text class="txt4">分钟</text>
				</template>
				<text class="seconds">{{ timerList && timerList[orderInfo.orderNo].durationSeconds }}</text>
				<text class="txt4">秒</text>
			</view>
		</view>
		<view class="line startTime" v-if="showEndTime">
			<text class="txt1">结束时间</text>
			<text class="txt2">2025-07-12 16:00:08</text>
		</view>
		<template v-if="showOrderInfo">
			<view class="line time">
				<text class="txt1">预约时间</text>
				<text class="txt2">{{ orderInfo.serviceStartTime }}</text>
				<!-- <text class="txt2">2025-07-12(周六) 13:00~16:00</text> -->
			</view>
			<view class="line info">
				<text class="txt1">预约信息</text>
				<text class="txt2">{{ orderInfo.buyerName }} {{ phoneEncrypt(orderInfo.buyerPhone) }}</text>
			</view>
			<view class="line addr">
				<text class="txt1">预约地址</text>
				<text class="txt2">{{ addrFormat(orderInfo.serviceAddress) }}</text>
			</view>
			<view class="line remark">
				<text class="txt1">预约备注</text>
				<text class="txt2">{{ orderInfo.buyerMessage }}</text>
			</view>
		</template>
		
		<view class="line partner" v-if="showPartner">
			<text class="txt1">接&nbsp;单&nbsp;人</text>
			<view class="userinfo">
				<!-- 头像 -->
				<!-- <view class="avatar"></view> -->
				<image class="avatar" :src="orderInfo.buyerAvatar"></image>
				<view class="name-age">
					<view class="name">
						<text>{{ orderInfo.buyerNickname }}</text>
						<view class="icon-v">V</view>
					</view>
					<view class="age-wrap">
						<view :class="['age', getGender(orderInfo.buyerGender)]">{{ orderInfo.buyerAge }}</view>
						<!-- <view class="xz"></view> -->
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { SKILL_ORDER_STATUS, SKILL_ORDER_STATUS_DESC } from '@/common/constants.js'
	export default {
		props: {
			orderInfo: {
				type: Object,
				default: () => {} // {status: 0, sex: 'male', age: 28}
			},
			isShowOrderInfo: {
				type: Boolean,
				default: false
			},
			timerList: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
				// durationHours: 0,
				// durationMinutes: 0,
				// durationSeconds: 0,
				timeout: null
			}
		},
		computed: {
			getOrderStatus() {
				return type => {
					let classObj = {
						className: '',
						statusName: ''
					}
					// let classList = {
					// 	[SKILL_ORDER_STATUS_DESC.TO_AGREE]: {
					// 		className: 'waiting',
					// 		statusName: '待接单'
					// 	},
					// 	[SKILL_ORDER_STATUS_DESC.TO_ACCESS]: {
					// 		className: 'waiting',
					// 		statusName: '待接单'
					// 	},
					// 	[SKILL_ORDER_STATUS_DESC.TO_ALLOCATE]: {
					// 		className: 'waiting',
					// 		statusName: '待接单'
					// 	},
					// 	[SKILL_ORDER_STATUS_DESC.TO_BEGIN]: {
					// 		className: 'before-start',
					// 		statusName: '待开始'
					// 	},
					// 	[SKILL_ORDER_STATUS_DESC.IN_PROGRESS]: {
					// 		className: 'doing',
					// 		statusName: '进行中'
					// 	},
					// 	[SKILL_ORDER_STATUS_DESC.COMPLETE]: {
					// 		className: 'done',
					// 		statusName: '已完成'
					// 	},
					// 	[SKILL_ORDER_STATUS_DESC.REFUNDED]: {
					// 		className: 'refunding',
					// 		statusName: '已退款'
					// 	},
					// 	[SKILL_ORDER_STATUS_DESC.REFUNDING]: {
					// 		className: 'refunding',
					// 		statusName: '退款中'
					// }};
					if (this.orderInfo['orderStatus']) {
						switch(this.orderInfo['orderStatus']) {
							case SKILL_ORDER_STATUS.TO_AGREE:
							case SKILL_ORDER_STATUS.TO_ALLOCATE: 
							  classObj = {
									className: 'waiting',
									statusName: '待接单'
								};
								break;
							case SKILL_ORDER_STATUS.TO_BEGIN:
							  classObj = {
									className: 'before-start',
									statusName: '待开始'
								};
								break;
							case SKILL_ORDER_STATUS.IN_PROGRESS:
							  classObj = {
									className: 'doing',
									statusName: '进行中'
								};
								break;
							case SKILL_ORDER_STATUS.COMPLETE:
						  case SKILL_ORDER_STATUS.TO_EVALUATE:
							  classObj = {
									className: 'done',
									statusName: '已完成'
								};
								break;
							case SKILL_ORDER_STATUS.REFUNDED:
							case SKILL_ORDER_STATUS.USER_CANCEL:
							case SKILL_ORDER_STATUS.EXPERT_REFUSE:
							case SKILL_ORDER_STATUS.TIMEOUT:
							case SKILL_ORDER_STATUS.REFUNDING:
							  classObj = {
									className: 'refunding',
									statusName: '已取消'
								};
								break;
							default: break;
						}
					}
					return classObj[type]
				}
			},
			isToAgree() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_AGREE
				 || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_ALLOCATE
			},
			isToBegin() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_BEGIN
			},
			isInProgress() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.IN_PROGRESS
			},
			isComplete() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.COMPLETE
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_EVALUATE
			},
			isRefunding() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.USER_CANCEL
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.EXPERT_REFUSE
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TIMEOUT
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.REFUNDING
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.REFUNDED
			},
			showOrderInfo() {
				return this.isToAgree
				  || this.isToBegin
					|| this.isShowOrderInfo
			},
			showDuringTime() {
				return this.isInProgress
			},
			showEndTime() {
				return this.isComplete || this.isRefunding
			},
			showStartTime() {
				return this.isInProgress
				  || this.isComplete
				  || this.isRefunding
			},
			showPartner() {
				return !this.isToAgree
			},
			phoneEncrypt() {
				return num => {
					const reg = /(\d{3})(\d{4})(\d{4})/
					return String(num).replace(reg, '$1****$3')
				}
			},
			addrFormat() {
				return addr => {
					let addrFmt = JSON.parse(addr)
					// console.log(addrFmt)
					return `${addrFmt.areaDetail}${addrFmt.detailAddress}`
				}
			},
			getGender() {
				return gender => {
					// 1 - 男  2 - 女
					return gender == 1 ? 'male' : 'female'
				}
			}
		},
		created() {
			if (this.isInProgress) {
				// console.log(this.timerList, this.orderInfo, this.isShowOrderInfo, '???????????')
				this.$emit('setDurationTime', {
					orderNo: this.orderInfo.orderNo,
					startTime: this.orderInfo.serviceStartTime
				})
				// this.setTimer()
			}
		},
		// beforeDestroy() {
			// this.timeout && clearInterval(this.timeout)
		// },
		methods: {
			setTimer() {
				if (this.orderInfo.serviceStartTime) {
					this.timeout = setInterval(() => {
						const timeDiff = Date.parse(new Date()) - Date.parse(new Date(this.orderInfo.serviceStartTime))
						let seconds = timeDiff / 1000
						let minutes = Math.floor(seconds / 60)
						let hours = Math.floor(minutes / 60)
						
						seconds %= 60
						minutes %= 60
						this.durationHours = hours
						this.durationMinutes = minutes
						this.durationSeconds = seconds
						console.log(this.orderInfo.orderNo, timeDiff, hours, minutes, seconds)
					}, 1000)
				}
			}
		}
	}
</script>

<style scoped lang="scss">
.panel-wrap {
	position: relative;
	padding: 24rpx 32rpx 32rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
	margin-bottom: 32rpx;
	.status {
		position: absolute;
		top: 0;
		right: 0;
		width: 124rpx;
		height: 48rpx;
		font-size: 24rpx;
		line-height: 48rpx;
		text-align: right;
		padding-right: 16rpx;
	}
	&.before-start .status {
		color: #f9af25;
		background: url('/static/images/expertOrder/beforeStart.svg') no-repeat 100% / 100%;
	}
	&.waiting .status {
		color: #ff5f54;
		background: url('/static/images/expertOrder/waiting.svg') no-repeat 100% / 100%;
	}
	&.doing .status {
		color: #6fba1a;
		background: url('/static/images/expertOrder/doing.svg') no-repeat 100% / 100%;
	}
	&.done .status {
		color: #666;
		background: url('/static/images/expertOrder/done.svg') no-repeat 100% / 100%;
	}
	&.refunding .status {
		color: #f9af25;
		background: url('/static/images/expertOrder/refunding.svg') no-repeat 100% / 100%;
	}
	.order-theme {
		height: 40rpx;
		font-weight: 600;
		margin-bottom: 30rpx;
		color: #66d47e;
		font-size: 28rpx;
		line-height: 40rpx;
		padding-left: 36rpx;
		background: url('/static/images/expertOrder/assemble.png') no-repeat left center / 28rpx 26rpx;
	}
	.line {
		// height: 40rpx;
		line-height: 40rpx;
		margin-bottom: 16rpx;
		display: flex;
		.txt1 {
			font-size: 24rpx;
			line-height: 40rpx;
			color: #999;
			flex-shrink: 0;
		}
		.txt2 {
			font-size: 24rpx;
			padding-left: 32rpx;
			line-height: 40rpx;
			color: #000;
			flex-grow: 1;
			.hours, .minutes, .seconds {
			  color: #66d47e
			}
			.txt4 {
				padding: 0 12rpx;
			}
		}
		.txt3 {
			line-height: 40rpx;
			font-size: 28rpx;
			color: #66d47e
		}
		&:last-child {
			margin-bottom: 0;
		}
	}
	.partner {
		.userinfo {
			height: 80rpx;
			display: flex;
			.avatar {
				height: 80rpx;
				width: 80rpx;
				background-color: #ccc;
				border-radius: 40rpx;
				margin-left: 36rpx;
				margin-right: 16rpx;
			}
			.name-age {
				height: 80rpx;
				.name {
					height: 32rpx;
					line-height: 32rpx;
					color: #000;
					font-size: 26rpx;
					font-weight: 600;
					display: flex;
					.icon-v {
						height: 28rpx;
						width: 28rpx;
						border-radius: 14rpx;
						background-color: #ff4242;
						font-size: 20rpx;
						text-align: center;
						margin-left: 8rpx;
						color: #fff;
					}
				}
				.age-wrap {
					display: flex;
					height: 40rpx;
					margin-top: 8rpx;
					.age {
						height: 40rpx;
						width: 72rpx;
						margin-right: 8rpx;
						line-height: 40rpx;
						padding-left: 32rpx;
						box-sizing: border-box;
						font-size: 24rpx;
						border-radius: 4rpx;
						background-repeat: no-repeat;
						background-position: 8rpx center;
						background-size: 20rpx 20rpx;
						&.female {
							color: #ff5f54;
							background-color: #fff2f0;
							background-image: url('/static/images/profile/woman.png');
						}
						&.male {
							color: #1ba2fc;
							background-color: #e8faff;
							background-image: url('/static/images/profile/man.png');
						}
					}
					.xz {
						border-radius: 4rpx;
						height: 40rpx;
						width: 40rpx;
						border-radius: 4rpx;
						background-color: rgba(0, 0, 0, .05);
					}
				}
			}
		}
	}
}
</style>