<template>
	<view class="reservation-order-page">
		<view class="search-bar">
				<uni-icons type="search" size="20" color="#000" class="icon"></uni-icons>
				<input type="text" placeholder="输入服务名称查找" class="fg search-input" v-model="searchValue" @input="onSearchInput">
		</view>
		<Tabs @switchTab="switchTab"></Tabs>
		<component
		  v-for="(compName, idx) in compList"
			:is="compName"
			v-show="currTabIdx === idx"
			class="order-wrap"
		></component>
	</view>
</template>

<script>
	import { debounce } from '/common/utils.js'
	import Tabs from './components/tabs.vue'
	import All from './components/all.vue'
	import Waiting from './components/waiting.vue'
	import BeforeStart from './components/beforeStart.vue'
	import Doing from './components/doing.vue'
	import Done from './components/done.vue'
	import Refunding from './components/refunding.vue'
	export default {
		components: {
			Tabs,
			All,
			Waiting,
			BeforeStart,
			Doing,
			Done,
			Refunding,
		},
		data() {
			return {
				searchValue: '',
				currTabIdx: 0,
				compList: ['All', 'Waiting', 'BeforeStart', 'Doing', 'Done', 'Refunding']
			}
		},
		methods: {
			onSearchInput: debounce(() => {
				console.log('防抖')
			}),
			switchTab(currTabIdx) {
				this.currTabIdx = currTabIdx
			}
		}
	}
</script>

<style lang="scss" scoped>
.reservation-order-page {
	height: 100%;
	box-sizing: border-box;
	background-color: #f4f8fb;
	padding-top: 32rpx;
	display: flex;
	flex-direction: column;
	.fg {
		flex-grow: 1
	}
	.search-bar {
		margin: 0 32rpx 8rpx;
		background-color: #fff;
		height: 64rpx;
		border-radius: 32rpx;
		padding-right: 24rpx;
		display: flex;
		align-items: center;
		flex-shrink: 0;
		.icon {
			margin: 0 16rpx 0 24rpx;
		}
		.search-input {
			font-size: 28rpx;
		}
	}
	.order-wrap {
		// overflow-y: auto;
		overflow: hidden;
		margin: 14rpx 32rpx 0;
	}
}
</style>