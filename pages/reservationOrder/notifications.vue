<template>
  <view class="reservation-notifications">
    <!-- Fixed header -->
    <view class="page-header">
      <view class="header-back" @click="goBack">
        <uni-icons type="left" size="20" color="#000"></uni-icons>
      </view>
      <text class="header-title">预约订单</text>
    </view>
    
    <!-- Scrollable content -->
    <scroll-view scroll-y class="notifications-container">
      <view class="notifications-list">
        <NotificationCard
          v-for="(notification, index) in notifications"
          :key="index"
          :title="notification.title"
          :appointmentTime="notification.appointmentTime"
          :serviceContent="notification.serviceContent"
          :duration="notification.duration"
          :price="notification.price"
          :type="notification.type"
          @click="handleNotificationClick(notification)"
        />
      </view>
    </scroll-view>
  </view>
</template>

<script>
import NotificationCard from './components/NotificationCard.vue';

export default {
  components: {
    NotificationCard
  },
  data() {
    return {
      notifications: [
        {
          title: '你有一条新的服务订单通知',
          appointmentTime: '2025-07-12(周六) 13:00~16:00',
          serviceContent: '旅游向导',
          duration: '3小时',
          price: '600贝壳币',
          type: 'new',
          status: 0
        },
        {
          title: '下单人取消订单通知',
          appointmentTime: '2025-07-12(周六) 13:00~16:00',
          serviceContent: '旅游向导',
          duration: '3小时',
          price: '600贝壳币',
          type: 'cancelled',
          status: 4
        },
        {
          title: '续费提醒通知',
          appointmentTime: '2025-07-12(周六) 13:00~16:00',
          serviceContent: '旅游向导',
          duration: '3小时',
          price: '600贝壳币',
          type: 'renewal',
          status: 3
        },
        {
          title: '你有一条新的服务订单通知',
          appointmentTime: '2025-07-12(周六) 13:00~16:00',
          serviceContent: '旅游向导',
          duration: '3小时',
          price: '600贝壳币',
          type: 'new',
          status: 0
        }
      ]
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    handleNotificationClick(notification) {
      uni.navigateTo({
        url: `/pages/reservationOrderDetail/reservationOrderDetail?status=${notification.status}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.reservation-notifications {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f8fb;
  
  .page-header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    position: relative;
    background-color: #fff;
    padding-top: var(--status-bar-height, 44px);
    flex-shrink: 0;
    
    .header-back {
      position: absolute;
      left: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 44rpx;
      height: 44rpx;
    }
    
    .header-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #000;
    }
  }
  
  .notifications-container {
    flex: 1;
    overflow: hidden;
    
    .notifications-list {
      padding: 32rpx;
    }
  }
}
</style> 