<template>
	<view class="container">
		<view class="header">
			<text class="title">日期选择器演示</text>
		</view>
		
		<view class="demo-section">
			<view class="section-title">基础用法</view>
			<view class="demo-item">
				<button class="demo-btn" @click="showDatePicker">选择日期范围</button>
				<view class="result-text">
					选择的日期：{{ getDateFilterText() }}
				</view>
			</view>
		</view>
		
		<view class="demo-section">
			<view class="section-title">在订单列表中使用</view>
			<view class="demo-item">
				<button class="demo-btn" @click="goToOrderList">查看订单列表</button>
			</view>
		</view>
		
		<!-- 日期选择器组件 -->
		<DateRangePicker 
			:visible.sync="showDatePickerVisible"
			@confirm="onDateRangeConfirm"
		/>
	</view>
</template>

<script>
import DateRangePicker from '@/components/DateRangePicker/DateRangePicker.vue'

export default {
	components: {
		DateRangePicker
	},
	data() {
		return {
			showDatePickerVisible: false,
			dateRange: {
				startDate: '',
				endDate: ''
			}
		}
	},
	methods: {
		showDatePicker() {
			this.showDatePickerVisible = true
		},
		
		onDateRangeConfirm(dateRange) {
			console.log('选择的日期范围:', dateRange)
			this.dateRange = dateRange
			uni.showToast({
				title: '日期选择成功',
				icon: 'success'
			})
		},
		
		getDateFilterText() {
			if (this.dateRange.startDate && this.dateRange.endDate) {
				return `${this.dateRange.startDate} 至 ${this.dateRange.endDate}`
			}
			return '未选择日期'
		},
		
		goToOrderList() {
			uni.navigateTo({
				url: '/pages/ordersBymerchant/index'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	padding: 40rpx;
	background: #F4F8FB;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.demo-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
}

.demo-item {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.demo-btn {
	background: #66D47E;
	color: #fff;
	border: none;
	border-radius: 45rpx;
	height: 90rpx;
	line-height: 90rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.result-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
	padding: 20rpx;
	background: #f8f8f8;
	border-radius: 8rpx;
}
</style> 