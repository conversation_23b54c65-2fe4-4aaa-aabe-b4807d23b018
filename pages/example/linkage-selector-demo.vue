<template>
  <view class="demo-container">
    <page-head title="联动选择器演示"></page-head>
    
    <view class="demo-section">
      <view class="demo-title">基础使用 - 多选模式</view>
      <view class="demo-desc">左右联动，支持多选，显示选中数量徽章</view>
      
      <view class="demo-content">
        <LinkageSelector
          :categories="skillCategories"
          v-model="selectedSkills"
          :multiple="true"
          :show-badge="true"
          @change="onSkillChange"
          @category-change="onCategoryChange"
        />
      </view>
      
      <view class="result-display">
        <text class="result-label">已选择：</text>
        <text class="result-value">{{ selectedSkills.join(', ') || '无' }}</text>
      </view>
    </view>

    <view class="demo-section">
      <view class="demo-title">单选模式</view>
      <view class="demo-desc">只能选择一个选项</view>
      
      <view class="demo-content single-mode">
        <LinkageSelector
          :categories="cityCategories"
          v-model="selectedCity"
          :multiple="false"
          :show-badge="false"
          @change="onCityChange"
        />
      </view>
      
      <view class="result-display">
        <text class="result-label">已选择：</text>
        <text class="result-value">{{ selectedCity.join(', ') || '无' }}</text>
      </view>
    </view>

    <view class="demo-section">
      <view class="demo-title">自定义字段</view>
      <view class="demo-desc">使用自定义的value和label字段</view>
      
      <view class="demo-content custom-fields">
        <LinkageSelector
          :categories="productCategories"
          v-model="selectedProducts"
          :multiple="true"
          value-key="id"
          label-key="name"
          disabled-key="unavailable"
          @change="onProductChange"
        />
      </view>
      
      <view class="result-display">
        <text class="result-label">已选择ID：</text>
        <text class="result-value">{{ selectedProducts.join(', ') || '无' }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn" @click="clearAll">清空所有</button>
      <button class="action-btn primary" @click="selectAllCurrent">全选当前分类</button>
    </view>
  </view>
</template>

<script>
import LinkageSelector from '@/components/LinkageSelector/LinkageSelector.vue'
import PageHead from '@/components/page-head/page-head.vue'

export default {
  components: {
    LinkageSelector,
    PageHead
  },
  
  data() {
    return {
      selectedSkills: ['爬山', '羽毛球', '唱歌'],
      selectedCity: [],
      selectedProducts: [1, 3],
      
      // 技能分类数据
      skillCategories: [
        {
          name: '运动类',
          items: ['爬山', '羽毛球', '网球', '篮球', '足球', '游泳', '跑步', '健身']
        },
        {
          name: '娱乐类', 
          items: ['唱歌', '跳舞', '游戏', '看电影', '聊天', '购物', '旅行', '摄影']
        },
        {
          name: '功能类',
          items: ['陪同', '代办', '咨询', '翻译', '维修', '清洁', '教学', '设计']
        },
        {
          name: '艺术类',
          items: ['绘画', '书法', '音乐', '舞蹈', '表演', '雕塑', '手工', '收藏']
        }
      ],
      
      // 城市分类数据
      cityCategories: [
        {
          name: '热门城市',
          items: ['北京', '上海', '广州', '深圳', '杭州', '南京']
        },
        {
          name: '直辖市',
          items: ['北京', '上海', '天津', '重庆']
        },
        {
          name: '省会城市', 
          items: ['广州', '成都', '武汉', '西安', '郑州', '济南', '沈阳', '长春']
        }
      ],
      
      // 商品分类数据（自定义字段）
      productCategories: [
        {
          name: '电子产品',
          items: [
            { id: 1, name: 'iPhone 15', price: 5999, unavailable: false },
            { id: 2, name: 'MacBook Pro', price: 12999, unavailable: true },
            { id: 3, name: 'iPad Air', price: 4599, unavailable: false },
            { id: 4, name: 'Apple Watch', price: 2999, unavailable: false }
          ]
        },
        {
          name: '服装鞋帽',
          items: [
            { id: 5, name: 'Nike运动鞋', price: 899, unavailable: false },
            { id: 6, name: 'Adidas外套', price: 599, unavailable: false },
            { id: 7, name: 'Uniqlo T恤', price: 99, unavailable: true },
            { id: 8, name: 'Zara连衣裙', price: 299, unavailable: false }
          ]
        },
        {
          name: '家居用品',
          items: [
            { id: 9, name: '小米台灯', price: 199, unavailable: false },
            { id: 10, name: '无印良品收纳盒', price: 89, unavailable: false },
            { id: 11, name: 'IKEA书架', price: 299, unavailable: true },
            { id: 12, name: '网易严选床单', price: 159, unavailable: false }
          ]
        }
      ]
    }
  },

  methods: {
    onSkillChange(value, item) {
      console.log('技能选择变化:', value, item)
    },
    
    onCityChange(value, item) {
      console.log('城市选择变化:', value, item)
    },
    
    onProductChange(value, item) {
      console.log('商品选择变化:', value, item)
    },
    
    onCategoryChange(index, category) {
      console.log('分类切换:', index, category.name)
    },
    
    clearAll() {
      this.selectedSkills = []
      this.selectedCity = []
      this.selectedProducts = []
      uni.showToast({
        title: '已清空所有选择',
        icon: 'success'
      })
    },
    
    selectAllCurrent() {
      // 这里可以调用组件的selectAll方法
      uni.showToast({
        title: '全选当前分类功能需要通过ref调用',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

.demo-section {
  margin: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.demo-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  padding: 30rpx 30rpx 10rpx;
}

.demo-desc {
  font-size: 24rpx;
  color: #666;
  padding: 0 30rpx 20rpx;
}

.demo-content {
  height: 600rpx;
  border-top: 1px solid #f0f0f0;
  
  &.single-mode {
    height: 500rpx;
  }
  
  &.custom-fields {
    height: 700rpx;
  }
}

.result-display {
  padding: 20rpx 30rpx;
  background: #f8f9fa;
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  
  .result-label {
    font-size: 28rpx;
    color: #666;
    margin-right: 10rpx;
  }
  
  .result-value {
    font-size: 28rpx;
    color: #4CAF50;
    font-weight: 500;
    flex: 1;
  }
}

.action-buttons {
  display: flex;
  gap: 30rpx;
  padding: 30rpx;
  
  .action-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    border: 2rpx solid #ddd;
    background: #fff;
    color: #666;
    font-size: 28rpx;
    
    &.primary {
      background: linear-gradient(135deg, #4CAF50, #81c784);
      color: #fff;
      border-color: #4CAF50;
    }
  }
}
</style> 