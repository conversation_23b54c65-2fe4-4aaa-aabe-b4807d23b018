<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">图片轮播组件示例</text>
    </view>
    
    <!-- 基础用法 -->
    <view class="demo-section">
      <view class="section-title">基础用法</view>
      <ImageCarousel 
        :imageList="basicImages" 
        @change="onBasicChange"
        @imageClick="onBasicImageClick"
      />
    </view>
    
    <!-- 自定义配置 -->
    <view class="demo-section">
      <view class="section-title">自定义配置</view>
      <ImageCarousel 
        :imageList="customImages" 
        :autoplay="false"
        :interval="5000"
        :duration="800"
        :circular="true"
        :initialIndex="1"
        @change="onCustomChange"
        @imageClick="onCustomImageClick"
      />
    </view>
    
    <!-- 网络图片 -->
    <view class="demo-section">
      <view class="section-title">网络图片</view>
      <ImageCarousel 
        :imageList="networkImages" 
        @change="onNetworkChange"
        @imageClick="onNetworkImageClick"
      />
    </view>
    
    <!-- 控制按钮 -->
    <view class="control-section">
      <view class="control-title">控制选项</view>
      <view class="control-buttons">
        <button class="control-btn" @click="toggleAutoplay">
          {{ autoplayEnabled ? '关闭自动播放' : '开启自动播放' }}
        </button>
        <button class="control-btn" @click="switchToNext">下一张</button>
        <button class="control-btn" @click="switchToPrev">上一张</button>
      </view>
    </view>
  </view>
</template>

<script>
import ImageCarousel from '@/components/ImageCarousel.vue'

export default {
  name: 'ImageCarouselDemo',
  components: {
    ImageCarousel
  },
  data() {
    return {
      autoplayEnabled: true,
      basicImages: [
        '/static/index/head1.png',
        '/static/index/head2.png',
        '/static/index/head3.png'
      ],
      customImages: [
        '/static/index/head1.png',
        '/static/index/head2.png',
        '/static/index/head3.png',
        '/static/index/head1.png',
        '/static/index/head2.png'
      ],
      networkImages: [
        'https://picsum.photos/800/400?random=1',
        'https://picsum.photos/800/400?random=2',
        'https://picsum.photos/800/400?random=3',
        'https://picsum.photos/800/400?random=4'
      ]
    }
  },
  methods: {
    onBasicChange(e) {
      console.log('基础轮播切换:', e)
      uni.showToast({
        title: `切换到第${e.current + 1}张图片`,
        icon: 'none'
      })
    },
    
    onBasicImageClick(e) {
      console.log('基础图片点击:', e)
      uni.previewImage({
        current: e.index,
        urls: this.basicImages
      })
    },
    
    onCustomChange(e) {
      console.log('自定义轮播切换:', e)
    },
    
    onCustomImageClick(e) {
      console.log('自定义图片点击:', e)
      uni.previewImage({
        current: e.index,
        urls: this.customImages
      })
    },
    
    onNetworkChange(e) {
      console.log('网络图片轮播切换:', e)
    },
    
    onNetworkImageClick(e) {
      console.log('网络图片点击:', e)
      uni.previewImage({
        current: e.index,
        urls: this.networkImages
      })
    },
    
    toggleAutoplay() {
      this.autoplayEnabled = !this.autoplayEnabled
      uni.showToast({
        title: this.autoplayEnabled ? '已开启自动播放' : '已关闭自动播放',
        icon: 'none'
      })
    },
    
    switchToNext() {
      // 这里可以通过ref调用组件方法
      uni.showToast({
        title: '切换到下一张',
        icon: 'none'
      })
    },
    
    switchToPrev() {
      uni.showToast({
        title: '切换到上一张',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.demo-header {
  background: #fff;
  padding: 40rpx 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
  
  .demo-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.demo-section {
  margin: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  
  .section-title {
    padding: 30rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    border-bottom: 1rpx solid #f0f0f0;
  }
}

.control-section {
  margin: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  
  .control-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .control-buttons {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
  
  .control-btn {
    background: #007aff;
    color: #fff;
    border: none;
    border-radius: 12rpx;
    padding: 24rpx;
    font-size: 28rpx;
    
    &:active {
      background: #0056cc;
    }
  }
}
</style> 