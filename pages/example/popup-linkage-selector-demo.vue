<template>
  <view class="demo-container">
    <page-head title="弹窗联动选择器演示"></page-head>
    
    <view class="demo-section">
      <view class="demo-title">技能选择</view>
      <view class="demo-desc">点击按钮弹出技能选择器，支持多选</view>
      
      <view class="trigger-area" @click="showSkillSelector = true">
        <view class="trigger-content">
          <text class="trigger-label">选择技能</text>
          <text class="trigger-value">{{ selectedSkillsText || '请选择技能' }}</text>
          <text class="trigger-arrow">›</text>
        </view>
      </view>
      
      <view v-if="selectedSkills.length" class="selected-tags">
        <view class="tag-title">已选择技能：</view>
        <view class="tags-list">
          <view 
            v-for="skill in selectedSkills" 
            :key="skill"
            class="skill-tag"
            @click="removeSkill(skill)"
          >
            {{ skill }}
            <text class="remove-icon">×</text>
          </view>
        </view>
      </view>
    </view>

    <view class="demo-section">
      <view class="demo-title">城市选择（单选）</view>
      <view class="demo-desc">单选模式，只能选择一个城市</view>
      
      <view class="trigger-area" @click="showCitySelector = true">
        <view class="trigger-content">
          <text class="trigger-label">选择城市</text>
          <text class="trigger-value">{{ selectedCity || '请选择城市' }}</text>
          <text class="trigger-arrow">›</text>
        </view>
      </view>
    </view>

    <view class="demo-section">
      <view class="demo-title">商品选择（自定义字段）</view>
      <view class="demo-desc">使用自定义字段，展示商品信息</view>
      
      <view class="trigger-area" @click="showProductSelector = true">
        <view class="trigger-content">
          <text class="trigger-label">选择商品</text>
          <text class="trigger-value">{{ selectedProductsText || '请选择商品' }}</text>
          <text class="trigger-arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn" @click="clearAll">清空所有选择</button>
      <button class="action-btn primary" @click="showAllSelectors">显示所有选择器</button>
    </view>

    <!-- 技能选择器弹窗 -->
    <PopupLinkageSelector
      :visible.sync="showSkillSelector"
      title="选择技能"
      :categories="skillCategories"
      v-model="selectedSkills"
      :multiple="true"
      :show-badge="true"
      @confirm="onSkillConfirm"
      @cancel="onSkillCancel"
      @change="onSkillChange"
    />

    <!-- 城市选择器弹窗 -->
    <PopupLinkageSelector
      :visible.sync="showCitySelector"
      title="选择城市"
      :categories="cityCategories"
      v-model="selectedCityArray"
      :multiple="false"
      :show-badge="false"
      @confirm="onCityConfirm"
      @cancel="onCityCancel"
    />

    <!-- 商品选择器弹窗 -->
    <PopupLinkageSelector
      :visible.sync="showProductSelector"
      title="选择商品"
      :categories="productCategories"
      v-model="selectedProducts"
      :multiple="true"
      value-key="id"
      label-key="name"
      disabled-key="unavailable"
      @confirm="onProductConfirm"
      @cancel="onProductCancel"
    />
  </view>
</template>

<script>
import PopupLinkageSelector from '@/components/LinkageSelector/PopupLinkageSelector.vue'
import PageHead from '@/components/page-head/page-head.vue'

export default {
  components: {
    PopupLinkageSelector,
    PageHead
  },
  
  data() {
    return {
      // 控制弹窗显示
      showSkillSelector: false,
      showCitySelector: false,
      showProductSelector: false,
      
      // 选中的值
      selectedSkills: ['爬山', '羽毛球'],
      selectedCityArray: [],
      selectedProducts: [1, 3],
      
      // 技能分类数据
      skillCategories: [
        {
          name: '运动类',
          items: ['爬山', '羽毛球', '网球', '篮球', '足球', '游泳', '跑步', '健身']
        },
        {
          name: '娱乐类', 
          items: ['唱歌', '跳舞', '游戏', '看电影', '聊天', '购物', '旅行', '摄影']
        },
        {
          name: '功能类',
          items: ['陪同', '代办', '咨询', '翻译', '维修', '清洁', '教学', '设计']
        },
        {
          name: '艺术类',
          items: ['绘画', '书法', '音乐', '舞蹈', '表演', '雕塑', '手工', '收藏']
        }
      ],
      
      // 城市分类数据
      cityCategories: [
        {
          name: '热门城市',
          items: ['北京', '上海', '广州', '深圳', '杭州', '南京']
        },
        {
          name: '直辖市',
          items: ['北京', '上海', '天津', '重庆']
        },
        {
          name: '省会城市', 
          items: ['广州', '成都', '武汉', '西安', '郑州', '济南', '沈阳', '长春']
        }
      ],
      
      // 商品分类数据（自定义字段）
      productCategories: [
        {
          name: '电子产品',
          items: [
            { id: 1, name: 'iPhone 15', price: 5999, unavailable: false },
            { id: 2, name: 'MacBook Pro', price: 12999, unavailable: true },
            { id: 3, name: 'iPad Air', price: 4599, unavailable: false },
            { id: 4, name: 'Apple Watch', price: 2999, unavailable: false }
          ]
        },
        {
          name: '服装鞋帽',
          items: [
            { id: 5, name: 'Nike运动鞋', price: 899, unavailable: false },
            { id: 6, name: 'Adidas外套', price: 599, unavailable: false },
            { id: 7, name: 'Uniqlo T恤', price: 99, unavailable: true },
            { id: 8, name: 'Zara连衣裙', price: 299, unavailable: false }
          ]
        },
        {
          name: '家居用品',
          items: [
            { id: 9, name: '小米台灯', price: 199, unavailable: false },
            { id: 10, name: '无印良品收纳盒', price: 89, unavailable: false },
            { id: 11, name: 'IKEA书架', price: 299, unavailable: true },
            { id: 12, name: '网易严选床单', price: 159, unavailable: false }
          ]
        }
      ]
    }
  },

  computed: {
    selectedSkillsText() {
      return this.selectedSkills.join('、')
    },
    
    selectedCity() {
      return this.selectedCityArray.length > 0 ? this.selectedCityArray[0] : ''
    },
    
    selectedProductsText() {
      if (!this.selectedProducts.length) return ''
      
      const names = []
      for (const category of this.productCategories) {
        for (const item of category.items) {
          if (this.selectedProducts.includes(item.id)) {
            names.push(item.name)
          }
        }
      }
      return names.join('、')
    }
  },

  methods: {
    // 技能选择相关方法
    onSkillConfirm(values) {
      console.log('技能选择确认:', values)
      uni.showToast({
        title: `已选择${values.length}项技能`,
        icon: 'success'
      })
    },
    
    onSkillCancel() {
      console.log('技能选择取消')
    },
    
    onSkillChange(values, item) {
      console.log('技能选择变化:', values, item)
    },
    
    removeSkill(skill) {
      const index = this.selectedSkills.indexOf(skill)
      if (index > -1) {
        this.selectedSkills.splice(index, 1)
      }
    },
    
    // 城市选择相关方法
    onCityConfirm(values) {
      console.log('城市选择确认:', values)
      if (values.length > 0) {
        uni.showToast({
          title: `已选择${values[0]}`,
          icon: 'success'
        })
      }
    },
    
    onCityCancel() {
      console.log('城市选择取消')
    },
    
    // 商品选择相关方法
    onProductConfirm(values) {
      console.log('商品选择确认:', values)
      uni.showToast({
        title: `已选择${values.length}件商品`,
        icon: 'success'
      })
    },
    
    onProductCancel() {
      console.log('商品选择取消')
    },
    
    // 清空所有选择
    clearAll() {
      this.selectedSkills = []
      this.selectedCityArray = []
      this.selectedProducts = []
      uni.showToast({
        title: '已清空所有选择',
        icon: 'success'
      })
    },
    
    // 显示所有选择器（用于演示）
    showAllSelectors() {
      this.showSkillSelector = true
      
      setTimeout(() => {
        this.showCitySelector = true
      }, 300)
      
      setTimeout(() => {
        this.showProductSelector = true
      }, 600)
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

.demo-section {
  margin: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.demo-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  padding: 30rpx 30rpx 10rpx;
}

.demo-desc {
  font-size: 24rpx;
  color: #666;
  padding: 0 30rpx 20rpx;
}

.trigger-area {
  padding: 30rpx;
  border-top: 1px solid #f0f0f0;
  background: #fff;
  
  &:active {
    background: #f8f9fa;
  }
}

.trigger-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .trigger-label {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }
  
  .trigger-value {
    flex: 1;
    font-size: 28rpx;
    color: #666;
    text-align: right;
    margin: 0 20rpx;
    
    &:empty {
      color: #ccc;
    }
  }
  
  .trigger-arrow {
    font-size: 32rpx;
    color: #ccc;
    transform: rotate(90deg);
  }
}

.selected-tags {
  padding: 20rpx 30rpx 30rpx;
  background: #f8f9fa;
  border-top: 1px solid #f0f0f0;
  
  .tag-title {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 16rpx;
  }
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.skill-tag {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #e8f5e8;
  color: #4CAF50;
  font-size: 24rpx;
  border-radius: 20rpx;
  border: 1px solid #4CAF50;
  
  .remove-icon {
    margin-left: 8rpx;
    font-size: 28rpx;
    color: #999;
    
    &:active {
      color: #666;
    }
  }
}

.action-section {
  display: flex;
  gap: 30rpx;
  padding: 30rpx;
  
  .action-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    border: 2rpx solid #ddd;
    background: #fff;
    color: #666;
    font-size: 28rpx;
    
    &.primary {
      background: linear-gradient(135deg, #4CAF50, #81c784);
      color: #fff;
      border-color: #4CAF50;
    }
  }
}
</style> 