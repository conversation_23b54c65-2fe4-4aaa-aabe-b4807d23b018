<template>
	<view class="order-detail-wrap">
		<view class="status-wrap">
			<image class="status-icon" :src="`/static/images/expertOrder/${getStatusInfo('icon')}.svg`"></image>
			<view class="status-info">
				<view v-if="isToAgree" class="status-txt">
					<text>待接单，剩余</text>
					<text class="remain">
						<template v-if="durationHours">{{ durationHours }}小时</template>
						<template v-if="durationMinutes">{{ durationMinutes }}分钟</template>
						{{ durationSeconds }}秒
					</text>
					<text>后</text>
				</view>
				<view v-else>{{ getStatusInfo('statusName') }}</view>
				<text class="tip">{{ getStatusInfo('tip') }}</text>
			</view>
		</view>
		<view class="warning" v-if="isToAgree">
			若时间到期后未接单，订单将自动关闭
		</view>
		<view class="order-theme">
			<view :class="['icon', getStatusInfo('className')]">{{ getStatusInfo('statusName') }}</view>
			<image class="pic" src="/static/images/expertOrder/foodGuide.png"></image>
			<view class="skill-info">
				<text class="txt1">美食向导</text>
				<text class="txt2">300 贝壳币/小时</text>
				<text class="txt3">共 3 件</text>
			</view>
			<view class="cost"><text class="ita">900</text> 贝壳币</view>
		</view>
		<view class="order-info panel-style mb32" v-if="isRefunded">
			<view class="title">退款信息</view>
			<view class="line">
				<text class="txt1">退款进度</text>
				<text class="txt2" id="orderId">已退款</text>
			</view>
			<view class="line">
				<text class="txt1">退款原因</text>
				<text class="txt2">计划有变</text>
			</view>
			<view class="line">
				<text class="txt1">退款时间</text>
				<text class="txt2">2025-07-12 12:00:39</text>
			</view>
			<view class="line">
				<text class="txt1">退款金额</text>
				<text class="txt2">600贝壳币</text>
			</view>
		</view>
		<view class="order-info panel-style service-info" v-if="showServiceInfo">
			<view class="title">服务信息</view>
			<view v-for="(item, idx) in serviceList" class="item-wrap">
				<view v-for="(it, index) in item" :class="['txt-line', it.isFinished ? 'finish' : '']">
					<template v-if="index === 0">
						<image
							class="icon-status"
							v-if="it.isFinished && idx === 0"
							src="/static/images/expertOrder/ic_success.svg"
						></image>
						<view class="icon-status circle" v-else-if="idx === 0"></view>
						<image v-else class="icon-status" src="/static/images/expertOrder/ic_success-gray.svg"></image>
					</template>
				  <text :class="[{pl60: index !== 0}]">{{ it.text }}</text>
				</view>
			</view>
		</view>
		<!-- 预约信息 -->
		<Panel :order-info="orderInfo" :is-show-order-info="Boolean(1)" class="panel-style">
			<template v-slot:header>
				<view class="title">预约信息</view>
			</template>
			<template v-if="showSendMsgBtn" v-slot:btn>
				<button class="send-msg">发消息</button>
			</template>
		</Panel>
		<view class="order-info panel-style">
			<view class="title">订单信息</view>
			<view class="line">
				<text class="txt1">订单编号</text>
				<text class="txt2" id="orderId">E00909090909090909XXXXX</text>
				<button class="btn" @click="copyOrderId">复制</button>
			</view>
			<view class="line">
				<text class="txt1">下单时间</text>
				<text class="txt2">2025-07-12 12:00:09</text>
			</view>
			<view class="line">
				<text class="txt1">支付时间</text>
				<text class="txt2">2025-07-12 12:00:39</text>
			</view>
		</view>
		<view class="footer" v-if="!isRefunded">
			<image class="icon-kf" src="/static/images/shoping/kf.png"></image>
			<view class="btns" v-if="showBtnGroup1">
				<button class="btn modify" @click="modDateTime">修改时间</button>
				<button class="btn cancel" @click="cancelOrder">取消订单</button>
			</view>
			<view class="btns" v-if="showBtnGroup2">
				<button class="btn cancel" @click="cancelOrder">取消订单</button>
				<button class="btn appointment" @click="gotoAppointmentDetail">约会详情</button>
			</view>
			<view class="btns" v-if="isComplete">
				<button class="btn cancel">申请退款</button>
			</view>
		</view>
		
		<uni-popup type="bottom" ref="dateTime">
			<Dt @close="closeBottomPop" @sendDateTime="sendDateTime"></Dt>
		</uni-popup>
	</view>
</template>

<script>
	import Panel from '../reservationOrder/components/panel.vue';
	import Dt from '../expertOrder/components/DateTime.vue';
	import { skillOrderApi } from '@/common/api';
	import { getTimeDiff } from '@/common/utils.js';
	import { SKILL_ORDER_STATUS, SKILL_ORDER_STATUS_DESC } from '@/common/constants';
	export default {
		components: {
			Panel,
			Dt,
		},
		data() {
			return {
				// orderInfo: {
				// 	status: 0,
				// 	sex: 'male',
				// 	age: 33
				// },
				orderInfo: {},
				timeout: null,
				durationHours: 0,
				durationMinutes: 0,
				durationSeconds: 0,
				serviceList: [
					[{ text: '服务开始' }, { text: '2025-07-12 14:00:00' }],
					[{ text: '同意接单' }, { text: '2025-07-12 13:30:00' }],
					[{ text: '下单成功' }, { text: '2025-07-12 13:00:00' }]
				]
			}
		},
		computed: {
			// 待接单
			isToAgree() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_AGREE
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_ALLOCATE
			},
			// 待开始
			isToBegin() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_BEGIN
			},
			// 已完成
			isComplete() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.COMPLETE
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_EVALUATE
			},
			// 进行中
			isInProgress() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.IN_PROGRESS
			},
			// 已取消
			isRefunded() {
				return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.USER_CANCEL
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.EXPERT_REFUSE
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TIMEOUT
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.REFUNDING
				  || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.REFUNDED
					
			},
			getStatusInfo() {
				return type => {
					let classObj = {}
					if (this.orderInfo['orderStatus']) {
						switch(this.orderInfo['orderStatus']) {
							case SKILL_ORDER_STATUS.TO_AGREE:
							case SKILL_ORDER_STATUS.TO_ALLOCATE: 
								classObj = {
									icon: 'ic_waiting',
									className: 'waiting',
									statusName: '待接单',
									tip: '正在等待接单，请保持关注'
								};
								break;
							case SKILL_ORDER_STATUS.TO_BEGIN:
								classObj = {
									icon: 'ic_beforeStart',
									className: 'before-start',
									statusName: '待开始',
									tip: '即将开始，请耐心等待~'
								};
								break;
							case SKILL_ORDER_STATUS.IN_PROGRESS:
								classObj = {
									icon: 'ic_doing',
									className: 'doing',
									statusName: '进行中',
									tip: '正在进行中，请保持联系'
								};
								break;
							case SKILL_ORDER_STATUS.COMPLETE:
							case SKILL_ORDER_STATUS.TO_EVALUATE:
								classObj = {
									icon: 'ic_done',
									className: 'done',
									statusName: '已完成',
									tip: '活动已完成，再来一单吧~'
								};
								break;
							case SKILL_ORDER_STATUS.REFUNDED:
							case SKILL_ORDER_STATUS.USER_CANCEL:
							case SKILL_ORDER_STATUS.EXPERT_REFUSE:
							case SKILL_ORDER_STATUS.TIMEOUT:
							case SKILL_ORDER_STATUS.REFUNDING:
								classObj = {
									icon: 'ic_refunding',
									className: 'refunding',
									statusName: '已取消',
									tip: '活动未能成行'
								};
								break;
							default: break;
						}
					}
					return classObj[type]
				}
			},
			getStatus() {
				let text = '';
				switch (this.orderInfo.orderStatus) {
					case SKILL_ORDER_STATUS.TO_AGREE: text = 'ic_waiting';break;
					case SKILL_ORDER_STATUS.TO_BEGIN: text = 'ic_beforeStart';break;
					case SKILL_ORDER_STATUS.IN_PROGRESS: text = 'ic_doing';break;
					case SKILL_ORDER_STATUS.COMPLETE: text = 'ic_done';break;
					case SKILL_ORDER_STATUS.REFUNDING: text = 'ic_refunding';break;
					default: break;
				}
				return text;
			},
			getIconBg() {
				let className = '';
				switch (this.orderInfo.orderStatus) {
					case SKILL_ORDER_STATUS.TO_AGREE: className = 'waiting';break;
					case SKILL_ORDER_STATUS.TO_BEGIN: className = 'before-start';break;
					case SKILL_ORDER_STATUS.IN_PROGRESS: className = 'doing';break;
					case SKILL_ORDER_STATUS.COMPLETE: className = 'done';break;
					case SKILL_ORDER_STATUS.REFUNDING: className = 'refunding';break;
					default: break;
				}
				return className;
			},
			getStatusText() {
				let text = '';
				switch (this.orderInfo.orderStatus) {
					case SKILL_ORDER_STATUS.TO_AGREE: text = '待接单';break;
					case SKILL_ORDER_STATUS.TO_BEGIN: text = '待开始';break;
					case SKILL_ORDER_STATUS.IN_PROGRESS: text = '进行中';break;
					case SKILL_ORDER_STATUS.COMPLETE: text = '已完成';break;
					case SKILL_ORDER_STATUS.REFUNDING: text = '已退款';break;
					default: break;
				}
				return text;
			},
			getTips() {
				let text = '';
				switch (this.orderInfo.orderStatus) {
					case SKILL_ORDER_STATUS.TO_AGREE: text = '正在等待接单，请保持关注';break;
					case SKILL_ORDER_STATUS.TO_BEGIN: text = '即将开始，请耐心等待~';break;
					case SKILL_ORDER_STATUS.IN_PROGRESS: text = '正在进行中，请保持联系';break;
					case SKILL_ORDER_STATUS.COMPLETE: text = '活动已完成，再来一单吧~';break;
					case SKILL_ORDER_STATUS.REFUNDING: text = '退款处理已完成，请留意账户变动';break;
					default: break;
				}
				return text;
			},
			showServiceInfo() {
				// return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.IN_PROGRESS
				//   || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.COMPLETE
				// 	|| this.orderInfo.orderStatus === SKILL_ORDER_STATUS.REFUNDING
				return this.isInProgress || this.isComplete || this.isRefunded
			},
			showSendMsgBtn() {
				// return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_BEGIN
				//   || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.IN_PROGRESS
				//   || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.COMPLETE
				//   || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.REFUNDING
				return this.isToBegin || this.isInProgress || this.isComplete || this.isRefunded
			},
			showBtnGroup1() {
				// return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_AGREE
				//   || this.orderInfo.orderStatus === SKILL_ORDER_STATUS.TO_BEGIN
				return this.isToAgree || this.isToBegin
			},
			showBtnGroup2() {
				// return this.orderInfo.orderStatus === SKILL_ORDER_STATUS.IN_PROGRESS
				return this.isInProgress
			}
		},
		onLoad(options) {
			// console.log(options);
			if (options.orderNo !== undefined) {
			  // this.orderInfo.orderStatus = Number(options.orderStatus);
				this.getSkillOrderDetail(options.orderNo)
			}
			// 已完成订单
			if (Number(options.status) === 3) {
				this.serviceList.unshift(...[[{
					text: '服务完成',
					isFinished: true
				}, {
					text: '2025-07-12 15:50:20'
				}], [{
					text: '下单人：确认安全',
				}, {
					text: '接单人：确认安全',
				}, {
					text: '2025-07-12 14:30:20'
				}], [{
					text: '下单人：确认安全',
				}, {
					text: '接单人：未确认',
				}, {
					text: '2025-07-12 14:20:50'
				}]])
			}
			if (Number(options.status) === 4) {
				this.serviceList[0][0].text = '订单取消'
			}
		},
		beforeDestroy() {
			this.timeout && clearInterval(this.timeout)
		},
		methods: {
			getSkillOrderDetail(orderNo) {
				skillOrderApi.getSkillOrderDetail(orderNo).then(res => {
					// console.log('技能订单详情', res)
					if (res && res.data) {
						this.orderInfo = res.data
						if (this.orderInfo.orderStatusDesc === SKILL_ORDER_STATUS_DESC.TO_AGREE) {
							console.log('进来了')
							this.setTimer();
						}
					}
				})
			},
			getTimeDiff() {
				const { hours, minutes, seconds } = getTimeDiff(Date.parse(new Date()), Date.parse(new Date(this.orderInfo.serviceStartTime)))
			  // console.log(hours, minutes, seconds)
			  this.durationHours = hours
			  this.durationMinutes = minutes
			  this.durationSeconds = seconds
			},
			setTimer() {
				if (this.orderInfo.serviceStartTime) {
					this.getTimeDiff()
					this.timeout = setInterval(() => {
					  this.getTimeDiff()
						// console.log(this.orderInfo.orderNo, timeDiff, hours, minutes, seconds)
					}, 1000)
				}
			},
			async copyOrderId() {
				const orderId = document.querySelector('#orderId').textContent
				try {
					await navigator.clipboard.writeText(orderId)
				} catch(err) {
					// console.log('复制失败', err)
					const ele = document.createElement('input');
					ele.setAttribute('readonly', 'readonly');
					ele.value = orderId;
					document.body.appendChild(ele);
					ele.select();
					document.execCommand('copy');
					ele.remove()
				}
			},
			modDateTime() {
				this.$refs.dateTime.open()
			},
			closeBottomPop() {
				this.$refs.dateTime.close()
			},
			sendDateTime(payload) {
				console.log(payload)
				this.closeBottomPop()
			},
			cancelOrder() {
				uni.navigateTo({
					url: '/pages/cancelOrder/cancelOrder'
				})
			},
			gotoAppointmentDetail() {
				uni.navigateTo({
					url: '/pages/appointmentDetail/appointmentDetail'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-detail-wrap {
		height: 100vh;
		overflow-y: auto;
		background-color: #f4f8fb;
		padding: 32rpx 32rpx 168rpx;
		.status-wrap {
			height: 96rpx;
			padding: 0 8rpx;
			display: flex;
			align-items: center;
			.status-icon {
				height: 72rpx;
				flex-shrink: 0;
				width:56rpx;
				margin-right: 24rpx;
			}
			.status-info {
				flex-grow: 1;
				.status-txt {
					height: 56rpx;
					line-height: 56rpx;
					font-size: 36rpx;
					color: #000;
					.remain {
						color: #ff5f54;
					}
				}
				.tip {
					color: #666;
					font-size: 24rpx;
					height: 40rpx;
					line-height: 40rpx;
				}
			}
		}
	  .warning {
			border-radius: 16rpx;
			margin-top: 24rpx;
			height: 88rpx;
			line-height: 88rpx;
			background-color: #fff;
			box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
			color: #000;
			font-size: 24rpx;
			padding-left: 72rpx;
			background: #fff url('/static/images/expertOrder/voice.png') no-repeat 24rpx center / 30rpx 28rpx;
		}
		.order-theme {
			position: relative;
			height: 152rpx;
			box-sizing: border-box;
			border-radius: 16rpx;
			background-color: #fff;
			box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
			padding: 24rpx;
			margin-top: 32rpx;
			display: flex;
			align-items: flex-end;
			.icon {
				position: absolute;
				top: -8rpx;
				right: 0;
				width:124rpx;
				line-height: 48rpx;
				height: 48rpx;
				text-indent: 36rpx;
				font-size: 24rpx;
				background-size: 100% 100%;
				&.before-start {
					background-image: url('/static/images/expertOrder/beforeStart.svg');
					color: #f9af25;
				}
				&.waiting {
					background-image: url('/static/images/expertOrder/waiting.svg');
					color: #ff5f54;
				}
				&.doing {
					background-image: url('/static/images/expertOrder/doing.svg');
					color: #6fba1a;
				}
				&.done {
					background-image: url('/static/images/expertOrder/done.svg');
					color: #666;
				}
				&.refunding {
					background-image: url('/static/images/expertOrder/refunding.svg');
					color: #f9af25;
				}
			}
			.pic {
				height: 96rpx;
				width: 96rpx;
				margin-right: 16rpx;
				flex-shrink: 0;
			}
			.skill-info {
				flex-grow: 1;
				display: flex;
				flex-direction: column;
				justify-content: flex-end;
				.txt1 {
					height: 40rpx;
					line-height: 40rpx;
					color: #000;
					font-size: 28rpx;
					font-weight: 600;
				}
				.txt2, .txt3 {
					height: 24rpx;
					line-height: 28rpx;
					font-size: 24rpx;
					color: #999;
				}
				.txt2 {
					margin: 4rpx 0;
				}
			}
			.cost {
				flex-shrink: 0;
				color: #000;
				font-size: 24rpx;
				height: 40rpx;
				line-height: 40rpx;
				.ita {
					font-style: italic;
				}
			}
		}
		.panel-style {
			margin-top: 24rpx;
			.title {
				height: 44rpx;
				color: #000;
				font-size: 28rpx;
				font-weight: 600;
				line-height: 44rpx;
				padding-bottom: 24rpx;
				border-bottom: 2rpx solid rgba(209,209,209, .5);
				margin-bottom: 32rpx;
			}
			::v-deep {
				.order-theme {
					display: none;
				}
				.status {
					display: none;
				}
				.during, .startTime {
					display: none;
				}
			}
			.send-msg {
				position: absolute;
				width: 136rpx;
				height: 56rpx;
				line-height: 54rpx;
				border: 2rpx solid #d1d1d1;
				border-radius: 30rpx;
				background-color: #fff;
				bottom: 40rpx;
				right: 32rpx;
				color: #000;
				font-size: 24rpx;
			}
		}
		.order-info {
			padding: 24rpx 32rpx 32rpx;
			background-color: #fff;
			border-radius: 16rpx;
			box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0, .08);
			margin-bottom: 300rpx;
			&.mb32 {
				margin-bottom: 32rpx;
			}
			.line {
				line-height: 40rpx;
				margin-bottom: 16rpx;
				display: flex;
				.txt1 {
					font-size: 24rpx;
					line-height: 40rpx;
					color: #999;
					flex-shrink: 0;
				}
				.txt2 {
					font-size: 24rpx;
					padding-left: 32rpx;
					line-height: 40rpx;
					color: #000;
				}
				.btn {
					border: 2rpx solid #d1d1d1;
					border-radius: 20rpx;
					height: 40rpx;
					line-height: 36rpx;
					// width: 80rpx;
					color: #000;
					font-size: 24rpx;
					padding: 0 16rpx;
					margin-left: 16rpx;
				}
				&:last-child {
					margin-bottom: 0;
				}
			}
		}
		.service-info {
			margin-bottom: 32rpx;
			.item-wrap {
				position: relative;
				padding-bottom: 40rpx;
				// border-left: 6rpx solid #d1d1d1;
				&::before {
					content: '';
					position: absolute;
					left: 10rpx;
					top: 26rpx;
					width: 6rpx;
					bottom: -6rpx;
					background-color: #d1d1d1;
				}
				&:nth-child(2) {
					
				}
				&:nth-child(n+3)::before {
					top: 26rpx;
				}
				&:last-child {
					padding-bottom: 0;
				}
				&:last-child::before {
					display: none;
				}
				.txt-line {
					height: 40rpx;
					line-height: 40rpx;
					color: #000;
					font-size: 24rpx;
					font-weight: 500;
					text-indent: 32rpx;
					display: flex;
					align-items: center;
					&:last-child {
						color: #999;
					}
					.icon-status {
						width: 28rpx;
						height: 28rpx;
						box-sizing: border-box;
						&.circle {
							z-index: 1;
							background-color: #fff;
							border-radius: 28rpx;
							border: 6rpx solid #66d47e;
						}
					}
					.pl60 {
						text-indent: 0;
						padding-left: 60rpx;
					}
				}
			}
		}
		.footer {
			position: fixed;
			background-color: #fff;
			bottom: 0;
			left: 0;
			right: 0;
			display: flex;
			height: 152rpx;
			padding: 16rpx 32rpx 0;
			justify-content: space-between;
			.icon-kf {
				width: 48rpx;
				height: 48rpx;
				margin-top: 14rpx;
			}
			.btns {
				height: 76rpx;
				display: flex;
				justify-content: flex-end;
				.btn {
					width: 192rpx;
					height: 72rpx;
					line-height: 72rpx;
					border: 2rpx solid #d1d1d1;
					color: #333;
					font-size: 32rpx;
					border-radius: 38rpx;
					background-color: #fff;
					margin-left: 16rpx;
					&::after {
						border: none !important;
					}
				}
				.appointment {
					color: #fff;
					background-color: #66d47e;
					border-color: #66d47e;
				}
			}
		}
	}
</style>