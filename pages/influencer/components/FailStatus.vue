<template>
  <div class="fail-status">
    <div class="fail-content">
      <!-- 失败图标 -->
      <div class="fail-icon">
        <span class="icon-x">X</span>
      </div>
      
      <!-- 失败标题 -->
      <div class="fail-title">审核失败</div>
      
      <!-- 失败原因 -->
      <div class="fail-reason">{{ failureReason || 'XXXXXX审核失败原因' }}</div>
      
      <!-- 再次申请按钮 -->
      <button class="reapply-btn" @click="handleReapply">
        再次申请
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Props
const props = defineProps({
  failureReason: {
    type: String,
    default: 'XXXXXX审核失败原因'
  }
})

// Emits
const emit = defineEmits(['reapply'])

// Methods
const handleReapply = () => {
    uni.redirectTo({
        url: '/pages/influencer/apply'
    })
//   emit('reapply')
}
</script>

<style scoped>
.fail-status {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.fail-status::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.fail-content {
  background: white;
  border-radius: 24rpx;
  padding: 80rpx 60rpx;
  text-align: center;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  max-width: 800rpx;
  width: 100%;
  position: relative;
}

.fail-icon {
  width: 160rpx;
  height: 160rpx;
  background-color: #999999;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 48rpx;
}

.icon-x {
  color: white;
  font-size: 72rpx;
  font-weight: bold;
}

.fail-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 32rpx;
}

.fail-reason {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 64rpx;
  line-height: 1.5;
}

.reapply-btn {
  width: 100%;
  height: 96rpx;
  background-color: #666666;
  color: white;
  border: none;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 48rpx;
}

.reapply-btn:hover {
  background-color: #555555;
  transform: translateY(-2rpx);
}

.reapply-btn:active {
  transform: translateY(0);
}

.decoration-line {
  width: 4rpx;
  height: 240rpx;
  background: linear-gradient(to bottom, #4A90E2, transparent);
  margin: 0 auto;
  position: relative;
}

.decoration-line::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 12rpx;
  height: 12rpx;
  background-color: #4A90E2;
  border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .fail-content {
    margin: 40rpx;
    padding: 60rpx 40rpx;
  }
  
  .fail-icon {
    width: 120rpx;
    height: 120rpx;
  }
  
  .icon-x {
    font-size: 56rpx;
  }
  
  .fail-title {
    font-size: 36rpx;
  }
}
</style>
