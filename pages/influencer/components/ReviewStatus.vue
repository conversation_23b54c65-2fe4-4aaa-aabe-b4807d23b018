<template>
  <div class="review-container">
    <div class="card status-card">
      <div class="status-icon">
        <uni-icons type="help" size="200" ></uni-icons>
      </div>
      <div class="status-title">申核中</div>
      <div class="status-desc">您的达人申请正在审核中，请耐心等待</div>
    </div>
    
    <div class="info-card">
      <div class="info-item">
        <div class="label">申请时间</div>
        <div class="value">{{ submitTime }}</div>
      </div>
      <div class="info-item">
        <div class="label">预计审核时间</div>
        <div class="value">1-3个工作日</div>
      </div>
    </div>
    

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const submitTime = ref('')

onMounted(() => {
  // 模拟获取申请时间
  const now = new Date()
  submitTime.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
})
</script>

<style scoped>
.review-container {
  max-width: 800rpx;
  margin: 0 auto;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card {
  background: #fafafa;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  padding: 48rpx;
  margin-bottom: 32rpx;
  width: 100%;
}

.status-card {
  text-align: center;
  padding: 64rpx 48rpx;
}

.status-icon {
  margin-bottom: 32rpx;
}

.icon {
  font-size: 96rpx;
  opacity: 0.8;
}

.status-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.status-desc {
  font-size: 32rpx;
  color: #666;
  line-height: 1.5;
}

.info-card {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: #888;
  font-size: 30rpx;
}

.value {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.tips-card {
  background: #f8f9fa;
  border-left: 4rpx solid #007aff;
}

.tips-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.tips-content {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
}

.tips-content p {
  margin: 12rpx 0;
}
</style>