<template>
  <div class="apply-skills-container">
    <NavigationCustorm>
      <template #title>
        搭子中心
      </template>
    </NavigationCustorm>
    <view class="content">
      <div v-for="skill in skills" :key="skill.skillCode" class="skill-item">
        <div class="skill-header">
          <view class="flex-row">
            <view class="skill-name">{{ skill.skillName }}</view>
            <image src="@/static/images/profile/icon-wenhao.png" class="info-icon" @click="handleInfo(skill)"></image>
          </view>
          <view>
            <image src="@/static/images/profile/icon-delete.png" class="info-icon" @click="handleInfo(skill)"></image>删除
          </view>
        </div>
        <view class="line"></view>
        <div class="skill-inputs">
          <div class="input-row">
            <span class="label">价格</span>
            <input type="number" v-model="skill.pricePerHour" placeholder="输入您的价格" class="price-input">
            <span class="unit">贝壳币</span>
          </div>
          <div class="input-row">
            <span class="label">起订</span>
            <input type="number" v-model="skill.minOrderHours" placeholder="输入服务时间" class="time-input">
            <span class="unit">小时</span>
          </div>
        </div>
      </div>
      <view class="skill-item add-skill-btn" @click="handleAddSkill">
        <image src="@/static/images/profile/icon-add.png" class="add-icon"></image>选择技能
      </view>
    </view>
    <!-- 说明 -->
    <uni-popup ref="popupInfo" type="bottom" borderRadius="30rpx 30rpx 0 0" class="type-popup" background-color="#fff">
      <view class="popup-content">
        <view class="popup-title">修改价格 <uni-icons type="closeempty" size="20" color="#333" @click="handleClose"
            class="close-icon"></uni-icons></view>
        <view class="popup-desc">
          <view>指导价：4000贝壳币/小时</view>
          <view>价格范围：3000~4500贝壳币/小时</view>
        </view>
      </view>
    </uni-popup>
    <!-- 技能 -->
    <uni-popup ref="popupSkill" type="bottom" borderRadius="30rpx 30rpx 0 0" class="type-popup" background-color="#fff">
      <view class="popup-content">
        <view class="popup-title">选择技能</view>
        <view class="picker-row">
          <picker-view indicator-style="height: 50px;" :value="skillIndex" @change="bindChange" class="picker-view">
            <picker-view-column>
              <view class="item" :class="{ 'active': skillIndex[0] === index }" v-for="(item, index) in skillList"
                :key="index">{{ item.name }}</view>
            </picker-view-column>
            <picker-view-column>
              <view class="item" :class="{ 'active': skillIndex[1] === index }"
                v-for="(item, index) in skillList[skillIndex[0] || 0]?.children || []" :key="index">{{ item.skillName }}
              </view>
            </picker-view-column>
          </picker-view>
        </view>
        <view class="popup-btn-row">
          <button class="btn cancel-btn" @click="handleCancel">取消</button>
          <button class="btn confirm-btn" @click="handleConfirm">确定</button>
        </view>
      </view>
    </uni-popup>
    <view class="apply-section">
      <button class="apply-btn" @click="submitApplication">提交审核</button>
    </view>
  </div>
</template>

<script setup>
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
import { ref } from 'vue'
import { skillApi } from '@/common/api'
const userInfo = ref(uni.getStorageSync('userInfo'))


const skills = ref([])
const popupInfo = ref(null)
const popupSkill = ref(null)
const showText = ref('')


const skillList = ref([])
const skillIndex = ref([0, 0])

const bindChange = (e) => {
  skillIndex.value = e.detail.value
  console.log(skillIndex.value)
}
const handleInfo = (skill) => {
  showText.value = skill.skillDesc
  popupInfo.value.open()
}
const handleClose = () => {
  popupInfo.value.close()
}
const submitApplication = async () => {
  skills.value.forEach(item => {
    if (item.pricePerHour == '' || item.minOrderHours == '') {
      uni.showToast({
        title: '请输入价格和起订时间',
        icon: 'none'
      })
      return
    }
  })
  const list = skills.value.map(item => {
    return {
      "dataId": item.dataId,
      "skillTemplateId": Number(item.skillCode),
      "customDesc": item.skillDesc,
      "pricePerHour": Number(item.pricePerHour),
      "minOrderHours": Number(item.minOrderHours)
    }
  })
  const res = await skillApi.addSkillApply({ skillList: list })
  if (res.code == 200) {
    uni.showToast({
      title: '提交成功',
      icon: 'success'
    })
    skills.value = []
  }
}

const handleCancel = () => {
  popupSkill.value.close()
}

/**
 * serviceTime: null
 * skillCode: "12"
 * skillDesc: null
 * skillImg: "resource/default-header.png"
 * skillName: "美食"
 * skillType: "娱乐类"
*/
const handleConfirm = () => {
  popupSkill.value.close()
  const skill = skillList.value[skillIndex.value[0]].children[skillIndex.value[1]]
  skills.value.push({ ...skill, pricePerHour: '', minOrderHours: '' })
  debugger
}
const getSkillList = async () => {
  skillList.value = []
  try {
    // 得到已申请的技能
    const res1 = await skillApi.getSkillsList({ darenUserId: userInfo.value.userId })

    const res = await skillApi.queryAllTemplates()
    Object.keys(res.data).forEach(key => {
      skillList.value.push({
        name: key,
        children: res.data[key]?.filter(item => !res1.rows?.some(item1 => item1.skillTemplateId == item.skillCode)) || []
      })
    })
  } catch (error) {

  }

}
getSkillList()
const handleAddSkill = async () => {
  popupSkill.value.open()

}
</script>

<style lang="scss" scoped>
.apply-skills-container {
  height: 100vh;
  overflow-y: auto;
  padding: 30rpx;
  max-width: 400px;
  margin: 0 auto;
  background-color: #f4f8fb;
}

.content {
  padding-top: 198rpx;
  margin-bottom: 240rpx;
}

.skill-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  background-color: #fff;
  color: #999999;
  font-size: 22rpx;

  .flex-row {
    display: flex;
    align-items: center;
  }

  .skill-name {
    color: #000000;
    font-size: 26rpx;
  }
}

.skill-item {
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16px;
  background-color: #fff;
  box-shadow: 0px 4rpx 4rpx 2rpx rgba(0, 0, 0, 0.02);
  border-radius: 20rpx;
}

.add-skill-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  color: #999;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 140rpx;
  border-radius: 20rpx;
  margin-top: 20rpx;
  box-sizing: border-box;

  .add-icon {
    width: 22rpx;
    height: 22rpx;
    flex-shrink: 0;
    margin-right: 8rpx;
  }
}

.info-icon {
  width: 22rpx;
  height: 22rpx;
  flex-shrink: 0;
  margin: 0 8rpx;
}

.skill-inputs {
  margin-top: 12px;
}

.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.label {
  width: 50px;
  font-size: 14px;
  color: #333;
}

.price-input,
.time-input {
  flex: 1;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.price-input:focus,
.time-input:focus {
  outline: none;
  border-color: #007bff;
}

.unit {
  font-size: 14px;
  color: #666;
}

.submit-btn {
  width: 100%;
  padding: 12px;
  background-color: #000;
  color: white;
  border: none;
  border-radius: 24px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 20px;
}

.submit-btn:hover {
  background-color: #333;
}



// 弹窗
.popup-title {
  height: 112rpx;
  line-height: 112rpx;
  font-size: 30rpx;
  color: #000000;
  text-align: center;
}

.popup-desc {
  min-height: 200rpx;
  font-size: 26rpx;
  color: #000;
  margin-top: 20rpx;
  padding: 30rpx;
  margin-bottom: 54rpx;
}

.close-icon {
  position: absolute;
  right: 30rpx;
  top: 0rpx;
}

.uni-picker-view-indicator:after {
  border-bottom: 1px solid #fff;
}

.uni-picker-view-indicator:before {
  border-top: 1px solid #fff;
}

:deep(.uni-picker-view-content) {
  display: flex;
  flex-direction: column;
  align-items: center;

  .item {
    display: flex;

    &.active {
      border-bottom: 4rpx solid #06e938;
    }
  }
}

:deep(.uni-input-placeholder) {

  font-size: 26rpx;
  color: #999999;


}

input {
  text-align: right;
}

:deep(.input-text) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.apply-section {
  height: 160rpx;
  box-sizing: border-box;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16rpx 30rpx;
  background-color: #fff;
  z-index: 9;

  .apply-btn {
    width: 100%;
    background: #66D47E;
    color: #fff;
    border: none;
    border-radius: 60rpx;
    padding: 16rpx 0;
    font-size: 30rpx;
    font-weight: bold;

    &:active {
      opacity: 0.8;
    }
  }
}

.popup-btn-row {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  margin-bottom: 54rpx;

  .btn {

    width: 328rpx;
    height: 92rpx;
    line-height: 92rpx;
    border-radius: 46rpx 46rpx 46rpx 46rpx;

    &::after {
      border: none !important;
    }

    &::before {
      border: none !important;
    }

    &.cancel-btn {
      color: #666666;
      background: #F5F5F5;
    }

    &.confirm-btn {
      color: #fff;
      background: #66D47E;
    }
  }
}

.picker-view {
  height: 400rpx;
  text-align: center;
  margin-right: 16rpx;
  background: transparent;
  display: block;

  .item {
    height: 50px;
    line-height: 50px;
    text-align: center;
    color: #666;
    font-size: 30rpx;

    &.active {
      color: #000;
      font-size: 34rpx;
    }
  }
}

.line {
  border-bottom: 1px solid #f4f8fb;
}
</style>