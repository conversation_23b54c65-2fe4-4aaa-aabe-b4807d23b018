<template>
  <view class="success-container">
    <NavigationCustorm>
      <template #title>
        技能认证
      </template>
    </NavigationCustorm>
    <div class="content">
      <!-- 顶部标题 -->
      <view class="header">
        <view class="row header-top">
          <text class="text label">交通费</text>
          <text class="text" @click="handleClick">{{ textSelect }}
            <uni-icons type="arrowright" size="12" color="#999"></uni-icons>
          </text>
        </view>
        <view v-if="textSelect === '固定交通补贴'" class="row header-bottom">
          <text class="text label">固定金额</text>
          <text class="text input-text">
            <input type="number" style="display: inline-block;margin-right: 10rpx;" v-model="fixedAmount"
              placeholder="请输入" @blur="updateTravelCost('2')"/>
            元</text>
        </view>
        <view v-if="textSelect === '顾客承担'" class="row header-bottom">
          <text class="text label">出发地址</text>
          <view class="text address" @click="handleLocation">
            <view class="address-text">{{ addressInfo.address }}</view><uni-icons type="location" size="15" color="#66D47E"
              class="location-icon"></uni-icons>
          </view>
        </view>
      </view>
      <view class="header-tip">
        提示：请正确维护交通费，过高的交通费会影响成交！
      </view>

      <!-- 标签页 -->
      <!-- <view class="tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab-item', currentTab === index ? 'active' : '']"
        @click="switchTab(index)"
      >
        {{ tab }}
      </view>
    </view> -->
      <view class="content-container">
        <!-- 内容列表 -->
        <view class="content-list">
          <view v-for="(item, index) in contentList" :key="index" class="content-item">
            <view class="item-left">
              <view class="icon-wrapper">
                <image class="icon" :src="getImgUrl(item.skillImg)" mode="widthFix"></image>
              </view>
              <view class="item-info">
                <text class="item-title">{{ item.skillName }}</text>
                <text class="item-subtitle">{{ item.pricePerHour }}贝壳币/小时</text>
                <text class="item-subtitle">{{ item.customDesc }}</text>
              </view>
            </view>
            <view class="item-right">
              <button class="order-btn" @click="editOrder(item)">编辑</button>
            </view>
          </view>
        </view>

        <!-- 底部记录 -->
        <view v-if="contentList.length>0" class="bottom-section">
          <text class="record-text" @click="handleApplyRecord">申请记录</text>
        </view>


      </view>

    </div>
    <!-- 申请技能按钮 -->
    <view class="apply-section">
      <button class="apply-btn" @click="applySkills">申请技能</button>
    </view>
    <!-- 交通方式 -->
    <uni-popup ref="popup" type="bottom" borderRadius="30rpx 30rpx 0 0" class="type-popup" background-color="#fff">
      <view class="popup-content">
        <view class="popup-title">交通费</view>
        <view class=" picker-row">
          <picker-view indicator-style="height: 50px;" :value="transportIndex" @change="bindChange" class="picker-view">
            <picker-view-column>
              <view class="item" :class="{ 'active': transportIndex[0] === index }"
                v-for="(item, index) in transportList" :key="index">{{ item }}</view>
            </picker-view-column>
          </picker-view>
        </view>
        <view class="popup-btn-row">
          <button class="btn cancel-btn" @click="handleCancel">取消</button>
          <button class="btn confirm-btn" @click="handleConfirm">确定</button>
        </view>
      </view>
    </uni-popup>
    <!-- 修改价格 -->
    <uni-popup ref="popupPrice" type="bottom" borderRadius="30rpx 30rpx 0 0" class="type-popup" background-color="#fff">
      <view class="popup-content">
        <view class="popup-title">修改价格 <uni-icons type="closeempty" size="20" color="#333" @click="handleCancelPrice"
            class="close-icon"></uni-icons></view>
        <view class=" picker-row">
          <view class="label">当前价</view>
          <view class="row-item">
            <view>技能</view>
            <view>{{ skillInfo.skillName }}</view>
          </view>
          <view class="row-item">
            <view>价格</view>
            <view>{{ skillInfo.pricePerHour }}贝壳币/小时</view>
          </view>
        </view>
        <view class="line"></view>
        <view class=" picker-row">
          <view class="label">申请价</view>
          <view class="row-item">
            <view>价格</view>
            <view class="input-text">
              <input type="number" style="display: inline-block;margin-right: 10rpx;"
                v-model="skillInfo.pricePerHourNew" placeholder="输入新的价格" />
              贝壳币
            </view>
          </view>
          <view class="row-item">
            <view>起订</view>
            <view class="input-text"> <input type="number" style="display: inline-block;margin-right: 10rpx;"
                v-model="skillInfo.minOrderHoursNew" placeholder="输入起订时长" />小时</view>
          </view>
        </view>

      </view>
      <view class="popup-btn-row price-btn-row">
        <button class="btn full-btn confirm-btn" @click="handleConfirmPrice">提交审核</button>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
import { HTTP_IMG } from '@/common/constant'
import { skillApi } from '@/common/api'

const tabs = ref(['技能', '简历'])
const currentTab = ref(0)
const popup = ref(null)
const popupPrice = ref(null)

const textSelect = ref('免费出现')
const transportIndex = ref([0])
const transportList = ['免费出现', '固定交通补贴', '顾客承担']
const indicatorStyle = `height: 100rpx;`

const userInfo = ref(uni.getStorageSync('userInfo'))
const fixedAmount = ref(null)
const bindChange = (e) => {
  transportIndex.value = e.detail.value
}
const contentList = ref([])
const getImgUrl = (url) => {
  return url.startsWith('http') ? url : `${HTTP_IMG}${url}`
}
const switchTab = (index) => {
  currentTab.value = index
}
const skillInfo = ref({})
const editOrder = (item) => {
  skillInfo.value = { ...item, pricePerHourNew: '', minOrderHoursNew: '' }
  popupPrice.value.open('bottom')
}
const handleClick = () => {
  popup.value.open('bottom')
}
const applySkills = () => {
  uni.navigateTo({
    url: '/pages/influencer/applySkills'
  })
}

const handleApplyRecord = () => {
  uni.navigateTo({
    url: '/pages/influencer/reviewRecord'
  })
}
const handleChangeFixedAmount = () => {
  console.log(fixedAmount.value)
}

const handleConfirmPrice = async () => {
  const params = {
    "dataId": skillInfo.value.dataId,
    "skillTemplateId": skillInfo.value.skillTemplateId,
    "pricePerHour": Number(skillInfo.value.pricePerHourNew),
    "minOrderHours": Number(skillInfo.value.minOrderHoursNew)
}
  const res = await skillApi.updateSkill(params)
  if(res.code===200){
    popupPrice.value.close()
    getSkillsList()
  }
  console.log(res)
}
const handleCancelPrice = () => {
  popupPrice.value.close()
}
const handleCancel = () => {
  popup.value.close()
}
const handleConfirm = () => {
  console.log(transportIndex.value)
  textSelect.value = transportList[transportIndex.value[0]]
  if(textSelect.value==='免费出现'){
    updateTravelCost('1')
  }
  popup.value.close()
}
const handleLocation = () => {
  uni.chooseLocation({
    success: res => {
      addressInfo.value = {
        latitude: res.latitude,
        longtitude: res.longitude,
        address: res.address
      }
      updateTravelCost('3')
    }
  })
}
const getSkillsList = async () => {
  contentList.value = []
  const res = await skillApi.getSkillsList({ darenUserId: userInfo.value.userId })
  console.log(res)
  contentList.value = res.rows || []
}
getSkillsList()
const getTravelCost = async () => {
  const res = await skillApi.getTravelCost()
  console.log(res)
  if(res.data){
    textSelect.value = res.data.type==='2'?'固定交通补贴':res.data.type==='3'?'顾客承担':'免费出现'
    if(res.data.type==='2'){
      fixedAmount.value = res.data.amount
    }else if(res.data.type==='3'){
      addressInfo.value = res.data
    }
  }
}
getTravelCost()

const addressInfo = ref({
  latitude: null,
  longtitude:null,
  address:null
})
const updateTravelCost = async (type) => {
  let params={}
  if(type==='1'){
    params = {
      "type": "1",//1免费出现，2固定交通费，3顾客承担
    }
  } else if(type==='2'){
    if(!fixedAmount.value){
      return
    }
    params = {
      "type": "2",//1免费出现，2固定交通费，3顾客承担
      "amount": Number(fixedAmount.value),//固定金额
    }
  }else if(type==='3'){
    params = {
      "type": "3",//1免费出现，2固定交通费，3顾客承担
      "latitude": addressInfo.value.latitude,
      "longtitude":addressInfo.value.longtitude,
      "address":addressInfo.value.address
    }
  }

  const res = await skillApi.updateTravelCost(params)
  console.log(res)
}
</script>

<style lang="scss" scoped>
.success-container {
  height: 100vh;
  background: #f4f8fb;
  display: flex;
  flex-direction: column;
}

.header {
  padding: 60rpx 0 40rpx;
  text-align: center;
  background: #fff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin: 16rpx 30rpx;

  .header-top {
    display: flex;
  }

  :deep(.input-text) {
    display: flex;
    align-items: center;

    span {
      display: inline-flex;
      align-items: center;
      justify-content: center;

    }

  }

  .label {
    flex-shrink: 0;
  }

  .address-text {
    padding-top: 36rpx;
  }

  .address {
    display: flex;
    align-items: center;
    min-width: 100rpx;
    justify-content: flex-end;

    text {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .location-icon {
      width: 30rpx;
      height: 30rpx;
      flex-shrink: 0;
    }
  }

  .header-bottom {
    border-top: 1px solid #eaeaea;
  }

  .row {
    min-height: 114rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 26rpx;
    color: #000000;

  }

  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
  }
}

.content {
  display: flex;
  flex-direction: column;
  padding-top: 192rpx;
  box-sizing: border-box;
  height: 100vh;
}

.tabs {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  padding: 8rpx;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 12rpx 0;
    font-size: 32rpx;
    color: #666;
    border-radius: 12rpx;
    transition: all 0.3s ease;

    &.active {
      background: #333;
      color: #fff;
      font-weight: bold;
    }
  }
}

.content-list {
  overflow-y: auto;

  .content-item {
    background: #F6F7FB;
    border-radius: 20rpx;
    padding: 10rpx 30rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item-left {
      display: flex;
      align-items: center;
      flex: 1;

      .icon-wrapper {
        width: 96rpx;
        height: 80rpx;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;

        .icon {
          font-size: 40rpx;
        }
      }

      .item-info {
        display: flex;
        flex-direction: column;

        .item-title {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }

        .item-subtitle {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 8rpx;
        }

        .item-reward {
          font-size: 26rpx;
          color: #007AFF;
          font-weight: 500;
        }
      }
    }

    .item-right {
      .order-btn {
        width: 84rpx;
        height: 46rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #F6F7FB;
        box-shadow: inset 0rpx -2rpx 4rpx 2rpx rgba(255, 255, 255, 0.5);
        border-radius: 28rpx 28rpx 28rpx 28rpx;
        border: 2rpx solid #CCCCCC;
        font-size: 24rpx;
        padding: 0 !important;
        color: #666;

        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}

.bottom-section {
  text-align: center;
  padding: 20rpx 0 20rpx;

  .record-text {
    font-size: 32rpx;
    color: #666;
  }
}

.apply-section {
  height: 160rpx;
  box-sizing: border-box;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16rpx 30rpx;
  background-color: #fff;
  z-index: 99;

  .apply-btn {
    width: 100%;
    background: #66D47E;
    color: #fff;
    border: none;
    border-radius: 60rpx;
    padding: 16rpx 0;
    font-size: 30rpx;
    font-weight: bold;

    &:active {
      opacity: 0.8;
    }
  }
}

.content-container {
  background: linear-gradient(180deg, #F0FEF1 0%, #FFFFFF 80rpx, #FFFFFF 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 16rpx 0;
  padding-top: 80rpx;
  border-top-left-radius: 22rpx;
  border-top-right-radius: 22rpx;
  border: 1rpx solid #fff;
  padding-bottom: 160rpx;
  flex: 1;
  overflow-y: auto;

}

.header-tip {
  font-family: 苹方-简, 苹方-简;
  font-weight: normal;
  font-size: 22rpx;
  color: #999999;
  margin: 22rpx 30rpx 38rpx 30rpx;
}

.popup-content {
  padding: 30rpx;
  position: relative;

  .popup-title {
    font-size: 30rpx;
    color: #000000;
    text-align: center;
    margin-bottom: 100rpx;
  }

  .label {
    font-size: 24rpx;
    height: 100rpx;
    line-height: 100rpx;
    color: #666;
    margin-bottom: 10rpx;
  }

  .row-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #000;
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }

  .close-icon {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
  }
}

.picker-view {
  min-height: 200rpx;
  height: 288rpx;
  text-align: center;
  margin-right: 16rpx;
  background: transparent;
  display: block;

  .item {
    height: 50px;
    line-height: 50px;
    text-align: center;
    color: #666;
    font-size: 30rpx;

    &.active {
      color: #000;
      font-size: 34rpx;
    }
  }
}

// 弹窗
.uni-picker-view-indicator:after {
  border-bottom: 1px solid #fff;
}

.uni-picker-view-indicator:before {
  border-top: 1px solid #fff;
}

:deep(.uni-picker-view-content) {
  display: flex;
  flex-direction: column;
  align-items: center;

  .item {
    display: flex;

    &.active {
      border-bottom: 4rpx solid #06e938;
    }
  }
}

.popup-btn-row {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;

  &.price-btn-row {
    box-sizing: border-box;
    height: 160rpx;
    line-height: 160rpx;
    border-top: 1rpx solid #EAEAEA;
    padding: 20rpx 30rpx 0 30rpx;
  }

  .btn {

    width: 328rpx;
    height: 92rpx;
    line-height: 92rpx;
    border-radius: 46rpx 46rpx 46rpx 46rpx;

    &::after {
      border: none !important;
    }

    &::before {
      border: none !important;
    }

    &.cancel-btn {
      color: #666666;
      background: #F5F5F5;
    }

    &.confirm-btn {
      color: #fff;
      background: #66D47E;
    }
  }

  .full-btn {
    width: 100%;
  }

}

.line {
  border-top: 2rpx solid #EAEAEA;
}

:deep(.uni-input-placeholder) {

  font-size: 26rpx;
  color: #999999;


}

input {
  text-align: right;
}

:deep(.input-text) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>