<template>
  <view class="review-record">
    <NavigationCustorm>
      <template #title>
        审核记录
      </template>
    </NavigationCustorm>
    <view class="content">


      <view v-for="(item, index) in recordList" :key="index">
        <view class="record-item">
          <view class="record-row">
            <text class="label">申请时间</text>
            <text class="value">{{ item.skillInfo?.createDate }}</text>
          </view>
          <view class="record-row">
            <text class="label">技能</text>
            <text class="value">{{ item.skillInfo?.skillName }}</text>
          </view>
          <view class="record-row">
            <text class="label">价格</text>
            <text class="value black">{{ item.skillInfo?.pricePerHour }}&nbsp;&nbsp;贝壳币/小时</text>
          </view>
          <view class="record-row">
            <text class="label">起订</text>
            <text class="value black">{{ item.skillInfo?.minOrderHours }}小时</text>
          </view>
          <view class="record-row">
            <text class="label">审核状态</text>
            <view class="value-box">
              <text class="value" :class="getStatusClass(item.skillInfo?.auditStatus)">{{ getText(
                item.skillInfo?.auditStatus) }}</text>
              <view v-if="item.skillInfo?.auditStatus === 0" class="continue-btn" @click="clickEdit(item)">编辑</view>
            </view>
          </view>
        </view>
        <view class="line"></view>
      </view>
    </view>
    <!-- 修改价格 -->
    <uni-popup ref="popupPrice" type="bottom" borderRadius="30rpx 30rpx 0 0" class="type-popup" background-color="#fff">
      <view class="popup-content">
        <view class="popup-title">修改价格 <uni-icons type="closeempty" size="20" color="#333" @click="handleCancelPrice"
            class="close-icon"></uni-icons></view>
        <view class=" picker-row">
          <view class="label">当前价</view>
          <view class="row-item">
            <view>技能</view>
            <view>{{ skillInfo.skillName }}</view>
          </view>
          <view class="row-item">
            <view>价格</view>
            <view>{{ skillInfo.pricePerHour }}贝壳币/小时</view>
          </view>
        </view>
        <view class="line"></view>
        <view class=" picker-row">
          <view class="label">申请价</view>
          <view class="row-item">
            <view>价格</view>
            <view class="input-text">
              <input type="number" style="display: inline-block;margin-right: 10rpx;"
                v-model="skillInfo.pricePerHourNew" placeholder="输入新的价格" />
              贝壳币
            </view>
          </view>
          <view class="row-item">
            <view>起订</view>
            <view class="input-text"> <input type="number" style="display: inline-block;margin-right: 10rpx;"
                v-model="skillInfo.minOrderHoursNew" placeholder="输入起订时长" />小时</view>
          </view>
        </view>

      </view>
      <view class="popup-btn-row price-btn-row">
        <button class="btn full-btn confirm-btn" @click="handleConfirmPrice">提交审核</button>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
import { skillApi } from '@/common/api'
import { ref } from 'vue'
const popupPrice = ref(null)
const newPrice = ref('')
const newTime = ref('')
const recordList = ref([])
const skillInfo = ref({})
const getStatusClass = (status) => {
  switch (status) {
    case 1:
      return 'status-success'
    case 0:
      return 'status-pending'
    case 2:
      return 'status-failed'
    default:
      return 'status-pending'
  }
}
const getText = (status) => {
  switch (status) {
    case 0:
      return '审核中'
    case 1:
      return '成功'
    case 2:
      return '失败'
    default:
      return '-'
  }
}
const clickEdit = (item) => {
  skillInfo.value = item.skillInfo
  popupPrice.value.open()
}
const handleConfirmPrice = async () => {
  const params = {
    "dataId": skillInfo.value.dataId,
    "skillTemplateId": skillInfo.value.skillTemplateId,
    "pricePerHour": Number(skillInfo.value.pricePerHourNew),
    "minOrderHours": Number(skillInfo.value.minOrderHoursNew)
  }
  const res = await skillApi.updateSkill(params)
  if (res.code === 200) {
    popupPrice.value.close()
    getReviewRecord()
  }
}
const handleCancelPrice = () => {
  popupPrice.value.close()
}
const submitEdit = () => {
  // 提交编辑逻辑
  console.log('新价格:', this.newPrice)
  console.log('新时间:', this.newTime)
  popupPrice.value.close()
}
const change = (e) => {
  console.log('popup状态变化:', e)
}
const getReviewRecord = async () => {
  recordList.value = []
  const res = await skillApi.getReviewRecord()
  recordList.value = res.data || []

}
getReviewRecord()
</script>

<style lang="scss" scoped>
.review-record {
  background-color: #f4f8fb;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.content {
  padding-top: 212rpx;
  margin-bottom: 40rpx;
  min-height: 100vh;
}

.record-item {
  background-color: #ffffff;
  padding: 32rpx;
}

.record-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
}

.record-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: #333;
}

.value {
  font-size: 28rpx;
  color: #333333;
  text-align: right;
}

.status-success {
  color: #52c41a;
}

.status-pending {
  color: #FF7930;
}

.status-failed {
  color: #ff4d4f;
}

.continue-btn {
  background-color: #f4f4f4;
  color: #666;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.edit-popup {
  padding: 40rpx;
  border-radius: 20rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  font-size: 28rpx;
  color: #999999;
}

.section-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.current-info {
  margin-bottom: 40rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666666;
}

.info-value {
  font-size: 26rpx;
  color: #333333;
}

.apply-info {
  margin-bottom: 40rpx;
}

.input-row {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid #eeeeee;
  padding-bottom: 15rpx;
}

.input-label {
  font-size: 26rpx;
  color: #333333;
  width: 100rpx;
  flex-shrink: 0;
}

.price-input,
.time-input {
  flex: 1;
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #333333;
}

.unit {
  font-size: 26rpx;
  color: #666666;
  width: 60rpx;
  text-align: right;
}

.submit-btn {
  background-color: #000000;
  color: #ffffff;
  text-align: center;
  padding: 20rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
}

.line {
  border-bottom: 1rpx solid #f4f8fb;
  margin: 0 32rpx;
}

.value-box {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 200rpx;
}

.black {
  color: #000;
}

.popup-content {
  padding: 30rpx;
  position: relative;

  .popup-title {
    font-size: 30rpx;
    color: #000000;
    text-align: center;
    margin-bottom: 100rpx;
  }

  .label {
    font-size: 24rpx;
    height: 100rpx;
    line-height: 100rpx;
    color: #666;
    margin-bottom: 10rpx;
  }

  .row-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #000;
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }

  .close-icon {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
  }
}

.uni-picker-view-indicator:after {
  border-bottom: 1px solid #fff;
}

.uni-picker-view-indicator:before {
  border-top: 1px solid #fff;
}

:deep(.uni-picker-view-content) {
  display: flex;
  flex-direction: column;
  align-items: center;

  .item {
    display: flex;

    &.active {
      border-bottom: 4rpx solid #06e938;
    }
  }
}

.popup-btn-row {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;

  &.price-btn-row {
    box-sizing: border-box;
    height: 160rpx;
    line-height: 160rpx;
    border-top: 1rpx solid #EAEAEA;
    padding: 20rpx 30rpx 0 30rpx;
  }

  .btn {

    width: 328rpx;
    height: 92rpx;
    line-height: 92rpx;
    border-radius: 46rpx 46rpx 46rpx 46rpx;

    &::after {
      border: none !important;
    }

    &::before {
      border: none !important;
    }

    &.cancel-btn {
      color: #666666;
      background: #F5F5F5;
    }

    &.confirm-btn {
      color: #fff;
      background: #66D47E;
    }
  }

  .full-btn {
    width: 100%;
  }

}

:deep(.uni-input-placeholder) {

  font-size: 26rpx;
  color: #999999;


}

input {
  text-align: right;
}

:deep(.input-text) {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
