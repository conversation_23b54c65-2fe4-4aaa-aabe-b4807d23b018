<template>
    <view class="container-influencer">
        <NavigationCustorm >
            <template #title>
                搭子中心
            </template>
        </NavigationCustorm>
        <view v-if="appplyStatus === 1">
            <ReviewStatus />
        </view>
        <view v-if="appplyStatus ===3">
            <FailStatus></FailStatus>
        </view>
    </view>
</template>
<script setup>
import { ref } from 'vue'
import ReviewStatus from './components/ReviewStatus.vue'
import FailStatus from './components/FailStatus.vue'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
const isInfluencer = ref(true )
// 申请状态 0: 未申请  1: 审核中 2: 审核通过 3: 审核不通过
const appplyStatus = ref(2)
const appplyStatusText = ref('')


//如果当前不是达人，则替换当前页面为申请达人页面
if(!isInfluencer.value){
    uni.redirectTo({
        url: '/pages/influencer/apply'
    })
}
// 如果审核成功，则替换当前页面为申请达人页面
if(appplyStatus.value === 2){
    uni.redirectTo({
        url: '/pages/influencer/success'
    })
}


</script>
<style lang="scss" scoped>

</style>