<template>
	<view class="container">
		<NavigationCustorm>
			<template #title>
				接入第三方SDK目录
			</template>
		</NavigationCustorm>
		<view class="content">
			<view class="section">
				<view class="section-title">【引言】</view>
				<view class="paragraph">
					小岛屿平台由小岛屿（南京）科技有限公司（统一社会信用代码：91320114MAEKP8B17A，下称"我们"）提供服务。为了向您提供更好的产品和服务体验，我们的应用集成了以下第三方SDK。我们深知个人信息对您的重要性，承诺将按照法律法规的规定，保护您的个人信息及隐私安全。
				</view>
				<view class="paragraph">
					本目录将帮助您了解以下内容：
				</view>
				<view class="list">
					<view class="list-item">· 一、登录认证类SDK</view>
					<view class="list-item">· 二、地图定位类SDK</view>
					<view class="list-item">· 三、即时通讯类SDK</view>
					<view class="list-item">· 四、支付类SDK</view>
					<view class="list-item">· 五、推送通知类SDK</view>
					<view class="list-item">· 六、功能增强类SDK</view>
					<view class="list-item">· 七、UI组件类SDK</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">一、登录认证类SDK</view>
				
				<view class="subsection">
					<view class="subsection-title">（一）微信登录SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>微信开放平台SDK
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供微信账号登录功能，支持用户使用微信账号快速登录小岛屿平台
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>用户微信账号基本信息（用户ID、昵称、头像）、身份验证信息
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>用户注册/登录时选择微信登录方式
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://open.weixin.qq.com/cgi-bin/showdocument?action=dir_list&t=resource/res_list&verify=1&id=open1419317851&lang=zh_CN
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（二）一键登录SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>Univerify一键登录SDK
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供运营商一键登录功能，支持移动、联通、电信三大运营商用户快速登录
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>手机号码、设备信息、网络状态信息
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>用户注册/登录时选择一键登录方式
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://www.dcloud.io/privacy.html
					</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">二、地图定位类SDK</view>
				
				<view class="subsection">
					<view class="subsection-title">（一）高德地图SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>高德地图SDK（AMap）
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供地图显示、位置定位、地理编码等功能，支持附近的人、位置分享等社交功能
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>设备位置信息、设备标识符、网络状态信息
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>附近的人功能、位置分享、地图显示
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://lbs.amap.com/pages/privacy/
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（二）腾讯地图SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>腾讯地图SDK（QQMap）
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>H5平台提供地图服务，支持地图显示和位置服务
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>设备位置信息、设备标识符
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>H5平台地图功能
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://lbs.qq.com/webApi/webservice/webservice.html
					</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">三、即时通讯类SDK</view>
				
				<view class="subsection">
					<view class="subsection-title">（一）悟空IM SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>悟空IM JavaScript SDK
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供即时通讯功能，支持私信聊天、群聊、消息推送等社交功能
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>用户ID、聊天记录、设备信息、网络状态信息
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>用户私信聊天、群聊功能
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://github.com/WuKongIM/WuKongIM
					</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">四、支付类SDK</view>
				
				<view class="subsection">
					<view class="subsection-title">（一）微信支付SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>微信支付SDK
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供微信支付功能，支持用户使用微信支付完成商品购买
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>支付金额、订单信息、设备标识符
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>商城商品购买支付
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://pay.weixin.qq.com/index.php/public/customer_service
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（二）支付宝SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>支付宝SDK
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供支付宝支付功能，支持用户使用支付宝完成商品购买
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>支付金额、订单信息、设备标识符
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>商城商品购买支付
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://opendocs.alipay.com/common/02kkv7
					</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">五、推送通知类SDK</view>
				
				<view class="subsection">
					<view class="subsection-title">（一）UniPush推送SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>UniPush推送服务
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供消息推送服务，支持系统通知、聊天消息、点赞评论等消息推送
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>设备标识符、推送令牌、应用使用情况
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>消息通知推送、系统公告推送
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://www.dcloud.io/privacy.html
					</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">六、功能增强类SDK</view>
				
				<view class="subsection">
					<view class="subsection-title">（一）二维码生成SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>uQRCode二维码生成插件
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供二维码生成功能，支持生成用户名片二维码、分享二维码等
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>无
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>用户名片二维码生成、内容分享二维码
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://github.com/Sansnn/uQRCode
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（二）语音识别SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>语音识别模块
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供语音识别功能，支持语音转文字、语音搜索等
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>语音数据、设备音频权限
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>语音搜索、语音输入
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://www.dcloud.io/privacy.html
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（三）人脸识别SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>人脸识别验证模块
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供人脸识别验证功能，支持身份验证、安全登录等
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>人脸图像数据、设备相机权限
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>身份验证、安全登录
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://www.dcloud.io/privacy.html
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（四）条形码扫描SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>条形码扫描模块
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供条形码、二维码扫描功能，支持扫描用户名片、商品码等
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>扫描的图像数据、设备相机权限
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>扫描用户名片、商品码扫描
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://www.dcloud.io/privacy.html
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（五）视频播放SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>视频播放器模块
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供视频播放功能，支持动态视频播放、视频聊天等
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>视频播放数据、设备信息
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>动态视频播放、视频聊天
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://www.dcloud.io/privacy.html
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（六）应用安装SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>uni-installApk模块
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供APK安装功能，支持应用更新安装
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>设备安装权限
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>应用更新安装
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://www.dcloud.io/privacy.html
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（七）进度通知SDK</view>
					<view class="paragraph">
						<view class="bold">SDK名称：</view>uts-progressNotification模块
					</view>
					<view class="paragraph">
						<view class="bold">SDK用途：</view>提供进度通知功能，支持下载进度、上传进度等通知
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>无
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>文件上传下载进度通知
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://www.dcloud.io/privacy.html
					</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">七、UI组件类SDK</view>
				
				<view class="paragraph">
					为了提供更好的用户体验，我们使用了uni-ui组件库，包含以下主要组件：
				</view>
				<view class="list">
					<view class="list-item">· uni-badge（徽章组件）</view>
					<view class="list-item">· uni-calendar（日历组件）</view>
					<view class="list-item">· uni-card（卡片组件）</view>
					<view class="list-item">· uni-datetime-picker（日期时间选择器）</view>
					<view class="list-item">· uni-file-picker（文件选择器）</view>
					<view class="list-item">· uni-forms（表单组件）</view>
					<view class="list-item">· uni-icons（图标组件）</view>
					<view class="list-item">· uni-list（列表组件）</view>
					<view class="list-item">· uni-popup（弹出层组件）</view>
					<view class="list-item">· uni-search-bar（搜索栏组件）</view>
					<view class="list-item">· uni-swipe-action（滑动操作组件）</view>
					<view class="list-item">· uni-table（表格组件）</view>
					<view class="list-item">· uni-tag（标签组件）</view>
					<view class="list-item">· uni-transition（过渡动画组件）</view>
				</view>
				<view class="paragraph">
					<view class="bold">SDK用途：</view>提供统一的UI组件，提升应用界面的一致性和用户体验
				</view>
				<view class="paragraph">
					<view class="bold">收集信息：</view>无
				</view>
				<view class="paragraph">
					<view class="bold">隐私政策链接：</view>https://www.dcloud.io/privacy.html
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">八、其他依赖库</view>
				
				<view class="subsection">
					<view class="subsection-title">（一）日期处理库</view>
					<view class="paragraph">
						<view class="bold">库名称：</view>dayjs
					</view>
					<view class="paragraph">
						<view class="bold">库用途：</view>提供轻量级的日期处理功能，支持日期格式化、计算等
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>无
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>消息时间显示、动态发布时间显示
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://day.js.org/
					</view>
				</view>
				
				<view class="subsection">
					<view class="subsection-title">（二）状态管理库</view>
					<view class="paragraph">
						<view class="bold">库名称：</view>Pinia
					</view>
					<view class="paragraph">
						<view class="bold">库用途：</view>提供Vue 3的状态管理功能，管理应用全局状态
					</view>
					<view class="paragraph">
						<view class="bold">收集信息：</view>无
					</view>
					<view class="paragraph">
						<view class="bold">使用场景：</view>用户信息管理、应用状态管理
					</view>
					<view class="paragraph">
						<view class="bold">隐私政策链接：</view>https://pinia.vuejs.org/
					</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">九、隐私保护说明</view>
				<view class="paragraph">
					我们深知个人信息对您的重要性，承诺将按照法律法规的规定，保护您的个人信息及隐私安全。对于上述第三方SDK，我们采取了以下保护措施：
				</view>
				<view class="list">
					<view class="list-item">· 严格筛选第三方SDK，确保其具备完善的数据保护能力</view>
					<view class="list-item">· 与第三方SDK签署数据保护协议，要求其按照我们的隐私政策处理数据</view>
					<view class="list-item">· 定期评估第三方SDK的数据处理行为，确保其合规性</view>
					<view class="list-item">· 仅在必要的情况下收集和使用您的个人信息</view>
					<view class="list-item">· 采用加密传输和存储技术保护您的个人信息</view>
				</view>
			</view>
			
			<view class="section">
				<view class="section-title">十、联系我们</view>
				<view class="paragraph">
					如您对本SDK目录有任何疑问、意见或建议，您可通过以下方式联系我们：
				</view>
				<view class="paragraph">
					1. 向【】客服邮箱发送邮件；
				</view>
				<view class="paragraph">
					2. 写信至：【】客服部（收）；邮编：【】。
				</view>
				<view class="paragraph">
					我们将在核实您的身份后尽快回复您的咨询。
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: #fff;
}

.content {
	padding: 230rpx 32rpx 32rpx 32rpx;
	.title {
		font-size: 40rpx;
		color: #000;
		font-weight: 600;
		text-align: center;
		margin-bottom: 20rpx;
	}
	
	.time {
		font-size: 24rpx;
		color: #000;
		text-align: center;
		margin-bottom: 60rpx;
	}
}

.section {
	margin-bottom: 40rpx;
	
	.section-title {
		font-size: 32rpx;
		color: #000;
		font-weight: 500;
		margin-bottom: 20rpx;
	}
	
	.subsection {
		margin-bottom: 30rpx;
		
		.subsection-title {
			font-size: 30rpx;
			color: #000;
			font-weight: 500;
			margin-bottom: 15rpx;
		}
	}
	
	.paragraph {
		font-size: 28rpx;
		color: #000;
		line-height: 1.6;
		margin-bottom: 20rpx;
		text-align: justify;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	.list {
		padding-left: 20rpx;
		margin-bottom: 20rpx;
		
		.list-item {
			font-size: 28rpx;
			color: #000;
			line-height: 1.6;
			margin-bottom: 10rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
		}
	}
	.bold {
		font-weight: bold;
		display: inline;

		&.inline-block {
			display: inline-block;
		}
	}

	.yellow {
		display: inline;
		//设置背景色
		background-color: #ff0;

		&.inline-block {
			display: inline-block;
		}
	}
}
</style>