<template>
	<view class="container">
		<NavigationCustorm>
			<template #title>
				小岛屿社区公约
			</template>
		</NavigationCustorm>
		<view class="content">
			<view class="title">小岛屿社区公约</view>
			<!-- 前言 -->
			<view class="section">
				<view class="sub-section">
          小岛屿是一个供广大用户建立美好关系、寻求相助的平台，一直坚守着善意、平等、真实的核心价值观。为此，我们建立了《小岛屿社区公约》（以下简称“《社区公约》”）来指引我们，让我们的社区能更好地持续健康发展。
				</view>
        <view class="sub-section">
          小岛屿社区是我们大家共同的社区，而《社区公约》的不断完善也变得极为重要，我们会随时向我们的社区传递什么行为是反对的，什么行为是鼓励支持的，并根据情况适时修订《社区公约》。
				</view>
			</view>

			<!-- 第一章 -->
			<view class="section">
				<view class="sub-section">
					<view class="sub-title">一、真实</view>
					<view class="paragraph">
						我们注册使用平台服务前,应当具备中华人民共和国法律规定的与自身行为相适应的民事行为能力,能独立承担法律责任。使用服务时提供真实、准确、完整的身份信息、禁止冒用、盗用他人身份或使用虚假信息注册、预约服务,否则自行承担由此引发的法律后果。
					</view>
					<view class="paragraph">
						我们在平台内的行为应当基于真实的需求,不得存在恶意下单、恶意维权等扰乱平台正常交易秩序的行为。
					</view>
				</view>
        <view class="sub-section">
					<view class="sub-title">二、安全</view>
					<view class="paragraph">
						我们在小岛屿社区的行为、发布的信息、商品及服务不得违反国家法律法规及小岛屿社区《用户协议》第五章用户行为规范等相关要求。
					</view>
					<view class="paragraph">
						平台非常重视个人隐私保护，会积极维护网络数据和用户信息安全，严格按照法律及相关法规采取技术保护措施并进行安全评估。我们不得发布或提供泄露他人隐私的信息，包括但不限于：公开他人真实姓名及身份证号、电话号码、住址等隐私信息。
					</view>
				</view>

        <view class="sub-section">
					<view class="sub-title">三、平等</view>
					<view class="paragraph">
						我们彼此是平等的，因此每一次互动中都应该尊重对方。在遵守所有适用法律的基础上，我们公平享有自由交友、自由交易等权利。每个人都应当遵循平等、公平、自愿的原则。
					</view>
					<view class="paragraph">
						我们应理性选择服务项目与提供服务人员,尊重服务人员劳动成果与专业建议,依约支付服务费用。禁止以不正当理由拒付、拖欠款项或恶意索赔,不得利用小岛屿平台从事违法违规活动（嫖娼卖淫、吸毒贩毒、赌博等）。
					</view>
          <view class="paragraph">
						本公约中已有规定的从本公约，本公约未有规定的依据《用户行为规范》等小岛屿社区相关协议规则执行。
					</view>
          <view class="paragraph">
						本公约生效时间为：2025年  7月   1日。
					</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script setup>
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background-color: #fff;
}

.content {
	padding: 230rpx 32rpx 32rpx 32rpx;
	.title {
		font-size: 40rpx;
		color: #000;
		font-weight: 600;
		text-align: center;
		margin-bottom: 20rpx;
	}

	.time {
		font-size: 24rpx;
		color: #000;
		text-align: center;
		margin-bottom: 60rpx;
	}
}

.catalog {
	background-color: #f8f9fa;
	padding: 30rpx;
	border-radius: 10rpx;
	margin-bottom: 40rpx;

	.catalog-title {
		font-size: 32rpx;
		color: #000;
		font-weight: 600;
		text-align: center;
		margin-bottom: 20rpx;
	}

	.catalog-item {
		font-size: 26rpx;
		color: #000;
		line-height: 2;
		padding: 5rpx 0;
	}
}

.preface {
	margin-bottom: 40rpx;
	padding: 30rpx;
	background-color: #fff9e6;
	border-left: 4rpx solid #ffd700;
	border-radius: 10rpx;
}

.section {
	margin-bottom: 50rpx;

	.section-title {
		font-size: 26rpx;
		color: #000;
		font-weight: 600;
		margin-bottom: 30rpx;
		padding-bottom: 10rpx;
		border-bottom: 2rpx solid #e9ecef;
	}

	.sub-section {
		margin-bottom: 30rpx;
		color: #000;

		.sub-title {
			font-size: 26rpx;
			color: #444;
			font-weight: 500;
			margin-bottom: 15rpx;
		}
	}

	.paragraph {
		font-size: 26rpx;
		color: #000;
		line-height: 1.8;
		margin-bottom: 20rpx;
		text-align: justify;
		text-indent: 0;
		font-family: 'PingFang SC';

		&:last-child {
			margin-bottom: 0;
		}
	}

	.list {
		padding-left: 30rpx;

		.list-item {
			font-size: 26rpx;
			color: #000;
			line-height: 1.6;
			margin-bottom: 10rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	.red {
		color: #ff0000;
		display: inline;

		&.inline-block {
			display: inline-block;
		}
	}

	.yellow {
		display: inline;
		//设置背景色
		background-color: #ff0;

		&.inline-block {
			display: inline-block;
		}
	}
}
</style>