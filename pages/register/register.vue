<template>
	<view class="container">
		<view class="form-box">
			<view class="input-group">
				<text class="label">手机号</text>
				<input 
					type="number" 
					v-model="form.phone" 
					placeholder="请输入手机号"
					maxlength="11"
				/>
			</view>
			
			<view class="input-group">
				<text class="label">验证码</text>
				<view class="verify-input">
					<input 
						type="number" 
						v-model="form.code" 
						placeholder="请输入验证码"
						maxlength="6"
					/>
					<button 
						class="verify-btn" 
						:disabled="!canSendCode || counting" 
						@click="sendVerifyCode"
					>
						{{counting ? `${countdown}s后重试` : '获取验证码'}}
					</button>
				</view>
			</view>
			
			<view class="input-group">
				<text class="label">密码</text>
				<input 
					:type="showPassword ? 'text' : 'password'" 
					v-model="form.password" 
					placeholder="请输入密码（6-20位字母和数字）"
				/>
				<text 
					class="iconfont" 
					:class="showPassword ? 'icon-eye' : 'icon-eye-close'"
					@click="togglePassword"
				></text>
			</view>
			
			<view class="input-group">
				<text class="label">确认密码</text>
				<input 
					:type="showConfirmPassword ? 'text' : 'password'" 
					v-model="form.confirmPassword" 
					placeholder="请再次输入密码"
				/>
				<text 
					class="iconfont" 
					:class="showConfirmPassword ? 'icon-eye' : 'icon-eye-close'"
					@click="toggleConfirmPassword"
				></text>
			</view>
			
			<button class="submit-btn" @click="handleRegister" :disabled="!isFormValid">
				注册
			</button>
			
			<view class="action-links">
				<text @click="navigateToLogin">已有账号？立即登录</text>
			</view>
		</view>
		
		<view class="agreement">
			<checkbox :checked="agreed" @click="toggleAgreement"></checkbox>
			<text>注册即代表同意</text>
			<text class="link" @click="navigateToUserAgreement">《用户协议》</text>
			<text>和</text>
			<text class="link" @click="navigateToPrivacyPolicy">《隐私政策》</text>
		</view>
	</view>
</template>

<script>
import { useUserStore } from '@/stores/user'
import { showToast } from '@/common/utils';

export default {
	data() {
		return {
			form: {
				phone: '',
				code: '',
				password: '',
				confirmPassword: ''
			},
			showPassword: false,
			showConfirmPassword: false,
			agreed: false,
			counting: false,
			countdown: 60,
			userStore: null
		}
	},
	
	onLoad() {
		this.userStore = useUserStore()
	},
	
	computed: {
		isFormValid() {
			return this.form.phone && 
				   this.form.code && 
				   this.form.password && 
				   this.form.confirmPassword && 
				   this.agreed && 
				   /^1\d{10}$/.test(this.form.phone) &&
				   /^[a-zA-Z0-9]{6,20}$/.test(this.form.password) &&
				   this.form.password === this.form.confirmPassword;
		},
		
		canSendCode() {
			return /^1\d{10}$/.test(this.form.phone);
		}
	},
	
	methods: {
		togglePassword() {
			this.showPassword = !this.showPassword;
		},
		
		toggleConfirmPassword() {
			this.showConfirmPassword = !this.showConfirmPassword;
		},
		
		toggleAgreement() {
			this.agreed = !this.agreed;
		},
		
		async sendVerifyCode() {
			if (!this.canSendCode || this.counting) return;
			
			try {
				// 调用发送验证码接口
				await this.$api.sendVerifyCode(this.form.phone);
				showToast('验证码已发送', 'success');
				this.startCountdown();
			} catch (error) {
				showToast('验证码发送失败，请重试');
			}
		},
		
		startCountdown() {
			this.counting = true;
			this.countdown = 60;
			
			const timer = setInterval(() => {
				if (this.countdown > 0) {
					this.countdown--;
				} else {
					this.counting = false;
					clearInterval(timer);
				}
			}, 1000);
		},
		
		async handleRegister() {
			if (!this.isFormValid) return;
			
			try {
				const success = await this.userStore.register(this.form);
				if (success) {
					showToast('注册成功', 'success');
					uni.reLaunch({
						url: '/pages/index/index'
					});
				}
			} catch (error) {
				showToast('注册失败，请重试');
			}
		},
		
		navigateToLogin() {
			uni.navigateBack();
		},
		
		navigateToUserAgreement() {
			uni.navigateTo({
				url: '/pages/agreement/user'
			});
		},
		
		navigateToPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/agreement/privacy'
			});
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 60rpx 40rpx;
	
	.form-box {
		.input-group {
			position: relative;
			margin-bottom: 40rpx;
			
			.label {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 20rpx;
				display: block;
			}
			
			input {
				width: 100%;
				height: 90rpx;
				background: #f5f5f5;
				border-radius: 45rpx;
				padding: 0 40rpx;
				font-size: 30rpx;
			}
			
			.verify-input {
				display: flex;
				align-items: center;
				
				input {
					flex: 1;
					margin-right: 20rpx;
				}
				
				.verify-btn {
					width: 200rpx;
					height: 90rpx;
					line-height: 90rpx;
					font-size: 28rpx;
					color: $color-primary;
					background: #f5f5f5;
					border-radius: 45rpx;
					padding: 0;
					
					&:disabled {
						color: #999;
					}
				}
			}
			
			.iconfont {
				position: absolute;
				right: 40rpx;
				bottom: 25rpx;
				font-size: 40rpx;
				color: #999;
			}
		}
		
		.submit-btn {
			width: 100%;
			height: 90rpx;
			background: $color-primary;
			border-radius: 45rpx;
			color: #fff;
			font-size: 32rpx;
			margin-top: 60rpx;
			
			&:disabled {
				opacity: 0.6;
			}
		}
		
		.action-links {
			text-align: center;
			margin-top: 30rpx;
			font-size: 28rpx;
			color: #666;
		}
	}
	
	.agreement {
		position: fixed;
		left: 0;
		bottom: 60rpx;
		width: 100%;
		text-align: center;
		font-size: 24rpx;
		color: #666;
		
		checkbox {
			transform: scale(0.7);
			margin-right: 10rpx;
		}
		
		.link {
			color: $color-primary;
		}
	}
}
</style> 