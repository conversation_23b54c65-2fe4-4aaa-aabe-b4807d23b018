<template>
  <view class="container">
    <page-header 
      title="性格测评"
      :show-visitor="true"
      right-text="跳过"
      @back="goBack"
      @visitor="skip"
    />
    <template v-if="!showResult">
      <view class="content chat-mode">
        <view class="desc">为了更好地给您进行好友的推荐，请回答下述问题</view>
        <view class="progress-bar">
          <view class="progress" :style="{ width: `${progress}%` }"></view>
        </view>
        <view class="chat-list">
          <!-- 已答题目和答案气泡（问题+所有选项一起包裹，选项不可点击，用户选择高亮） -->
          <template v-for="(answer, idx) in answers">
            <view v-if="answer !== null && answer !== undefined" :key="'q'+idx" class="chat-row bot-row">
              <image class="avatar bot-avatar" src="/static/index/icon/avatar_robot.png" />
              <view class="chat-item bot">
                <text class="chat-question">{{ questions[idx].question }}</text>
                <view class="chat-options-in-bot">
                  <view
                    v-for="(option, oidx) in questions[idx].options"
                    :key="'answered-opt'+oidx"
                    class="chat-option-bubble-in-bot"
                    :class="{ 'selected': answer === option.id, 'disabled': true }"
                  >
                    <text>{{ option.text }}</text>
                    <text v-if="answer === option.id" class="check">✔</text>
                  </view>
                </view>
              </view>
            </view>
            <view v-if="answer !== null && answer !== undefined" :key="'a'+idx" class="chat-row user-row">
              <view class="chat-item user">
                <text class="chat-answer">{{ questions[idx].options.find(opt => opt.id === answer)?.text }}</text>
              </view>
              <image class="avatar user-avatar" src="/static/index/icon/avatar.png" />
            </view>
          </template>
          <!-- 当前题目气泡（问题+选项一起包裹） -->
          <view v-if="currentIndex < questions.length" class="chat-row bot-row">
            <image class="avatar bot-avatar" src="/static/index/icon/avatar_robot.png" />
            <view class="chat-item bot">
              <text class="chat-question">{{ questions[currentIndex].question }}</text>
              <view class="chat-options-in-bot">
                <view
                  v-for="(option, idx) in questions[currentIndex].options"
                  :key="'opt'+idx"
                  class="chat-option-bubble-in-bot"
                  @click="handleChatOption(idx)"
                >
                  <text>{{ option.text }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="result-bg">
        <view class="result-title">恭喜完成测评</view>
        <view class="result-sub">测评报告</view>
        <view class="result-card">
          <view class="result-header">测评结果</view>
          <view class="result-type">{{ result.type }}</view>
          <view class="result-desc" v-for="(p, i) in result.desc" :key="i">{{ p }}</view>
        </view>
        <view class="result-btns">
          <button class="result-btn outline" @click="restart">重新测评</button>
          <button class="result-btn" @click="completeSetup">完成</button>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import PageHeader from '@/components/PageHeader.vue'
import { get, post } from '@/common/request'

const userStore = useUserStore()

const questions = ref([])
const currentIndex = ref(0)
const selected = ref(null)
const showResult = ref(false)
const answers = ref([]) // 记录用户的答案
const result = ref({
  type: '',
  desc: []
})

const progress = computed(() => (questions.value.length ? ((currentIndex.value + 1) / questions.value.length) * 100 : 0))

// 获取题目
async function fetchQuestions() {
  try {
    const res = await get('/question/queryAllQuestions')
    if (res.code === 200 && Array.isArray(res.data)) {
      questions.value = res.data.map(q => ({
        groupId: q.groupId,
        question: q.groupName,
        options: q.options.map(opt => ({
          id: opt.id,
          text: opt.option,
          score: opt.score
        }))
      }))
    } else {
      uni.showToast({ title: res.msg || '获取题目失败', icon: 'none' })
    }
  } catch (e) {
    uni.showToast({ title: '获取题目失败', icon: 'none' })
  }
}

function selectOption(idx) {
  selected.value = idx
}

function nextQuestion() {
  answers.value[currentIndex.value] = selected.value
  if (currentIndex.value < questions.value.length - 1) {
    currentIndex.value++
    selected.value = null
  } else {
    generateResult()
    showResult.value = true
  }
}

// 选项点击，记录选项id
function handleChatOption(idx) {
  answers.value[currentIndex.value] = questions.value[currentIndex.value].options[idx].id;
  if (currentIndex.value < questions.value.length - 1) {
    currentIndex.value++;
  } else {
    // 回答完最后一题，显示提示后跳转到登录页面
    uni.showToast({
      title: '即将跳转',
      icon: 'success',
      duration: 3000
    });
    setTimeout(() => {
      uni.navigateTo({ url: '/pages/login/login' });
    }, 3000);
  }
}

// 提交答案，传字符串数组
async function commitAnswers() {
  try {
    const res = await post('/question/commit', answers.value.map(String))
    if (res.code === 200) {
      uni.showToast({ title: '提交成功', icon: 'success' })
      await fetchResultFromServer(); // 提交成功后获取测评结果
    } else {
      uni.showToast({ title: res.msg || '提交失败', icon: 'none' })
    }
  } catch (e) {
    uni.showToast({ title: '提交失败', icon: 'none' })
  }
}

// 获取测评结果
async function fetchResultFromServer() {
  try {
    const res = await get('/question/getFeatures')
    if (res.code === 200 && Array.isArray(res.data) && res.data.length > 0) {
      // 只展示最新一条
      const latest = res.data[res.data.length - 1]
      result.value = {
        type: latest.optionName,
        desc: [
          `得分：${latest.optionScore}`
        ]
      }
    } else {
      uni.showToast({ title: res.msg || '获取测评结果失败', icon: 'none' })
    }
  } catch (e) {
    uni.showToast({ title: '获取测评结果失败', icon: 'none' })
  }
}

// 根据答案生成测评结果
function generateResult() {
  // 统计各类型答案的数量
  const answerCounts = [0, 0, 0, 0]
  answers.value.forEach(answer => {
    if (answer !== null && answer !== undefined) {
      answerCounts[answer]++
    }
  })
  // 找出最多的答案类型
  const maxIndex = answerCounts.indexOf(Math.max(...answerCounts))
  // 根据答案类型生成结果
  const resultTypes = [
    {
      type: 'Dominance－支配型/领导者（D）',
      desc: [
        '高D型的人通常是果断的决策者，喜欢挑战和竞争。',
        '你是一个直接、果断、自信的人，喜欢掌控局面，追求结果。在工作中，你善于制定目标并推动执行，能够快速做出决策。你重视效率和成果，不喜欢被细节拖累。',
        '你的优势在于领导力和执行力，但需要注意在团队合作中给予他人更多空间，避免过于强势。'
      ]
    },
    {
      type: 'Influence－活泼型/社交者（I）',
      desc: [
        '高I型的人通常是较为活泼的团队活动组织者。',
        '你是一个情感丰富而外露的人，性格活跃、爱说、爱讲故事、幽默，能够抓住听众的注意力。你常常是聚会的中心人物，热情诚挚，喜欢与人交往。',
        '你的优势在于沟通能力和人际关系，但需要注意控制情绪波动，提高执行力和专注度。'
      ]
    },
    {
      type: 'Steadiness－稳定型/支持者（S）',
      desc: [
        '高S型的人通常是可靠的团队成员和耐心的倾听者。',
        '你是一个温和、耐心、可靠的人，善于倾听和支持他人。你重视和谐的关系，不喜欢冲突，愿意为团队付出。你做事认真负责，注重细节和品质。',
        '你的优势在于稳定性和可靠性，但需要学会表达自己的需求和想法，提高决策的主动性。'
      ]
    },
    {
      type: 'Compliance－谨慎型/思考者（C）',
      desc: [
        '高C型的人通常是精确的分析者和完美主义者。',
        '你是一个理性、谨慎、追求完美的人，善于分析和思考。你重视准确性和质量，喜欢深入研究问题，制定详细的计划。你做事有条理，注重规则和标准。',
        '你的优势在于分析能力和精确性，但需要学会放松，提高人际交往的灵活性。'
      ]
    }
  ]
  result.value = resultTypes[maxIndex]
}

function skip() {
  uni.navigateTo({ url: '/pages/login/login' })
}

function goBack() {
  uni.navigateBack()
}

function restart() {
  currentIndex.value = 0
  selected.value = null
  showResult.value = false
  answers.value = []
  fetchQuestions()
}

async function completeSetup() {
  // 不再调用提交接口，直接跳转到登录页面
  uni.showToast({
    title: '测评完成',
    icon: 'success',
    duration: 1500
  })
  setTimeout(() => {
    uni.navigateTo({ url: '/pages/login/login' })
  }, 1500)
}

onMounted(() => {
  fetchQuestions()
  console.log('性格测评页面已加载')
  console.log('当前用户设置状态:', userStore.getUserSetupStatus())
  console.log('是否需要完成设置:', userStore.checkUserSetupRequired())
})
</script>

<style scoped>
.container { 
  background: #f7fdfb; 
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.content{
  padding: 0 36rpx;
  flex: 1;
}
.desc { color: #888; font-size: 24rpx; margin-bottom: 16rpx; }
.progress-bar { width: 100%; height: 12rpx; background: #e0e0e0; border-radius: 6rpx; margin-bottom: 16rpx; }
.progress { height: 100%; background: #3ec06c; border-radius: 6rpx; transition: width 0.3s; }
.question-info { font-size: 24rpx; color: #3ec06c; margin-bottom: 24rpx; }
.question { font-size: 32rpx; font-weight: 500; margin-bottom: 32rpx; }
.options { display: flex; flex-direction: column; gap: 24rpx; }
.option { background: #fff; border-radius: 16rpx; padding: 32rpx; font-size: 28rpx; color: #333; display: flex; align-items: center; justify-content: space-between; border: 2rpx solid #fff; }
.option.selected { color: #3ec06c; border-color: #3ec06c; font-weight: bold; }
.check { color: #3ec06c; font-size: 32rpx; }
.next-btn { 
  width: 100%; 
  height: 88rpx; 
  background: #3ec06c; 
  color: #fff; 
  font-size: 32rpx; 
  border-radius: 44rpx; 
  margin: 48rpx 36rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: calc(100% - 72rpx);
}
.next-btn:disabled { background: #cccccc; color: #ffffff; }
.result-bg {
  min-height: 100vh;
  background: linear-gradient(180deg, #b6f5d2 0%, #f7fdfb 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 48rpx;
}
.result-title {
  color: #19c37d;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.result-sub {
  color: #7be3b2;
  font-size: 24rpx;
  margin-bottom: 32rpx;
}
.result-card {
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(25,195,125,0.08);
  padding: 40rpx 32rpx;
  width: 80vw;
  max-width: 700rpx;
  margin-bottom: 48rpx;
}
.result-header {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.result-type {
  color: #19c37d;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}
.result-desc {
  color: #333;
  font-size: 26rpx;
  margin-bottom: 16rpx;
  line-height: 1.7;
}
.result-btns {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-bottom: 48rpx;
}
.result-btn {
  flex: 1;
  max-width: 240rpx;
  height: 72rpx;
  background: #19c37d;
  color: #fff;
  font-size: 28rpx;
  border-radius: 36rpx;
  border: none;
}
.result-btn.outline {
  background: #fff;
  color: #19c37d;
  border: 2rpx solid #19c37d;
}
.chat-mode {
  background: #f7fdfb;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.chat-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-top: 32rpx;
  margin-bottom: 32rpx;
}
.chat-row {
  display: flex;
  align-items: flex-end;
  margin-bottom: 8rpx;
}
.bot-row {
  flex-direction: row;
  justify-content: flex-start;
}
.user-row {
  flex-direction: row;
  justify-content: flex-end;
}
.avatar {
  min-width: 43px;
  width: 43px;
  height: 43px;
  border-radius: 50%;
}
.bot-avatar {
  order: 0;
}
.user-avatar {
  order: 1;
}
.chat-item.bot {
  background: #e8f7ee;
  color: #19c37d;
  border-radius: 24rpx 24rpx 24rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(25,195,125,0.08);
  padding: 24rpx 32rpx;
  max-width: 80%;
  word-break: break-all;
}
.chat-item.user {
  background: #fff;
  color: #333;
  border: 2rpx solid #19c37d;
  border-radius: 24rpx 24rpx 0 24rpx;
  padding: 24rpx 32rpx;
  max-width: 80%;
  word-break: break-all;
}
.chat-options-in-bot {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 24rpx;
}
.chat-option-bubble-in-bot {
  background: #fff;
  color: #19c37d;
  border: 2rpx solid #19c37d;
  border-radius: 24rpx;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  cursor: pointer;
  transition: background 0.2s;
  align-self: flex-start;
  margin-bottom: 8rpx;
}
.chat-option-bubble-in-bot:active {
  background: #e8f7ee;
}
.chat-option-bubble-in-bot.selected {
  background: #19c37d;
  color: #fff;
  border-color: #19c37d;
  font-weight: bold;
  position: relative;
}
.chat-option-bubble-in-bot.disabled {
  cursor: not-allowed;
  opacity: 0.7;
}
.check {
  color: #fff;
  font-size: 32rpx;
  margin-left: 12rpx;
}
</style>