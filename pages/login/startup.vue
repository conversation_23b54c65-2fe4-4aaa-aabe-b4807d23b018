<!--
	Small Island 启动页面
	
	包含3张引导图片的滚动展示
	支持跳过功能和自动标记已展示状态
	
	开发者：diwanjian
	最后更新：2024年
-->
<template>
  <view class="startup-container">
    <!-- 跳过按钮 -->
    <view class="skip-btn" @click="skipStartup">跳过</view>

    <!-- 轮播图 -->
    <swiper
      class="startup-swiper"
      :indicator-dots="true"
      :autoplay="false"
      :interval="4000"
      :duration="500"
      :circular="false"
      @change="onSwiperChange"
    >
      <!-- 第一页 - 线下小岛 自在社交 -->
      <swiper-item class="swiper-item">
        <view class="slide-content slide-1">
          <view class="slide-bg">
            <image src="/static/index/login/l1.png" class="slide-image" mode="aspectFill"></image>
          </view>
          <!-- 在背景图上方添加 b1.png -->
          <view class="overlay-image">
            <image src="/static/index/login/b1.png" class="feature-image" mode="aspectFit"></image>
          </view>
        </view>
      </swiper-item>

      <!-- 第二页 - 活力青春 -->
      <swiper-item class="swiper-item">
        <view class="slide-content slide-2">
          <view class="slide-bg">
            <image src="/static/index/login/l2.png" class="slide-image" mode="aspectFill"></image>
          </view>
          <!-- 在背景图上方添加 b2.png -->
          <view class="overlay-image">
            <image src="/static/index/login/b2.png" class="feature-image" mode="aspectFit"></image>
          </view>
        </view>
      </swiper-item>

      <!-- 第三页 - 温暖陪伴 -->
      <swiper-item class="swiper-item">
        <view class="slide-content slide-3">
          <view class="slide-bg">
            <image src="/static/index/login/l3.png" class="slide-image" mode="aspectFill"></image>
          </view>
          <!-- 在背景图上方添加 b3.png -->
          <view class="overlay-image">
            <image src="/static/index/login/b3.png" class="feature-image" mode="aspectFit"></image>
          </view>
          <view class="slide-text">
            <view class="action-buttons">
              <button class="action-btn secondary ab-sty1" @click="goToPersonalityTest">
                <text>认识小岛屿</text>
                <image src="/static/index/icon/right.png" class="btn-icon"></image>
              </button>
              <button class="action-btn secondary ab-sty2" @click="startJourney">
                <text>开启小岛屿之旅</text>
                <image src="/static/index/icon/right.png" class="btn-icon"></image>
              </button>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 底部指示器 -->
    <view class="startup-footer">
      <view class="custom-indicators">
        <view
          v-for="(item, index) in 3"
          :key="index"
          class="indicator-dot"
          :class="{ active: currentIndex === index }"
        ></view>
      </view>
    </view>
  </view>
</template>

<script>
  import { ref, onMounted } from 'vue'

  export default {
    name: 'StartupPage',
    setup() {
      const currentIndex = ref(0)

      // 轮播图切换事件
      const onSwiperChange = e => {
        currentIndex.value = e.detail.current
      }

      // 跳过启动页
      const skipStartup = () => {
        markStartupShown()
        navigateToLogin()
      }

      // 性格测评（暂时跳转到登录页）
      const goToPersonalityTest = () => {
        markStartupShown()
        // 性格测评页面暂时不需要，先跳转到登录页

        uni.redirectTo({
          url: '/pages/login/character'
        })
      }

      // 开启小岛屿之旅
      const startJourney = () => {
        markStartupShown()
        navigateToLogin()
      }

      // 标记启动页已展示
      const markStartupShown = () => {
        uni.setStorageSync('startupShown', true)
      }

      // 跳转到登录页
      const navigateToLogin = () => {
        uni.redirectTo({
          url: '/pages/login/login'
        })
      }

      return {
        currentIndex,
        onSwiperChange,
        skipStartup,
        goToPersonalityTest,
        startJourney
      }
    }
  }
</script>

<style lang="scss" scoped>
  .startup-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    overflow: hidden;
    position: fixed;
  }

  .skip-btn {
    position: absolute;
    top: 60rpx;
    right: 40rpx;
    z-index: 100;
    padding: 16rpx 32rpx;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 40rpx;
    color: #fff;
    font-size: 28rpx;
    /* #ifdef APP-PLUS */
    top: calc(var(--status-bar-height) + 60rpx);
    /* #endif */
  }

  .startup-swiper {
    width: 100%;
    height: 100%;

    /* #ifdef APP-PLUS || H5 */
    ::v-deep .uni-swiper-dots {
      display: none !important;
    }
    /* #endif */

    /* #ifdef MP-WEIXIN */
    ::v-deep .wx-swiper-dots {
      display: none !important;
    }
    /* #endif */
  }

  .swiper-item {
    width: 100%;
    height: 100%;
  }

  .slide-content {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .slide-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .slide-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .overlay-image {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 50%;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .feature-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .slide-text {
    position: fixed;
    z-index: 3;
    text-align: center;
    padding: 40rpx;
    color: #fff;
    margin-top: 60rpx;
    bottom: 50px;
    /* #ifdef APP-PLUS */
    bottom: calc(50px + env(safe-area-inset-bottom));
    /* #endif */
  }

  // 第一页样式
  .slide-1 {
    .slide-text {
      .main-title {
        font-size: 48rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
        color: #2e8b57;
        text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
      }

      .sub-title {
        font-size: 32rpx;
        margin-bottom: 40rpx;
        color: #2e8b57;
        text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
      }

      .description {
        margin-bottom: 60rpx;
        color: #2e8b57;

        .desc-line {
          font-size: 28rpx;
          line-height: 1.6;
          margin-bottom: 10rpx;
          text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  // 第二页样式
  .slide-2 {
    .slide-text {
      .main-title {
        font-size: 48rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
        color: #fff;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
      }

      .sub-title {
        font-size: 32rpx;
        margin-bottom: 40rpx;
        color: #fff;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
      }

      .description {
        color: #fff;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);

        .desc-line {
          font-size: 28rpx;
          line-height: 1.6;
          margin-bottom: 10rpx;
        }
      }
    }
  }

  // 第三页样式
  .slide-3 {
    .slide-text {
      .main-title {
        font-size: 48rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
        color: #fff;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
      }

      .sub-title {
        font-size: 32rpx;
        margin-bottom: 40rpx;
        color: #fff;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
      }

      .description {
        color: #fff;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
        margin-bottom: 60rpx;

        .desc-line {
          font-size: 28rpx;
          line-height: 1.6;
          margin-bottom: 10rpx;
        }
      }

      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 20rpx;

        .action-btn {
          width: 300rpx;
          height: 80rpx;
          border-radius: 40rpx;
          font-size: 32rpx;
          font-weight: bold;
          box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 20rpx;

          &.primary {
            background: linear-gradient(45deg, #ff8a65 0%, #ff7043 100%);
            color: #fff;
          }

          &.secondary {
            background: linear-gradient(45deg, #ffb74d 0%, #ffa726 100%);
            color: #fff;
          }

          text {
            flex: 1;
            text-align: center;
          }

          .btn-icon {
            width: 24rpx;
            height: 24rpx;
            margin-left: 10rpx;
          }
        }
      }
    }
  }

  .startup-footer {
    position: absolute;
    bottom: 60rpx;
    left: 0;
    right: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 40rpx;
    /* #ifdef APP-PLUS */
    bottom: calc(60rpx + env(safe-area-inset-bottom));
    /* #endif */
  }

  .custom-indicators {
    display: flex;
    justify-content: center;

    .indicator-dot {
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.4);
      margin: 0 8rpx;
      transition: all 0.3s ease;

      &.active {
        background: #fff;
        transform: scale(1.2);
      }
    }
  }
  .ab-sty1 {
    position: relative;
    right: -95px;
    top: -281px;
  }
  .ab-sty2 {
    position: relative;
    top: -133px;
    left: -65px;
  }
</style>
