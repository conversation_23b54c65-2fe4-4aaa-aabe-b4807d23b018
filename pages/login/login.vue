<!--
	Small Island 登录页面
	
	支持的登录方式：
	✅ 本机号码一键登录 - 集成uni一键登录，支持三大运营商
	✅ 短信验证码登录 - 4位验证码，自动验证登录
	✅ QQ登录 - 支持获取QQ用户基本信息
	✅ 微信登录 - 支持UnionID机制，安全的code换取模式
	
	开发者：diwanjian
	最后更新：2024年
-->
<template>
  <view
    class="container"
    :class="{ 'welcome-bg': currentStep === 'welcome', 'form-bg': currentStep !== 'welcome' }"
  >
    <!-- 温馨提示弹窗 -->
    <view class="privacy-popup" v-if="showPrivacyPopup">
      <view class="privacy-content">
        <view class="privacy-title">温馨提示</view>
        <view class="privacy-content-box">
          <view class="privacy-text">
            <text>欢迎加入小岛屿！</text>
            <text class="privacy-desc"
              >小岛屿重视与保障您的个人隐私，我们将通过<text
                class="link"
                @click="navigateToUserAgreement"
                >《用户协议》</text
              >和<text class="link" @click="navigateToPrivacyPolicy">《隐私政策》</text
              >，帮助您了解我们为您提供的服务、我们如何处理个人信息以及您享有的权利。我们会严格按照相关法规要求，采取各种安全措施来保护您的个人信息。</text
            >
          </view>
          <view class="privacy-desc">点击"同意"按钮，表示您已知情并同意以上协议和以下约定：</view>
          <view class="privacy-desc"
            >1、为了保障软件的安全运行和账户安全，我们会申请收集您的设备信息、IP地址、MAC地址</view
          >
          <view class="privacy-desc"
            >2、为了提供个性化内容推荐和社交功能，我们会收集您的兴趣标签、互动记录等信息</view
          >
          <view class="privacy-desc"
            >3、为了保障账户安全和防范风险，我们会记录您的登录行为和操作日志</view
          >
          <view class="privacy-desc"
            >4、在您使用地理位置相关功能时，我们会请求获取您的位置信息</view
          >
          <view class="privacy-desc"
            >5、我们承诺严格保护您的隐私，不会将您的个人信息泄露给第三方</view
          >
        </view>
        <button class="agree-btn" @click="handleAgreePrivacy">同意</button>
        <view class="disagree-text" @click="handleDisagreePrivacy">不同意进入访客模式</view>
      </view>
    </view>
    <!-- 欢迎界面 -->
    <view v-if="currentStep === 'welcome'" class="welcome-page">
      <!-- <view class="slogan">
				<view class="slogan-horizontal">&nbsp;</view>
				<view class="slogan-vertical">
					<view class="slogan-line">&nbsp;</view>
				</view>
				<view class="slogan-vertical">
					<view class="slogan-line">&nbsp;</view>
				</view>
			</view> -->
      <view class="action-buttons-container">
        <view class="action-buttons">
          <button class="primary-btn" @click="handleUniverifyLogin">登录 / 注册</button>
        </view>

        <view class="third-party-login">
          <view class="login-icon-item wechat-item" @click="handleWechatLogin">
            <image src="/static/index/Wechat.png" class="login-icon"></image>
            <text class="login-text">微信</text>
          </view>
          <view class="login-icon-item qq-item" @click="handleQQLogin">
            <image src="/static/index/QQ.png" class="login-icon"></image>
            <text class="login-text">QQ</text>
          </view>
          <view class="login-icon-item sms-item" @click="handleSmsLogin">
            <image src="/static/index/login/msg.png" class="login-icon"></image>
            <text class="login-text">短信登录</text>
          </view>
        </view>

        <view class="agreement" :class="{ highlight: showAgreementHighlight }">
          <checkbox :checked="agreed" @click="toggleAgreement"></checkbox>
          <text>我已阅读并同意</text>
          <text class="link" @click="navigateToUserAgreement">《用户协议》</text>
          <text>和</text>
          <text class="link" @click="navigateToPrivacyPolicy">《隐私政策》</text>
        </view>
      </view>
    </view>

    <!-- 手机号输入界面 -->
    <view v-else-if="currentStep === 'phone'" class="form-page">
      <view class="page-header">
        <text class="back-btn" @click="goBack">‹</text>
        <text class="page-title"></text>
        <text class="visitor-mode" @click="enterVisitorMode">游客进入</text>
      </view>

      <view class="form-box">
        <view class="form-head">手机号码登录</view>
        <view class="form-tip">未注册的手机号验证后自动创建用户</view>

        <view class="phone-input-group">
          <view class="area-code">+86</view>
          <input
            type="number"
            v-model="phone"
            placeholder="请输入手机号"
            maxlength="11"
            @input="handlePhoneInput"
            @blur="checkPhoneFormat"
          />
          <text class="clear-btn" v-if="phone" @click="clearPhone">×</text>
        </view>

        <button
          class="submit-btn"
          :class="{ disabled: !isPhoneValid || loading }"
          :disabled="!isPhoneValid || loading"
          @click="handleSendCode"
        >
          {{ loading ? '发送中...' : '获取短信验证码' }}
        </button>
      </view>
    </view>

    <!-- 验证码输入界面 -->
    <view v-else-if="currentStep === 'code'" class="form-page">
      <view class="page-header">
        <text class="back-btn" @click="goBack">‹</text>
        <text class="page-title">输入验证码</text>
      </view>

      <view class="form-box">
        <view class="code-tip">
          <text>已发送验证码至{{ maskedPhone }}</text>
        </view>

        <view class="code-input-group">
          <view class="code-inputs">
            <input
              v-for="(item, index) in codeArray"
              :key="index"
              :id="`code-input-${index}`"
              type="number"
              :value="codeArray[index]"
              maxlength="1"
              class="code-input"
              @input="handleCodeInput($event, index)"
              @focus="handleCodeFocus(index)"
            />
          </view>
        </view>

        <view class="resend-area">
          <text v-if="countdown > 0" class="countdown">重新获取({{ countdown }}s)</text>
          <text v-else class="resend-btn" @click="handleResendCode">重新获取验证码</text>
        </view>

        <button class="submit-btn" :disabled="!isCodeComplete || loginLoading" @click="handleLogin">
          {{ loginLoading ? '登录中...' : '登录' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
  import { ref, computed, onUnmounted, onMounted } from 'vue'
  import { useUserStore } from '@/stores/user'
  import { userApi } from '@/common/api'

  export default {
    setup() {
      const userStore = useUserStore()

      // 响应式数据
      const currentStep = ref('welcome') // welcome, phone, code
      const phone = ref('')
      const codeArray = ref(['', '', '', '']) // 改为4位验证码
      const agreed = ref(false)
      const loading = ref(false)
      const loginLoading = ref(false)
      const countdown = ref(0)
      const phoneError = ref('')
      const showAgreementHighlight = ref(false) // 协议高亮提示状态
      let countdownTimer = null
      const showPrivacyPopup = ref(false)

      // 计算属性
      const isPhoneValid = computed(() => {
        const phoneNumber = phone.value.trim()
        return phoneNumber && /^1[3-9]\d{9}$/.test(phoneNumber)
      })

      const maskedPhone = computed(() => {
        if (!phone.value) return ''
        return phone.value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      })

      const isCodeComplete = computed(() => {
        return codeArray.value.every(item => item !== '')
      })

      const verificationCode = computed(() => {
        return codeArray.value.join('')
      })

      // 方法
      const toggleAgreement = () => {
        agreed.value = !agreed.value
        // 如果用户勾选了协议，清除高亮状态
        if (agreed.value && showAgreementHighlight.value) {
          showAgreementHighlight.value = false
        }
      }

      const goBack = () => {
        if (currentStep.value === 'code') {
          currentStep.value = 'phone'
          // 清空验证码
          codeArray.value = ['', '', '', '']
          // 清除倒计时
          if (countdownTimer) {
            clearInterval(countdownTimer)
            countdownTimer = null
            countdown.value = 0
          }
        } else if (currentStep.value === 'phone') {
          currentStep.value = 'welcome'
          phoneError.value = ''
          phone.value = ''
        }
      }

      const handleUniverifyLogin = async () => {
        // 检查协议同意状态并处理温馨提示
        const shouldContinue = await checkAgreementAndPrivacy()
        if (!shouldContinue) return

        // 直接使用预登录检测，这是最可靠的方法
        detectByPreLogin()
          .then(() => {
            // 检测通过后调用一键登录
            performUniverifyLogin()
          })
          .catch(error => {
            // 检测失败，显示相应提示
            console.log('预登录检测失败:', error)
            uni.navigateTo({
              url: '/pages/login/phone-login'
            })
          })
      }

      // 通过预登录检测网络和SIM卡状态
      const detectByPreLogin = () => {
        return new Promise((resolve, reject) => {
          uni.preLogin({
            provider: 'univerify',
            success: res => {
              console.log('预登录成功，网络和SIM卡状态正常:', res)
              resolve()
            },
            fail: error => {
              console.log('预登录失败:', error)

              const errorCode = error.errCode || error.code
              let errorMessage = ''
              let showModal = false
              let modalContent = ''
              let modalTitle = ''

              switch (errorCode) {
                case 30001:
                  errorMessage = '当前网络环境不适合执行该操作'
                  modalTitle = '网络环境提示'
                  modalContent =
                    '当前网络环境不适合一键登录，请检查：\n1. 是否开启了手机流量\n2. 是否插入了有效的SIM卡\n3. 网络信号是否良好'
                  showModal = true
                  break
                case 30005:
                  errorMessage = '预登录失败，请检查网络或重试'
                  modalTitle = '网络连接提示'
                  modalContent =
                    '预登录失败，可能的原因：\n1. 未开启手机流量\n2. 未插入SIM卡\n3. 网络信号不佳\n\n请检查后重试。'
                  showModal = true
                  break
                case 40101:
                case 40201:
                case 40301:
                  errorMessage = '运营商鉴权失败，请检查手机卡'
                  modalTitle = 'SIM卡提示'
                  modalContent =
                    '运营商鉴权失败，请检查：\n1. 是否插入了有效的SIM卡\n2. SIM卡是否正常工作\n3. 是否开启了手机流量'
                  showModal = true
                  break
                default:
                  // 对于未知错误，先尝试直接进行一键登录，让系统自己处理
                  console.log('预登录失败，但尝试直接进行一键登录')
                  resolve()
                  return
              }

              // if (showModal) {
              // 	uni.showModal({
              // 		title: modalTitle,
              // 		content: modalContent,
              // 		confirmText: '知道了',
              // 		showCancel: false
              // 	})
              // }

              reject(new Error(errorMessage))
            }
          })
        })
      }

      // 执行一键登录
      const performUniverifyLogin = () => {
        // 调用一键登录
        uni.login({
          provider: 'univerify',
          success: async authResult => {
            const loginRes = authResult.authResult
            console.log('一键登录成功:', loginRes)

            try {
              // 首先调用云函数获取手机号
              const cloudResult = await new Promise((resolve, reject) => {
                uniCloud
                  .callFunction({
                    name: 'uni-univerify',
                    data: {
                      access_token: loginRes.access_token,
                      openid: loginRes.openid
                    }
                  })
                  .then(res => {
                    console.log('云函数调用成功:', res)
                    resolve(res)
                  })
                  .catch(err => {
                    console.error('云函数调用失败:', err)
                    reject(err)
                  })
              })

              // 检查云函数返回结果
              if (!cloudResult.result || cloudResult.result.code !== 200) {
                throw new Error(cloudResult.result?.message || '获取手机号失败')
              }

              // 调用store中的一键登录处理方法
              const result = await userStore.univerifyLogin({
                access_token: loginRes.access_token,
                phoneData: cloudResult.result.data
              })

              uni.hideLoading()
              uni.closeAuthView()

              if (result.success) {
                handleLoginSuccess(result.message)
              } else {
                handleLoginError(result)
              }
            } catch (error) {
              uni.hideLoading()
              uni.closeAuthView()
              handleLoginError(error, '一键登录失败，请重试')
            }
          },
          fail: error => {
            console.log('一键登录失败:', error)

            // 根据错误码处理不同情况
            const errorCode = error.errCode || error.code
            let errorMessage = '一键登录失败'

            switch (errorCode) {
              case 30001:
                errorMessage = '当前网络环境不适合执行该操作'
                break
              case 30002:
                errorMessage = '用户点击了其他登录方式'
                break
              case 30003:
                errorMessage = '用户关闭验证界面'
                break
              case 30004:
                errorMessage = '一键登录失败，请检查网络或重试'
                break
              case 30005:
                errorMessage = '预登录失败，请检查网络或重试'
                break
              case 30006:
                errorMessage = '一键登录失败，请重试'
                break
              case 30007:
                errorMessage = '获取本机号码校验token失败'
                break
              case 30008:
                errorMessage = '用户点击了自定义按钮'
                break
              case 40004:
                errorMessage = '应用未配置或未审核通过'
                break
              case 40047:
                errorMessage = '一键登录取号失败'
                break
              case 40053:
                errorMessage = '手机号校验失败'
                break
              case 40101:
              case 40201:
              case 40301:
                errorMessage = '运营商鉴权失败，请检查手机卡'
                break
              default:
                errorMessage = `一键登录失败(${errorCode})，请重试`
            }

            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000
            })
          }
        })
      }

      const handleWechatLogin = async () => {
        // 检查协议同意状态并处理温馨提示
        const shouldContinue = await checkAgreementAndPrivacy()
        if (!shouldContinue) return

        // 调用微信登录
        uni.login({
          provider: 'weixin',
          onlyAuthorize: true, // 微信登录仅请求授权认证
          success: async authResult => {
            const { code } = authResult
            console.log('微信登录成功，获取到code:', code)

            try {
              // 显示登录中状态
              uni.showLoading({
                title: '登录中...'
              })

              // 调用store中的微信登录处理方法
              const result = await userStore.wechatLogin({
                code: code
              })

              uni.hideLoading()

              if (result.success) {
                handleLoginSuccess(result.message)
              } else if (result.needBindPhone) {
                // 需要绑定手机号，跳转到手机号绑定页面
                console.log('微信用户首次登录，跳转到手机号绑定页面')
                uni.navigateTo({
                  url: '/pages/login/wechat-bind-phone'
                })
              } else {
                handleLoginError(result)
              }
            } catch (error) {
              uni.hideLoading()
              handleLoginError(error, '微信登录失败，请重试')
            }
          },
          fail: error => {
            console.log('微信登录失败:', error)

            // 根据错误码处理不同情况
            const errorCode = error.errCode || error.code
            let errorMessage = '微信登录失败'

            switch (errorCode) {
              case -1:
                errorMessage = '微信登录失败，请重试'
                break
              case -2:
                errorMessage = '用户取消微信登录'
                break
              case -3:
                errorMessage = '微信登录被拒绝'
                break
              case -4:
                errorMessage = '微信登录失败，请检查网络'
                break
              case -5:
                errorMessage = '微信登录失败，请重试'
                break
              case -6:
                errorMessage = '微信登录失败，请检查网络'
                break
              case -7:
                errorMessage = '微信登录失败，请重试'
                break
              case -8:
                errorMessage = '微信登录失败，请重试'
                break
              case -9:
                errorMessage = '微信登录失败，请重试'
                break
              case -10:
                errorMessage = '微信登录失败，请重试'
                break
              default:
                errorMessage = `微信登录失败(${errorCode})，请重试`
            }

            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000
            })
          }
        })
      }

      const handleQQLogin = async () => {
        // 检查协议同意状态并处理温馨提示
        const shouldContinue = await checkAgreementAndPrivacy()
        if (!shouldContinue) return

        // 调用QQ登录
        uni.login({
          provider: 'qq',
          onlyAuthorize: true, // QQ登录仅请求授权认证
          success: async authResult => {
            const { code } = authResult
            console.log('QQ登录成功，获取到code:', code, authResult)

            try {
              // 显示登录中状态
              uni.showLoading({
                title: '登录中...'
              })

              // 调用store中的QQ登录处理方法
              const result = await userStore.qqLogin({
                code: code
              })

              uni.hideLoading()

              if (result.success) {
                handleLoginSuccess(result.message)
              } else if (result.needBindPhone) {
                // 需要绑定手机号，跳转到手机号绑定页面
                console.log('QQ用户首次登录，跳转到手机号绑定页面')
                uni.navigateTo({
                  url: '/pages/login/qq-bind-phone'
                })
              } else {
                handleLoginError(result)
              }
            } catch (error) {
              uni.hideLoading()
              handleLoginError(error, 'QQ登录失败，请重试')
            }
          },
          fail: error => {
            console.log('QQ登录失败:', error)

            // 根据错误码处理不同情况
            const errorCode = error.errCode || error.code
            let errorMessage = 'QQ登录失败'

            switch (errorCode) {
              case -1:
                errorMessage = 'QQ登录失败，请重试'
                break
              case -2:
                errorMessage = '用户取消QQ登录'
                break
              case -3:
                errorMessage = 'QQ登录被拒绝'
                break
              case -4:
                errorMessage = 'QQ登录失败，请检查网络'
                break
              case -5:
                errorMessage = 'QQ登录失败，请重试'
                break
              case -6:
                errorMessage = 'QQ登录失败，请检查网络'
                break
              case -7:
                errorMessage = 'QQ登录失败，请重试'
                break
              case -8:
                errorMessage = 'QQ登录失败，请重试'
                break
              case -9:
                errorMessage = 'QQ登录失败，请重试'
                break
              case -10:
                errorMessage = 'QQ登录失败，请重试'
                break
              default:
                errorMessage = `QQ登录失败(${errorCode})，请重试`
            }

            uni.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 3000
            })
          }
        })
      }

      const handleSmsLogin = async () => {
        // 检查协议同意状态并处理温馨提示
        const shouldContinue = await checkAgreementAndPrivacy()
        if (!shouldContinue) return

        // 跳转到手机号登录页面
        uni.navigateTo({
          url: '/pages/login/phone-login'
        })
      }

      const startCountdown = () => {
        countdown.value = 60
        countdownTimer = setInterval(() => {
          countdown.value--
          if (countdown.value <= 0) {
            clearInterval(countdownTimer)
            countdownTimer = null
          }
        }, 1000)
      }

      const checkPhoneFormat = () => {
        // 清空之前的错误提示
        phoneError.value = ''

        // 如果手机号为空，不显示错误提示
        if (!phone.value.trim()) {
          return
        }

        // 手机号格式校验
        if (!/^1[3-9]\d{9}$/.test(phone.value)) {
          phoneError.value = '请输入正确的11位手机号'
          uni.showToast({
            title: '请输入正确的手机号',
            icon: 'none'
          })
        }
      }

      const handlePhoneInput = event => {
        const value = event.detail.value
        // 限制只能输入数字，最多11位
        phone.value = value.replace(/\D/g, '').slice(0, 11)
      }

      const handleSendCode = async () => {
        // 清空之前的错误提示
        phoneError.value = ''

        // 手机号为空校验
        if (!phone.value.trim()) {
          uni.showToast({
            title: '请输入手机号',
            icon: 'none'
          })
          return
        }

        // 手机号格式校验
        if (!/^1[3-9]\d{9}$/.test(phone.value)) {
          uni.showToast({
            title: '请输入正确的手机号',
            icon: 'none'
          })
          return
        }

        try {
          loading.value = true

          // 调用发送验证码接口
          const result = await userStore.sendSmsCode(phone.value)

          if (result && result.success) {
            // 发送成功
            uni.showToast({
              title: '验证码已发送',
              icon: 'success',
              duration: 1500
            })

            // 切换到验证码输入页面
            currentStep.value = 'code'

            // 清空之前的验证码
            codeArray.value = ['', '', '', '']

            // 开始倒计时
            startCountdown()

            // 自动获取第一个输入框的焦点
            setTimeout(() => {
              const firstInput = uni.createSelectorQuery().select('#code-input-0')
              firstInput
                .fields({ node: true }, res => {
                  if (res && res.node) {
                    res.node.focus()
                  }
                })
                .exec()
            }, 100)
          } else {
            // 发送失败
            uni.showToast({
              title: result?.message || '发送失败，请重试',
              icon: 'none',
              duration: 2000
            })
          }
        } catch (error) {
          console.error('发送短信验证码失败:', error)

          // 根据错误类型显示不同提示
          let errorMsg = '发送失败，请重试'

          if (error.code === 'PHONE_EXISTS') {
            errorMsg = '该手机号已被注册'
          } else if (error.code === 'SMS_LIMIT') {
            errorMsg = '发送太频繁，请稍后再试'
          } else if (error.code === 'INVALID_PHONE') {
            errorMsg = '手机号格式不正确'
          } else if (error.message?.includes('网络')) {
            errorMsg = '网络连接失败，请检查网络后重试'
          } else if (error.message?.includes('404')) {
            errorMsg = '服务暂时不可用，请稍后重试'
          }

          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          })
        } finally {
          loading.value = false
        }
      }

      const handleResendCode = async () => {
        if (countdown.value > 0) return

        try {
          // 显示发送中状态
          uni.showLoading({
            title: '发送中...'
          })

          const result = await userStore.sendSmsCode(phone.value)

          uni.hideLoading()

          if (result.success) {
            uni.showToast({
              title: result.message,
              icon: 'success'
            })

            // 立即清空验证码输入框
            codeArray.value = ['', '', '', '']

            // 重新开始倒计时
            startCountdown()

            // 自动获取第一个输入框的焦点
            setTimeout(() => {
              const firstInput = uni.createSelectorQuery().select('#code-input-0')
              firstInput
                .fields({ node: true }, res => {
                  if (res && res.node) {
                    res.node.focus()
                  }
                })
                .exec()
            }, 100)
          } else {
            uni.showToast({
              title: result.message,
              icon: 'none'
            })
          }
        } catch (error) {
          uni.hideLoading()
          console.error('重新发送验证码失败:', error)

          // 根据错误类型显示不同提示
          let errorMsg = '发送失败，请重试'

          if (error.code === 'SMS_LIMIT') {
            errorMsg = '发送太频繁，请稍后再试'
          } else if (error.message?.includes('网络')) {
            errorMsg = '网络连接失败，请检查网络后重试'
          }

          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          })
        }
      }

      const handleCodeInput = (event, index) => {
        const value = event.detail.value
        const previousValue = codeArray.value[index]

        // 只允许输入数字，并只取第一个字符
        const numericValue = value.replace(/[^0-9]/g, '').slice(0, 1)
        codeArray.value[index] = numericValue

        // 如果输入了数字且不是最后一个输入框，自动聚焦到下一个
        if (numericValue && index < 3) {
          setTimeout(() => {
            const nextInput = uni.createSelectorQuery().select(`#code-input-${index + 1}`)
            nextInput
              .fields({ node: true }, res => {
                if (res && res.node) {
                  res.node.focus()
                }
              })
              .exec()
          }, 100)
        }
        // 如果是删除操作（之前有值现在没值）且不是第一个输入框，聚焦到前一个
        else if (!numericValue && previousValue && index > 0) {
          setTimeout(() => {
            const prevInput = uni.createSelectorQuery().select(`#code-input-${index - 1}`)
            prevInput
              .fields({ node: true }, res => {
                if (res && res.node) {
                  res.node.focus()
                }
              })
              .exec()
          }, 100)
        }

        // 检查验证码是否输入完毕，如果完毕则自动触发登录
        setTimeout(() => {
          const isComplete = codeArray.value.every(item => item !== '')
          if (isComplete && !loginLoading.value) {
            handleLogin()
          }
        }, 150)
      }

      const handleCodeFocus = index => {
        // 清空当前输入框的值，方便重新输入
        if (codeArray.value[index]) {
          codeArray.value[index] = ''
        }
      }

      const handleLogin = async () => {
        if (!isCodeComplete.value) {
          uni.showToast({
            title: '请输入完整的验证码',
            icon: 'none'
          })
          return
        }

        try {
          loginLoading.value = true

          // 显示登录中状态
          uni.showLoading({
            title: '登录中...'
          })

          const result = await userStore.login({
            phone: phone.value,
            code: verificationCode.value
          })

          uni.hideLoading()

          if (result.success) {
            handleLoginSuccess(result.message)
          } else {
            handleLoginError(result)
          }
        } catch (error) {
          handleLoginError(error)
        } finally {
          loginLoading.value = false
        }
      }

      const navigateToUserAgreement = () => {
        uni.navigateTo({
          url: '/pages/agreement/user'
        })
      }

      const navigateToPrivacyPolicy = () => {
        uni.navigateTo({
          url: '/pages/agreement/privacy'
        })
      }

      // 预登录功能
      const preLogin = () => {
        // 检查是否支持一键登录
        uni.getProvider({
          service: 'oauth',
          success: result => {
            if (result.provider.includes('univerify')) {
              // 支持一键登录，进行预登录
              uni.preLogin({
                provider: 'univerify',
                success: res => {
                  console.log('预登录成功:', res)
                },
                fail: error => {
                  console.log('预登录失败:', error)
                  // 预登录失败不影响用户操作，只是登录时可能稍慢
                }
              })
            }
          },
          fail: error => {
            console.log('获取服务提供商失败:', error)
          }
        })
      }

      const handleAgreePrivacy = () => {
        showPrivacyPopup.value = false
        // 存储访问状态
        uni.setStorageSync('hasVisited', true)
        // 存储用户同意状态
        uni.setStorageSync('userAgreed', true)
        // 自动勾选协议
        agreed.value = true
        preLogin()
        // 继续执行登录逻辑
        if (window.privacyResolve) {
          window.privacyResolve(true)
          window.privacyResolve = null
        }
      }

      const handleDisagreePrivacy = () => {
        showPrivacyPopup.value = false
        // 存储访问状态
        uni.setStorageSync('hasVisited', true)
        // 存储用户不同意状态
        uni.setStorageSync('userAgreed', false)
        // 这里可以添加进入访客模式的逻辑
        uni.showToast({
          title: '已进入访客模式',
          icon: 'none',
          duration: 2000
        })
        // 取消登录逻辑
        if (window.privacyResolve) {
          window.privacyResolve(false)
          window.privacyResolve = null
        }
      }

      const clearPhone = () => {
        phone.value = ''
      }

      const enterVisitorMode = () => {
        uni.setStorageSync('userAgreed', false)
        uni.showToast({
          title: '已进入游客模式',
          icon: 'none',
          duration: 2000
        })
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/login/choose'
          })
        }, 1500)
      }

      // 统一的登录成功处理方法
      const handleLoginSuccess = (message = '登录成功') => {
        // 显示成功提示
        // uni.showToast({
        //   title: message,
        //   icon: 'success'
        // })

        // 清除所有定时器
        if (countdownTimer) {
          clearInterval(countdownTimer)
          countdownTimer = null
          countdown.value = 0
        }

        // 重置页面状态
        currentStep.value = 'welcome'
        phone.value = ''
        codeArray.value = ['', '', '', '']
        agreed.value = false

        // 延迟跳转，让用户看到成功提示
        setTimeout(async () => {
          // 调用接口检查用户是否已完成标签选择
          try {
            const result = await userApi.queryDetail()
            if (result && result.data && result.data.tagDone === 1) {
              console.log('用户已完成标签选择，跳转到首页')
              // 用户已完成标签选择，更新本地状态并跳转到首页
              uni.setStorageSync('userSetupStatus', 'completed')
              uni.switchTab({
                url: '/pages/index/index'
              })
            } else {
              console.log('用户未完成标签选择，跳转到标签选择页面')
              // 用户未完成标签选择，跳转到choose页面
              uni.navigateTo({
                url: '/pages/login/choose'
              })
            }
          } catch (error) {
            console.error('检查用户标签状态失败:', error)
            // 接口调用失败，默认跳转到choose页面
            uni.navigateTo({
              url: '/pages/login/choose'
            })
          }
        }, 1500)
      }

      // 统一的登录失败处理方法
      const handleLoginError = (error, defaultMessage = '登录失败，请重试') => {
        console.error('登录失败:', error)

        // 根据错误类型提供不同的错误信息
        let errorMessage = defaultMessage

        if (error.code === 'INVALID_CODE') {
          errorMessage = '验证码错误，请重新输入'
          // 清空验证码输入框
          codeArray.value = ['', '', '', '']
          // 聚焦到第一个输入框
          setTimeout(() => {
            const firstInput = uni.createSelectorQuery().select('#code-input-0')
            firstInput
              .fields({ node: true }, res => {
                if (res && res.node) {
                  res.node.focus()
                }
              })
              .exec()
          }, 100)
        } else if (error.code === 'CODE_EXPIRED') {
          errorMessage = '验证码已过期，请重新获取'
          // 清空验证码输入框
          codeArray.value = ['', '', '', '']
          // 重置倒计时
          if (countdownTimer) {
            clearInterval(countdownTimer)
            countdownTimer = null
          }
          countdown.value = 0
        } else if (error.message?.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络后重试'
        } else if (error.message?.includes('404')) {
          errorMessage = '服务暂时不可用，请稍后重试'
        } else if (error.message?.includes('手机号')) {
          errorMessage = '获取手机号失败，请重试'
        } else if (error.message?.includes('云函数')) {
          errorMessage = '服务暂时不可用，请稍后重试'
        } else if (error.message?.includes('code')) {
          errorMessage = '微信授权失败，请重试'
        } else if (error.message?.includes('用户信息')) {
          errorMessage = '获取用户信息失败，请重试'
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      }

      // 检查协议同意状态
      const checkAgreement = () => {
        if (!agreed.value) {
          // 显示协议高亮提示
          showAgreementHighlight.value = true

          uni.showToast({
            title: '请先勾选并同意用户协议',
            icon: 'none',
            duration: 2000
          })

          // 2秒后移除高亮效果
          setTimeout(() => {
            showAgreementHighlight.value = false
          }, 2000)

          return false
        }
        return true
      }

      // 检查是否需要展示温馨提示
      const checkAndShowPrivacyTips = () => {
        return new Promise(resolve => {
          const hasVisited = uni.getStorageSync('hasVisited')
          if (!hasVisited) {
            // 首次访问，显示温馨提示
            showPrivacyPopup.value = true
            // 等待用户操作，在handleAgreePrivacy或handleDisagreePrivacy中resolve
            window.privacyResolve = resolve
          } else {
            // 已经访问过，直接继续
            resolve(true)
          }
        })
      }

      // 检查协议同意状态并处理温馨提示
      const checkAgreementAndPrivacy = () => {
        if (!agreed.value) {
          // 未勾选协议，先检查是否需要显示温馨提示
          const hasVisited = uni.getStorageSync('hasVisited')
          if (!hasVisited) {
            // 首次访问，显示温馨提示
            showPrivacyPopup.value = true
            return new Promise(resolve => {
              window.privacyResolve = resolve
            })
          } else {
            // 已经访问过，但未勾选协议，显示协议高亮提示
            showAgreementHighlight.value = true
            uni.showToast({
              title: '请先勾选并同意用户协议',
              icon: 'none',
              duration: 2000
            })
            // 2秒后移除高亮效果
            setTimeout(() => {
              showAgreementHighlight.value = false
            }, 2000)
            return Promise.resolve(false)
          }
        }
        return Promise.resolve(true)
      }

      // 检查是否需要展示启动页
      const checkStartupPage = () => {
        const startupShown = uni.getStorageSync('startupShown')
        if (!startupShown) {
          // 首次启动，跳转到启动页
          uni.redirectTo({
            url: '/pages/login/startup'
          })
          return false
        }
        return true
      }

      // 组件挂载时先检查启动页，再进行预登录准备
      onMounted(() => {
        if (checkStartupPage()) {
          preLogin()
        }
      })

      // 组件卸载时清理定时器和回调
      onUnmounted(() => {
        if (countdownTimer) {
          clearInterval(countdownTimer)
        }
        // 清理隐私提示回调
        if (window.privacyResolve) {
          window.privacyResolve = null
        }
      })

      return {
        currentStep,
        phone,
        codeArray,
        agreed,
        loading,
        loginLoading,
        countdown,
        isPhoneValid,
        maskedPhone,
        isCodeComplete,
        phoneError,
        showAgreementHighlight,
        toggleAgreement,
        goBack,
        handleUniverifyLogin,
        detectByPreLogin,
        performUniverifyLogin,
        handleWechatLogin,
        handleQQLogin,
        handleSmsLogin,
        handleSendCode,
        handleResendCode,
        handleCodeInput,
        handleCodeFocus,
        handleLogin,
        checkPhoneFormat,
        navigateToUserAgreement,
        navigateToPrivacyPolicy,
        preLogin,
        showPrivacyPopup,
        handleAgreePrivacy,
        handleDisagreePrivacy,
        clearPhone,
        enterVisitorMode,
        handlePhoneInput,
        handleLoginSuccess,
        handleLoginError,
        checkAgreement,
        checkAgreementAndPrivacy
      }
    }
  }
</script>

<style lang="scss" scoped>
  .container {
    position: fixed;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    /* #ifdef APP-PLUS */
    padding-top: var(--status-bar-height);
    /* #endif */
    &.welcome-bg {
      background-image: url('/static/index/login/bg.png');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }

    &.form-bg {
      background: #fff url('/static/index/login/bg1.png') no-repeat top center;
      background-size: 100% auto;
    }
  }

  // 欢迎页面样式
  .welcome-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 60rpx 40rpx 40rpx;
    padding-bottom: 120rpx;
    text-align: center;
    color: #2e8b57;
    box-sizing: border-box;

    .logo {
      margin-bottom: 30rpx;

      image {
        width: 120rpx;
        height: 120rpx;
      }
    }

    .slogan {
      margin-bottom: 40rpx;

      .slogan-horizontal {
        font-size: 48rpx;
        font-weight: bold;
        color: #2e8b57;
        transform: rotate(-5deg);
        margin-bottom: 30rpx;
        font-style: italic;
        height: 180px;
      }

      .slogan-vertical {
        display: inline-block;
        margin: 0 10rpx;
        vertical-align: top;
        width: 10px;
        margin: 0 30px;
        font-size: 18px;
        .slogan-line {
          font-size: 32rpx;
          color: #2e8b57;
          letter-spacing: 2rpx;
          font-weight: 500;
        }
      }
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      margin-bottom: 30rpx;

      .primary-btn {
        width: 90%;
        height: 90rpx;
        border-radius: 45rpx;
        color: #fff;
        font-weight: bold;
        box-shadow: 0 8rpx 24rpx rgba(62, 192, 108, 0.12);
        background: linear-gradient(270deg, #73d13d 0%, #34bc4d 100%);
        line-height: 48px;
        font-size: 16px;
        &:disabled {
          opacity: 0.5;
        }
      }
    }

    .divider {
      margin-bottom: 30rpx;
      font-size: 28rpx;
      color: #2e8b57;
    }

    .third-party-login {
      margin-bottom: 30rpx;
      display: flex;
      justify-content: center;
      gap: 40rpx;
      flex-wrap: wrap;

      .login-icon-item {
        width: 90rpx;
        height: 90rpx;
        border-radius: 50%;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);

        .login-icon {
          width: 90rpx;
          height: 90rpx;
          border-radius: 50%;
        }

        .iconfont {
          font-size: 48rpx;
        }

        &.wechat-item {
          background: transparent;

          .iconfont {
            color: #fff;
          }
        }

        &.qq-item {
          background: transparent;

          .iconfont {
            color: #fff;
          }
        }
      }

      .login-text {
        display: none;
      }

      // 隐藏短信登录
      // .sms-item { display: none !important; }
    }

    .agreement {
      font-size: 24rpx;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20rpx;

      checkbox {
        transform: scale(0.7);
        margin-right: 10rpx;
        accent-color: #3ec06c;
      }

      /* #ifdef APP-PLUS || H5 */
      ::v-deep .uni-checkbox-input {
        border-radius: 50% !important;
      }
      /* #endif */

      /* #ifdef MP-WEIXIN */
      ::v-deep .wx-checkbox-input {
        border-radius: 50% !important;
      }
      /* #endif */

      .link {
        color: #66d47e;
        text-decoration: underline;
        margin: 0 4rpx;
      }

      &.highlight {
        color: #fff;
        background: rgba(255, 107, 107, 0.2);
        border: 2rpx solid #ff6b6b;
        border-radius: 20rpx;
        padding: 20rpx;
        animation: shake 0.5s ease-in-out;

        .link {
          color: #ffeb3b;
        }
      }
    }

    @keyframes shake {
      0%,
      100% {
        transform: translateX(0);
      }

      25% {
        transform: translateX(-10rpx);
      }

      75% {
        transform: translateX(10rpx);
      }
    }
  }

  // 表单页面样式
  .form-page {
    min-height: 100vh;

    .page-header {
      display: flex;
      align-items: center;
      padding: 24rpx 32rpx;
      position: relative;
      border-bottom: none;

      .back-btn {
        font-size: 48rpx;
        color: #333;
        padding: 20rpx;
      }

      .page-title {
        font-size: 36rpx;
        font-weight: 500;
        color: #333;
        flex: 1;
        text-align: left;
        margin-left: 8rpx;
      }

      .visitor-mode {
        font-size: 28rpx;
        color: #666;
      }
    }

    .form-box {
      padding: 32rpx;

      .form-head {
        font-size: 38rpx;
        color: #333;
        margin-bottom: 32rpx;
        font-weight: bold;
      }

      .form-tip {
        font-size: 28rpx;
        color: #999;
        margin-bottom: 32rpx;
      }

      .phone-input-group {
        display: flex;
        align-items: center;
        border: 2rpx solid #eee;
        border-radius: 16rpx;
        padding: 24rpx 32rpx;
        margin-bottom: 32rpx;
        background-color: white;
        .area-code {
          font-size: 32rpx;
          color: #333;
          margin-right: 24rpx;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            right: -12rpx;
            top: 50%;
            transform: translateY(-50%);
            width: 2rpx;
            height: 32rpx;
            background: #eee;
          }
        }

        input {
          flex: 1;
          font-size: 32rpx;
          color: #333;
          margin-left: 24rpx;
          background: transparent;
          border: none;
        }

        .clear-btn {
          font-size: 40rpx;
          color: #999;
          padding: 10rpx;
        }
      }

      .submit-btn {
        width: 100%;
        height: 88rpx;
        line-height: 88rpx;
        background: #3ec06c;
        color: #fff;
        font-size: 32rpx;
        border-radius: 44rpx;
        margin-top: 48rpx;
        transition: all 0.3s ease;

        &:disabled {
          background: #cccccc;
          color: #ffffff;
          opacity: 1;
        }
      }
    }
  }

  .action-buttons-container {
    height: 260px;
    display: flex;
    flex-direction: column;
    // justify-content: space-evenly;
  }

  // 添加提示框样式
  .privacy-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;

    .privacy-content {
      width: 85%;
      max-width: 600rpx;
      background: #fff;
      border-radius: 24rpx;
      padding: 40rpx;
      box-sizing: border-box;

      .privacy-title {
        font-size: 36rpx;
        font-weight: bold;
        text-align: center;
        margin-bottom: 30rpx;
        color: #333;
      }

      .privacy-text {
        margin-bottom: 20rpx;
        text-align: left;

        text {
          display: block;
          margin-bottom: 20rpx;
          color: #333;
          font-size: 28rpx;
          line-height: 1.6;
        }
      }

      .privacy-desc {
        color: #666;
        font-size: 26rpx;
        line-height: 1.6;
        margin-bottom: 20rpx;
        text-align: left;
      }

      .agree-btn {
        width: 100%;
        height: 88rpx;
        line-height: 88rpx;
        background: #3ec06c;
        color: #fff;
        border-radius: 44rpx;
        margin: 30rpx 0;
        font-size: 32rpx;
      }

      .disagree-text {
        text-align: center;
        color: #999;
        font-size: 28rpx;
        padding: 20rpx;
      }

      .link {
        color: #66d47e !important;
        text-decoration: underline;
        margin: 0 4rpx;
        display: inline-block !important;
      }
    }
  }
  .privacy-content-box {
    max-height: 400rpx;
    overflow-y: auto;
  }
</style>
