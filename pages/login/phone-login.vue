<template>
	<view class="form-page">
		<page-header 
			:show-visitor="true"
			@back="goBack"
			@visitor="enterVisitorMode"
		/>
		
		<view class="form-box">
			<view class="form-head">手机号码登录</view>
			<view class="form-tip">未注册的手机号验证后自动创建用户</view>
			
			<view class="phone-input-group">
				<view class="area-code">+86</view>
				<input 
					type="number" 
					v-model="phone" 
					placeholder="请输入手机号"
					maxlength="11"
					@input="handlePhoneInput"
					@blur="checkPhoneFormat"
				/>
				<text class="clear-btn" v-if="phone" @click="clearPhone">×</text>
			</view>
			
			<button 
				class="submit-btn"
				:class="{ 'disabled': !isPhoneValid || loading }"
				:disabled="!isPhoneValid || loading"
				@click="handleSendCode"
			>
				{{ loading ? '发送中...' : '获取短信验证码' }}
			</button>
		</view>
	</view>
</template>

<script>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import PageHeader from '@/components/PageHeader.vue'

export default {
	components: {
		PageHeader
	},
	setup() {
		const userStore = useUserStore()
		const phone = ref('')
		const loading = ref(false)
		const phoneError = ref('')
		
		const isPhoneValid = computed(() => {
			const phoneNumber = phone.value.trim()
			return phoneNumber && /^1[3-9]\d{9}$/.test(phoneNumber)
		})
		
		const handlePhoneInput = (event) => {
			const value = event.detail.value
			phone.value = value.replace(/\D/g, '').slice(0, 11)
		}
		
		const checkPhoneFormat = () => {
			phoneError.value = ''
			
			if (!phone.value.trim()) {
				return
			}
			
			if (!/^1[3-9]\d{9}$/.test(phone.value)) {
				phoneError.value = '请输入正确的11位手机号'
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
			}
		}
		
		const clearPhone = () => {
			phone.value = ''
		}
		
		const handleSendCode = async () => {
			phoneError.value = ''
			
			if (!phone.value.trim()) {
				uni.showToast({
					title: '请输入手机号',
					icon: 'none'
				})
				return
			}
			
			if (!/^1[3-9]\d{9}$/.test(phone.value)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
				return
			}
			
			try {
				loading.value = true
				const result = await userStore.sendSmsCode(phone.value)
				
				if (result && result.success) {
					uni.showToast({
						title: '验证码已发送',
						icon: 'success',
						duration: 1500
					})
					// 跳转到验证码输入页面
					uni.navigateTo({
						url: `/pages/login/verify-code?phone=${phone.value}`
					})
				} else {
					uni.showToast({
						title: result?.message || '发送失败，请重试',
						icon: 'none',
						duration: 2000
					})
				}
			} catch (error) {
				console.error('发送短信验证码失败:', error)
				let errorMsg = '发送失败，请重试'
				if (error.code === 'PHONE_EXISTS') {
					errorMsg = '该手机号已被注册'
				} else if (error.code === 'SMS_LIMIT') {
					errorMsg = '发送太频繁，请稍后再试'
				} else if (error.code === 'INVALID_PHONE') {
					errorMsg = '手机号格式不正确'
				}
				uni.showToast({
					title: errorMsg,
					icon: 'none',
					duration: 2000
				})
			} finally {
				loading.value = false
			}
		}
		
		const goBack = () => {
			uni.navigateBack()
		}
		
		const enterVisitorMode = () => {
			uni.setStorageSync('userAgreed', false)
			uni.showToast({
				title: '已进入游客模式',
				icon: 'none',
				duration: 2000
			})
			setTimeout(() => {
				uni.switchTab({
					url: '/pages/index/index'
				})
			}, 1500)
		}
		
		return {
			phone,
			loading,
			phoneError,
			isPhoneValid,
			handlePhoneInput,
			checkPhoneFormat,
			clearPhone,
			handleSendCode,
			goBack,
			enterVisitorMode
		}
	}
}
</script>

<style lang="scss" scoped>
.form-page {
    background: #fff url('/static/index/login/bg1.png') no-repeat top center;
    background-size: 100% auto;
    min-height: 100vh;
	
	.form-box {
		padding: 32rpx;
		
		.form-head {
			font-size: 38rpx;
			color: #333;
			margin-bottom: 32rpx;
			font-weight: bold;
		}
		
		.form-tip {
			font-size: 28rpx;
			color: #999;
			margin-bottom: 32rpx;
		}
		
		.phone-input-group {
			display: flex;
			align-items: center;
			border: 2rpx solid #eee;
			border-radius: 16rpx;
			padding: 24rpx 32rpx;
			margin-bottom: 32rpx;
			background-color: white;
			
			.area-code {
				font-size: 32rpx;
				color: #333;
				margin-right: 24rpx;
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					right: -12rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 2rpx;
					height: 32rpx;
					background: #eee;
				}
			}
			
			input {
				flex: 1;
				font-size: 32rpx;
				color: #333;
				margin-left: 24rpx;
				background: transparent;
				border: none;
			}
			
			.clear-btn {
				font-size: 40rpx;
				color: #999;
				padding: 10rpx;
			}
		}
		
		.submit-btn {
			width: 100%;
			height: 88rpx;
			line-height: 88rpx;
			background: #3ec06c;
			color: #fff;
			font-size: 32rpx;
			border-radius: 44rpx;
			margin-top: 48rpx;
			transition: all 0.3s ease;
			
			&:disabled {
				background: #cccccc;
				color: #ffffff;
				opacity: 1;
			}
		}
	}
}
</style> 