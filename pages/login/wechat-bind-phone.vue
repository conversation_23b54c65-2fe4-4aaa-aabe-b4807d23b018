<template>
	<view class="form-page">
		<page-header title="" @back="goBack" />

		<view class="form-box">
			<view class="fb-title">绑定手机号</view>
			<view class="form-tip">首次登录需要绑定手机号，验证后自动创建用户</view>

			<view class="phone-input-group">
				<view class="area-code">+86</view>
				<input type="number" v-model="phone" placeholder="请输入手机号" maxlength="11" @input="handlePhoneInput"
					@blur="checkPhoneFormat" />
				<text class="clear-btn" v-if="phone" @click="clearPhone">×</text>
			</view>

			<button class="submit-btn" :class="{ 'disabled': !isPhoneValid || loading }"
				:disabled="!isPhoneValid || loading" @click="handleSendCode">
				{{ loading ? '发送中...' : '获取短信验证码' }}
			</button>

			<view class="visitor-mode-area">
				<text class="visitor-mode" @click="enterVisitorMode">游客进入</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onUnmounted, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { onLoad, onReady, onUnload } from '@dcloudio/uni-app'
import PageHeader from '@/components/PageHeader.vue'
import { userApi } from '@/common/api'

const userStore = useUserStore()

// 响应式数据
const phone = ref('')
const loading = ref(false)
const phoneError = ref('')
const wechatCode = ref('') // 存储微信code

// 计算属性
const isPhoneValid = computed(() => {
	const phoneNumber = phone.value.trim()
	return phoneNumber && /^1[3-9]\d{9}$/.test(phoneNumber)
})

// 方法
const goBack = () => {
	// 返回登录页面
	uni.navigateBack()
}

const checkPhoneFormat = () => {
	// 清空之前的错误提示
	phoneError.value = ''

	// 如果手机号为空，不显示错误提示
	if (!phone.value.trim()) {
		return
	}

	// 手机号格式校验
	if (!/^1[3-9]\d{9}$/.test(phone.value)) {
		phoneError.value = '请输入正确的11位手机号'
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		})
	}
}

const handlePhoneInput = (event) => {
	const value = event.detail.value
	// 限制只能输入数字，最多11位
	phone.value = value.replace(/\D/g, '').slice(0, 11)
}

const handleSendCode = async () => {
	// 清空之前的错误提示
	phoneError.value = ''

	// 手机号为空校验
	if (!phone.value.trim()) {
		uni.showToast({
			title: '请输入手机号',
			icon: 'none'
		})
		return
	}

	// 手机号格式校验
	if (!/^1[3-9]\d{9}$/.test(phone.value)) {
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		})
		return
	}

	try {
		loading.value = true
		
		// 调用发送验证码接口
		const result = await userStore.sendSmsCode(phone.value)

		if (result && result.success) {
			// 发送成功
			uni.showToast({
				title: '验证码已发送',
				icon: 'success',
				duration: 1500
			})
			
			// 跳转到验证码输入页面，传递微信绑定标识
			uni.navigateTo({
				url: `/pages/login/wechat-bind-code?phone=${phone.value}`
			})
		} else {
			// 发送失败
			uni.showToast({
				title: result?.message || '发送失败，请重试',
				icon: 'none',
				duration: 2000
			})
		}
	} catch (error) {
		console.error('发送短信验证码失败:', error)
		
		// 根据错误类型显示不同提示
		let errorMsg = '发送失败，请重试'
		
		if (error.code === 'PHONE_EXISTS') {
			errorMsg = '该手机号已被注册'
		} else if (error.code === 'SMS_LIMIT') {
			errorMsg = '发送太频繁，请稍后再试'
		} else if (error.code === 'INVALID_PHONE') {
			errorMsg = '手机号格式不正确'
		} else if (error.message?.includes('网络')) {
			errorMsg = '网络连接失败，请检查网络后重试'
		} else if (error.message?.includes('404')) {
			errorMsg = '服务暂时不可用，请稍后重试'
		}
		
		uni.showToast({
			title: errorMsg,
			icon: 'none',
			duration: 2000
		})
	} finally {
		loading.value = false
	}
}

const clearPhone = () => {
	phone.value = ''
}

const enterVisitorMode = () => {
	uni.setStorageSync('userAgreed', false)
	uni.showToast({
		title: '已进入游客模式',
		icon: 'none',
		duration: 2000
	})
	setTimeout(() => {
		uni.navigateTo({
			url: '/pages/login/choose'
		})
	}, 1500)
}

// 组件挂载时获取微信code
onMounted(() => {
	// 从本地存储获取微信code
	const storedCode = uni.getStorageSync('wechatCode')
	if (storedCode) {
		wechatCode.value = storedCode
		console.log('获取到微信code:', storedCode)
	} else {
		console.log('未找到微信code，返回登录页面')
		uni.navigateBack()
	}
})

onLoad(params => {
	try {
		// 获取传递过来的手机号
		const pages = getCurrentPages()
		if (!pages || pages.length === 0) {
			console.log('当前页面栈为空')
			return
		}

		const currentPage = pages[pages.length - 1]
		const phoneParam = params.phone

		if (phoneParam) {
			phone.value = phoneParam
		}
	} catch (error) {
		console.error('微信绑定手机号页面初始化失败:', error)
	}
})
</script>

<style lang="scss" scoped>
.form-page {
	background: #fff url('/static/index/login/bg1.png') no-repeat top center;
	background-size: 100% auto;
	min-height: 100vh;

	.form-box {
		padding: 32rpx;

		.fb-title {
			font-size: 26px;
			line-height: 60px;
			margin-top: -20px;
		}

		.form-tip {
			font-size: 28rpx;
			color: #999;
			margin-bottom: 32rpx;
		}

		.phone-input-group {
			display: flex;
			align-items: center;
			border: 2rpx solid #eee;
			border-radius: 16rpx;
			padding: 24rpx 32rpx;
			margin-bottom: 32rpx;
			background-color: white;

			.area-code {
				font-size: 32rpx;
				color: #333;
				margin-right: 24rpx;
				position: relative;

				&::after {
					content: '';
					position: absolute;
					right: -12rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 2rpx;
					height: 32rpx;
					background: #eee;
				}
			}

			input {
				flex: 1;
				font-size: 32rpx;
				color: #333;
				margin-left: 24rpx;
				background: transparent;
				border: none;
			}

			.clear-btn {
				font-size: 40rpx;
				color: #999;
				padding: 10rpx;
			}
		}

		.submit-btn {
			width: 100%;
			height: 88rpx;
			line-height: 88rpx;
			background: #3ec06c;
			color: #fff;
			font-size: 32rpx;
			border-radius: 44rpx;
			margin-top: 48rpx;
			transition: all 0.3s ease;

			&:disabled {
				background: #cccccc;
				color: #ffffff;
				opacity: 1;
			}
		}

		.visitor-mode-area {
			text-align: center;
			margin-top: 32rpx;

			.visitor-mode {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
}
</style> 