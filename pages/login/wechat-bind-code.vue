<template>
  <view class="form-page">
    <page-header title="" @back="goBack" />

    <view class="form-box">
      <view class="fb-title">输入短信验证码</view>
      <view class="code-tip">
        <text>已发送验证码至<text class="phone-number">{{ maskedPhone }}</text></text>
      </view>

      <view class="code-input-group">
        <view class="code-inputs">
          <input
            v-for="(item, index) in codeArray"
            :key="index"
            :id="`code-input-${index}`"
            type="number"
            :value="codeArray[index]"
            :focus="focusedIndex === index"
            maxlength="1"
            class="code-input"
            @input="handleCodeInput($event, index)"
            @focus="handleCodeFocus(index)"
          />
        </view>
      </view>

      <view class="resend-area">
        <text v-if="countdown > 0" class="countdown">{{ countdown }}秒后可重新获取验证码</text>
        <text v-else class="resend-btn" @click="handleResendCode">重新获取验证码</text>
      </view>

      <button class="submit-btn" :disabled="!isCodeComplete || loginLoading" @click="handleBind">
        {{ loginLoading ? '绑定中...' : '绑定手机号' }}
      </button>
    </view>
  </view>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted } from 'vue'
  import { useUserStore } from '@/stores/user'
  import { onLoad, onReady, onUnload } from '@dcloudio/uni-app'
  import PageHeader from '@/components/PageHeader.vue'
  import { userApi } from '@/common/api'
  
  const userStore = useUserStore()
  const phone = ref('')
  const codeArray = ref(['', '', '', ''])
  const loginLoading = ref(false)
  const countdown = ref(60)
  const focusedIndex = ref(-1)
  let countdownTimer = null
  
  onUnmounted(() => {
    if (countdownTimer) {
      clearInterval(countdownTimer)
    }
  })

  const maskedPhone = computed(() => {
    if (!phone.value) return ''
    return phone.value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  })

  const isCodeComplete = computed(() => {
    return codeArray.value.every(item => item !== '')
  })

  const verificationCode = computed(() => {
    return codeArray.value.join('')
  })

  const startCountdown = () => {
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }
    }, 1000)
  }

  const focusInput = (index) => {
    // 使用 focus 属性来控制焦点，这是最兼容的方式
    focusedIndex.value = index
  }

  const handleCodeInput = (event, index) => {
    const value = event.detail.value
    const previousValue = codeArray.value[index]

    const numericValue = value.replace(/[^0-9]/g, '').slice(0, 1)
    codeArray.value[index] = numericValue

    if (numericValue && index < 3) {
      // 自动聚焦到下一个输入框
      setTimeout(() => {
        focusInput(index + 1)
      }, 50)
    } else if (!numericValue && previousValue && index > 0) {
      // 删除时聚焦到前一个输入框
      setTimeout(() => {
        focusInput(index - 1)
      }, 50)
    }

    setTimeout(() => {
      const isComplete = codeArray.value.every(item => item !== '')
      if (isComplete && !loginLoading.value) {
        handleBind()
      }
    }, 150)
  }

  const handleCodeFocus = index => {
    focusedIndex.value = index
    if (codeArray.value[index]) {
      codeArray.value[index] = ''
    }
  }

  const handleResendCode = async () => {
    if (countdown.value > 0) return

    codeArray.value = ['', '', '', '']
    focusedIndex.value = 0 // 重新聚焦到第一个输入框

    try {
      const result = await userStore.sendSmsCode(phone.value)
      if (result.success) {
        uni.showToast({
          title: result.message,
          icon: 'success'
        })
        startCountdown()
      } else {
        uni.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      uni.showToast({
        title: '发送失败，请重试',
        icon: 'none'
      })
    }
  }

  const handleBind = async () => {
    if (!isCodeComplete.value) {
      uni.showToast({
        title: '请输入完整的验证码',
        icon: 'none'
      })
      return
    }

    try {
      loginLoading.value = true
      
      // 获取存储的微信code
      const wechatCode = uni.getStorageSync('wechatCode')
      if (!wechatCode) {
        throw new Error('微信授权信息丢失，请重新登录')
      }

      // 调用微信绑定手机号接口
      const result = await userStore.wechatBindPhone({
        code: wechatCode,
        phone: phone.value,
        smsCode: verificationCode.value
      })
      
      if (result.success) {
        uni.showToast({
          title: result.message,
          icon: 'success'
        })

        // 清除存储的微信code
        uni.removeStorageSync('wechatCode')

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          // 检查用户是否已完成初始设置
          if (!uni.getStorageSync('userSetupStatus')) {
            // 未完成初始设置，跳转到标签选择页面
            uni.navigateTo({
              url: '/pages/login/choose'
            })
          } else {
            // 已完成初始设置，直接跳转到首页
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        }, 1500)
      } else {
        uni.showToast({
          title: result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('微信绑定手机号失败:', error)
      uni.showToast({
        title: error.message || '绑定失败，请重试',
        icon: 'none'
      })
    } finally {
      loginLoading.value = false
    }
  }

  const goBack = () => {
    uni.navigateBack()
  }

  onLoad(params => {
    try {
      // 获取传递过来的手机号
      const pages = getCurrentPages()
      if (!pages || pages.length === 0) {
        console.log('当前页面栈为空')
        return
      }

      const currentPage = pages[pages.length - 1]
      const phoneParam = params.phone

      if (phoneParam) {
        phone.value = phoneParam
        startCountdown()
        // 页面加载后自动聚焦到第一个输入框
        setTimeout(() => {
          focusedIndex.value = 0
        }, 300)
      } else {
        console.log('未获取到手机号参数')
      }
    } catch (error) {
      console.error('微信绑定验证码页面初始化失败:', error)
    }
  })
</script>

<style lang="scss" scoped>
  .form-page {
    background: #fff url('/static/index/login/bg1.png') no-repeat top center;
    background-size: 100% auto;
    min-height: 100vh;

    .form-box {
      padding: 32rpx;

      .code-tip {
        font-size: 28rpx;
        color: #999;
        margin-bottom: 48rpx;
      }

      .code-input-group {
        margin-bottom: 48rpx;

        .code-inputs {
          display: flex;
          justify-content: space-between;
          gap: 24rpx;

          .code-input {
            width: 100rpx;
            height: 100rpx;
            border: 2rpx solid #eee;
            background-color: white;
            border-radius: 16rpx;
            text-align: center;
            font-size: 40rpx;
            color: #333;

            &:focus {
              border-color: #3ec06c;
            }
          }
        }
      }

      .resend-area {
        margin-bottom: 48rpx;

        .countdown {
          font-size: 28rpx;
          color: #999;
        }

        .resend-btn {
          font-size: 28rpx;
          color: #3ec06c;
        }
      }

      .submit-btn {
        width: 100%;
        height: 88rpx;
        line-height: 88rpx;
        background: #3ec06c;
        color: #fff;
        font-size: 32rpx;
        border-radius: 44rpx;
        margin-top: 48rpx;
        transition: all 0.3s ease;

        &:disabled {
          background: #cccccc;
          color: #ffffff;
          opacity: 1;
        }
      }
    }
  }
  .phone-number {
    color: black;
  }
  .fb-title {
    font-size: 26px;
    line-height: 60px;
    margin-top: -20px;
  }
</style> 