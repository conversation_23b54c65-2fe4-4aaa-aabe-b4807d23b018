<!--
	标签选择页面
	用户登录后选择感兴趣的标签，帮助系统推荐内容
	
	开发者：diwanjian
	最后更新：2024年
-->
<template>
	<view class="container">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<uni-icons type="left" size="24" color="#ffffff" class="back-icon"></uni-icons>
				<!-- <text class="back-text">返回</text> -->
			</view>
			<text class="skip-btn" @click="handleSkip">跳过</text>
		</view>

		<view class="content">
			<!-- 提示文本 -->
			<view class="title-text">
				选择兴趣，帮你推荐最热最有趣的博主
			</view>

			<!-- 标签云 -->
			<view class="tag-cloud">
				<view
					v-for="(tag, index) in displayTags"
					:key="index"
					class="tag-bubble"
					:class="{ active: selectedTags.includes(tag) }"
					:style="getTagStyle(index)"
					@click="toggleTag(tag)"
				>
					{{ tag }}
					<image v-if="selectedTags.includes(tag)" class="heart-icon" src="/static/images/heart-white.png" />
				</view>
			</view>
		</view>

		<!-- 底部区域 -->
		<view class="footer">
			<!-- 已选标签 -->
			<view class="selected-tags-container" v-if="selectedTags.length > 0">
				<scroll-view scroll-x="true" class="selected-tags-scroll" :show-scrollbar="false">
					<view class="selected-tags">
						<view
							v-for="(tag, index) in selectedTags"
							:key="index"
							class="selected-tag-item"
						>
							{{ tag }}
							<image class="close-icon" src="/static/index/close.png" @click="toggleTag(tag)"></image>
						</view>
					</view>
				</scroll-view>
			</view>
			
			<!-- 换一批 -->
			<view class="refresh-container">
				<image src="/static/images/plane.png" class="plane-icon" />
				<view class="refresh-btn" @click="refreshTags">
					换一批
				</view>
			</view>

			<!-- 开启按钮 -->
			<button class="start-btn" :disabled="selectedTags.length === 0" @click="handleStart">
				开启小岛屿之旅 (已选 {{ selectedTags.length }} 个)
			</button>
		</view>
	</view>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { userApi } from '@/common/api'

export default {
	setup() {
		const userStore = useUserStore()
		const selectedTags = ref([])
		const displayTags = ref([])
		const allTags = ref([])

		const fetchTags = async () => {
			try {
				const res = await userStore.queryAllTags()
				// 真实返回的数据结构是 { code: 200, data: [{ groupName: '...', options: [{ id: ..., option: '...' }] }] }
				if (res.data && res.data.length > 0) {
					const tags = res.data.flatMap(group => 
						group.options
							.filter(opt => opt.option !== '其他') // 过滤掉"其他"标签
							.map(opt => opt.option)
					)
					allTags.value = tags
					refreshTags()
				}
			} catch (error) {
				console.error('获取标签列表失败:', error)
				uni.showToast({
					title: '获取标签失败，请检查网络',
					icon: 'none'
				})
			}
		}

		const refreshTags = () => {
			if(allTags.value.length === 0) return;
			const shuffled = [...allTags.value].sort(() => 0.5 - Math.random())
			displayTags.value = shuffled.slice(0, 10)
		}

		onMounted(() => {
			fetchTags()
		})

		const toggleTag = (tag) => {
			const index = selectedTags.value.indexOf(tag)
			if (index === -1) {
				if (selectedTags.value.length < 10) {
					selectedTags.value.push(tag)
				} else {
					uni.showToast({
						title: '最多选择10个标签',
						icon: 'none'
					})
				}
			} else {
				selectedTags.value.splice(index, 1)
			}
		}
		
		const getTagStyle = (index) => {
			const minSize = 130
			const maxSize = 180
			const size = Math.random() * (maxSize - minSize) + minSize
			
			// 松散云朵式布局
			const containerWidth = 750 // 容器宽度
			const containerHeight = 750 // 容器高度
			const totalTags = displayTags.value.length
			
			// 计算每个标签的实际宽度
			const calculateTagWidth = (tagText) => {
				const baseWidth = 50
				const charWidth = 28
				return Math.max(120, tagText.length * charWidth + baseWidth)
			}
			
			const tagText = displayTags.value[index] || ''
			const tagWidth = calculateTagWidth(tagText)
			const tagHeight = size / 2
			
			// 预定义网格系统，确保分布均匀
			const cols = 3 // 3列，创造更松散的布局
			const rows = Math.ceil(totalTags / cols) // 根据标签数量计算行数
			
			// 计算网格位置
			const row = Math.floor(index / cols)
			const col = index % cols
			
			// 基础网格位置
			const gridX = (containerWidth / (cols + 1)) * (col + 1)
			const gridY = (containerHeight / (rows + 1)) * (row + 1)
			
			// 添加随机偏移，创造自然分散效果
			const maxOffsetX = 120 // X方向最大偏移，增大偏移范围
			const maxOffsetY = 80 // Y方向最大偏移，增大偏移范围
			
			// 使用固定种子确保位置一致性
			const seedX = (index * 17 + 23) % 100 / 100 // 伪随机种子
			const seedY = (index * 31 + 47) % 100 / 100 // 伪随机种子
			
			const offsetX = (seedX - 0.5) * maxOffsetX * 2
			const offsetY = (seedY - 0.5) * maxOffsetY * 2
			
			let x = gridX + offsetX
			let y = gridY + offsetY
			
			// 边界检查，确保标签不会超出容器
			const margin = 80 // 边距，留出更多空间
			x = Math.max(margin + tagWidth / 2, Math.min(containerWidth - margin - tagWidth / 2, x))
			y = Math.max(margin + tagHeight / 2, Math.min(containerHeight - margin - tagHeight / 2, y))
			
			// 碰撞检测优化（简化版）
			// 对于靠得太近的标签进行微调
			const otherTagPositions = []
			for (let i = 0; i < index; i++) {
				const otherRow = Math.floor(i / cols)
				const otherCol = i % cols
				const otherGridX = (containerWidth / (cols + 1)) * (otherCol + 1)
				const otherGridY = (containerHeight / (rows + 1)) * (otherRow + 1)
				
				const otherSeedX = (i * 17 + 23) % 100 / 100
				const otherSeedY = (i * 31 + 47) % 100 / 100
				const otherOffsetX = (otherSeedX - 0.5) * maxOffsetX * 2
				const otherOffsetY = (otherSeedY - 0.5) * maxOffsetY * 2
				
				otherTagPositions.push({
					x: otherGridX + otherOffsetX,
					y: otherGridY + otherOffsetY
				})
			}
			
			// 检查与其他标签的距离，如果太近则调整
			const minDistance = 140 // 最小距离，确保松散效果
			for (const otherPos of otherTagPositions) {
				const distance = Math.sqrt((x - otherPos.x) ** 2 + (y - otherPos.y) ** 2)
				if (distance < minDistance) {
					// 沿着远离的方向移动
					const angle = Math.atan2(y - otherPos.y, x - otherPos.x)
					const moveDistance = minDistance - distance + 10
					x += Math.cos(angle) * moveDistance
					y += Math.sin(angle) * moveDistance
					
					// 再次边界检查
					x = Math.max(margin + tagWidth / 2, Math.min(containerWidth - margin - tagWidth / 2, x))
					y = Math.max(margin + tagHeight / 2, Math.min(containerHeight - margin - tagHeight / 2, y))
				}
			}
			
			return {
				height: `${size / 2}rpx`,
				'line-height': `${size / 2}rpx`,
				position: 'absolute',
				left: `${x}rpx`,
				top: `${y}rpx`,
				transform: 'translate(-50%, -50%)',
			}
		}

		const goBack = () => {
			uni.navigateBack()
		}

		const handleSkip = () => {
			uni.switchTab({
				url: '/pages/index/index'
			})
		}

		const handleStart = async () => {
			if (selectedTags.value.length === 0) {
				uni.showToast({
					title: '请至少选择一个标签',
					icon: 'none'
				})
				return
			}

			try {
				console.log('开始保存标签:', selectedTags.value)
				
				// 先保存标签到后端
				const commitResult = await userStore.commitTags(selectedTags.value)
				console.log('标签提交结果:', commitResult)
				
				// 再保存用户设置状态
				const saveResult = await userStore.saveUserTags(selectedTags.value)
				console.log('用户设置状态保存结果:', saveResult)
				console.log('保存后的用户设置状态:', userStore.getUserSetupStatus())
				
				if (saveResult.success) {
					// 标记用户标签选择完成
					try {
						const tagDoneResult = await userApi.tagDone()
						console.log('标记标签完成结果:', tagDoneResult)
					} catch (error) {
						console.error('标记标签完成失败:', error)
						// 这里不阻断流程，继续执行
					}
					
					uni.showToast({
						title: '保存成功',
						icon: 'success',
						duration: 1500
					})
					
					// 延迟跳转到主页面
					setTimeout(() => {
						console.log('准备跳转到主页面')
						uni.switchTab({
							url: '/pages/index/index'
						})
					}, 1500)
				} else {
					uni.showToast({
						title: saveResult.message || '保存失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('保存标签失败:', error)
				uni.showToast({
					title: '保存失败，请重试',
					icon: 'none'
				})
			}
		}

		return {
			selectedTags,
			displayTags,
			toggleTag,
			goBack,
			handleSkip,
			handleStart,
			refreshTags,
			getTagStyle,
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	width: 100vw;
	height: 100vh;
	background-image: url('/static/images/choose-bg.png');
	background-size: cover;
	background-position: center;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}

/* #ifdef APP-PLUS */
.container {
	padding-top: var(--status-bar-height);
}
/* #endif */

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 32rpx;
	height: 88rpx;
	box-sizing: border-box;

	.back-btn {
		display: flex;
		align-items: center;
		gap: 8rpx;
		padding: 8rpx 12rpx;
		// background: rgba(255, 255, 255, 0.1);
		border-radius: 20rpx;
		transition: all 0.2s ease;
		
		&:active {
			background: rgba(255, 255, 255, 0.2);
			transform: scale(0.95);
		}
		
		.back-icon {
			width: 24rpx;
			height: 24rpx;
		}
		
		.back-text {
			font-size: 28rpx;
			color: #ffffff;
		}
	}

	.skip-btn {
		font-size: 28rpx;
		color: #ffffff;
		background: rgba(0, 0, 0, 0.2);
		padding: 8rpx 20rpx;
		border-radius: 20rpx;
	}
}

.content {
	flex: 1;
	padding: 0 32rpx;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	
	.title-text {
		font-size: 30rpx;
		color: #ffffff;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
		margin-bottom: 40rpx;
		padding: 0 20rpx;
	}

	.tag-cloud {
		position: relative;
		width: 100%;
		height: 750rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		
		.tag-bubble {
			padding: 0 20rpx;
			font-size: 28rpx;
			color: white;
			background-image: url('/static/index/login/pop.png');
			background-size: 100% 100%;
			background-repeat: no-repeat;
			background-position: center;
			transition: all 0.2s ease-in-out;
			display: flex;
			justify-content: center;
			align-items: center;
			text-align: center;
			width: auto;
			min-width: 120rpx;
			padding-bottom: 6px;
			white-space: nowrap;
			box-sizing: border-box;
			
			&.active {
				background-image: url('/static/index/login/pop-active.png');
				color: #ffffff;
				transform: scale(1.05);
			}

			.heart-icon {
				width: 24rpx;
				height: 24rpx;
				margin-left: 8rpx;
				position: absolute;
				right: 8rpx;
				top: 50%;
				transform: translateY(-50%);
			}
		}
	}
}

.footer {
	padding: 20rpx 32rpx;
	padding-bottom: 40rpx;

	.selected-tags-container {
		margin-bottom: 20rpx;
		height: 60rpx;

		.selected-tags-scroll {
			width: 100%;
			white-space: nowrap;
			
			.selected-tags {
				display: flex;
				gap: 16rpx;
			}
		}

		.selected-tag-item {
			display: inline-flex;
			align-items: center;
			color: black;
			padding: 8rpx 16rpx;
			font-size: 24rpx;

			background: linear-gradient( 180deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.4) 100%);
			box-shadow: 0px 0px 2px 1px rgba(0,0,0,0.04);
			border-radius: 20px 20px 20px 20px;
			border: 1px solid rgba(255,255,255,0.6);

			.close-icon {
				width: 12rpx;
				height: 12rpx;
				margin-left: 8rpx;
			}
		}
	}

	.refresh-container {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
		gap: 8rpx;

		.plane-icon {
			width: 60rpx;
			height: 60rpx;
		}

		.refresh-btn {
			background: rgba(0, 0, 0, 0.3);
			color: #fff;
			padding: 12rpx 24rpx;
			border-radius: 30rpx;
			font-size: 28rpx;
		}
	}

	.start-btn {
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		background: linear-gradient(90deg, #3DE089, #2BBD75);
		color: #ffffff;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 48rpx;
		border: none;
		box-shadow: 0 8rpx 16rpx rgba(61, 224, 137, 0.3);
		
		&[disabled] {
			background: #BEEDC5;
			color: white;
			box-shadow: none;
		}
	}
}
</style> 