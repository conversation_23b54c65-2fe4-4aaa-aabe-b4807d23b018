<template>
  <view class="chat-page">
    <!-- 状态栏占位 -->
    <view class="status-bar-placeholder"></view>

    <!-- 自定义导航栏 -->
    <view class="custom-nav-bar">
      <view class="nav-left" @tap="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
      <view class="nav-title">{{ displayTitle }}</view>
      <view class="nav-right" v-if="channelType === 2" @tap="viewGroupMembers">
        <text class="nav-menu-icon">···</text>
      </view>
      <view class="nav-right" v-else></view>
    </view>
    <!-- 聊天列表 -->
    <view class="chat-container" :class="{ 'panel-expanded': showEmojiPanel || showMorePanel }">
      <chat-list
        ref="chatListRef"
        :isKeyboardVisible="isKeyboardVisible"
        :messages="messages"
        :loading="loading"
        :channel-type="channelType"
        :channel-id="channelID"
        @load-more="handleLoadMore"
        @image-click="handleImageClick"
        @voice-click="handleVoiceClick"
        @location-click="handleLocationClick"
        @atUser="handleAtUserFromList"
      />
    </view>

    <!-- 底部输入区域 -->
    <view class="input-container" :style="inputContainerStyle">
      <!-- 输入框区域 -->
      <view class="input-wrapper">
        <!-- 语音/键盘切换按钮 -->
        <!-- <view class="voice-btn" @tap="toggleVoiceInput">
          <text v-if="!showVoiceInput" class="btn-text">🎤</text>
          <text v-else class="btn-text">⌨️</text>
        </view> -->

        <!-- 文本输入框（带表情后缀） -->
        <view v-if="!showVoiceInput" class="text-input-wrapper">
          <!-- @用户选择面板 -->
          <at-user-panel
            v-if="showAtPanel"
            :visible="showAtPanel"
            :keyword="atKeyword"
            @select="handleAtUserSelect"
            @close="closeAtPanel"
          />
          <textarea
            v-model="inputText"
            class="text-input"
            :style="textareaStyle"
            placeholder="发消息..."
            :adjust-position="false"
            :cursor-spacing="isAndroid ? 40 : 20"
            :show-confirm-bar="false"
            confirm-type="send"
            :confirm-hold="false"
            @confirm="sendMessage"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
            @keydown="handleKeyDown"
            auto-height
            :maxlength="1000"
            hold-keyboard="true"
          />
        </view>

        <!-- 语音录制按钮 -->
        <view v-else class="voice-record-wrapper">
          <view
            class="voice-record-btn"
            :class="{ recording: isRecording }"
            @touchstart="startRecord"
            @touchend="endRecord"
            @touchcancel="cancelRecord"
          >
            {{ isRecording ? '松开 发送' : '按住 说话' }}
          </view>
          <!-- 表情按钮（语音模式下也显示） -->
          <view class="emoji-btn" @tap="toggleEmojiPanel">
            <text class="btn-text">😀</text>
          </view>
        </view>

        <!-- 发送按钮 -->
        <!-- <view class="send-btn" @tap="sendMessage">
          <text>发送</text>
        </view> -->

        <!-- 更多功能按钮 -->
        <view class="more-btn" @tap="toggleEmojiPanel">
          <image src="/static/message/smile.png" class="input-icon" />
        </view>
        <view class="more-btn" @tap="toggleMorePanel">
          <image src="/static/message/plus.png" class="input-icon" />
        </view>
      </view>

      <!-- 表情面板 -->
      <view v-if="showEmojiPanel" class="emoji-panel">
        <scroll-view class="emoji-scroll" scroll-y>
          <view class="emoji-list">
            <text
              v-for="(emoji, index) in emojiList"
              :key="index"
              class="emoji-item"
              @tap="insertEmoji(emoji)"
              >{{ emoji }}</text
            >
          </view>
        </scroll-view>
        <!-- 发送按钮 -->
        <view class="emoji-send-btn" @tap="sendMessage">
          <text>发送</text>
        </view>
      </view>

      <!-- 更多功能面板 -->
      <view v-if="showMorePanel" class="more-panel">
        <view class="more-grid">
          <view class="more-item" @tap="selectImage">
            <view class="more-icon">
              <image src="/static/message/photo.png" style="width: 56rpx; height: 48rpx;" />
            </view>
            <text>相册</text>
          </view>
          <view class="more-item" @tap="takePhoto">
            <view class="more-icon">
              <image src="/static/message/camera.png" style="width: 56rpx; height: 48rpx;" />
            </view>
            <text>拍摄</text>
          </view>
          <view class="more-item" @tap="selectLocation">
            <view class="more-icon">
              <image src="/static/message/location.png" style="width: 46rpx; height: 56rpx;" />
            </view>
            <text>位置</text>
          </view>
          <!-- <view class="more-item" @tap="selectFile">
            <view class="more-icon">
              <text class="icon-text">📁</text>
            </view>
            <text>文件</text>
          </view> -->
        </view>
      </view>
    </view>

    <!-- 测试工具区域 -->
    <!-- <view class="test-tools" v-if="showTestTools">
      <view class="test-input-area">
        <input
          type="text"
          v-model="testMessageId"
          class="test-input"
          placeholder="输入消息ID进行定位测试"
        />
        <button class="test-button" @tap="testScrollToMessage">测试定位</button>
        <button class="test-button get-id" @tap="getRandomMessageId">获取ID</button>
      </view>
      <view class="test-scroll-area">
        <text>滚动位置测试：</text>
        <button class="test-button" @tap="testScrollPosition(1000)">1000</button>
        <button class="test-button" @tap="testScrollPosition(2000)">2000</button>
        <button class="test-button" @tap="testScrollPosition(3000)">3000</button>
        <button class="test-button" @tap="testScrollPosition(4000)">4000</button>
        <button class="test-button" @tap="testScrollPosition(5000)">5000</button>
      </view>
      <view class="test-info" v-if="testInfo">
        <text>{{ testInfo }}</text>
      </view>
      <button class="test-toggle" @tap="showTestTools = false">隐藏测试工具</button>
    </view>
    <button v-else class="test-toggle show" @tap="showTestTools = true">显示测试工具</button> -->

    <!-- 语音录制遮罩 -->
    <view v-if="isRecording" class="recording-overlay">
      <view class="recording-modal">
        <image src="/static/record.png" class="recording-icon" />
        <text class="recording-text">{{ recordingText }}</text>
        <view class="recording-tips">上滑取消发送</view>
      </view>
    </view>

    <!-- 调试信息（测试期间显示键盘高度变化） -->
    <!-- <view v-if="isKeyboardVisible" class="debug-keyboard-info">
      <text>原始键盘高度: {{ keyboardHeight }}px</text>
      <text>调整后偏移: {{ Math.min(120, keyboardHeight * 0.25) }}px</text>
      <text>调整策略: 固定120px 或 25%，取较小值</text>
    </view> -->
  </view>
</template>

<script setup>
  import { ref, computed, watch, onUnmounted, nextTick } from 'vue'
  import { formatTime } from '@/utils/date'
  import WKIMManager from '@/utils/wukongim'
  import { useUserStore } from '@/stores/user'
  import { useGroupStore } from '@/stores/group'
  import { storeToRefs } from 'pinia'
  import { onLoad, onReady, onUnload } from '@dcloudio/uni-app'
  import ChatList from './components/ChatList.vue'
  import AtUserPanel from './components/AtUserPanel.vue'
  import { useConversationStore } from '@/stores/conversation'
  import { conversationApi, groupsApi } from '@/common/api'
  // 初始化store
  const store = useUserStore()
  const { userInfo } = storeToRefs(store)
  const conversationStore = useConversationStore()
  const groupStore = useGroupStore()
  const userList = ref(Array.from(new Set()))
  // 页面参数
  const channelID = ref('')
  const channelType = ref(1)
  const nickName = ref('')
  const groupCode = ref('')
  const memberCount = ref(0)
  const displayTitle = computed(() => {
    if (channelType.value == 2) {
      return nickName.value + `(${memberCount.value})`
    }
    return nickName.value
  })
  // 消息相关
  const loading = ref(false)
  const hasMore = ref(true) // 是否有更多历史消息

  // 键盘相关
  const keyboardHeight = ref(0)
  const isAndroid = ref(false)
  const isKeyboardVisible = ref(false)

  // 计算 textarea 的动态样式
  const textareaStyle = computed(() => {
    return {
      position: 'relative',
      transition: 'all 0.3s ease'
    }
  })

  // 计算输入容器的动态样式
  const inputContainerStyle = computed(() => {
    if (!isKeyboardVisible.value || keyboardHeight.value <= 0) {
      return {
        position: 'relative',
        bottom: '0px',
        transition: 'bottom 0.3s ease'
      }
    }

    // 调整策略：只需要稍微推移，让输入框刚好露出键盘即可
    // 使用固定的小距离推移，而不是按键盘高度比例
    const adjustedHeight = Math.min(
      120, // 固定推移120px（之前217px太大了）
      keyboardHeight.value * 0.25 // 或键盘高度的25%，取较小值
    )

    // return {
    //   position: 'relative',
    //   bottom: `${keyboardHeight.value}px`,
    //   transition: 'bottom 0.3s ease'
    // }
  })

  // 使用计算属性获取消息
  const messages = ref([])

  // 输入相关
  const inputText = ref('')
  const showVoiceInput = ref(false)
  const showEmojiPanel = ref(false)
  const showMorePanel = ref(false)

  // @用户相关
  const showAtPanel = ref(false)
  const atKeyword = ref('')
  const atPosition = ref(-1)
  const atUsers = ref([])

  // 检测是否输入了@符号
  const handleKeyDown = e => {
    // 注意：在移动端可能无法捕获keydown事件
    console.log('keydown event:', e)
    if (e.key === '@' && channelType.value === 2) {
      e.preventDefault() // 阻止默认行为
      showAtPanel.value = true
      atKeyword.value = ''
    }
  }

  // 关闭@面板
  const closeAtPanel = () => {
    showAtPanel.value = false
  }

  watch(inputText, (newText, oldText) => {
    console.log('inputText changed:', newText, oldText)

    // 检查是否删除了一个空格，且最后一个字符不是空格
    if (
      oldText &&
      newText &&
      oldText.length === newText.length + 1 &&
      oldText.endsWith(' ') &&
      !newText.endsWith(' ')
    ) {
      // 查找最后一个@符号的位置
      const lastAtIndex = newText.lastIndexOf('@')
      if (lastAtIndex >= 0) {
        // 删除从@开始到当前位置的所有内容
        inputText.value = newText.substring(0, lastAtIndex)
        showAtPanel.value = false
        return
      }
    }

    // 检查是否输入了@符号
    if (newText.endsWith('@') && channelType.value === 2) {
      // 检查是否已经有@符号在输入中
      const atCount = (newText.match(/@/g) || []).length
      const lastAtIndex = newText.lastIndexOf('@')

      // 如果这是第一个@符号，或者前一个@符号后面已经有内容，则显示面板
      if (atCount === 1 || (lastAtIndex > 0 && newText.substring(0, lastAtIndex).includes(' @'))) {
        showAtPanel.value = true
        atKeyword.value = ''

        // 使用setTimeout确保键盘能正常关闭
        // 这是因为uni.hideKeyboard()可能需要在DOM更新后执行
        setTimeout(() => {
          uni.hideKeyboard()
          console.log('键盘已关闭')
        }, 100)
      } else {
        // 如果已经有一个@符号且没有内容，则移除新输入的@符号
        inputText.value = newText.substring(0, newText.length - 1)
      }
      return
    }

    // 如果@面板已打开，更新搜索关键词
    if (showAtPanel.value) {
      // 如果输入了空格，关闭面板
      if (newText.endsWith(' ')) {
        showAtPanel.value = false
        return
      }

      // 获取最后一个@后面的内容作为搜索关键词
      const lastAtIndex = newText.lastIndexOf('@')
      if (lastAtIndex >= 0) {
        const searchText = newText.substring(lastAtIndex + 1)
        atKeyword.value = searchText
      } else {
        showAtPanel.value = false
      }
    }
  })

  // 处理选择@的用户
  const handleAtUserSelect = user => {
    // 关闭@面板
    showAtPanel.value = false

    // 构建@用户文本，只显示用户名，但在零宽空格前添加用户ID作为标记
    const atText = `@${user.name}\u200B `

    // 删除输入框中的@符号
    const lastAtIndex = inputText.value.lastIndexOf('@')
    if (lastAtIndex >= 0) {
      inputText.value = inputText.value.substring(0, lastAtIndex)
    }

    // 在当前输入框末尾添加@用户
    inputText.value += atText

    // 添加到@用户列表
    groupStore.addAtUser({
      ...user,
      atText: `@${user.name} ` // 存储不带标记的@文本，用于发送消息时替换
    })

    // 使用延时确保面板关闭后再重新获取焦点
    setTimeout(() => {
      // #ifdef H5
      const textareaEl = document.querySelector('.text-input')
      if (textareaEl) {
        textareaEl.focus()
      }
      // #endif

      // #ifdef APP-PLUS || MP-WEIXIN
      const textareaComponent = uni.createSelectorQuery().select('.text-input')
      if (textareaComponent) {
        textareaComponent
          .context(res => {
            if (res && res.context) {
              res.context.focus()
            }
          })
          .exec()
      }
      // #endif
    }, 300)
  }

  // 处理聊天列表中长按头像@用户
  const handleAtUserFromList = user => {
    // 关闭@面板
    showAtPanel.value = false

    // 构建@用户文本，只显示用户名，但在零宽空格前添加用户ID作为标记
    const atText = `@${user.name}\u200B `

    // 检查是否已经有@符号在输入中
    const lastAtIndex = inputText.value.lastIndexOf('@')
    if (lastAtIndex >= 0 && !inputText.value.substring(lastAtIndex).includes(' ')) {
      // 如果已经有@符号且没有内容，则删除它
      inputText.value = inputText.value.substring(0, lastAtIndex)
    }

    // 在当前输入框末尾添加@用户
    inputText.value += atText

    // 添加到@用户列表
    groupStore.addAtUser({
      ...user,
      atText: `@${user.name} ` // 存储不带标记的@文本，用于发送消息时替换
    })

    // 使用延时确保面板关闭后再重新获取焦点
    setTimeout(() => {
      // #ifdef H5
      const textareaEl = document.querySelector('.text-input')
      if (textareaEl) {
        textareaEl.focus()
      }
      // #endif

      // #ifdef APP-PLUS || MP-WEIXIN
      const textareaComponent = uni.createSelectorQuery().select('.text-input')
      if (textareaComponent) {
        textareaComponent
          .context(res => {
            if (res && res.context) {
              res.context.focus()
            }
          })
          .exec()
      }
      // #endif
    }, 300)
  }

  // 语音录制相关
  const isRecording = ref(false)
  const recordingText = ref('正在录音...')
  const recorderManager = ref(null)
  const getMessages = async () => {
    messages.value = []
    // 如果聊天列表组件已加载，重置@消息计数器
    if (chatListRef.value && typeof chatListRef.value.resetAtMessageCount === 'function') {
      chatListRef.value.resetAtMessageCount()
    }

    const historyMessages = await WKIMManager.syncMessages(
      {
        channelID: channelID.value,
        channelType: channelType.value
      },
      {
        start_message_seq: 0,
        end_message_seq: 0,
        limit: 20,
        pullMode: 0
      }
    )
    hasMore.value = historyMessages.hasMore == 1
    messages.value = historyMessages.messages
    const userIds = Array.from(new Set(messages.value.map(item => item.fromUID)))
    const usersRes = await conversationApi.getUserInfo(userIds)
    messages.value.forEach(async item => {
      item.avatar = usersRes.data[item.fromUID].img.includes('http')
        ? usersRes.data[item.fromUID].img
        : 'http://47.123.3.183:9000/' + usersRes.data[item.fromUID].img
      item.nickname = usersRes.data[item.fromUID].nickname
      debugger
      // 根据contentType判断消息类型
      let messageType = 'text'
      if (item.content) {
        switch (item.content.type) {
          case 1:
            messageType = 'text'
            break
          case 2:
            messageType = 'image'
            break
          case 101:
            messageType = 'location'
            break
          default:
            messageType = 'text'
        }
      }

      // 确保每条消息都有metadata字段
      if (!item.metadata) {
        item.metadata = {}
      }

      // 设置消息类型
      item.type = messageType
    })

    // 清除未读消息
    conversationApi.clearUnread({
      uid: userInfo.value.userId,
      channel_id: channelID.value,
      channel_type: channelType.value,
      unread: 0
    })

    // 确保消息加载完成后滚动到底部
    nextTick(() => {
      setTimeout(() => {
        scrollToBottom()
        console.log('消息加载完成，滚动到底部')
      }, 500)
    })
  }
  // 系统信息
  const systemInfo = uni.getSystemInfoSync()
  const statusBarHeight = systemInfo.statusBarHeight || 0

  // 设置自定义导航栏高度
  const setCustomNavBarHeight = () => {
    // 动态设置CSS变量
    // #ifdef H5
    document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight}px`)
    // #endif

    // 非H5环境下，直接使用变量，在样式中通过计算处理
    console.log('系统信息:', systemInfo)
    console.log('状态栏高度:', statusBarHeight)
    console.log('是否安卓:', systemInfo.platform === 'android')
    if (systemInfo.platform === 'android' && systemInfo.safeArea) {
      console.log('安全区域:', systemInfo.safeArea)
    }
  }

  // 页面加载时获取路由参数
  onLoad(params => {
    // 设置自定义导航栏高度
    setCustomNavBarHeight()

    channelID.value = params.channelID || ''
    channelType.value = parseInt(params.channelType) || 1
    nickName.value = params.nickName || ''
    groupCode.value = params.groupCode || ''
    memberCount.value = params.memberCount || 0
    getMessages()

    // 设置当前会话
    conversationStore.setCurrentConversation({
      channelID: channelID.value,
      channelType: channelType.value,
      title: nickName.value
    })

    // 如果是群聊，加载群成员信息
    if (channelType.value === 2 && groupCode.value) {
      // 设置当前群组ID并加载群成员
      groupStore.setCurrentGroupCode(groupCode.value)
    }

    // 清除未读消息
    conversationStore.clearUnread(channelID.value, channelType.value)
  })

  // 页面初次渲染完成后初始化
  onReady(() => {
    // 初始化语音管理器
    // initRecorderManager()
    WKIMManager.addMessageListener(handleMessageReceived)
    // 检测平台
    const sysInfo = uni.getSystemInfoSync()
    isAndroid.value = sysInfo.platform === 'android'
  })

  // 移除复杂的键盘监听逻辑，现在依赖manifest.json中的adjustPan配置

  // 页面卸载时清理资源
  onUnload(() => {
    // 清理会话数据
    conversationStore.clearCurrentConversation()
  })

  // 组件卸载时清理资源 - 现在不需要特殊处理

  const handleMessageReceived = async message => {
    console.log('🔍 收到消息:', message)
    console.log('🔍 收到消息:', message.content)
    const usersRes = await conversationApi.getUserInfo([message.fromUID])
    message.avatar = usersRes.data[message.fromUID].img.includes('http')
      ? usersRes.data[message.fromUID].img
      : 'http://47.123.3.183:9000/' + usersRes.data[message.fromUID].img
    message.nickname = usersRes.data[message.fromUID].nickname
    if (
      message.channel.channelID === channelID.value &&
      message.channel.channelType === channelType.value
    ) {
      const existingIndex = messages.value.findIndex(
        msg =>
          msg.sending &&
          msg.content === message.content &&
          msg.fromUID === message.fromUID &&
          msg.messageType === message.messageType
      )

      if (existingIndex > -1) {
        messages.value[existingIndex] = {
          ...message,
          timestamp: message.timestamp * 1000,
          sending: false
        }
      } else {
        // 根据contentType判断消息类型
        let messageType = 'text'
        if (message.content && message.content.contentType) {
          switch (message.content.contentType) {
            case 1:
              messageType = 'text'
              break
            case 2:
              messageType = 'image'
              // 处理图片尺寸
              if (message.content.width && message.content.height) {
                const maxWidth = 160 // 最大宽度改为160px
                const maxHeight = 120 // 最大高度改为120px
                const ratio = Math.min(
                  maxWidth / message.content.width,
                  maxHeight / message.content.height
                )
                message.content.displayWidth = Math.floor(message.content.width * ratio)
                message.content.displayHeight = Math.floor(message.content.height * ratio)
              }
              break
            case 101:
              messageType = 'location'
              break
            default:
              messageType = 'text'
          }
        }

        const formattedMessage = {
          ...message,
          timestamp: message.timestamp * 1000,
          type: messageType,
          avatar: message.avatar || '/static/icons/avatar.png',
          nickname: message.nickname || message.fromUID || '未知用户',
          message_id: message.messageID || message.message_id,
          // 确保metadata被正确传递
          metadata: message.metadata || {}
        }
        messages.value.unshift(formattedMessage)
        nextTick(() => {
          if (message.fromUID === userInfo.value.userId) {
            scrollToBottom()
          }
        })
      }
    }
  }

  // 表情列表
  const emojiList = ref([
    '😀',
    '😃',
    '😄',
    '😁',
    '😆',
    '😅',
    '😂',
    '🤣',
    '😊',
    '😇',
    '🙂',
    '🙃',
    '😉',
    '😌',
    '😍',
    '🥰',
    '😘',
    '😗',
    '😙',
    '😚',
    '😋',
    '😛',
    '😝',
    '😜',
    '🤪',
    '🤨',
    '🧐',
    '🤓',
    '😎',
    '🤩',
    '🥳',
    '😏',
    '😒',
    '😞',
    '😔',
    '😟',
    '😕',
    '🙁',
    '😣',
    '😖',
    '😫',
    '😩',
    '🥺',
    '😢',
    '😭',
    '😤',
    '😠',
    '😡',
    '🤬',
    '🤯'
  ])
  // 处理加载更多历史消息
  const handleLoadMore = async () => {
    if (loading.value) return
    if (!hasMore.value) return
    try {
      loading.value = true
      debugger
      const startMessageSeq =
        messages.value.length > 0 ? messages.value[messages.value.length - 1].message_seq - 1 : 0
      const historyMessages = await WKIMManager.syncMessages(
        {
          channelID: channelID.value,
          channelType: channelType.value
        },
        {
          start_message_seq: startMessageSeq,
          limit: 50,
          pullMode: 0
        }
      )
      const userIds = Array.from(new Set(historyMessages.messages.map(item => item.fromUID)))
      const usersRes = await conversationApi.getUserInfo(userIds)
      historyMessages.messages.forEach(async item => {
        item.avatar = usersRes.data[item.fromUID].img.includes('http')
          ? usersRes.data[item.fromUID].img
          : 'http://47.123.3.183:9000/' + usersRes.data[item.fromUID].img
        item.nickname = usersRes.data[item.fromUID].nickname

        // 根据contentType判断消息类型
        let messageType = 'text'
        if (item.content && item.content.contentType) {
          switch (item.content.contentType) {
            case 1:
              messageType = 'text'
              break
            case 2:
              messageType = 'image'
              break
            case 101:
              messageType = 'location'
              break
            default:
              messageType = 'text'
          }
        }

        // 确保每条消息都有metadata字段
        if (!item.metadata) {
          item.metadata = {}
        }

        // 设置消息类型
        item.type = messageType
      })
      conversationStore.prependConversationHistory(historyMessages.messages)
      messages.value = messages.value.concat(historyMessages.messages)
      hasMore.value = historyMessages.hasMore

      // // 在下一个渲染周期执行
      // nextTick(() => {
      //   // 使用锚点定位
      //   if (chatListRef.value) {
      //     chatListRef.value.scrollToAnchor()
      //   }
      // })
    } catch (error) {
      console.error('加载更多历史消息失败:', error)
      uni.showToast({
        title: '加载更多消息失败',
        icon: 'none'
      })
    } finally {
      loading.value = false
    }
  }

  // 输入相关处理
  const toggleVoiceInput = () => {
    showVoiceInput.value = !showVoiceInput.value
    showEmojiPanel.value = false
    showMorePanel.value = false
  }

  const toggleEmojiPanel = () => {
    showEmojiPanel.value = !showEmojiPanel.value
    showMorePanel.value = false
    showVoiceInput.value = false
  }

  const toggleMorePanel = () => {
    showMorePanel.value = !showMorePanel.value
    showEmojiPanel.value = false
    showVoiceInput.value = false
  }

  const handleInputFocus = e => {
    console.log('输入框获焦，事件详情:', e.detail)

    showEmojiPanel.value = false
    showMorePanel.value = false

    // 获取键盘高度（DCloud 方案核心）
    if (e.detail && e.detail.height) {
      keyboardHeight.value = e.detail.height
      isKeyboardVisible.value = true
      console.log('键盘弹起，高度:', keyboardHeight.value)
    }

    // 延时滚动到底部
    setTimeout(() => {
      scrollToBottom()
    }, 300)
  }

  const handleInputBlur = e => {
    console.log('输入框失焦，事件详情:', e.detail)

    // 键盘收起
    keyboardHeight.value = 0
    isKeyboardVisible.value = false
    console.log('键盘收起')
  }

  const insertEmoji = emoji => {
    inputText.value += emoji
  }

  // 发送消息
  const sendMessage = async () => {
    const content = inputText.value.trim()
    if (!content) {
      uni.showToast({
        title: '请输入消息内容',
        icon: 'none'
      })
      return
    }

    try {
      // 检查是否有@用户，并构建消息内容
      let messageContent = content
      const atUserList = groupStore.getAtUsers

      // 创建metadata对象
      const metadata = {}

      // 如果有@用户，使用mention属性
      if (channelType.value === 2 && atUserList.length > 0) {
        // 替换消息中的@用户标记
        atUserList.forEach(user => {
          // 将带有零宽空格标记的@用户文本替换为普通@用户文本
          const markedText = `@${user.name}\u200B `
          const plainText = `@${user.name} `
          messageContent = messageContent.replace(markedText, plainText)
        })

        console.log(
          '发送消息包含@用户:',
          atUserList.map(user => `${user.name}(${user.uid})`).join(', ')
        )

        // 将@用户ID添加到metadata的mention属性中，而不是mentionedUsers
        // 注意：这里不再使用metadata.mentionedUsers
        metadata.mention = {
          uids: atUserList.map(user => user.uid)
        }

        // 清空@用户列表，为下一条消息做准备
        groupStore.clearAtUsers()
      }

      inputText.value = ''
      showEmojiPanel.value = false
      showMorePanel.value = false
      showAtPanel.value = false

      // 关闭键盘
      // #ifdef APP-PLUS || MP-WEIXIN
      uni.hideKeyboard()
      // #endif

      // #ifdef H5
      const textareaEl = document.querySelector('.text-input')
      if (textareaEl) {
        textareaEl.blur()
      }
      // #endif

      await WKIMManager.sendMessage(
        channelID.value,
        channelType.value,
        'text',
        messageContent,
        metadata
      )
    } catch (error) {
      console.error('发送消息失败:', error)
      uni.showToast({
        title: '发送失败',
        icon: 'none'
      })
    }
  }

  // 添加滚动到底部的方法
  const scrollToBottom = () => {
    // 直接调用聊天列表组件的scrollToBottom方法
    if (chatListRef.value) {
      chatListRef.value.scrollToBottom()
    }
  }

  // 在消息列表组件中添加ref
  const chatListRef = ref(null)

  // 语音录制相关
  const initRecorderManager = () => {
    recorderManager.value = uni.getRecorderManager()

    recorderManager.value.onStart(() => {
      console.log('开始录音')
      recordingText.value = '正在录音...'
    })

    recorderManager.value.onStop(res => {
      console.log('录音结束', res)
      if (res.tempFilePath) {
        sendVoiceMessage(res.tempFilePath, res.duration)
      }
    })

    recorderManager.value.onError(err => {
      console.error('录音错误', err)
      uni.showToast({
        title: '录音失败',
        icon: 'error'
      })
    })
  }

  const startRecord = () => {
    isRecording.value = true
    recorderManager.value.start({
      duration: 60000,
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 48000,
      format: 'mp3'
    })
  }

  const endRecord = () => {
    isRecording.value = false
    recorderManager.value.stop()
  }

  const cancelRecord = () => {
    isRecording.value = false
    recorderManager.value.stop()
  }

  const sendVoiceMessage = async (filePath, duration) => {
    try {
      const voiceContent = {
        url: filePath,
        duration: Math.ceil(duration / 1000)
      }

      await WKIMManager.sendMessage(channelID.value, channelType.value, 'voice', voiceContent)
    } catch (error) {
      console.error('Failed to send voice message:', error)
      uni.showToast({
        title: '发送语音失败',
        icon: 'error'
      })
    }
  }

  // 多媒体消息
  const selectImage = () => {
    uni.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album'],
      success: res => {
        console.log('🔍 选择图片:', res)

        // 构建统一的文件对象
        const fileObj = {
          path: res.tempFilePaths[0],
          tempFilePath: res.tempFilePaths[0],
          size: res.tempFiles[0]?.size || 0,
          type: 'image',
          name: res.tempFilePaths[0].split('/').pop() || 'image.jpg'
        }

        // 发送图片消息
        sendImageMessage(fileObj)
      }
    })
    showMorePanel.value = false
  }

  const takePhoto = () => {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: res => {
        console.log('🔍 拍照图片:', res)

        // 构建统一的文件对象
        const fileObj = {
          path: res.tempFilePaths[0],
          tempFilePath: res.tempFilePaths[0],
          size: res.tempFiles[0]?.size || 0,
          type: 'image',
          name: res.tempFilePaths[0].split('/').pop() || 'image.jpg'
        }

        // 发送图片消息
        sendImageMessage(fileObj)
      }
    })
    showMorePanel.value = false
  }

  const sendImageMessage = async fileObj => {
    try {
      console.log('📸 准备发送图片对象:', fileObj)

      uni.showLoading({
        title: '上传中...'
      })

      // 获取图片尺寸
      const imageSize = await getImageSizeFromFile(fileObj)
      console.log('🔍 获取图片尺寸:', imageSize)

      // 上传文件
      let uploadResult

      // 处理文件路径
      let filePath = fileObj.path || fileObj.tempFilePath

      // #ifdef APP-PLUS
      try {
        // 检查文件是否存在
        const fileExists = await new Promise(resolve => {
          plus.io.resolveLocalFileSystemURL(
            filePath,
            () => resolve(true),
            () => resolve(false)
          )
        })

        if (!fileExists) {
          throw new Error('文件不存在: ' + filePath)
        }

        console.log('文件存在，原始路径:', filePath)

        // 统一转换文件路径
        if (filePath.startsWith('file:///')) {
          // 已经是完整路径，直接使用
          console.log('使用完整路径:', filePath)
        } else {
          // 转换路径为完整路径
          const convertedPath = plus.io.convertLocalFileSystemURL(filePath)
          if (convertedPath) {
            filePath = convertedPath
            console.log('转换后的路径:', filePath)
          } else {
            console.warn('路径转换失败，使用原始路径')
          }
        }
      } catch (error) {
        console.error('文件路径处理失败:', error)
        throw error
      }
      // #endif

      console.log('最终使用的文件路径:', filePath)

      // 使用uni.uploadFile上传（适用于所有平台）
      uploadResult = await new Promise((resolve, reject) => {
        const uploadTask = uni.uploadFile({
          url: 'http://47.123.3.183:80/api/common/file/uploadOne',
          filePath: filePath,
          name: 'file',
          formData: {
            bucket: 'chat'
          },
          header: {
            token: uni.getStorageSync('token')
          },
          success: res => {
            console.log('上传响应状态码:', res.statusCode)
            console.log('上传响应数据:', res.data)
            console.log('上传响应类型:', typeof res.data)

            try {
              let data = res.data
              // 检查响应数据是否为空
              if (!data || data.trim() === '') {
                console.error('服务器返回空响应')
                reject(new Error('服务器返回空响应'))
                return
              }

              // 尝试解析 JSON
              try {
                data = JSON.parse(data)
              } catch (parseError) {
                console.error('JSON解析错误:', parseError)
                console.error('原始响应数据:', data)
                reject(new Error('服务器响应格式错误'))
                return
              }

              // 检查响应状态
              if (data.code === 200) {
                console.log('🔍 上传成功:', data)
                resolve(data)
              } else {
                console.error('上传失败:', data.msg || '未知错误')
                reject(new Error(data.msg || '上传失败'))
              }
            } catch (e) {
              console.error('处理响应失败:', e)
              reject(e)
            }
          },
          fail: err => {
            console.error('上传请求失败:', err)
            reject(err)
          }
        })

        // 监听上传进度
        uploadTask.onProgressUpdate(res => {
          console.log('上传进度:', res.progress)
          console.log('已经上传的数据长度:', res.totalBytesSent)
          console.log('预期需要上传的数据总长度:', res.totalBytesExpectedToSend)
        })
      })

      console.log('🔍 上传结果:', uploadResult)

      // 构建图片消息内容
      const imageContent = {
        url: 'http://47.123.3.183:9000/' + uploadResult.data,
        width: imageSize.width,
        height: imageSize.height,
        displayWidth: Math.min(400, imageSize.width),
        displayHeight: Math.min(300, imageSize.height)
      }

      // 发送图片消息
      await WKIMManager.sendMessage(channelID.value, channelType.value, 'image', imageContent)

      uni.hideLoading()
      console.log('✅ 图片消息发送成功')
    } catch (error) {
      uni.hideLoading()
      console.error('Failed to send image:', error)
      uni.showToast({
        title: '发送图片失败',
        icon: 'error'
      })
    }
  }

  // 获取图片尺寸的简单函数
  const getImageSizeFromFile = fileObj => {
    return new Promise(resolve => {
      // #ifdef APP-PLUS
      // Android平台特殊处理
      if (uni.getSystemInfoSync().platform === 'android') {
        try {
          const androidPath = plus.io.convertLocalFileSystemURL(
            fileObj.path || fileObj.tempFilePath
          )
          if (androidPath) {
            uni.getImageInfo({
              src: androidPath,
              success: res => {
                resolve({
                  width: res.width,
                  height: res.height
                })
              },
              fail: () => {
                console.warn('获取Android图片尺寸失败')
                resolve({ width: 300, height: 200 }) // 默认尺寸
              }
            })
            return
          }
        } catch (error) {
          console.error('Android路径转换失败:', error)
        }
      }
      // #endif

      // 其他平台的处理保持不变
      let objectUrl = fileObj.path || fileObj.tempFilePath

      // #ifdef H5
      if (
        typeof URL !== 'undefined' &&
        typeof Blob !== 'undefined' &&
        (fileObj instanceof Blob ||
          (typeof fileObj === 'object' && fileObj.size && fileObj.type && fileObj.slice))
      ) {
        try {
          objectUrl = URL.createObjectURL(fileObj)
        } catch (e) {
          console.warn('创建对象URL失败:', e)
        }
      }

      const img = new Image()
      img.onload = () => {
        resolve({
          width: img.naturalWidth || img.width,
          height: img.naturalHeight || img.height
        })
        if (objectUrl && typeof URL !== 'undefined' && objectUrl.startsWith('blob:')) {
          try {
            URL.revokeObjectURL(objectUrl)
          } catch (e) {
            console.warn('释放对象URL失败:', e)
          }
        }
      }

      img.onerror = () => {
        console.warn('获取图片尺寸失败:', objectUrl)
        resolve({ width: 300, height: 200 })
        if (objectUrl && typeof URL !== 'undefined' && objectUrl.startsWith('blob:')) {
          try {
            URL.revokeObjectURL(objectUrl)
          } catch (e) {
            console.warn('释放对象URL失败:', e)
          }
        }
      }

      img.src = objectUrl
      // #endif

      // #ifndef H5
      uni.getImageInfo({
        src: objectUrl,
        success: res => {
          resolve({
            width: res.width,
            height: res.height
          })
        },
        fail: () => {
          console.warn('获取图片尺寸失败:', objectUrl)
          resolve({ width: 300, height: 200 })
        }
      })
      // #endif
    })
  }

  const selectLocation = () => {
    // const res = {
    //   longitude: 118.706724,
    //   latitude: 32.029564,
    //   name: '南京市建邺区',
    //   address: '智立方A座'
    // }
    // const locationData = {
    //   lng: res.longitude,
    //   lat: res.latitude,
    //   title: res.name,
    //   address: res.address,
    //   img: `https://restapi.amap.com/v3/staticmap?location=${res.longitude},${res.latitude}&zoom=16&size=300*100&markers=mid,,A:${res.longitude},${res.latitude}&key=94ceef06c2d0f9647b90667ff5f04ccd`
    // }
    // sendLocationMessage(locationData)
    uni.chooseLocation({
      success: res => {
        console.log('chooseLocation------>', res)
        const locationData = {
          lng: res.longitude,
          lat: res.latitude,
          title: res.name,
          address: res.address,
          img: `https://restapi.amap.com/v3/staticmap?location=${res.longitude},${res.latitude}&zoom=16&size=400*200&markers=mid,,A:${res.longitude},${res.latitude}&key=d7e9160c93e0d4002aa60f3f7c9e6a31`
        }
        sendLocationMessage(locationData)
      }
    })
    showMorePanel.value = false
  }

  const sendLocationMessage = async locationData => {
    try {
      await WKIMManager.sendMessage(channelID.value, channelType.value, 'location', locationData)
    } catch (error) {
      console.error('Failed to send location:', error)
      uni.showToast({
        title: '发送位置失败',
        icon: 'error'
      })
    }
  }

  const selectFile = () => {
    // 文件选择功能（需要根据平台适配）
    uni.showToast({
      title: '文件功能开发中',
      icon: 'none'
    })
    showMorePanel.value = false
  }

  // 事件处理
  const handleImageClick = image => {
    uni.previewImage({
      urls: [image],
      current: image
    })
  }

  const handleVoiceClick = voice => {
    // 播放语音消息
    const audioContext = uni.createInnerAudioContext()
    audioContext.src = voice.url || voice
    audioContext.play()
  }

  const handleLocationClick = location => {
    uni.openLocation({
      latitude: location.lat,
      longitude: location.lng,
      name: location.name,
      address: location.address
    })
  }

  // 返回上一页
  const goBack = () => {
    uni.navigateBack()
  }

  // 查看群成员
  const viewGroupMembers = () => {
    if (groupCode.value) {
      uni.navigateTo({
        url: `/pages/community/detail?groupCode=${groupCode.value}&groupName=${encodeURIComponent(nickName.value)}&channelId=${channelID.value}`
      })
    } else {
      uni.showToast({
        title: '获取群信息失败',
        icon: 'none'
      })
    }
  }

  // 测试工具相关
  const showTestTools = ref(false)
  const testMessageId = ref('')
  const testInfo = ref('')

  const testScrollToMessage = () => {
    // 检查输入的消息ID是否有效
    if (!testMessageId.value) {
      uni.showToast({
        title: '请输入消息ID',
        icon: 'none'
      })
      return
    }

    console.log('测试滚动到消息ID:', testMessageId.value)

    // 调用ChatList组件的方法
    if (chatListRef.value) {
      // 如果ChatList组件有scrollToAtMessage方法，直接调用
      if (typeof chatListRef.value.scrollToAtMessage === 'function') {
        chatListRef.value.scrollToAtMessage(testMessageId.value)
      } else {
        // 否则通过全局变量调用
        if (window.scrollToAtMessage) {
          window.scrollToAtMessage(testMessageId.value)
        } else {
          uni.showToast({
            title: '滚动方法不可用',
            icon: 'none'
          })
        }
      }
    } else {
      uni.showToast({
        title: '聊天列表组件未加载',
        icon: 'none'
      })
    }
  }

  const getRandomMessageId = () => {
    // 检查是否有消息
    if (!messages.value || messages.value.length === 0) {
      testInfo.value = '没有可用的消息'
      return
    }

    // 从消息列表中随机选择一条消息
    const randomIndex = Math.floor(Math.random() * messages.value.length)
    const randomMessage = messages.value[randomIndex]

    if (randomMessage && randomMessage.message_id) {
      testMessageId.value = randomMessage.message_id.toString()
      testInfo.value = `已获取消息ID: ${testMessageId.value} (索引: ${randomIndex})`
    } else {
      testInfo.value = '无法获取有效的消息ID'
    }
  }

  const testScrollPosition = position => {
    console.log('测试滚动到位置:', position)
    if (chatListRef.value) {
      chatListRef.value.scrollToPosition(position)
    } else {
      uni.showToast({
        title: '聊天列表组件未加载',
        icon: 'none'
      })
    }
  }
</script>

<style lang="scss" scoped>
  /* 确保页面不会产生滚动 */
  page {
    height: 100vh;
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    position: relative;
  }

  .chat-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    background-color: #f5f5f5;
    box-sizing: border-box;
    position: fixed;
    overflow: hidden;
  }

  .status-bar-placeholder {
    width: 100%;
    height: var(--status-bar-height, 20px);
  }

  .custom-nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    height: 90rpx; /* 增加高度 */
    background-color: #ffffff;
    border-bottom: 1rpx solid #eaeaea;
    padding-top: 0; /* 移除顶部内边距，因为已经有占位视图 */
    box-sizing: border-box;
    position: relative;
    z-index: 10;

    .nav-left,
    .nav-right {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-back-icon {
      font-size: 48rpx;
      font-weight: bold;
    }

    .nav-menu-icon {
      font-size: 32rpx;
      color: #999;
    }

    .nav-title {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .chat-container {
    flex: 1;
    overflow: hidden;
    position: relative;
    // padding-bottom: 120rpx; /* 为底部输入区域留出空间 */
    transition: padding-bottom 0.3s ease;

    &.panel-expanded {
      padding-bottom: 620rpx;
    }
  }
  .input-container {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    background-color: #f7f7f7;
    border-top: 1rpx solid #e5e5e5;
    z-index: 100;
    box-sizing: border-box;
    overflow: visible;
    // padding-bottom: env(safe-area-inset-bottom);
  }

  .input-wrapper {
    display: flex;
    align-items: flex-end;
    padding: 24rpx 16rpx;
    padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
    min-height: 100rpx;
    background-color: #fff;
    width: 100%;
    box-sizing: border-box;
    overflow: visible;
    position: relative;
    /* Android 设备额外适配 */
    /* #ifdef APP-PLUS */
    padding-bottom: calc(12rpx + env(safe-area-inset-bottom) + 8rpx);
    /* #endif */
  }

  .voice-btn,
  .emoji-btn,
  .more-btn,
  .send-btn {
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    flex-shrink: 0; /* 确保按钮不会被压缩 */

    &:last-child {
      margin-right: 0;
    }
  }

  .voice-btn,
  .emoji-btn,
  .more-btn {
    width: 72rpx;
  }

  .input-icon {
    width: 48rpx;
    height: 48rpx;
  }

  .btn-text {
    font-size: 36rpx;
  }

  .text-input-wrapper {
    flex: 1;
    margin: 0;
    // max-width: calc(100% - 240rpx);
    min-width: 0;
    position: relative;
    display: flex;
    align-items: center;
    background-color: #f3f3f3;
    border-radius: 8rpx;
    border: 1rpx solid #e5e5e5;
    padding: 16rpx 20rpx;
    padding-right: 100rpx;
    box-sizing: border-box;
    min-height: 32rpx;

    .text-input {
      font-size: 28rpx;
      line-height: 1.4;
      min-height: 32rpx;
      max-height: 160rpx;
      width: 100%;
      border: none;
      background: transparent;
      box-sizing: border-box;
      padding: 0;
      outline: none;
      /* 增加Android端的光标间距 */
      /* #ifdef APP-PLUS */
      cursor-spacing: 30px;
      /* #endif */
    }
  }

  .emoji-suffix-btn {
    position: absolute;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(245, 245, 245, 0.8);
    transition: all 0.2s ease;
    flex-shrink: 0;
  }

  .emoji-suffix-btn:active {
    background: rgba(245, 245, 245, 1);
    transform: translateY(-50%) scale(0.95);
  }

  .emoji-icon {
    font-size: 36rpx;
  }

  .voice-record-wrapper {
    flex: 1;
    margin: 0;
    max-width: calc(100% - 220rpx); /* 适当调整宽度给表情按钮留空间 */
    min-width: 0;
    display: flex;
    align-items: center;

    .voice-record-btn {
      margin-right: 12rpx;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .send-btn {
    background-color: #07c160;
    color: #fff;
    border-radius: 8rpx;
    padding: 16rpx 24rpx;
    font-size: 28rpx;
    min-width: 100rpx;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4rpx;
    flex-shrink: 0;

    &:active {
      background-color: #06ad56;
      transform: scale(0.95);
    }

    text {
      color: #fff;
      font-weight: 500;
      font-size: 28rpx;
    }
  }

  .emoji-panel {
    height: 500rpx;
    width: 100%;
    background-color: #fff;
    border-top: 1rpx solid #e5e5e5;
    box-sizing: border-box;
    overflow: hidden;
    position: relative; /* 添加相对定位 */
  }

  .emoji-scroll {
    height: 100%;
    width: 100%;
    padding: 20rpx;
    padding-bottom: 100rpx; /* 为发送按钮留出空间 */
    box-sizing: border-box;
  }

  .emoji-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: 100%;
  }

  .emoji-item {
    width: 80rpx;
    height: 80rpx;
    font-size: 48rpx;
    text-align: center;
    line-height: 80rpx;
    margin: 10rpx;
    flex-shrink: 0;
  }

  /* 表情框发送按钮 */
  .emoji-send-btn {
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    background-color: #07c160;
    color: #fff;
    border-radius: 8rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    z-index: 10;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    &:active {
      background-color: #06ad56;
      transform: scale(0.95);
    }

    text {
      color: #fff;
      font-weight: 500;
    }
  }

  .more-panel {
    height: 500rpx;
    width: 100%;
    background-color: #fff;
    border-top: 1rpx solid #e5e5e5;
    padding: 40rpx 20rpx;
    box-sizing: border-box;
    overflow: hidden;
  }

  .more-grid {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: flex-start;
  }

  .more-item {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60rpx;
    box-sizing: border-box;
    padding: 0 10rpx;
  }

  .more-icon {
    width: 120rpx;
    height: 120rpx;
    background-color: #f5f5f5;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-text {
    font-size: 60rpx;
  }

  .more-item text {
    font-size: 28rpx;
    color: #666;
  }

  .recording-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
  }

  .recording-modal {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 20rpx;
    padding: 60rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .recording-icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 30rpx;
  }

  .recording-text {
    color: #fff;
    font-size: 32rpx;
    margin-bottom: 20rpx;
  }

  .recording-tips {
    color: #ccc;
    font-size: 24rpx;
  }

  .test-tools {
    position: fixed;
    bottom: 180rpx;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 20rpx;
    box-sizing: border-box;
    z-index: 999;
    border-top: 1px solid #e5e5e5;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  }

  .test-input-area {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
  }

  .test-input {
    flex: 1;
    padding: 16rpx;
    border: 1rpx solid #e5e5e5;
    border-radius: 8rpx;
    font-size: 28rpx;
  }

  .test-button {
    padding: 16rpx 24rpx;
    background-color: #07c160;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    margin-left: 20rpx;
    font-size: 28rpx;
    flex-shrink: 0;
  }

  .test-button.get-id {
    background-color: #1989fa;
  }

  .test-toggle {
    padding: 12rpx 24rpx;
    background-color: #f2f2f2;
    color: #333;
    border: none;
    border-radius: 8rpx;
    width: 100%;
    font-size: 28rpx;
  }

  .test-toggle.show {
    position: fixed;
    bottom: 180rpx;
    right: 20rpx;
    width: auto;
    background-color: #07c160;
    color: #fff;
    z-index: 999;
    opacity: 0.8;
  }

  .test-info {
    margin-top: 10rpx;
    color: #666;
    font-size: 28rpx;
    background-color: #f5f5f5;
    padding: 10rpx;
    border-radius: 8rpx;
    margin-bottom: 10rpx;
  }

  .test-scroll-area {
    margin-top: 10rpx;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    text {
      color: #666;
      font-size: 28rpx;
      margin-right: 10rpx;
    }

    .test-button {
      padding: 12rpx 16rpx;
      margin: 6rpx;
      font-size: 24rpx;
      background-color: #1989fa;
    }
  }

  /* 调试信息样式 */
  .debug-keyboard-info {
    position: fixed;
    top: 200rpx;
    right: 20rpx;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20rpx;
    border-radius: 10rpx;
    font-size: 24rpx;
    z-index: 9999;
    max-width: 400rpx;

    text {
      display: block;
      margin-bottom: 10rpx;
      word-break: break-all;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
</style>
