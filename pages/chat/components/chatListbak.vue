<template>
  <view class="chat-list-container">
    <scroll-view
      :bounces="false"
      :scroll-anchoring="true"
      class="chat-list"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-with-animation="useAnimation"
      :show-scrollbar="false"
      @scrolltolower="handleScrollToUpper"
      ref="scrollView"
      :id="scrollViewId"
      :scroll-into-view="scrollIntoView"
      style="transform: rotate(180deg)"
    >
      <!-- <view class="loading" v-if="loading">
        <uni-load-more status="loading" />
      </view> -->

      <view class="messages-container" ref="messagesContainer">
        <view class="date-divider" v-if="showDateDivider">
          <text>{{ currentDate }}</text>
        </view>
        <block v-for="(message, index) in messages" :key="message.message_id">
          <!-- 添加锚点元素，放在每条消息前面 -->
          <view :id="`anchor-${message.message_id}`" class="message-anchor"></view>
          <view class="date-divider" v-if="shouldShowDate(message, index)">
            <text>{{ formatDate(message.timestamp) }}</text>
          </view>
          <view :class="['message-wrapper', `message-item-${message.message_id}`]">
            <chat-message
              :type="message.type"
              :content="message.content"
              :is-self="message.fromUID === currentUID"
              :avatar="message.avatar"
              :nickname="message.nickname"
              :time="formatTime(message.timestamp)"
              :duration="message.duration"
              :channel-type="channelType"
              @image-click="handleImageClick"
              @voice-click="handleVoiceClick"
              @location-click="handleLocationClick"
              ref="messageRefs"
            />
          </view>
        </block>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { ref, computed, watch, onMounted, nextTick } from 'vue'
  import ChatMessage from './ChatMessage.vue'
  import { formatDate, formatTime } from '@/utils/date'
  import { useUserStore } from '@/stores/user'
  import { useConversationStore } from '@/stores/conversation'
  import { storeToRefs } from 'pinia'

  // 使用pinia store获取用户信息
  const userStore = useUserStore()
  const conversationStore = useConversationStore()
  const { userInfo } = storeToRefs(userStore)

  const props = defineProps({
    messages: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    channelType: {
      type: Number,
      default: 1
    },
    channelId: {
      type: String,
      default: ''
    }
  })

  // 计算当前用户ID
  const currentUID = computed(() => {
    return userInfo.value?.userId || ''
  })

  const emit = defineEmits(['loadMore', 'imageClick', 'voiceClick', 'locationClick'])

  const scrollTop = ref(0)
  const showDateDivider = ref(false)
  const messagesContainer = ref(null) // 消息容器引用
  const scrollView = ref(null) // 滚动视图引用
  const messageRefs = ref([]) // 消息组件引用
  const isHistoryLoading = ref(false) // 内部历史加载状态
  const useAnimation = ref(true) // 是否使用滚动动画
  const scrollViewId = 'chat-list-scroll-view' // 滚动视图ID
  const anchormessage_id = ref(null) // 锚点消息ID
  const scrollIntoView = ref('') // 用于scroll-into-view属性

  const currentDate = computed(() => {
    if (props.messages.length > 0) {
      return formatDate(props.messages[0].timestamp)
    }
    return ''
  })

  const shouldShowDate = (message, index) => {
    if (index === 0) return false
    const prevMessage = props.messages[index - 1]
    const currentDate = new Date(message.timestamp)
    const prevDate = new Date(prevMessage.timestamp)
    return currentDate.toDateString() !== prevDate.toDateString()
  }

  // 处理滚动到顶部事件，触发父组件的loadMore
  const handleScrollToUpper = () => {
    if (props.loading || isHistoryLoading.value) return

    // 禁用滚动动画，避免加载历史消息时的闪现
    useAnimation.value = false

    // 记录第一条消息ID作为锚点
    if (props.messages.length > 0) {
      anchormessage_id.value = props.messages[0].message_id
    }

    // 通知父组件加载更多消息
    emit('loadMore')

    // 标记为正在加载历史消息
    isHistoryLoading.value = true

    // 延迟恢复滚动动画
    setTimeout(() => {
      isHistoryLoading.value = false
      setTimeout(() => {
        useAnimation.value = true
      }, 500)
    }, 100)
  }

  // 滚动到锚点元素
  const scrollToAnchor = () => {
    if (!anchormessage_id.value) return

    // 设置scroll-into-view属性，实现锚点定位
    scrollIntoView.value = `anchor-${anchormessage_id.value}`
    console.log('设置滚动到锚点:', scrollIntoView.value)

    // 延迟清除锚点ID
    setTimeout(() => {
      scrollIntoView.value = ''
    }, 500)
  }

  const handleImageClick = image => {
    emit('imageClick', image)
  }

  const handleVoiceClick = voice => {
    emit('voiceClick', voice)
  }

  const handleLocationClick = location => {
    emit('locationClick', location)
  }

  // 滚动到底部方法
  const scrollToBottom = () => {
    // 启用滚动动画
    useAnimation.value = false

    nextTick(() => {
      // 先设置为0，再设置为一个较大的值，确保每次都能触发滚动
      scrollTop.value = 0
    })
  }

  // 暴露方法给父组件
  defineExpose({
    scrollToBottom,
    scrollToAnchor
  })

  // 监听消息变化
  watch(
    () => props.messages,
    async (newMessages, oldMessages) => {
      if (newMessages.length > oldMessages.length && !isHistoryLoading.value) {
        // 如果是新消息，滚动到底部
        // scrollToBottom()
      }
    },
    { deep: true }
  )

  onMounted(() => {
    // 初始加载时滚动到底部
    scrollToBottom()
  })
</script>

<style lang="scss" scoped>
  .chat-list-container {
    height: 100%;
    width: 100%;
  }

  .chat-list {
    height: 100%;
    width: 100%;
    background: transparent;
    box-sizing: border-box;

    .loading {
      padding: 20rpx 0;
      text-align: center;
    }

    .messages-container {
      padding: 0;
      min-height: calc(100% - 40rpx);
      width: 100%;
      box-sizing: border-box;
      background: transparent;
    }

    .date-divider {
      text-align: center;
      margin: 20rpx 0;
      padding: 0 20rpx;

      text {
        background-color: rgba(0, 0, 0, 0.1);
        color: #ddd;
        font-size: 24rpx;
        padding: 4rpx 20rpx;
        border-radius: 20rpx;
      }
    }

    .message-anchor {
      height: 2rpx;
      width: 100%;
      position: relative;
      z-index: 10;
    }

    .message-wrapper {
      width: 100%;
    }
  }
</style>
