<template>
  <view class="at-reminder-bubble" v-if="visible" @tap="handleClick">
    <view class="bubble-content">
      <!-- <text class="at-icon">@</text> -->
      <text class="at-text">{{ text }}</text>
    </view>
  </view>
</template>

<script setup>
  import { ref } from 'vue'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: '有人@我'
    },
    messageId: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits(['click'])

  const handleClick = () => {
    emit('click', props.messageId)
  }
</script>

<style lang="scss" scoped>
  .at-reminder-bubble {
    position: fixed;
    bottom: 120rpx;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1989fa;
    border-radius: 40rpx;
    padding: 12rpx 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    z-index: 100;
    max-width: 80%;

    .bubble-content {
      display: flex;
      align-items: center;
      justify-content: center;

      .at-icon {
        color: #ffffff;
        font-size: 32rpx;
        font-weight: bold;
        margin-right: 8rpx;
      }

      .at-text {
        color: #ffffff;
        font-size: 28rpx;
      }
    }

    &:active {
      opacity: 0.8;
    }
  }
</style>
