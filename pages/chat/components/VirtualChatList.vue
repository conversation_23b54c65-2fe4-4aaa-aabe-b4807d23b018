<template>
  <view class="virtual-chat-container">
    <scroll-view
      class="virtual-chat-list"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-with-animation="useAnimation"
      :show-scrollbar="false"
      @scrolltolower="handleLoadMore"
      ref="scrollView"
      :id="scrollViewId"
      :style="{ transform: 'rotate(180deg)' }"
    >
      <!-- 加载更多指示器 -->
      <view v-if="loading || isLoading" class="loading-more">
        <uni-load-more status="loading" />
      </view>

      <!-- 消息列表容器 -->
      <view class="messages-list" :style="{ minHeight: '100%' }">
        <block v-for="(message, index) in reversedMessages" :key="message.message_id">
          <!-- 日期分隔线 -->
          <view
            class="date-divider"
            v-if="shouldShowDate(message, index)"
            :style="{ transform: 'rotate(180deg)' }"
          >
            <text>{{ formatDate(message.timestamp) }}</text>
          </view>

          <!-- 消息内容 -->
          <view
            :class="['message-wrapper', `message-item-${message.message_id}`]"
            :style="{ transform: 'rotate(180deg)' }"
          >
            <chat-message
              :type="message.type"
              :content="message.content"
              :is-self="message.fromUID === currentUID"
              :avatar="message.avatar"
              :nickname="message.nickname"
              :time="formatTime(message.timestamp)"
              :duration="message.duration"
              :channel-type="channelType"
              @image-click="handleImageClick"
              @voice-click="handleVoiceClick"
              @location-click="handleLocationClick"
            />
          </view>
        </block>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { ref, computed, watch, onMounted, nextTick } from 'vue'
  import ChatMessage from './ChatMessage.vue'
  import { formatDate, formatTime } from '@/utils/date'
  import { useUserStore } from '@/stores/user'
  import { useConversationStore } from '@/stores/conversation'
  import WKIMManager from '@/utils/wukongim'
  import { storeToRefs } from 'pinia'

  // 使用pinia store获取用户信息
  const userStore = useUserStore()
  const conversationStore = useConversationStore()
  const { userInfo } = storeToRefs(userStore)

  const props = defineProps({
    messages: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    channelType: {
      type: Number,
      default: 1
    },
    channelId: {
      type: String,
      default: ''
    }
  })

  // 计算当前用户ID
  const currentUID = computed(() => {
    return userInfo.value?.userId || ''
  })

  const emit = defineEmits(['loadMore', 'imageClick', 'voiceClick', 'locationClick'])

  // 滚动相关
  const scrollTop = ref(0)
  const scrollViewId = 'virtual-chat-list-scroll-view'
  const useAnimation = ref(true)
  const isLoading = ref(false)
  const hasMore = ref(true)
  const scrollView = ref(null)

  // 反转消息列表，用于倒置布局
  const reversedMessages = computed(() => {
    return [...props.messages].reverse()
  })

  // 判断是否显示日期分隔线
  const shouldShowDate = (message, index) => {
    // 在倒置布局中，最后一条消息是最早的消息，应该显示日期
    if (index === reversedMessages.value.length - 1) {
      return true
    }

    // 检查当前消息和下一条消息是否属于不同日期
    // 在倒置布局中，下一条消息是索引+1
    const nextIndex = index + 1
    if (nextIndex < reversedMessages.value.length) {
      const currentDate = new Date(message.timestamp)
      const nextDate = new Date(reversedMessages.value[nextIndex].timestamp)

      // 如果日期不同，则当前消息是该日期的第一条消息
      return currentDate.toDateString() !== nextDate.toDateString()
    }

    return false
  }

  // 加载更多历史消息
  const handleLoadMore = async () => {
    if (isLoading.value || props.loading) return
    if (!hasMore.value) return

    isLoading.value = true

    // 临时禁用滚动动画
    useAnimation.value = false

    // 记录当前消息数量
    const oldMessagesCount = props.messages.length

    try {
      const startMessageSeq =
        conversationStore.conversationHistory.length > 0
          ? conversationStore.conversationHistory[0].message_seq - 1
          : 0

      const historyMessages = await WKIMManager.syncMessages(
        {
          channelID: props.channelId,
          channelType: props.channelType
        },
        {
          start_message_seq: startMessageSeq,
          limit: 30,
          pullMode: 0
        }
      )

      // 添加历史消息
      conversationStore.prependConversationHistory(historyMessages.messages)
      hasMore.value = historyMessages.hasMore

      // 使用nextTick确保DOM已更新
      nextTick(() => {
        // 通过延迟设置scrollTop来避免闪烁
        setTimeout(() => {
          // 重新启用滚动动画
          useAnimation.value = true

          // 等待一帧后再滚动，避免闪烁
          setTimeout(() => {
            // 不做任何滚动位置调整，让倒置布局自然工作
          }, 50)
        }, 100)
      })
    } catch (error) {
      console.error('加载更多历史消息失败:', error)
      uni.showToast({
        title: '加载更多消息失败',
        icon: 'none'
      })
      useAnimation.value = true
    } finally {
      isLoading.value = false
    }
  }

  // 处理图片点击
  const handleImageClick = image => {
    emit('imageClick', image)
  }

  // 处理语音点击
  const handleVoiceClick = voice => {
    emit('voiceClick', voice)
  }

  // 处理位置点击
  const handleLocationClick = location => {
    emit('locationClick', location)
  }

  // 滚动到底部方法（在倒置布局中实际是滚动到顶部）
  const scrollToBottom = () => {
    useAnimation.value = true
    nextTick(() => {
      scrollTop.value = 0
    })
  }

  // 监听消息变化
  watch(
    () => props.messages,
    (newMessages, oldMessages) => {
      if (newMessages.length > oldMessages.length) {
        // 如果是新消息，判断是否需要滚动到底部
        const latestMessage = newMessages[newMessages.length - 1]
        if (latestMessage && latestMessage.fromUID === currentUID.value) {
          // 如果是自己发送的消息，滚动到底部
          scrollToBottom()
        } else if (scrollTop.value < 10) {
          // 如果当前已经在底部附近，也滚动到底部
          scrollToBottom()
        }
      }
    },
    { deep: true }
  )

  onMounted(() => {
    // 初始加载时滚动到底部
    scrollToBottom()
  })

  // 暴露方法给父组件
  defineExpose({
    scrollToBottom
  })
</script>

<style lang="scss" scoped>
  .virtual-chat-container {
    height: 100%;
    width: 100%;
  }

  .virtual-chat-list {
    height: 100%;
    width: 100%;
    background: transparent;
    box-sizing: border-box;
  }

  .messages-list {
    width: 100%;
    position: relative;
  }

  .loading-more {
    padding: 20rpx 0;
    text-align: center;
    transform: rotate(180deg);
  }

  .date-divider {
    text-align: center;
    margin: 20rpx 0;
    padding: 0 20rpx;

    text {
      background-color: rgba(0, 0, 0, 0.1);
      color: #ddd;
      font-size: 24rpx;
      padding: 4rpx 20rpx;
      border-radius: 20rpx;
    }
  }

  .message-wrapper {
    width: 100%;
    padding: 10rpx 0;
  }
</style>
