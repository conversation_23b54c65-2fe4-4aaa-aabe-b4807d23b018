<template>
  <view class="at-user-panel-container" v-if="visible" @tap.stop>
    <view class="at-user-panel-mask" @tap="closePanel"></view>
    <view class="at-user-panel">
      <view class="panel-header">
        <text class="panel-title">选择提醒的人</text>
        <text class="close-btn" @tap="closePanel">×</text>
      </view>
      <view class="search-bar">
        <input
          class="search-input"
          v-model="searchKeyword"
          placeholder="搜索群成员"
          @input="handleSearch"
          focus
        />
      </view>
      <scroll-view class="member-list" scroll-y>
        <view
          class="member-item"
          v-for="member in filteredMembers"
          :key="member.uid"
          @tap="selectMember(member)"
        >
          <image
            class="avatar"
            :src="
              member.avatar.includes('http')
                ? member.avatar
                : 'http://************:9000/' + member.avatar
            "
            mode="aspectFill"
          />
          <view class="nickname">{{ member.name }}</view>
        </view>
        <view class="empty-tip" v-if="filteredMembers.length === 0">
          <text>没有找到匹配的群成员</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue'
  import { useGroupStore } from '@/stores/group'
  import { storeToRefs } from 'pinia'
  import { useUserStore } from '@/stores/user'

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    keyword: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits(['select', 'close'])

  const groupStore = useGroupStore()
  const userStore = useUserStore()
  const { groupMembers } = storeToRefs(groupStore)

  const searchKeyword = ref('')

  // 监听外部传入的关键词变化，但不包含@符号
  watch(
    () => props.keyword,
    newVal => {
      // 不直接使用@后面的内容作为搜索关键词
      searchKeyword.value = newVal
    }
  )

  // 监听面板显示状态
  watch(
    () => props.visible,
    newVal => {
      if (newVal) {
        // 面板显示时确保键盘关闭
        setTimeout(() => {
          uni.hideKeyboard()
          console.log('AtUserPanel显示，键盘已关闭')
        }, 100)
      }
    },
    { immediate: true }
  )

  // 过滤后的成员列表，排除当前用户自己
  const filteredMembers = computed(() => {
    // 先过滤掉当前用户
    const membersWithoutSelf = groupMembers.value.filter(
      member => member.uid !== userStore.userInfo?.userId
    )

    // 再根据搜索关键词过滤
    if (!searchKeyword.value) {
      return membersWithoutSelf
    }

    const keyword = searchKeyword.value.toLowerCase()
    return membersWithoutSelf.filter(member => member.name.toLowerCase().includes(keyword))
  })

  // 处理搜索输入
  const handleSearch = () => {
    // 可以在这里添加防抖逻辑
  }

  // 选择群成员
  const selectMember = member => {
    emit('select', member)
  }

  // 关闭面板
  const closePanel = () => {
    emit('close')
  }
</script>

<style lang="scss" scoped>
  .at-user-panel-container {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 999;
  }

  .at-user-panel-mask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .at-user-panel {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    max-height: 70vh;
    z-index: 1000;
    display: flex;
    flex-direction: column;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      border-bottom: 1rpx solid #eee;

      .panel-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }

      .close-btn {
        font-size: 40rpx;
        color: #999;
        padding: 0 20rpx;
      }
    }

    .search-bar {
      padding: 20rpx;
      border-bottom: 1rpx solid #eee;

      .search-input {
        background-color: #f5f5f5;
        border-radius: 30rpx;
        padding: 10rpx 20rpx;
        font-size: 28rpx;
      }
    }

    .member-list {
      flex: 1;
      max-height: 60vh;

      .member-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        border-bottom: 1rpx solid #f5f5f5;

        &:active {
          background-color: #f9f9f9;
        }

        .avatar {
          width: 70rpx;
          height: 70rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .nickname {
          font-size: 28rpx;
          color: #333;
        }
      }

      .empty-tip {
        padding: 40rpx 0;
        text-align: center;
        color: #999;
        font-size: 28rpx;
      }
    }
  }
</style>
