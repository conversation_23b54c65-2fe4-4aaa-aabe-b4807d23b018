<template>
  <view class="message-item" :class="{ self: isSelf }" style="transform: rotate(180deg)">
    <image
      class="avatar"
      :src="avatar.trim()"
      mode="aspectFill"
      @longpress="handleLongPressAvatar"
      @click="handleAvatarClick"
    ></image>
    <view class="message-content">
      <view class="nickname" v-if="channelType !== 1">{{
        isSelf ? userStore.userInfo?.nickname : nickname
      }}</view>
      <!-- 图片消息单独处理 -->
      <template v-if="type === 'image'">
        <image
          :src="getImageContent()"
          :style="{
            width: getImageWidth() + 'px',
            height: getImageHeight() + 'px'
          }"
          @tap="handleImageClick"
          class="message-image"
        ></image>
        <!-- <text class="time">{{ time }}</text> -->
      </template>
      <!-- 其他类型消息使用气泡框 -->
      <template v-else>
        <view :class="isSelf && type === 'text' ? 'message-bubble is-self' : 'message-bubble'">
          <!-- 文本消息 - 分别处理表情、@用户和文本 -->
          <view v-if="type === 'text'" class="message-text">
            <template v-for="(part, index) in processTextWithEmojisAndMentions" :key="index">
              <!-- 普通文本 -->
              <text v-if="part.type === 'text'">{{ part.content }}</text>
              <!-- 表情符号 -->
              <text v-else-if="part.type === 'emoji'" class="emoji">{{ part.content }}</text>
              <!-- @用户 -->
              <text v-else-if="part.type === 'mention'" class="mention">{{ part.content }}</text>
            </template>
          </view>

          <!-- 语音消息 -->
          <view v-if="type === 'voice'" class="voice-message" @tap="handleVoiceClick">
            <text class="voice-icon">🎵</text>
            <text class="duration">{{ duration || 0 }}''</text>
          </view>

          <!-- 位置消息 -->
          <view v-if="type === 'location'" class="location-message" @tap="handleLocationClick">
            <text class="location-title">{{ content.title || '' }}</text>
            <text class="location-address">{{ content.address || '' }}</text>
            <image style="width: 100%" :src="content.img" mode="aspectFill"></image>
          </view>
        </view>
        <!-- <text class="time">{{ time }}</text> -->
      </template>
    </view>
  </view>
</template>

<script setup>
  import { computed, onMounted, ref } from 'vue'
  import { useUserStore } from '@/stores/user'
  import { HTTP_IMG } from '@/common/constant'
  const userStore = useUserStore()

  const props = defineProps({
    type: {
      type: String,
      default: 'text'
    },
    content: {
      type: [String, Object],
      default: ''
    },
    isSelf: {
      type: Boolean,
      default: false
    },
    avatar: {
      type: String,
      default: 'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
    },
    nickname: {
      type: String,
      default: ''
    },
    time: {
      type: String,
      default: ''
    },
    duration: {
      type: Number,
      default: 0
    },
    channelType: {
      type: Number,
      default: 1
    },
    messageId: {
      type: String,
      default: ''
    },
    metadata: {
      type: Object,
      default: () => ({})
    },
    fromUID: {
      type: String,
      default: ''
    }
  })

  // 用于防止长按和点击事件冲突
  const isLongPress = ref(false)
  const longPressTimer = ref(null)

  // 处理头像点击事件
  const handleAvatarClick = () => {
    // 如果是长按触发的，不执行点击操作
    if (isLongPress.value) {
      isLongPress.value = false
      return
    }

    // 如果是自己的头像，不触发跳转
    if (!props.isSelf) {
      uni.navigateTo({ url: '/pages/expertDetail/expert-detail?id=' + props.fromUID })
    }
  }

  // 处理长按头像
  const handleLongPressAvatar = () => {
    isLongPress.value = true

    // 设置定时器，确保isLongPress状态在点击事件之后重置
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value)
    }

    longPressTimer.value = setTimeout(() => {
      isLongPress.value = false
    }, 500)

    // 只有在群聊中且不是自己的消息时才触发
    if (!props.isSelf && props.channelType === 2) {
      console.log('长按头像，触发@用户事件')
      emit('longPressAvatar')
    }
  }

  const emit = defineEmits(['imageClick', 'voiceClick', 'locationClick', 'longPressAvatar', 'atMe'])

  // 检查消息是否@了当前用户
  const checkIfAtMe = computed(() => {
    // 如果是自己发送的消息，不需要检查
    if (props.isSelf) return false

    // 检查是否已经读过这条@消息
    const readAtMessages = uni.getStorageSync('readAtMessages') || []
    if (readAtMessages.includes(props.messageId.toString())) return false

    // 检查消息内容中的mention属性
    if (props.content && props.content.mention) {
      // 检查是否@所有人
      if (props.content.mention.all) {
        return true
      }

      // 检查是否@了当前用户
      if (props.content.mention.uids && Array.isArray(props.content.mention.uids)) {
        return props.content.mention.uids.includes(userStore.userInfo?.userId)
      }
    }

    // 兼容旧的metadata.mentionedUsers方式
    if (props.channelType === 2 && props.metadata && props.metadata.mentionedUsers) {
      // 检查mentionedUsers数组中是否包含当前用户ID
      return props.metadata.mentionedUsers.includes(userStore.userInfo?.userId)
    }

    return false
  })

  // 在组件挂载时检查是否有人@我
  onMounted(() => {
    if (checkIfAtMe.value) {
      emit('atMe', {
        messageId: props.messageId,
        content: props.content,
        fromUser: {
          uid: props.fromUID,
          nickname: props.nickname
        }
      })
    }
  })

  // 获取文本内容
  const getTextContent = () => {
    if (typeof props.content === 'string') {
      return props.content
    } else if (props.content && typeof props.content === 'object') {
      return props.content.text || props.content.content || ''
    }
    return ''
  }

  // 处理表情符号和@用户的显示
  const processTextWithEmojisAndMentions = computed(() => {
    const text = getTextContent()

    // 使用正则表达式匹配表情符号和@用户
    const emojiRegex =
      /[\u{1F300}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu
    const mentionRegex = /@\S+/g

    // 将文本分割成表情、@用户和普通文本部分
    let parts = []
    let lastIndex = 0
    let currentText = text

    // 先处理@用户
    let mentionMatch
    while ((mentionMatch = mentionRegex.exec(currentText)) !== null) {
      // 添加@用户前的文本
      if (mentionMatch.index > lastIndex) {
        parts.push({
          type: 'text',
          content: currentText.substring(lastIndex, mentionMatch.index)
        })
      }

      // 添加@用户
      parts.push({
        type: 'mention',
        content: mentionMatch[0]
      })

      lastIndex = mentionMatch.index + mentionMatch[0].length
    }

    // 添加最后一段文本
    if (lastIndex < currentText.length) {
      parts.push({
        type: 'text',
        content: currentText.substring(lastIndex)
      })
    }

    // 如果没有@用户，使用原始文本
    if (parts.length === 0) {
      parts = [
        {
          type: 'text',
          content: currentText
        }
      ]
    }

    // 再处理表情符号
    let finalParts = []
    for (const part of parts) {
      if (part.type !== 'text') {
        finalParts.push(part)
        continue
      }

      let textPart = part.content
      let textLastIndex = 0
      let emojiMatch

      while ((emojiMatch = emojiRegex.exec(textPart)) !== null) {
        // 添加表情前的文本
        if (emojiMatch.index > textLastIndex) {
          finalParts.push({
            type: 'text',
            content: textPart.substring(textLastIndex, emojiMatch.index)
          })
        }

        // 添加表情
        finalParts.push({
          type: 'emoji',
          content: emojiMatch[0]
        })

        textLastIndex = emojiMatch.index + emojiMatch[0].length
      }

      // 添加最后一段文本
      if (textLastIndex < textPart.length) {
        finalParts.push({
          type: 'text',
          content: textPart.substring(textLastIndex)
        })
      }
    }

    return finalParts.length > 0 ? finalParts : [{ type: 'text', content: text }]
  })

  // 获取图片内容
  const getImageContent = () => {
    if (typeof props.content === 'string') {
      return props.content
    }
    return props.content?.url || ''
  }

  // 获取图片显示宽度
  const getImageWidth = () => {
    if (typeof props.content === 'object') {
      const maxWidth = 160 // 最大宽度改为160px
      const maxHeight = 120 // 最大高度改为120px
      const ratio = Math.min(maxWidth / props.content.width, maxHeight / props.content.height)
      const displayWidth = Math.floor(props.content.width * ratio)
      return displayWidth
    }
    return props.content?.width || 200 // 默认宽度
  }

  // 获取图片显示高度
  const getImageHeight = () => {
    if (typeof props.content === 'object') {
      const maxWidth = 160 // 最大宽度改为160px
      const maxHeight = 120 // 最大高度改为120px
      const ratio = Math.min(maxWidth / props.content.width, maxHeight / props.content.height)
      const displayHeight = Math.floor(props.content.height * ratio)
      return displayHeight
    }
    return props.content?.height || 150 // 默认高度
  }

  // 获取位置名称
  const getLocationName = () => {
    if (props.content && typeof props.content === 'object') {
      return props.content.name || '位置信息'
    }
    return '位置信息'
  }

  // 获取位置地址
  const getLocationAddress = () => {
    if (props.content && typeof props.content === 'object') {
      return props.content.address || ''
    }
    return ''
  }

  const handleImageClick = () => {
    emit('imageClick', getImageContent())
  }

  const handleVoiceClick = () => {
    emit('voiceClick', props.content)
  }

  const handleLocationClick = () => {
    emit('locationClick', props.content)
  }
</script>

<style lang="scss" scoped>
  .message-item {
    display: flex;
    margin-bottom: 20rpx;
    padding: 0 20rpx;

    &.self {
      flex-direction: row-reverse;

      .message-content {
        align-items: flex-end;
        margin-right: 20rpx;
        margin-left: 0;

        .message-bubble {
          background-color: #ffffff;
          border-radius: 12rpx;
          &::before {
            right: -15rpx;
            left: auto;
            border-right-color: transparent;
            border-left-color: #ffffff;
          }
          &.is-self {
            background: #d8f4da;
            border-radius: 4px;
            &::before {
              right: -15rpx;
              left: auto;
              border-right-color: transparent;
              border-left-color: #d8f4da;
            }
          }
        }
      }
    }
  }

  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .message-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-left: 20rpx;
    max-width: 70%;
  }

  .nickname {
    font-size: 24rpx;
    color: #000;
    margin-bottom: 8rpx;
  }

  .message-image {
    max-width: 160px;
    max-height: 120px;
    border-radius: 8rpx;
    object-fit: contain;
    margin: 8rpx 0;
  }

  .message-bubble {
    background-color: #ffffff;
    // padding: 16rpx 24rpx;
    border-radius: 12rpx;
    position: relative;
    word-break: break-all;

    &::before {
      content: '';
      position: absolute;
      left: -16rpx;
      top: 20rpx;
      border: 8rpx solid transparent;
      border-right-color: #ffffff;
    }

    .message-text {
      font-size: 28rpx;
      color: #000;
      line-height: 1.4;
      font-family: PingFang SC;
      padding: 16rpx 24rpx;
    }

    .emoji {
      display: inline-block;
      font-size: 32rpx;
      vertical-align: middle;
    }

    .mention {
      display: inline-block;
      color: #1989fa;
      font-weight: 500;
    }

    .voice-message {
      display: flex;
      align-items: center;
      min-width: 120rpx;

      .voice-icon {
        font-size: 40rpx;
        margin-right: 10rpx;
      }

      .duration {
        font-size: 28rpx;
        color: #666;
      }
    }

    .location-message {
      width: 450rpx;
      height: 300rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-size: 28rpx;
      color: #666;

      .location-title {
        font-size: 28rpx;
        font-weight: 500;
        padding: 8rpx 0 0 24rpx;
        color: #0d0e0f;
      }

      .location-address {
        font-size: 20rpx;
        color: #666;
        padding: 0 24rpx 8rpx 24rpx;
      }
    }
  }

  .time {
    font-size: 24rpx;
    color: #ddd;
    margin-top: 8rpx;
  }
</style>
