<template>
  <view class="chat-list-container">
    <scroll-view
      :bounces="false"
      :scroll-anchoring="true"
      class="chat-list"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-with-animation="useAnimation"
      :show-scrollbar="false"
      @scrolltolower="handleScrollToUpper"
      ref="scrollView"
      :id="scrollViewId"
      style="transform: rotate(180deg)"
    >
      <!-- <view class="loading" v-if="loading">
        <uni-load-more status="loading" />
      </view> -->

      <view :class="['messages-container', { 'messages-container-keyboard': isKeyboardVisible }]" ref="messagesContainer">
        <block v-for="(message, index) in messages" :key="message.message_id">
          <!-- 日期分隔符，倒序时放在消息上方 -->

          <view
            :id="`message-${message.message_id}`"
            :class="[
              'message-wrapper',
              `message-item-${message.message_id}`,
              { 'message-highlight': highlightMessageId === message.message_id }
            ]"
          >
            <chat-message
              :type="message.type"
              :content="message.content"
              :is-self="message.fromUID === currentUID"
              :avatar="message.avatar"
              :nickname="message.nickname"
              :time="formatTime(message.timestamp)"
              :duration="message.duration"
              :channel-type="channelType"
              :message-id="message.message_id"
              :metadata="message.metadata"
              :fromUID="message.fromUID"
              @image-click="handleImageClick"
              @voice-click="handleVoiceClick"
              @location-click="handleLocationClick"
              @longPressAvatar="handleLongPressAvatar(message)"
              @atMe="handleAtMe"
              ref="messageRefs"
            />
          </view>
          <view
            class="date-divider"
            v-if="shouldShowDate(message, index)"
            style="transform: rotate(180deg)"
          >
            <text>{{ formatDate(props.messages[index].timestamp) }}</text>
          </view>
        </block>
      </view>
    </scroll-view>

    <!-- @我提醒气泡 -->
    <at-reminder-bubble
      :visible="showAtReminder"
      :text="atReminderText"
      :message-id="atMessageId"
      @click="scrollToAtMessage"
    />
  </view>
</template>

<script setup>
  import { ref, computed, watch, nextTick } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import ChatMessage from './ChatMessage.vue'
  import AtReminderBubble from './AtReminderBubble.vue'
  import { formatDate, formatTime } from '@/utils/date'
  import { useUserStore } from '@/stores/user'
  import { useConversationStore } from '@/stores/conversation'
  import { useGroupStore } from '@/stores/group'
  import { storeToRefs } from 'pinia'
  import { groupsApi } from '@/common/api'
  // 使用pinia store获取用户信息
  const userStore = useUserStore()
  const conversationStore = useConversationStore()
  const groupStore = useGroupStore()
  const { userInfo } = storeToRefs(userStore)

  const props = defineProps({
    messages: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    channelType: {
      type: Number,
      default: 1
    },
    channelId: {
      type: String,
      default: ''
    },
    isKeyboardVisible: {
      type: Boolean,
      default: false
    }
  })

  // 计算当前用户ID
  const currentUID = computed(() => {
    return userInfo.value?.userId || ''
  })

  const emit = defineEmits(['loadMore', 'imageClick', 'voiceClick', 'locationClick', 'atUser'])

  const scrollTop = ref(0)
  const showDateDivider = ref(false)
  const messagesContainer = ref(null) // 消息容器引用
  const scrollView = ref(null) // 滚动视图引用
  const messageRefs = ref([]) // 消息组件引用
  const isHistoryLoading = ref(false) // 内部历史加载状态
  const useAnimation = ref(true) // 是否使用滚动动画
  const scrollViewId = 'chat-list-scroll-view' // 滚动视图ID
  const anchormessage_id = ref(null) // 锚点消息ID

  // @我提醒相关
  const showAtReminder = ref(false)
  const atReminderText = ref('有人@我')
  const atMessageId = ref('')
  const atMessages = ref([]) // 存储所有@我的消息ID
  const highlightMessageId = ref(null) // 当前高亮的消息ID

  // 添加一个变量记录上一次滚动位置
  const lastScrollTop = ref(0)

  // 添加一个变量，记录当前已显示的@消息数量
  const atMessageCount = ref(0)
  const MAX_AT_MESSAGES = 9 // 最大显示的@消息数量

  // 处理@我事件
  const handleAtMe = data => {
    console.log('收到@我的消息:', data)
    const readAtMessages = uni.getStorageSync('readAtMessages') || []
    if (readAtMessages.includes(data.messageId)) return
    // 检查是否已达到最大显示数量
    if (atMessageCount.value >= MAX_AT_MESSAGES) {
      console.log('已达到最大@消息显示数量:', MAX_AT_MESSAGES)
      return
    }

    // 增加计数
    atMessageCount.value++

    // 添加到@我的消息列表
    if (!atMessages.value.includes(data.messageId)) {
      atMessages.value.push(data.messageId)
    }

    // 显示提醒气泡
    atMessageId.value = data.messageId
    atReminderText.value = `${data.fromUser?.nickname || '有人'}@我`
    showAtReminder.value = true
  }

  // 高亮消息方法
  const highlightMessage = messageId => {
    if (!messageId) return

    // 设置高亮消息ID
    highlightMessageId.value = messageId

    // 3秒后取消高亮
    setTimeout(() => {
      highlightMessageId.value = null
    }, 3000)
  }

  // 滚动到@我的消息位置
  const scrollToAtMessage = messageId => {
    if (!messageId) return

    // 确保messageId是字符串类型
    const messageIdStr = messageId.toString()

    console.log('开始处理@消息定位:', messageIdStr)

    // 将消息ID添加到已读@消息列表中
    const readAtMessages = uni.getStorageSync('readAtMessages') || []
    if (!readAtMessages.includes(messageIdStr)) {
      readAtMessages.push(messageIdStr)
      uni.setStorageSync('readAtMessages', readAtMessages)
    }

    // 高亮显示被@的消息
    highlightMessage(messageIdStr)

    // 隐藏提醒气泡
    showAtReminder.value = false

    // 从@我的消息列表中移除该消息
    atMessages.value = atMessages.value.filter(id => id !== messageIdStr)

    // 查找消息在数组中的位置
    const messageIndex = props.messages.findIndex(msg => {
      const msgId = msg.message_id ? msg.message_id.toString() : ''
      return msgId === messageIdStr
    })

    if (messageIndex !== -1) {
      console.log(`找到消息索引: ${messageIndex}, 总消息数: ${props.messages.length}`)

      // 使用uni.createSelectorQuery获取元素位置
      const query = uni.createSelectorQuery()
      query.select(`.message-item-${messageIdStr}`).boundingClientRect()
      query.select('.messages-container').boundingClientRect()
      query.exec(res => {
        if (res && res[0]) {
          // 获取元素的top位置
          console.log('消息元素:', res)
          const scrollPosition = res[1].height + res[1].top - res[0].height - res[0].top

          console.log('消息位置:', scrollPosition)

          // 启用动画
          useAnimation.value = true

          // 先滚动到稍微偏离的位置，然后再滚动到准确位置，这样可以确保滚动效果生效
          scrollTop.value = scrollPosition + 100
          setTimeout(() => {
            scrollTop.value = scrollPosition
          }, 300)
        } else {
          console.log('未找到消息元素，使用索引计算位置')
          // 如果找不到元素，使用索引计算一个估计位置
          const AVERAGE_MESSAGE_HEIGHT = 120
          const estimatedPosition = messageIndex * AVERAGE_MESSAGE_HEIGHT

          scrollTop.value = estimatedPosition + 100
          setTimeout(() => {
            scrollTop.value = estimatedPosition
          }, 300)
        }
      })
    } else {
      console.log('未找到消息，滚动到底部')
      scrollToBottom()
    }

    // // 如果还有其他@我的消息，显示下一条
    // if (atMessages.value.length > 0) {
    //   setTimeout(() => {
    //     atMessageId.value = atMessages.value[0]
    //     showAtReminder.value = true
    //   }, 2000)
    // }
  }

  // 倒序渲染时，分隔符应出现在每个日期组的第一条消息上方
  const shouldShowDate = (message, index) => {
    // 最新的消息（index === 0）一定显示分隔符
    // if (index === 0) return false
    if (index === props.messages.length - 1) return true
    // 与前一条消息比较日期
    const prevMessage = props.messages[index + 1]
    const currentDate = new Date(message.timestamp)
    const prevDate = new Date(prevMessage.timestamp)
    return currentDate.toDateString() !== prevDate.toDateString()
  }

  const currentDate = computed(() => {
    if (props.messages.length > 0) {
      return formatDate(props.messages[0].timestamp)
    }
    return ''
  })

  // 处理滚动到顶部事件，触发父组件的loadMore
  const handleScrollToUpper = () => {
    if (props.loading || isHistoryLoading.value) return

    // 禁用滚动动画，避免加载历史消息时的闪现
    useAnimation.value = false

    // 记录第一条消息ID作为锚点
    if (props.messages.length > 0) {
      anchormessage_id.value = props.messages[0].message_id
    }

    // 通知父组件加载更多消息
    emit('loadMore')

    // 标记为正在加载历史消息
    isHistoryLoading.value = true

    // 延迟恢复滚动动画
    setTimeout(() => {
      isHistoryLoading.value = false
      setTimeout(() => {
        useAnimation.value = true
      }, 500)
    }, 100)
  }

  // 滚动到锚点元素
  const scrollToAnchor = () => {
    if (!anchormessage_id.value) return

    // 设置scroll-into-view属性，实现锚点定位
    scrollIntoView.value = `anchor-${anchormessage_id.value}`
    console.log('设置滚动到锚点:', scrollIntoView.value)

    // 延迟清除锚点ID
    setTimeout(() => {
      scrollIntoView.value = ''
    }, 500)
  }

  const handleImageClick = image => {
    emit('imageClick', image)
  }

  const handleVoiceClick = voice => {
    emit('voiceClick', voice)
  }

  const handleLocationClick = location => {
    emit('locationClick', location)
  }

  // 处理长按头像@用户
  const handleLongPressAvatar = message => {
    // 如果是群聊且不是自己的消息
    if (props.channelType === 2 && message.fromUID !== currentUID.value) {
      // 查找群成员信息
      const member = groupStore.getMemberById(message.fromUID)
      if (member) {
        // 触发@用户事件
        emit('atUser', member)
      } else {
        // 如果在群成员列表中找不到，使用消息中的信息构建一个临时用户对象
        const tempUser = {
          uid: message.fromUID,
          name: message.nickname || message.fromUID,
          avatar: message.avatar
        }
        emit('atUser', tempUser)
      }
    }
  }

  // 滚动到底部方法
  const scrollToBottom = () => {
    console.log('执行滚动到底部')

    // 启用滚动动画
    useAnimation.value = true

    // 设置scrollTop为0，因为消息列表是倒序的，所以0是底部
    scrollTop.value = 0

    // 更新lastScrollTop
    lastScrollTop.value = 0
  }

  // 滚动到指定位置
  const scrollToPosition = position => {
    console.log('滚动到指定位置:', position)

    // 启用动画
    useAnimation.value = true

    // 设置滚动位置
    scrollTop.value = position
  }

  // 重置@消息计数器
  const resetAtMessageCount = () => {
    atMessageCount.value = 0
    atMessages.value = []
    showAtReminder.value = false
  }

  // 暴露方法给父组件
  defineExpose({
    scrollToBottom,
    scrollToAnchor,
    scrollToAtMessage,
    scrollToPosition,
    resetAtMessageCount
  })

  // 监听消息变化
  watch(
    () => props.messages,
    async (newMessages, oldMessages) => {
      if (newMessages.length > oldMessages.length && !isHistoryLoading.value) {
        // 如果是新消息，滚动到底部
        // scrollToBottom()
      }

      // 如果消息列表被完全替换（例如切换会话），重置@消息计数
      if (
        oldMessages.length > 0 &&
        newMessages.length > 0 &&
        newMessages[0].message_id !== oldMessages[0].message_id
      ) {
        resetAtMessageCount()
      }
    },
    { deep: true }
  )

  onLoad(option => {
    if (option.channelType == 2) {
      groupsApi
        .getGroupMembers({
          groupCode: option.groupCode
        })
        .then(res => {
          console.log(res)
        })
    }

    // 重置@消息计数器
    resetAtMessageCount()

    // 初始加载时滚动到底部
    setTimeout(() => {
      scrollToBottom()
      console.log('初始加载完成，滚动到底部')
    }, 500)
  })
</script>

<style lang="scss" scoped>
  .chat-list-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    height: 100%;
    width: 100%;
  }

  .chat-list {
    height: 100%;
    width: 100%;
  }

  .messages-container {
    padding: 20rpx 0;
    min-height: 100%;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }
  .messages-container-keyboard {
    padding-top: calc(env(safe-area-inset-top) + 120rpx);
  }

  .message-wrapper {
    position: relative;
    width: 100%;
    padding: 4rpx 0;

    &.message-highlight {
      background-color: rgba(255, 249, 196, 0.8);
      animation: highlight-pulse 2s ease-in-out;
      border-radius: 8rpx;
      padding: 8rpx 0;
      box-shadow: 0 0 8rpx rgba(0, 0, 0, 0.1);
    }
  }

  @keyframes highlight-pulse {
    0% {
      background-color: rgba(255, 249, 196, 1);
    }
    50% {
      background-color: rgba(255, 249, 196, 0.5);
    }
    100% {
      background-color: rgba(255, 249, 196, 1);
    }
  }

  .message-anchor {
    height: 20px;
    width: 100%;
    position: relative;
    z-index: 100;
    background-color: transparent;
    margin: 10rpx 0;
    display: block;
    // 添加边框，便于调试时查看
    // border: 1px dashed #ccc;
  }

  .date-divider {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20rpx 0;

    text {
      font-size: 24rpx;
      color: #000;
      background-color: #fff;
      padding: 4rpx 16rpx;
      border-radius: 10rpx;
      font-family: PingFang SC;
    }
  }

  .loading {
    padding: 20rpx 0;
    display: flex;
    justify-content: center;
  }
</style>
