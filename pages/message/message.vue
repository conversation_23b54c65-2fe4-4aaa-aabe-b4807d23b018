<template>
  <view class="container">
    <view class="message-list">
      <view
        class="message-item"
        v-for="(item, index) in messageList"
        :key="index"
        @click="navigateToChat(item)"
      >
        <image
          class="avatar"
          :src="item.channel.logo || '/static/avatar/default.png'"
          mode="aspectFill"
        ></image>
        <view class="message-content">
          <view class="message-header">
            <text class="username">{{ item.channel.title || item.channel.channelID }}</text>
            <text class="time">{{ formatTime(item.timestamp) }}</text>
          </view>
          <view class="message-brief">
            <text class="last-message">{{ getLastMessageContent(item) }}</text>
            <view class="unread" v-if="item.unread > 0">{{ item.unread }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue'
  import { useUserStore } from '@/stores/user'
  import { storeToRefs } from 'pinia'
  import { formatTime } from '@/utils/date'
  import WKIMManager from '@/utils/wukongim'

  // 初始化store
  const store = useUserStore()
  const { userInfo } = storeToRefs(store)

  // 会话列表数据
  const messageList = ref([])

  // 获取最后一条消息的显示内容
  const getLastMessageContent = conversation => {
    if (!conversation.lastMessage) return '暂无消息'

    // 根据消息类型返回不同的显示内容
    const content = conversation.lastMessage.content
    if (typeof content === 'string') {
      return content
    } else if (content && content.type) {
      switch (content.type) {
        case 'text':
          return content.text || ''
        case 'image':
          return '[图片]'
        case 'voice':
          return '[语音]'
        case 'location':
          return '[位置]'
        default:
          return '[未知消息类型]'
      }
    }
    return '暂无消息'
  }

  // 同步会话列表
  const syncConversations = async () => {
    try {
      const conversations = await WKIMManager.getConversationList()
      messageList.value = conversations
    } catch (error) {
      console.error('获取会话列表失败:', error)
    }
  }

  // 跳转到聊天页面
  const navigateToChat = conversation => {
    uni.navigateTo({
      url: `/pages/chat/index?channelID=${conversation.channel.channelID}&channelType=${conversation.channel.channelType}&nickName=${conversation.channel.title || conversation.channel.channelID}`
    })
  }

  // 添加会话监听器
  const onConversationChange = (conversation, action) => {
    if (action === 'add') {
      // 新增会话
      messageList.value.unshift(conversation)
    } else if (action === 'update') {
      // 更新会话
      const index = messageList.value.findIndex(
        item =>
          item.channel.channelID === conversation.channel.channelID &&
          item.channel.channelType === conversation.channel.channelType
      )
      if (index > -1) {
        messageList.value[index] = conversation
      }
    } else if (action === 'remove') {
      // 删除会话
      messageList.value = messageList.value.filter(
        item =>
          item.channel.channelID !== conversation.channel.channelID ||
          item.channel.channelType !== conversation.channel.channelType
      )
    }

    // 按时间戳排序
    messageList.value.sort((a, b) => b.timestamp - a.timestamp)
  }

  onMounted(() => {
    // 首次加载会话列表
    // syncConversations()

    // 添加会话监听器
    WKIMManager.addConversationListener(onConversationChange)
  })

  // 组件卸载时移除监听器
  onUnmounted(() => {
    WKIMManager.removeConversationListener(onConversationChange)
  })
</script>

<style lang="scss">
  .container {
    .message-list {
      .message-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        // background-color: #fff;
        border-bottom: 1rpx solid #eee;

        .avatar {
          width: 100rpx;
          height: 100rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .message-content {
          flex: 1;

          .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10rpx;

            .username {
              font-size: 32rpx;
              color: #333;
              font-weight: 500;
            }

            .time {
              font-size: 24rpx;
              color: #999;
            }
          }

          .message-brief {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .last-message {
              font-size: 28rpx;
              color: #666;
              @include text-ellipsis;
            }

            .unread {
              min-width: 36rpx;
              height: 36rpx;
              padding: 0 10rpx;
              background-color: #ff5a5f;
              border-radius: 18rpx;
              font-size: 24rpx;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }
    }
  }
</style>
