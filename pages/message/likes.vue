<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-nav-bar2">
      <view class="nav-left" @tap="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
      <view class="nav-title">获赞</view>
      <view class="nav-right"></view>
    </view>

    <!-- 获赞列表 -->
    <scroll-view scroll-y class="likes-list-container">
      <!-- 按日期分组显示 -->
      <block v-for="(group, index) in likesByDate" :key="index">
        <!-- 日期分隔线 -->
        <view class="date-divider">
          <text>{{ group.date }}</text>
        </view>

        <!-- 该日期下的获赞列表 -->
        <view class="likes-list">
          <view
            class="like-item"
            v-for="(like, likeIndex) in group.likes"
            :key="likeIndex"
            @tap="goToDetail(like)"
          >
            <image class="avatar" :src="like.avatar" mode="aspectFill"></image>
            <view class="like-content">
              <text class="username">{{ like.username }}</text>
              <view class="like-info">
                <text class="like-icon">👍</text>
                <text class="like-text">赞了你的评论</text>
              </view>
              <text class="time">{{ like.time }}</text>
            </view>
            <image
              class="content-preview"
              :src="like.contentImage"
              mode="aspectFill"
              v-if="like.contentImage"
            ></image>
          </view>
        </view>
      </block>

      <!-- 无数据提示 -->
      <view class="empty-tip" v-if="likesByDate.length === 0">
        <text>暂无获赞记录</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { ref, onMounted } from 'vue'

  // 模拟获赞数据
  const likesByDate = ref([
    {
      date: '2025年05月16日',
      likes: [
        {
          id: '1',
          username: '夏小帅',
          avatar: 'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg',
          time: '10:00',
          contentType: 'comment',
          contentText: '赞了你的评论',
          contentImage: '/static/index/expert/1.png'
        },
        {
          id: '2',
          username: '王雨欣',
          avatar: 'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg',
          time: '15:43',
          contentType: 'comment',
          contentText: '赞了你的评论',
          contentImage: '/static/index/expert/1.png'
        }
      ]
    },
    {
      date: '2025年05月15日',
      likes: [
        {
          id: '3',
          username: '夏小帅',
          avatar: 'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg',
          time: '15:43',
          contentType: 'comment',
          contentText: '赞了你的评论',
          contentImage: '/static/index/expert/2.png'
        }
      ]
    },
    {
      date: '2025年04月20日',
      likes: [
        {
          id: '4',
          username: '黄诗琪',
          avatar: 'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg',
          time: '17:12',
          contentType: 'comment',
          contentText: '赞了你的评论',
          contentImage: ''
        }
      ]
    }
  ])

  // 返回上一页
  const goBack = () => {
    uni.navigateBack()
  }

  // 跳转到详情页
  const goToDetail = like => {
    // 根据点赞类型跳转到不同的详情页
    if (like.contentType === 'comment') {
      uni.navigateTo({
        url: `/pages/moment/detail?id=${like.id}`
      })
    }
  }

  // 获取点赞列表数据
  const getLikesData = async () => {
    try {
      // 这里应该调用API获取真实数据
      // const res = await api.getLikesList()
      // if (res.code === 200) {
      //   // 处理数据，按日期分组
      //   likesByDate.value = processLikesData(res.data)
      // }
    } catch (error) {
      console.error('获取点赞列表失败:', error)
      uni.showToast({
        title: '获取数据失败',
        icon: 'none'
      })
    }
  }

  // 按日期处理点赞数据
  const processLikesData = data => {
    // 实际项目中，这里应该将API返回的数据按日期分组
    return data
  }

  onMounted(() => {
    getLikesData()
  })
</script>

<style lang="scss" scoped>
  .page-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #f5f5f5;
    box-sizing: border-box;
  }

  .custom-nav-bar2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    background-color: #ffffff;
    padding-top: var(--status-bar-height);
    height: calc(88rpx + var(--status-bar-height));

    .nav-left,
    .nav-right {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-back-icon {
      font-size: 48rpx;
      font-weight: bold;
    }

    .nav-title {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .likes-list-container {
    flex: 1;
    height: 0;
  }

  .date-divider {
    padding: 20rpx;
    background-color: #f5f5f5;

    text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .likes-list {
    background-color: #ffffff;
  }

  .like-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }

    .like-content {
      flex: 1;

      .username {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
      }

      .like-info {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .like-icon {
          font-size: 28rpx;
          color: #ff9500;
          margin-right: 8rpx;
        }

        .like-text {
          font-size: 26rpx;
          color: #666;
        }
      }

      .time {
        font-size: 24rpx;
        color: #999;
      }
    }

    .content-preview {
      width: 100rpx;
      height: 100rpx;
      border-radius: 8rpx;
      object-fit: cover;
    }
  }

  .empty-tip {
    padding: 60rpx 0;
    text-align: center;
    color: #999;
    font-size: 28rpx;
  }
</style>
