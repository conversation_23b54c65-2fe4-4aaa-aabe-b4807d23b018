<template>
  <view class="real-authentication">
    <NavigationCustorm style="background: #fff;">
      <template #title>
        实名认证
      </template>
    </NavigationCustorm>
    <view class="real-authentication-content">
      <view class="row">
        <view class="label">证件类型</view>
        <view class="value">身份证</view>
      </view>
      <view class="row">
        <view class="label">证件姓名</view>
        <view class="value">{{ maskedRealname }}</view>
      </view>
      <view class="row">
        <view class="label">证件证号</view>
        <view class="value">{{ maskedIdcard }}</view>
      </view>
      <view class="line"></view>
      <view class="row tips">
        证件信息设置后不能修改，如有疑问 <text class="link" @click="handleContact">联系客服</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
import { userApi } from '@/common/api'
import { ref, computed } from 'vue'

const handleContact = () => {
  uni.navigateTo({
    url: '/pages/profile/contact'
  })
}

const idcardInfo = ref({})

// 计算属性：将姓名脱敏显示
const maskedRealname = computed(() => {
  if (!idcardInfo.value.realname) return ''
  const realname = idcardInfo.value.realname
  if (realname.length <= 1) return '*'
  return '*' + realname.substring(1)
})

// 计算属性：将身份证号脱敏显示
const maskedIdcard = computed(() => {
  if (!idcardInfo.value.idcard) return ''
  const idcard = idcardInfo.value.idcard
  if (idcard.length <= 2) return idcard
  return idcard.charAt(0) + '*'.repeat(idcard.length - 2) + idcard.charAt(idcard.length - 1)
})

const getRealAuthentication = async () => {
  const res = await userApi.getRealAuthentication()
  idcardInfo.value = res.data
}

getRealAuthentication()
</script>
<style lang="scss" scoped>
.real-authentication {
  padding-top: 198rpx;
  margin-bottom: 240rpx;
  background-color: #f4f8fb;
  height: 100vh;
}
.real-authentication-content {
  background: #fff;
  border-radius: 16rpx;
  margin: 32rpx;
  padding: 40rpx 32rpx;
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    &:last-child {
      margin-bottom: 0;
    }
    .label {
      color: #333;
      font-size: 30rpx;
    }
    .value {
      color: #bcbcbc;
      font-size: 30rpx;
    }
    &.tips {
      color: #999;
      font-size: 24rpx;
      margin-top: 32rpx;
      justify-content: flex-start;
      .link {
        color: #66D47E;
        margin-left: 16rpx;
      }
    }
  }
  .line {
    border-bottom: 1rpx solid #f4f8fb;
  }
}
</style>