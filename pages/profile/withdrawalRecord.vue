<template>
  <div class="withdrawal-record-container">
    <NavigationCustorm >
      <template #title>
        提现记录
      </template>
    </NavigationCustorm>
    <view class="withdrawal-record-content">
    <div
      class="withdrawal-record-card"
      v-for="(item, idx) in records"
      :key="idx"
    >
      <div class="row">
        <span class="label">流水号</span>
        <span class="value serial">{{ item.incomeRecordCode }} | <span class="serial-text" @click="copySerial(item.incomeRecordCode)">复制</span></span>
      </div>
      <div class="row">
        <span class="label">提现时间</span>
        <span class="value">{{ item.recordTime }}</span>
      </div>
      <div class="row">
        <span class="label">提现金额</span>
        <span class="value amount">¥{{ formatAmount(item.amount) }}</span>
      </div>
      <div class="row">
        <span class="label">提现状态</span>
        <span
          class="value status"
          :class="{
            success: item.status === 0,
            fail: item.status === 1,
          }"
        >
          {{ item.status===0?'成功':item.status===1?'失败':'提现中' }}
        </span>
      </div>
      <view class="line" v-if="idx !== records.length - 1"></view>
    </div>
  </view>
  </div>
</template>

<script setup>
import { ref,onMounted } from 'vue'
import { profileApi } from '@/common/api'
import { useUserStore } from '@/stores/user'
import { formatAmount } from '@/common/utils'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

const userStore = useUserStore()
const records = ref([])

const copySerial = (serial) => {
  uni.setClipboardData({
    data: serial,
  })
  uni.showToast({ title: '复制成功', icon: 'success' })
}
onMounted(async()=>{
  const res = await profileApi.queryWithdrawalRecord({
    pageNum:1,
    pageSize:10,
    type:1
  })
  if(res.code==200){

    records.value = res.rows||[]
  }
})
</script>

<style scoped>
.withdrawal-record-container {
  max-width: 800rpx;
  margin: 0 auto;
  background: #F4F8FB;
  height:100vh;
  .tag-page-header{
    color: #fff;
  }
}
.withdrawal-record-content{
  padding: 222rpx 30rpx 30rpx 30rpx;
  height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
}
.withdrawal-record-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  padding: 32rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}
.row {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
  justify-content: space-between;
}
.label {
  font-family: 苹方-简, 苹方-简;
  font-weight: bold;
  font-size: 26rpx;
  color: #333333;
}
.value {
  font-size: 32rpx;
  margin-left: 16rpx;
  color: #666666;
}
.serial-text{
  font-family: 苹方-简, 苹方-简;
  font-weight: normal;
  font-size: 26rpx;
  color: #000000;
}
.amount{
  font-size: 30rpx;
  color: #000000;
}
.serial {
  font-family: monospace;
}
.copy-btn {
  margin-left: 12rpx;
  font-size: 24rpx;
  color: #888;
  background: #f0f0f0;
  border: none;
  border-radius: 8rpx;
  padding: 4rpx 16rpx;
  cursor: pointer;
}
.status{
  color: #FF7930;
}
.status.success {
  color: #32BD4A;
  font-weight: bold;
}
.status.pending {
  color: #ff9900;
  font-weight: bold;
}
.status.fail {
  color: #FF5F54;
  font-weight: bold;
}
.line{
  width: 100%;
  height: 1rpx;
  background: #D1D1D1;
}
</style>
