<template>
  <view class="about-us">
    <NavigationCustorm>
      <template #title>
        关于小岛屿
      </template>
    </NavigationCustorm>
    <view class="about-us-content">
      <!-- Logo区域 -->
      <view class="about-us-header">
        <image src="/static/images/profile/logo.png" mode="widthFix" class="about-us-logo"></image>
        <view class="app-description">让每一座小岛屿都不再是一座孤岛</view>
        <view class="app-version">当前版本：{{version}}</view>
      </view>

      <!-- 菜单项 -->
      <view class="menu-list">
        <view class="menu-item" @click="navigateTo('/pages/agreement/user')">
          <view class="menu-text">用户协议</view>
          <image src="/static/icon/arrow-right.png" mode="widthFix" class="arrow-icon"></image>
          <uni-icons type="arrowright" size="20" color="#D1D1D1"></uni-icons>
        </view>

        <view class="menu-item" @click="navigateTo('/pages/agreement/convention')">
          <view class="menu-text">平台文明公约</view>
          <image src="/static/icon/arrow-right.png" mode="widthFix" class="arrow-icon"></image>
          <uni-icons type="arrowright" size="20" color="#D1D1D1"></uni-icons>
        </view>

        <view class="menu-item" @click="navigateTo('/pages/agreement/privacy')">
          <view class="menu-text">隐私权政策</view>
          <image src="/static/icon/arrow-right.png" mode="widthFix" class="arrow-icon"></image>
          <uni-icons type="arrowright" size="20" color="#D1D1D1"></uni-icons>
        </view>

        <view class="menu-item" @click="navigateTo('/pages/agreement/sdk')">
          <view class="menu-text">接入第三方SDK目录</view>
          <image src="/static/icon/arrow-right.png" mode="widthFix" class="arrow-icon"></image>
          <uni-icons type="arrowright" size="20" color="#D1D1D1"></uni-icons>
        </view>
      </view>

      <!-- 底部版权信息 -->
      <view class="footer">
        <view class="copyright">Copyright © {{year}}</view>
        <view class="company">小岛屿网页科技有限公司 版权所有</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
import { getVersion } from '@/common/utils'

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 导航到指定页面
const navigateTo = (url) => {
  uni.navigateTo({
    url: url
  })
}
const year = ref(new Date().getFullYear())
const version = ref(getVersion())
</script>

<style lang="scss" scoped>
.about-us {
  height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .about-us-content{
    background: #F4F8FB;
    display: flex;
    flex-direction: column;
    flex: 1;
    padding-top: 192rpx;
  }
  // Logo区域
  .about-us-header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20rpx 32rpx 40rpx;

    .about-us-logo {
      width: 260rpx;
      height: 180rpx;
      margin-bottom: 24rpx;
    }

    .app-name {
      font-size: 48rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 16rpx;
    }

    .app-description {
      font-size: 28rpx;
      color: #000;
      margin-bottom: 20rpx;
      text-align: center;
      line-height: 1.5;
    }

    .app-version {
      font-size: 26rpx;
      color: #999;
    }
  }

  // 菜单列表
  .menu-list {
    margin-top: 20rpx;
    background-color: #fff;

    .menu-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 96rpx;
      padding: 0 32rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .menu-text {
        font-size: 32rpx;
        color: #000;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
      }

      .arrow-icon {
        width: 24rpx;
        height: 24rpx;
        opacity: 0.4;
      }
    }
  }

  // 底部版权信息
  .footer {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    padding: 40rpx 32rpx 60rpx;

    .copyright {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 8rpx;
    }

    .company {
      font-size: 24rpx;
      color: #999;
      text-align: center;
    }
  }
}
</style>