<template>
  <div class="withdrawal-channel-container">
    <div class="withdrawal-channel-card">
      <div class="row">
        <span class="label">真实姓名</span>
        <span class="value">XXXX</span>
      </div>
      <div class="row">
        <span class="label">微信号</span>
        <span class="value">xxxxxxxxxx</span>
      </div>
      <div class="row">
        <span class="label">手机号</span>
        <span class="value">189******1234</span>
      </div>
      <div class="row">
        <span class="label">验证码</span>
        <input
          class="input"
          v-model="code"
          type="text"
          placeholder="请输入验证码"
        />
        <button class="code-btn" @click="getCode">获取验证码</button>
      </div>
    </div>
    <button class="bind-btn" :disabled="!code" @click="bindNow">立即绑定</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const code = ref('')

function getCode() {
  // 这里可以添加获取验证码的逻辑
  alert('验证码已发送')
}

function bindNow() {
  // 这里可以添加绑定逻辑
  alert('绑定成功')
}
</script>

<style scoped>
.withdrawal-channel-container {
  max-width: 800rpx;
  margin: 0 auto;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.withdrawal-channel-card {
  background: #fafafa;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  padding: 32rpx 24rpx;
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}
.row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.label {
  color: #888;
  font-size: 30rpx;
  min-width: 120rpx;
}
.value {
  font-size: 32rpx;
  margin-left: 16rpx;
  flex: 1;
  color: #222;
}
.input {
  flex: 1;
  font-size: 32rpx;
  padding: 8rpx 12rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-left: 16rpx;
  outline: none;
  background: #fff;
}
.code-btn {
  margin-left: 12rpx;
  font-size: 24rpx;
  color: #888;
  background: #f0f0f0;
  border: none;
  border-radius: 8rpx;
  padding: 4rpx 16rpx;
  cursor: pointer;
  transition: background 0.2s;
}
.code-btn:hover {
  background: #e0e0e0;
}
.bind-btn {
  width: 100%;
  max-width: 600rpx;
  background: #f0f0f0;
  color: #888;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  margin-top: 32rpx;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.bind-btn:enabled {
  background: #2ecc40;
  color: #fff;
}
.bind-btn:disabled {
  background: #f0f0f0;
  color: #888;
  cursor: not-allowed;
}
</style>
