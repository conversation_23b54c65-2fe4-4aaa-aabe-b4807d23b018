<template>
	<view class="container">
		<!-- 顶部背景区域 -->
		<view class="profile-header" :style="{ backgroundImage: 'url(/static/images/profile/bg.png)' }">
			<!-- 访客 -->
			<view class="tr-content">
				<!-- 访客数量 -->
				<!-- <view class="visitor">
					<view v-if="userStore.userInfo?.visitorCount" class="num">+{{ userStore.userInfo?.visitorCount }}</view>
					<image src="/static/images/profile/visitor.png" style="width: 40rpx;height: 40rpx;"></image>
				</view> -->
				<image src="/static/images/profile/safety.png" @click="handleMenuClick('/pages/settings/safety')"
					style="width: 40rpx;height: 40rpx;margin-left: 20rpx;"></image>
				<image src="/static/images/profile/setting.png" @click="handleMenuClick('/pages/settings/index')"
					style="width: 40rpx;height: 40rpx;margin-left: 20rpx;"></image>
			</view>
			<view class="user-info">
				<view class="left-content">
					<image v-if="userStore.userInfo?.isRealNameVerified&&!userStore.userInfo?.openOrder" src="/static/images/profile/close-status.png" style="width: 106rpx;height: 106rpx;margin-right: 8rpx;" class="close-status" @click="handleMenuClick('/pages/user/edit')"></image>
					<image v-if="userStore.userInfo?.isRealNameVerified&&userStore.userInfo?.openOrder" src="/static/images/profile/open-status.png" style="width: 106rpx;height: 106rpx;margin-right: 8rpx;" class="close-status" @click="handleMenuClick('/pages/user/edit')"></image>
					<image class="avatar" :src="`${HTTP_IMG}${userStore.userInfo?.avatar}` || '/static/avatar/default.png'"
						mode="aspectFill" @click="handleMenuClick('/pages/user/edit')"></image>
					<view class="info-content">
						<view class="nickname-box">
							<text class="nickname">{{ userStore.userInfo?.nickname }}</text>
							<image v-if="userStore.userInfo?.isRealNameVerified" src="/static/index/level/vmark.png"
								style="width: 26rpx;height: 26rpx;margin-left: 8rpx;"></image>
							<!-- <view class="open-order" v-if="!userStore.userInfo?.openOrder" @click="handleOpenOrder">
								<image src="/static/images/profile/open.png" style="width: 112rpx;height: 36rpx;margin-right: 8rpx;">
								</image>
							</view>
							<view class="close-order" v-else @click="handleCloseOrder">
								<image src="/static/images/profile/close.png" style="width: 112rpx;height: 36rpx;margin-right: 8rpx;;">
								</image>
							</view> -->
						</view>
						<view class="account-content">
							<view class="accountCode">
								<text>岛屿号：{{ userStore.userInfo?.accountCode || '--' }}</text>
							</view>
							<!-- <image src="/static/images/profile/qcode.png" class="qcode"></image> -->
							<QrCode></QrCode>
						</view>
					</view>
				</view>
				<view>
					<view v-if="userStore.userInfo?.isRealNameVerified&&!userStore.userInfo?.openOrder" class="switch-box" @click="handleOpenOrder">
						<image src="/static/images/profile/switch.png" style="width: 32rpx;height: 42rpx;margin-right: 8rpx;">
						</image>
						<view class="switch-text">开启接单</view>
					</view>
					<view v-if="userStore.userInfo?.isRealNameVerified&&userStore.userInfo?.openOrder" class="switch-box" @click="handleCloseOrder">
						<image src="/static/images/profile/switch.png" style="width: 32rpx;height: 42rpx;margin-right: 8rpx;">
						</image>
						<view class="switch-text switch-text-close">关闭接单</view>
					</view>
					<!-- <uni-icons v-if="!userStore.userInfo?.showOfflineVerifyButton" type="scan" size="36" @click="handleScan"
						class="scan-code"></uni-icons> -->
				</view>
			</view>
		</view>
		<view class="profile-content">
			<!-- 关注区域 -->
			<view class="profile-follower">
				<view class="flex-row">
					<view class="stats">
						<view class="stat-item">
							<text class="count">{{ userStore.userInfo?.friendCount || 0 }}</text>
							<text class="label">朋友</text>
						</view>
						<view class="stat-item">
							<text class="count">{{
								userStore.userInfo?.followerCount || 0
							}}</text>
							<text class="label">粉丝</text>
						</view>
						<view class="stat-item">
							<text class="count">{{ userStore.userInfo?.followingCount || 0 }}</text>
							<text class="label">关注</text>
						</view>
					</view>
					<view class="my-info"
						@click="handleMenuClick(`/pages/expertDetail/expert-detail?id=${userStore.userInfo?.userId}`)">
						<image src="/static/images/profile/profile.png" style="width: 28rpx;height: 28rpx;"></image>
						<text>我的资料</text>
					</view>
				</view>
				<text class="signature">{{ userStore?.userInfo?.signature }}</text>
				<view class="info-tags">
					<view v-if="userLocation" class="location">
						<uni-icons type="location" size="16" color="#F9AF25"></uni-icons>
						<text>{{ userLocation }}</text>
					</view>
					<view class="sex">
						<image src="/static/images/profile/man.png" v-if="userStore?.userInfo?.sex === 1"
							style="width: 20rpx;height: 20rpx;"></image>
						<image src="/static/images/profile/woman.png" v-if="userStore?.userInfo?.sex === 2"
							style="width: 20rpx;height: 20rpx;"></image>
						<text v-if="age" class="age">{{ age }}</text>
					</view>
					<!-- 星座 -->
					<view v-if="constellation.label" class="constellation">
						<image :src="constellation.icon" style="width: 32rpx;height: 32rpx;"></image>
						<text>{{ constellation.label }}</text>
					</view>
				</view>
			</view>
			<!-- 钱包 -->
			<div class="money">
				<div class="money-item my-wallet" @click="handleMenuClick('/pages/profile/wallet')">
					<view>
						<text>我的钱包</text>
						<div class="count">{{ userInfo.wallet || 0 }}贝壳币</div>
					</view>
					<image src="/static/images/profile/wallet.png" style="width: 80rpx;height: 80rpx;"></image>
				</div>
				<div class="money-item my-income" @click="handleMenuClick('/pages/profile/income')">
					<view>
						<text>我的收益</text>
						<div class="count">{{ formatAmount(userInfo.income) || 0 }} 元</div>
					</view>
					<image src="/static/images/profile/income.png" style="width: 80rpx;height: 80rpx;"></image>
				</div>
			</div>
			<!-- 菜单入口 -->
			<view class="menu-list">

				<swiper class="swiper-box" circular :indicator-dots="true" :autoplay="autoplay" :interval="2000">
					<swiper-item class="swiper-item">
						<view class="menu-item" v-for="(item, index) in menuItems.slice(0, 8)" :key="index"
							@click="handleMenuClick(item.path)">
							<view class="menu-content">
								<image :src="`/static/images/profile/${item.icon}`"
									style="width: 44rpx;height: 44rpx;margin-bottom: 24rpx;"></image>
								<text class="menu-text">{{ item.text }}</text>
							</view>
						</view>
					</swiper-item>
					<swiper-item class="swiper-item">
						<view class="menu-item" v-for="(item, index) in menuItems.slice(8)" :key="index"
							@click="handleMenuClick(item.path)">
							<view class="menu-content">
								<image :src="`/static/images/profile/${item.icon}`"
									style="width: 44rpx;height: 44rpx;margin-bottom: 24rpx;"></image>
								<text class="menu-text">{{ item.text }}</text>
							</view>
						</view>
					</swiper-item>
				</swiper>
				<!-- <view class="menu-item" v-for="(item, index) in menuItems" :key="index" @click="handleMenuClick(item.path)">
					<view class="menu-content">
						<image :src="`/static/images/profile/${item.icon}`"
							style="width: 44rpx;height: 44rpx;margin-bottom: 24rpx;"></image>
						<text class="menu-text">{{ item.text }}</text>
					</view>
				</view> -->
			</view>
			<!-- 展示列表 -->
			<ShowList></ShowList>
		</view>
		<uni-popup ref="popup" mode="center">
			<view class="confirm-popup-content">
				<view class="title">温馨提示</view>
				<view v-if="!userStore.userInfo?.openOrder" class="content">是否开启接单功能？</view>
				<view v-if="userStore.userInfo?.openOrder" class="content">是否关闭接单？</view>
				<view v-if="userStore.userInfo?.openOrder" class="content">关闭后将无法接收新订单</view>
				<view class="btn-box">
					<view class="cancel" @click="handleCancel">取消</view>
					<view class="confirm" @click="handleConfirm">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import ShowList from './components/ShowList.vue'
import QrCode from './components/QrCode.vue'
import { reactive, computed, ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { userApi, profileApi } from '@/common/api'
import { objGet } from '@/uni_modules/uni-forms/components/uni-forms/utils'
import { HTTP_IMG } from '@/common/constant'
import { formatAmount } from '@/common/utils'
import aquarius from '@/static/images/constellation/<EMAIL>'
import taurus from '@/static/images/constellation/<EMAIL>'
import gemini from '@/static/images/constellation/<EMAIL>'
import cancer from '@/static/images/constellation/<EMAIL>'
import leo from '@/static/images/constellation/<EMAIL>'
import virgo from '@/static/images/constellation/<EMAIL>'
import libra from '@/static/images/constellation/<EMAIL>'
import scorpio from '@/static/images/constellation/<EMAIL>'
import sagittarius from '@/static/images/constellation/<EMAIL>'
import capricorn from '@/static/images/constellation/<EMAIL>'
import pisces from '@/static/images/constellation/<EMAIL>'

const popup = ref(null)
const userStore = useUserStore()
console.log('avatar==========', userStore.userInfo?.avatar)
const age = ref(0)
// 这是星座的计算方法，根据生日计算星座
const getConstellation = (birthday) => {
	if (!birthday) return {
		label: '',
		icon: ''
	}

	// 将生日字符串转换为Date对象
	const birthDate = new Date(birthday)
	const month = birthDate.getMonth() + 1 // getMonth()返回0-11，需要+1
	const day = birthDate.getDate()

	// 根据月日判断星座
	if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) {
		return {
			label: '白羊座',
			icon: aries
		}
	} else if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) {
		return {
			label: '金牛座',
			icon: taurus
		}
	} else if ((month === 5 && day >= 21) || (month === 6 && day <= 21)) {
		return {
			label: '双子座',
			icon: gemini
		}
	} else if ((month === 6 && day >= 22) || (month === 7 && day <= 22)) {
		return {
			label: '巨蟹座',
			icon: cancer
		}
	} else if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) {
		return {
			label: '狮子座',
			icon: leo
		}
	} else if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) {
		return {
			label: '处女座',
			icon: virgo
		}
	} else if ((month === 9 && day >= 23) || (month === 10 && day <= 23)) {
		return {
			label: '天秤座',
			icon: libra
		}
	} else if ((month === 10 && day >= 24) || (month === 11 && day <= 22)) {
		return {
			label: '天蝎座',
			icon: scorpio
		}
	} else if ((month === 11 && day >= 23) || (month === 12 && day <= 21)) {
		return {
			label: '射手座',
			icon: sagittarius
		}
	} else if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) {
		return {
			label: '摩羯座',
			icon: capricorn
		}
	} else if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) {
		return {
			label: '水瓶座',
			icon: aquarius
		}
	} else if ((month === 2 && day >= 19) || (month === 3 && day <= 20)) {
		return {
			label: '双鱼座',
			icon: pisces
		}
	}

	return {
		label: '',
		icon: ''
	}
}

const constellation = computed(() => {
	const birthday = userStore?.userInfo?.birthday
	// 根据生日计算星座
	return getConstellation(birthday)
})
const userInfo = reactive({
	wallet: 0,
	income: 0,
})
const userLocation = computed(() => {
	return userStore?.userInfo?.location?.split('-')[1] || ''
})

const menuItems = reactive([
	{
		icon: 'my-order.png',
		text: '我的订单',
		path: '/pages/myOrder/myOrder'
	},
	{
		icon: 'my-recruit.png',
		text: '我的招募',
		path: '/pages/recruit/index'
	},
	{
		icon: 'my-merchant.png',
		text: '商家订单',
		path: '/pages/ordersBymerchant/index'
	},
	{
		icon: 'my-appointment.png',
		text: '预约订单',
		path: '/pages/reservationOrder/reservationOrder'
	},
	{
		icon: 'my-shop.png',
		text: '商城订单',
		path: '/pages/orders/directsalesOrders'
	},
	{
		icon: 'my-center.png',
		text: '技能认证',
		path: '/pages/influencer/success'
	},
	{
		icon: 'my-level.png',
		text: '我的等级',
		path: '/pages/profile/level'
	},
	{
		icon: 'my-auth.png',
		text: '实名认证',
		path: '/pages/profile/RealAuthentication'
	},
	{
		icon: 'my-address.png',
		text: '我的地址',
		path: '/pages/profile/address?title=profile'
	},
	{
		icon: 'my-about.png',
		text: '关于我们',
		path: '/pages/profile/aboutUS'
	},
	{
		icon: 'my-service.png',
		text: '联系客服',
		path: '/pages/profile/contact'
	}
])
const handleOpenOrder = () => {
	popup.value.open()
}
const handleCloseOrder = () => {
	popup.value.open()
}
const handleCancel = () => {
	popup.value.close()
}
const handleConfirm = () => {
	userStore.userInfo.openOrder = !userStore.userInfo?.openOrder
	popup.value.close()
}
const handleMenuClick = (item) => {
	// 如果未登录且不是登录页面，则跳转到登录页
	if (!userStore.isLoggedIn && item.path !== '/pages/login/login') {
		goToLogin()
		return
	}

	// 如果已经实名认证，且点击实名认证，终止
	if (userStore.userInfo?.isRealNameVerified && item === '/pages/profile/RealAuthentication') {
		uni.navigateTo({
			url: '/pages/profile/RealAuthenticationDetail'
		})
		return
	}
	uni.navigateTo({
		url: item
	})
}
const handleScan = () => {
	uni.showToast({
		title: '暂未开放',
		icon: 'none'
	})

}
// 跳转到登录页面
const goToLogin = () => {
	uni.navigateTo({
		url: '/pages/login/login'
	})
}
const getIncome = async () => {
	const res = await profileApi.getMyIncome(userStore.userInfo?.userId)
	// const res ={
	// 	data:{
	// 		"createBy": "f10fb259a9bc4f5a99ff3e655a07807a",
	// 		"createTime": "2025-06-22 20:19:52",
	// 		"updateBy": "",
	// 		"updateTime": "2025-06-23 18:46:00",
	// 		"remark": null,
	// 		"id": 2,
	// 		"userId": "f10fb259a9bc4f5a99ff3e655a07807a",
	// 		"balance": 110,
	// 		"version": 2
	// 	}
	// }
	if (res.code === 200) {
		userInfo.income = res.data?.balance
	}

}
const getWallet = async () => {
	const res = await profileApi.getMyWallet(userStore.userInfo?.userId)
	if (res.code === 200) {
		userInfo.wallet = res.data?.walletAccount
	}
}
const getAge = () => {
	if (!userStore?.userInfo?.birthday) {
		age.value = 0
	} else {
		const now = new Date()
		const birth = new Date(userStore?.userInfo?.birthday)
		age.value = now.getFullYear() - birth.getFullYear()
	}
}
// 添加 onShow 生命周期钩子
onShow(async () => {
	const savedLoginInfo = uni.getStorageSync('loginInfo')

	await userStore.getUserInfo(userStore.userInfo?.userId || savedLoginInfo.userId)
	getIncome()
	getWallet()
	getAge()
})
</script>

<style lang="scss" scoped>
.container {
	background-color: #f5f5f5;

	.profile-header {
		background-color: #fff;
		padding: 40rpx 30rpx;
		background-size: 100%;
		background-repeat: no-repeat;

		.tr-content {
			margin-top: 88rpx;
			display: flex;
			justify-content: flex-end;

			.visitor {
				position: relative;
			}

			.num {
				height: 28rpx;
				line-height: 28rpx;
				padding: 0 8rpx;
				background: #FF5F54;
				border-radius: 4px 4px 4px 2px;
				font-size: 20rpx;
				position: absolute;
				left: 10rpx;
				top: -22rpx;
				z-index: 1;
			}
		}

		.user-info {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin: 50rpx 0 20rpx 0;

			.left-content {
				display: flex;
				align-items: center;
				position: relative;
				.close-status{
					position: absolute;
					top: -10rpx;
					left: -10rpx;
					z-index: 1;
				}
			}

			.avatar {
				width: 160rpx;
				height: 160rpx;
				border: 4rpx solid #fff;
				border-radius: 50%;
				margin-right: 30rpx;
			}

			.info-content {
				margin-right: 20rpx;

				.nickname-box {
					display: flex;
					align-items: center;
				}

				.nickname {
					font-size: 36rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 10rpx;
				}

				.account-content {
					display: flex;
					align-items: flex-end;

					.accountCode {
						height: 36rpx;
						background-color: rgba(255, 255, 255, 0.5);
						font-size: 22rpx;
						padding: 0 12rpx;
						border-radius: 18rpx;
						margin-top: 14rpx;
						margin-right: 20rpx;
					}
				}


			}
		}
	}

	.signature {
		font-size: 24rpx;
		color: #666;
	}

	.info-tags {
		display: flex;
		margin-top: 20rpx;

		.location {
			line-height: 40rpx;
			background: #FBF5E2;
			font-size: 10px;
			color: #000000;
			padding: 0 8rpx;
			margin-right: 20rpx;
			border-radius: 8rpx;
		}

		.sex {
			padding: 0 8rpx;
			line-height: 40rpx;
			background: #FFF2F0;
			font-size: 10px;
			padding: 0 8rpx;
			border-radius: 8rpx;
			display: flex;
			align-items: center;

			.age {
				margin-left: 8rpx;
			}
		}

		.constellation {
			margin-left: 18rpx;
			background: #E6FFFB;
			color: #08979C;
			font-size: 10px;
			padding: 0 8rpx;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
		}
	}

	.profile-content {
		border-top-left-radius: 30rpx;
		border-top-right-radius: 30rpx;
		background: linear-gradient(180deg, #FFFFFF 0%, #F4F8FB 100%);
		position: relative;
		top: -34rpx;
		padding: 30rpx;
		padding-bottom: 80rpx;

		.profile-follower {
			.flex-row {
				display: flex;
				justify-content: space-between;
				align-items: end;
				margin-bottom: 20rpx;

				.stats {
					display: flex;

					.stat-item {
						width: 152rpx;
						display: flex;
						flex-direction: column;
						align-items: flex-start;

						.count {
							font-size: 38rpx;
							font-weight: bold;
							color: #000;
							margin-bottom: 8rpx;
						}

						.label {
							font-size: 24rpx;
							color: #666;
						}
					}
				}

				.my-info {
					position: absolute;
					right: 0rpx;
					height: 68rpx;
					display: flex;
					align-items: center;
					padding: 0 40rpx;
					border-top-left-radius: 68rpx;
					border-bottom-left-radius: 68rpx;
					background-color: #F6F7FB;

				}
			}
		}

		.money {
			display: flex;
			justify-content: space-between;
			margin-top: 40rpx;

			.money-item {
				height: 128rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				box-sizing: border-box;
				width: 48%;
				border-radius: 20rpx;
				background: linear-gradient(90deg, #EFFEF1 0%, #F4FEF2 100%);
				padding: 20rpx 40rpx;
			}

			.count {
				font-size: 22rpx;
				color: #4CAD63;
			}
		}
	}

	.menu-list {
		padding: 40rpx 0;
		background-color: #fff;
		border-radius: 30rpx;
		margin-top: 22rpx;
	}

	:deep(.uni-swiper-dots-horizontal) {
		bottom: 0rpx !important;
	}

	.swiper-item {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		/* 每行4列，每列宽度相等 */
		gap: 20rpx;

		.menu-item {
			display: flex;
			justify-content: center;
			align-items: flex-start;
			margin-bottom: 40rpx;

			.menu-content {
				display: flex;
				flex-direction: column;
				align-items: center;

				.iconfont {
					font-size: 40rpx;
					margin-right: 20rpx;
					color: #666;
				}

				.menu-text {
					font-size: 24rpx;
					color: #666;
				}
			}

			.icon-arrow-right {
				font-size: 32rpx;
				color: #999;
			}
		}
	}

	.login-section {
		margin-top: 20rpx;
		padding: 40rpx 30rpx;
		background-color: #fff;
		text-align: center;

		.login-btn {
			width: 80%;
			height: 80rpx;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: #fff;
			border-radius: 40rpx;
			font-size: 32rpx;
			font-weight: 500;
			border: none;
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);

			&:active {
				opacity: 0.8;
				transform: translateY(2rpx);
			}
		}
	}

	.open-order {
		margin-left: 20rpx;
	}

	.close-order {
		display: flex;
		align-items: center;
		margin-left: 20rpx;
	}

	.confirm-popup-content {
		width: 540rpx;
		background-color: #fff;
		border-radius: 30rpx;
		padding: 40rpx 60rpx;
		box-sizing: border-box;

		.title {
			font-family: 苹方-简, 苹方-简;
			font-weight: normal;
			font-size: 30rpx;
			color: #000000;
			line-height: 42rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
			margin-bottom: 30rpx;
		}

		.content {
			font-family: 苹方-简, 苹方-简;
			font-weight: normal;
			font-size: 24rpx;
			color: #666666;
			line-height: 36rpx;
			text-align: center;
		}

		.btn-box {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;

			.cancel,
			.confirm {
				width: 200rpx;
				height: 80rpx;
				border-radius: 40rpx;
			}
		}

		.cancel {
			width: 200rpx;
			height: 80rpx;
			background-color: #F5F5F5;
			border-radius: 40rpx;
			color: #666666;
			text-align: center;
			line-height: 80rpx;
		}

		.confirm {
			width: 200rpx;
			height: 80rpx;
			background: #34BC4D;
			border-radius: 40rpx;
			color: #fff;
			text-align: center;
			line-height: 80rpx;
		}
	}

	.switch-box {
		position: relative;

		image {
			position: absolute;
			top: -30rpx;
			left: 64rpx;
		}

		.switch-text {
			width: 160rpx;
			height: 62rpx;
			background: linear-gradient(270deg, #FBC789 0%, #FEA500 100%);
			border-radius: 31rpx;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			font-weight: 500;
		}
		.switch-text-close{
			background: linear-gradient( 270deg, #E84D44 0%, #E97047  100%);
		}
	}

}
</style>
