<template>
  <view class="contact">
    <NavigationCustorm style="background: #fff;">
      <template #title>
        联系客服
      </template>
    </NavigationCustorm>
    <view class="contact-content">
      <view class="row">
        <view class="label">电话</view>
        <view class="value">XXXXXXXXXXXX</view>
      </view>
    </view>
  </view>
</template>
<script setup>
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
</script>
<style lang="less" scoped>
.contact {
  padding-top: 198rpx;
  margin-bottom: 240rpx;
  background-color: #f4f8fb;
  height: 100vh;
}
.contact-content {
  background: #fff;
  border-radius: 16rpx;
  margin: 32rpx;
  padding: 40rpx 32rpx;
}
.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>