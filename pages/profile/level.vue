<template>
  <view class="level-page"
    :class="{ 'level-page-bg': activeTab === 'charm', 'level-page-bg-wealth': activeTab === 'wealth' }">
    <NavigationCustorm :isFixed="false" :color="'#fff'">
      <template #title>
        我的等级
      </template>
    </NavigationCustorm>
    <!-- 标签页导航 -->
    <view class="tab-nav">
      <view class="tab-item" :class="{ active: activeTab === 'wealth' }" @click="switchTab('wealth')">
        财富等级
      </view>
      <view class="tab-item" :class="{ active: activeTab === 'charm' }" @click="switchTab('charm')">
        魅力等级
      </view>
    </view>

    <!-- 等级内容区域 -->
    <view class="level-content">
      <!-- 等级显示卡片 -->
      <view class="level-card">
        <view class="flex-row-between">
          <view class="level-card-bg">


            <view class="level-title">
              <image v-if="activeTab === 'wealth'" src="@/static/images/level/vip-lv-v-1.png" class="level-card-num level-card-num-1"></image>
              <image v-else="activeTab === 'charm'" src="@/static/images/level/ml-lv1-v.png" class="level-card-num level-card-num-1"></image>
              <image v-if="activeTab !== 'wealth'" src="@/static/images/level/ml-lv1-1.png" class="level-card-num level-card-num-2"></image>
              <image v-else src="@/static/images/level/vip-lv1-1.png" class="level-card-num level-card-num-2"></image>
            </view>
            <view class="upgrade-text">距离升级还差: {{ activeTab === 'wealth' ? levelInfo.lackWealthScore :
              levelInfo.lackCharmScore }}</view>
          </view>
          <image v-if="activeTab === 'charm'" src="@/static/images/level/ml-lv1-10.png" class="level-card-img"></image>
          <image v-else src="@/static/images/level/vip-lv1.png" class="level-card-img"></image>
        </view>
        <!-- 进度条 -->
        <view class="progress-container">
          <view class="progress-bar">
            <view class="progress-fill"
              :style="{ width: activeTab === 'wealth' ? (100 - levelInfo.lackWealthScore / 100) + '%' : (100 - levelInfo.lackCharmScore / 100) + '%' }">
            </view>
          </view>
          <view class="level-labels">
            <text class="level-label level-label-1">LV{{ activeTab === 'wealth' ? levelInfo.wealthLevel :
              levelInfo.charmLevel }}</text>
            <text class="level-label level-label-2">LV{{ activeTab === 'wealth' ? (levelInfo.wealthLevel + 1) :
              (levelInfo.charmLevel + 1 )}}</text>
          </view>
        </view>
      </view>


    </view>
    <!-- 说明内容 -->
    <view v-if="activeTab === 'charm'" class="description-section">
      <view v-for="(item, index) in charmDescription" :key="index">


        <view class="description-title">
          <view class="description-title-icon"></view>{{ item.title }}
        </view>
        <view class="description-text">
          {{ item.content }}
        </view>
      </view>
    </view>
    <view v-else class="description-section">
      <view v-for="(item, index) in wealthDescription" :key="index">


        <view class="description-title">
          <view class="description-title-icon"></view>{{ item.title }}
        </view>
        <view class="description-text">
          {{ item.content }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useUserStore } from '@/stores/user'
import { onShow } from '@dcloudio/uni-app'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
import { userApi } from '@/common/api'


const userStore = useUserStore()
const title = ref('等级')
const activeTab = ref('wealth')

const upgradePoints = ref(100)
const progressPercent = ref(30)
const wealthDescription = ref([{
  title: '什么是财富等级？',
  content: '财富等级是你在平台中财富实力的象征，等级'
},
{
  title: '如何提升财富等级',
  content: '可通过充值虚拟货币、商城购物进行提升。=10财富值；商城购物，消费1元=8财富值，累计足够的经验值后财富等级会自动升级。'
}
])
const charmDescription = ref([{
  title: '如何什么是魅力等级？',
  content: '魅力等级是你在平台中魅力的象征，等级越高越容易获得大家的关注，可通过发状态、点赞、评论进行提升。累计足够的经验值后财富等级会自动升级。'
},
{
  title: '魅力等级勋章熄灭规则是什么？',
  content: '财富等级是你在平台中财富实力的象征，等级越高越容易获得大家的关注，可通过充值、商城购物进行提升。充值虚拟货币，消费1元=10财富值；商城购物，消费1元=8财富值，累计足够的经验值后财富等级会自动升级。'
}
])
const levelInfo = reactive({
  wealthLevel: 1,
  charmLevel: 1,
  lackWealthScore: 10,
  lackCharmScore: 10
})

const getLevelInfo = async () => {
  const result = await userApi.queryDetail()
  console.log('调用/user/queryDetail接口成功:', result)
  if (result.code === 200) {
    const { wealthLevel, charmLevel, lackWealthScore, lackCharmScore } = result.data
    Object.assign(levelInfo, {
      wealthLevel,
      charmLevel,
      lackWealthScore,
      lackCharmScore
    })
  }
}
getLevelInfo()
const switchTab = (tab) => {
  activeTab.value = tab
  if (tab === 'charm') {
    upgradePoints.value = 150
    progressPercent.value = 45
  } else {
    upgradePoints.value = 100
    progressPercent.value = 30
  }
}
const goBack = () => {
  uni.navigateBack()
}
onShow(() => {
  switchTab('wealth')
})
</script>

<style lang="scss" scoped>
.level-page {
  min-height: 100vh;
  // background: url('@/static/images/level/bg-1.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;

  &.level-page-bg {
    // background: url('@/static/images/level/bg-1.png') no-repeat center center;
    background: linear-gradient(315deg, #E1D2FE 0%, #FFC1FF 50%, #A2AFFF 100%);
    .upgrade-text{
      color: #402A98;
    }
    .progress-fill {
      background: linear-gradient(90deg, #8236D7 0%, #B05FEA 100%);
    }
  }

  &.level-page-bg-wealth {
    background: #A8B9C1;

    .level-card {
      background: linear-gradient(315deg, #A2B4BC 0%, #DAE3EA 100%);
      box-shadow: 0px 2 8px 0px rgba(0, 0, 0, 0.15);
      border-radius: 11px;
      border-image: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)) 1 1;
      // background: linear-gradient(180deg, #CEBFFF 0%, #FDF9FF 100%);
      // box-shadow: 0px 0px 6px 1px rgba(168, 109, 57, 0.16), inset 2px 3px 6px 1px rgba(255, 255, 255, 0.24);
    }
    .upgrade-text{
      color: #31454D;
    }
    .progress-fill {
      background: linear-gradient(90deg, #A2B4BC 0%, #C2CFD8 100%);
    }
  }
}

.tag-page-header {
  display: block;

  .tag-page-header-bg {
    width: 100%;
    height: 112rpx;
  }

  &.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9;
    background-color: #fff;
  }
}

:deep(.tag-page-header-title) {
  width: 100%;
  height: 92rpx;
  line-height: 92rpx;
  font-size: 34rpx;
  color: #fff !important;
  text-align: center;
  position: relative;

}

.back-icon {
  position: absolute;
  left: 32rpx;
}

.right-text {
  position: absolute;
  right: 30rpx;
  font-size: 26rpx;
  color: #000;
  font-family: 苹方-简, 苹方-简;
}

.tab-nav {
  display: flex;
  justify-content: center;
  align-items: center;
}

.tab-item {
  padding: 30rpx 0;
  text-align: center;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  margin: 0 24rpx;

  &.active {
    color: #fff;
    font-weight: 500;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 120rpx;
      height: 4rpx;
      background-color: #fff;
    }
  }
}

.level-content {
  padding: 40rpx;
}

.level-card {
  box-sizing: border-box;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  width: 100%;

  border-radius: 11px 11px 11px 11px;
  position: relative;

  .level-card-img {
    width: 204rpx;
    height: 192rpx;
  }

  .level-card-num {
    height: 60rpx;
    background-size: 100% 100%;
    background-repeat: no-repeat;

    &.level-card-num-1 {
      width: 70rpx;
    }

    &.level-card-num-2 {
      width: 30rpx;
    }
  }
}

.level-title {
  font-size: 80rpx;
  font-weight: bold;
  color: #333;
  text-align: left;
  margin-bottom: 20rpx;
}

.upgrade-text {
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.progress-container {
  .progress-bar {
    height: 16rpx;
    background-color: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    margin-bottom: 20rpx;

    .progress-fill {
      height: 100%;
      border-radius: 8rpx;
      transition: width 0.3s ease;
    }
  }

  .level-labels {
    display: flex;
    justify-content: space-between;

    .level-label {
      font-size: 24rpx;
      color: #999;

      &.level-label-1 {
        color: #31454D;
      }

      &.level-label-2 {
        color: #31454D;
      }
    }
  }
}

.description-section {
  flex: 1;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;
}

.description-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;

  .description-title-icon {
    width: 8rpx;
    height: 28rpx;
    margin-right: 10rpx;
    background: linear-gradient(180deg, #FF93AC 0%, #FECB81 100%);
    border-radius: 3px 3px 3px 3px;
  }
}

.description-text {
  font-size: 28rpx;
  color: #999;
  line-height: 48rpx;
  margin-bottom: 48rpx;
}
</style>
