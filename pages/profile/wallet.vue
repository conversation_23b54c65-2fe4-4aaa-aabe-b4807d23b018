<template>
	<view class="wallet-container">
		<!-- 余额卡片 -->
		<view class="balance-card">
			<view class="balance-title">钻石余额</view>
			<view class="balance-value">{{ balance.toFixed(2) }}</view>
		</view>

		
		
		<!-- 充值档位 -->
		<view class="recharge-container">
			<view class="section-title">请选择充值档位</view>
			<!-- 充值记录入口 -->
			<view class="record-link" @click="goToRecord">
				充值记录
				<uni-icons type="right" size="20" color="#999" />
			</view>
		</view>
		<view class="recharge-list">
			<view
				v-for="(item, idx) in rechargeOptions"
				:key="item.islandCoin"
				:class="['recharge-item', idx === selectedIndex ? 'active' : '']"
				@click="selectOption(idx)"
			>
				<view class="diamond-row">
					<image src="/static/icons/diamond.png" class="diamond-icon" />
					<text class="diamond-num">{{ item.islandCoin }}</text>
				</view>
				<view class="rmb">￥{{ item.amount/100 }}</view>
			</view>
		</view>
		<view class="desc">
				未成年人切勿充值，适合娱乐、合理消费、理性打赏<br />
				如充值遇到问题，请及时联系客服
			</view>

		<!-- 协议说明和按钮，固定底部 -->
		<view class="wallet-bottom-bar">
			<view class="agreement-row">
				<radio :checked="agree" @click="agree = !agree" color="#3ec6a1" style="transform:scale(0.8);margin-right:8rpx;"/>
				<text>充值即代表您已阅读并同意</text>
				<text class="link" @click="goToAgreement">《充值服务协议》</text>
			</view>
			
			<button class="submit-btn" :class="{disabled:!agree}" :disabled="!agree" @click="submitPay">确认充值</button>
		</view>
	</view>
</template>

<script>
	import { payApi } from '@/common/api';
	export default {
		data() {
			return {
				balance: 0.00,
				rechargeOptions: [],
				selectedIndex: 0,
				agree: false
			}
		},
		onLoad() {
			this.getQuotaList();
		},
		methods: {
			getQuotaList(){
				payApi.getQuotaList().then(res => {
					console.log('res',res);
					this.rechargeOptions = res.data;
				})
			},
			/**
			 * 选择充值档位
			 * @param {number} idx 选中的索引
			 */
			selectOption(idx) {
				this.selectedIndex = idx;
			},
			/**
			 * 跳转充值记录
			 */
			goToRecord() {
				uni.navigateTo({ url: '/pages/profile/rechargeRecord' });
			},
			/**
			 * 跳转充值协议
			 */
			goToAgreement() {
				uni.navigateTo({ url: '/pages/agreement/user' });
			},
			async submitPay() {
				if (!this.agree) return;
				const option = this.rechargeOptions[this.selectedIndex];
				// 这里应调用后端接口获取微信支付参数
				// 假设后端返回如下参数
				const payParams = await this.getWxPayParams(option);
				console.log('payParams',payParams);
				uni.requestPayment({
					provider: 'wxpay',
					orderInfo: {
						"appid": payParams.appId,  // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
						"noncestr": payParams.nonceStr, // 随机字符串
						"package": "Sign=WXPay",        // 固定值
						"partnerid": payParams.mchId,      // 微信支付商户号
						"prepayid": payParams.prepayId, // 统一下单订单号
						"timestamp": payParams.timeStamp,        // 时间戳（单位：秒）
						"sign": payParams.sign // 签名，这里用的 MD5/RSA 签名
					}, // 微信支付订单信息
					success: () => {
						console.log('success');
						uni.showToast({ title: '支付成功', icon: 'success' });
						// 充值成功后刷新余额
						this.getBalance();
					},
					fail: (err) => {
						console.log('err',err);
						uni.showToast({ title: '支付失败', icon: 'none' });
					}
				});
			},
			/**
			 * 获取微信支付参数（需后端实现，这里仅为演示）
			 * @param {Object} option 充值选项
			 * @returns {Promise<Object>}
			 */
			async getWxPayParams(option) {
				const res = await payApi.recharge({
					// amount: option.amount * 100,
					// insurance: option.islandCoin,
					orderType: 0,
					rechargeId: option.rechargeId,
					payMethod: 1
				})
				return res.data
			},
			/**
			 * 获取钻石余额（需后端实现，这里仅为演示）
			 */
			getBalance() {
				payApi.getWallet().then(res => {
					this.balance = res.data.walletAccount;
				})
			}
		},
		onShow() {
			this.getBalance();
		}
	}
</script>

<style>
	.wallet-container {
		padding: 32rpx 24rpx 0 24rpx;
		background: #f8faff;
		height: calc(100vh - 200rpx);
		position: relative;
		/* padding-bottom: 260rpx; */
	}
	.balance-card {
		background: #66D47E;
		border-radius: 23rpx;
		box-shadow: 0 8rpx 32rpx #b3c6ff33;
		padding: 60rpx 0 58rpx 0;
		text-align: center;
		margin-bottom: 32rpx;
	}
	.balance-title {
		color: #fff;
		font-size: 28rpx;
		margin-bottom: 10rpx;
		letter-spacing: 2rpx;
		opacity: 0.8;
	}
	.balance-value {
		color: #fff;
		font-size: 56rpx;
		font-weight: bold;
		letter-spacing: 2rpx;
		margin-top: 8rpx;
	}
	.record-link {
		/* position: absolute; */
		right: 40rpx;
		top: 56rpx;
		color: #666;
		font-size: 26rpx;
		z-index: 2;
		display: flex;
		align-items: center;
	}
	.recharge-container{
		display: flex;
		justify-content: space-between;
	}
	.section-title {
		font-size: 28rpx;
		color: #222;
		font-weight: bold;
		margin-bottom: 18rpx;
		margin-top: 24rpx;
	}
	.recharge-list {
		display: flex;
		flex-wrap: wrap;
		gap: 18rpx 18rpx;
		margin-bottom: 32rpx;
		justify-content: flex-start;
	}
	.recharge-item {
		width: 30%;
		min-width: 180rpx;
		background: #f6f8ff;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 8rpx #e0e7ff55;
		padding: 28rpx 0 18rpx 0;
		text-align: center;
		margin-bottom: 0;
		border: 4rpx solid transparent;
		transition: border 0.2s, background 0.2s;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	.recharge-item.active {
		border: 4rpx solid #6dd28d;
		background: #e6f9ef;
		box-shadow: 0 4rpx 16rpx #b3c6ff33;
	}
	.diamond-row {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 8rpx;
	}
	.diamond-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
	}
	.diamond-num {
		color: #000;
		font-size: 41.98rpx;
		font-weight: bold;
	}
	.rmb {
		color: #999;
		font-size: 26.72rpx;
	}
	.wallet-bottom-bar {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background: #fff;
		/* box-shadow: 0 -2rpx 16rpx #b3c6ff22; */
		padding: 24rpx 24rpx 32rpx 24rpx;
		z-index: 10;
	}
	.agreement-row {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		margin-bottom: 12rpx;
		color: #000;
	}
	.link {
		color: #66D47E;
		text-decoration: underline;
		margin-left: 4rpx;
	}
	.desc {
		color: #999;
		font-size: 23rpx;
		margin: 0 0 18rpx 0;
		line-height: 1.7;
		text-align: left;
	}
	.submit-btn {
		width: 100%;
		height: 92rpx;
		line-height: 92rpx;
		font-size: 30rpx;
		color: #fff;
		border-radius: 46rpx;
		background: #66D47E;
		margin-top: 8rpx;
		font-weight: bold;
		/* box-shadow: 0 4rpx 16rpx #b3c6ff33; */
		border: none;
		transition: background 0.2s, color 0.2s;
	}
	.submit-btn.disabled {
		/* background: #e0e0e0 !important; */
		color: #aaa !important;
	}
</style>
