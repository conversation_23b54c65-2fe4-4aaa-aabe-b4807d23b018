<template>
	<view class="recharge-list">
		<view class="recharge-item" v-for="item in records" :key="item.orderNo">
			<view class="row">
				<text class="label">订单编号</text>
				<text class="order-no value">{{ item.orderNo }} |<text class="copy-btn" @click="copyOrderNo(item.orderNo)">复制</text></text>
				
			</view>
			<!-- <view class="row">
				<text class="label">支付状态</text>
				<text class="value">{{ orderStateList.find(state => state.value === item.orderState)?.label || '待支付' }}</text>
			</view> -->
			<view class="row" v-if="item.payTime">
				<text class="label">充值时间</text>
				<text class="value order-no">{{ item.payTime }}</text>
			</view>
			<view class="row">
				<text class="label">充值金额</text>
				<text class="amount">¥{{ item.amount }}</text>
			</view>
			<view class="row">
				<text class="label">贝壳币</text>
				<text class="coin">{{ item.amount }}</text>
			</view>
		</view>
		
		<!-- 加载状态提示 -->
		<view v-if="loading" class="loading-text">加载中...</view>
		<view v-else-if="finished && records.length > 0" class="finished-text">没有更多了</view>
		<view v-if="!loading && records.length === 0" class="empty-state">
			<image class="empty-image" src="/static/images/order/nodata.png" mode="aspectFit"></image>
			<text class="empty-text">暂无相关订单</text>
		</view>
	</view>
</template>

<script>
import { payApi } from '@/common/api';
	export default {
		data() {
			return {
				// 充值记录列表
				records: [],
				// 订单状态列表
				orderStateList: [
					{value: '1', label: '待支付'},
					{value: '2', label: '支付完成'},
					{value: '3', label: '支付失败'},
					{value: '4', label: '退款中'},
					{value: '5', label: '退款成功'},
					{value: '6', label: '退款失败'},
					{value: '7', label: '已取消'},
				],
				// 分页参数
				pageNum: 1,
				pageSize: 10,
				total: 0,
				loading: false,
				finished: false
			}
		},
		onLoad() {
			this.initData();
		},
		/**
		 * 页面滚动到底部触发
		 */
		onReachBottom() {
			this.loadMore();
		},
		methods: {
			/**
			 * 初始化数据
			 */
			initData() {
				this.pageNum = 1;
				this.finished = false;
				this.records = [];
				this.getWalletRecord(false);
			},
			
			/**
			 * 加载更多数据
			 */
			loadMore() {
				if (this.finished || this.loading) return;
				this.pageNum++;
				this.getWalletRecord(true);
			},
			
			/**
			 * 复制订单编号到剪贴板
			 * @param {string} orderNo 订单编号
			 */
			copyOrderNo(orderNo) {
				uni.setClipboardData({
					data: orderNo,
					success: () => {
						uni.showToast({
							title: '已复制',
							icon: 'none'
						})
					}
				})
			},
			
			/**
			 * 获取充值记录
			 * @param {Boolean} append 是否追加数据
			 */
			getWalletRecord(append = false) {
				if (this.loading || this.finished) return;
				
				this.loading = true;
				payApi.getWalletRecord({
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					type: 0
				}).then(res => {
					this.total = res.total || 0;
					const newRows = res.rows || [];
					
					if (append) {
						// 追加数据
						this.records = this.records.concat(newRows);
					} else {
						// 替换数据
						this.records = newRows;
					}
					
					// 判断是否全部加载完
					if (this.records.length >= this.total) {
						this.finished = true;
					}
				}).catch(err => {
					console.error('获取充值记录失败:', err);
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					});
				}).finally(() => {
					this.loading = false;
				});
			}
		}
	}
</script>

<style scoped>
	.recharge-list {
		padding: 20rpx 0;
	}
	.recharge-item {
		background: #fff;
		border-radius: 16rpx;
		margin: 0 20rpx 24rpx 20rpx;
		padding: 24rpx 20rpx 18rpx 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
	}
	.row {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	.label {
		color: #333333;
		font-size: 26rpx;
		min-width: 120rpx;
	}
	.order-no {
		color: #666666;
		font-size: 28rpx;
		margin-left: 10rpx;
		flex: 1;
		word-break: break-all;
	}
	.copy-btn {
		color: #000;
		font-size: 28rpx;
		margin-left: 10rpx;
		padding: 4rpx 0;
		border-radius: 8rpx;
		/* background: #f4f8ff; */
	}
	.value, .amount, .coin {
		color: #666;
		font-size: 28rpx;
		margin-left: 10rpx;
		flex: 1;
		text-align: right;
	}
	.amount {
		color: #000;
		font-weight: bold;
	}
	.coin {
		color: #666;
		/* font-weight: bold; */
	}
	
	/* 加载状态样式 */
	.loading-text, .finished-text, .empty-text {
		text-align: center;
		padding: 40rpx 20rpx;
		color: #999;
		font-size: 28rpx;
	}
	.loading-text {
		color: #666;
	}
	.finished-text {
		color: #ccc;
	}
	.empty-text {
		color: #999;
	}
	.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
	padding: 40rpx 0;
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-weight: 600;
	font-size: 27rpx;
	color: #666666;
}
</style>
