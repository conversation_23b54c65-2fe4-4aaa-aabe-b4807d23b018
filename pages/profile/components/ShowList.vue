<template>
    <view class="content">
        <view class="nav">
            <scroll-view class="tab-scroll" scroll-x="true" :scroll-left="scrollLeft">
            <view class="tab-scroll_box">
                <view
                v-for="(item, index) in category"
                :key="index"
                :class="{ active: isActive === index,item:true }"
                @click="changeTab(index)"
                >
                {{ item.name }}
                </view>
            </view>
            </scroll-view>
        </view>
        <swiper class="swiper-content" :current="isActive" @change="onSwiperChange" :style="{ height: swiperHeight + 'rpx' }">
            <swiper-item>
                <Browse v-if="isActive === 0" @emit-swiper-height="adjustSwiperHeight"/>
            </swiper-item>
            <swiper-item>
                <Favorite v-if="isActive === 1" @emit-swiper-height="adjustSwiperHeight"/>
            </swiper-item>
            <swiper-item>
                <Collect v-if="isActive === 2" @emit-swiper-height="adjustSwiperHeight"/>
            </swiper-item>
        </swiper>
    </view>
</template>

<script>
import VideoCard from './VideoCard.vue'; // 你的视频卡片组件
import Collect from './Collect.vue'; // 你的视频卡片组件
import Favorite from './Favorite.vue'; // 你的视频卡片组件
import Browse from './Browse.vue'; // 你的视频卡片组件
export default {
    components: { VideoCard,Collect,Favorite,Browse },
    data() {
        return {
            isActive: 0,
            scrollLeft: 0,
            swiperHeight: 0,
            category: [
                { name: '浏览', type: 'browse' },
                { name: '喜欢', type: 'favorite' },
                { name: '收藏', type: 'collect' },
            ],
            page: 1,
            loading: false,
            noMore: false,
        };
    },
    methods: {
        adjustSwiperHeight(height) {
            if (height && height > 0) {
                this.swiperHeight = height+100;
            }
        },
        changeTab(index) {
            this.isActive = index;
            this.page = 1;
            this.noMore = false;
        },
        onSwiperChange(e) {
            this.changeTab(e.detail.current);
        },
    },
};
</script>

<style lang="scss">
.content {
    display: flex;
    flex-direction: column;
    height: 100%;
    margin-top: 20rpx;
    background-color: #ffffff;
    border-radius: 30rpx 30rpx 0 0;
}

.nav {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #fff;
    padding: 20rpx 0;
    margin-left: 30rpx;
    .item{
        color: #666;
        &.active{
            color: #000;
        }
    }
}

.tab-scroll {
    white-space: nowrap;
    overflow: hidden;
}
.tab-scroll_box {
    display: flex;
    .item {
        text-align: center;
        margin-right:60rpx;
    }
}

.active {
    font-weight: bold;
    
    &::after {
        content: ''; /* 设置content为空 */
        display: block; /* 确保::after占据空间 */
        border-bottom: 4px solid #5CC942; /* 添加下划线 */
        width: 100%; /* 确保下划线宽度与元素宽度一致 */
        border-radius: 4rpx;
        margin-top: 8rpx;
    }
}
.swiper-content {
    height: 100vh;
}
.main-scroll {
    flex: 1;
    height: 0;
}
.video-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}
.loading, .no-more {
    text-align: center;
    color: #999;
    margin: 20rpx 0;
}
</style>
  