<template>
    <view>
        <!-- <uni-icons type="wallet-filled" size="24" @click="handleOpenPopup"></uni-icons> -->
        <image src="/static/images/profile/qcode.png" class="qcode" @click="handleOpenPopup"></image>
        <!-- 普通弹窗 -->
        <uni-popup ref="popup" type="center" @change="change">
            <view class="qrcode-box-container">

                <view class="qrcode-box">
                    <image class="avatar"
                        :src="`${HTTP_IMG}${userStore.userInfo?.avatar}` || '/static/avatar/default.png'"
                        mode="aspectFill"></image>
                    <view>
                        <view class="qrcode-box-title">{{ userStore?.userInfo?.nickname }}</view>
                        <view class="qrcode-box-content">{{ userStore?.userInfo?.location }}</view>
                    </view>
                </view>
                <image src="/static/images/profile/qrcode.png" class="qcode-img" @click="handleOpenPopup"></image>
                <view class="qrcode-box-text">扫一扫上面二维码，添加我为好友</view>
                <!-- <uqrcode ref="uqrcode" canvas-id="qrcode" :value="deliveryCode" :options="{ margin: 10 }"></uqrcode> -->
            </view>
        </uni-popup>
    </view>
</template>
<script setup>
import { ref, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'
import { HTTP_IMG } from '@/common/constant'
const popup = ref(false)
const verification = ref(false)
const userStore = useUserStore()
const deliveryCode = ref(userStore.userInfo?.accountCode)

// 4位核销码输入
const codeArray = ref(['', '', '', ''])
const detail = ref(null)
const loading = ref(false)
const errorMsg = ref('')
const loginLoading = ref(false)


const handleCodeInput = (event, index) => {
    const value = event.detail.value
    const previousValue = codeArray.value[index]

    // 只允许输入数字，并只取第一个字符
    const numericValue = value.replace(/[^0-9]/g, '').slice(0, 1)
    codeArray.value[index] = numericValue

    // 如果输入了数字且不是最后一个输入框，自动聚焦到下一个
    if (numericValue && index < 3) {
        setTimeout(() => {
            const nextInput = uni.createSelectorQuery().select(`#code-input-${index + 1}`)
            nextInput.fields({ node: true }, (res) => {
                if (res && res.node) {
                    res.node.focus()
                }
            }).exec()
        }, 100)
    }
    // 如果是删除操作（之前有值现在没值）且不是第一个输入框，聚焦到前一个
    else if (!numericValue && previousValue && index > 0) {
        setTimeout(() => {
            const prevInput = uni.createSelectorQuery().select(`#code-input-${index - 1}`)
            prevInput.fields({ node: true }, (res) => {
                if (res && res.node) {
                    res.node.focus()
                }
            }).exec()
        }, 100)
    }

}

const handleCodeFocus = (index) => {
    // 清空当前输入框的值，方便重新输入
    if (codeArray.value[index]) {
        codeArray.value[index] = ''
    }
}

const handleNext = async () => {
    await nextTick()
    const code = codeArray.value.join('')
    if (code.length !== 4) {
        errorMsg.value = '请输入完整核销码'
        return
    }
    loading.value = true
    errorMsg.value = ''
    try {
        // const res = await verifyCodeApi.getVerifyInfo(code)
        detail.value = {
            image: '/static/images/profile/image.png',
            name: '测试商品',
            desc: '测试商品描述',
            price: 100
        }
        if (res && res.data) {
            detail.value = res.data
        } else {
            detail.value = null
            errorMsg.value = res.message || '未查询到商品信息'
        }
    } catch (err) {
        // 造假数据
        // detail.value = null
        // errorMsg.value = err.message || '查询失败'
    } finally {
        loading.value = false
    }
}
const handleVerify = () => {
    verification.value.close()
}
const handleOpenPopup = () => {
    popup.value.open('center')
}
const change = () => { }
const handleScan = () => {
    verification.value.open('bottom')

    // 只允许通过相机扫码
    // uni.scanCode({
    //     onlyFromCamera: true,
    //     success: function (res) {
    //         console.log('条码类型：' + res.scanType);
    //         console.log('条码内容：' + res.result);
    //     }
    // });
}
</script>
<style lang="scss" scoped>
.qcode {
    width: 32rpx;
    height: 32rpx;
}
.qcode-img {
    width: 500rpx;
    height: 500rpx;
}
.scan-code {
    margin-left: 40rpx;
}

.verification-popup {
    padding: 120rpx 80rpx 160rpx 80rpx;
    text-align: center;
}

.verification-title {
    font-weight: bold;
    font-size: 32rpx;
    margin-bottom: 48rpx;
}

.verification-desc {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 96rpx;
}

.verification-code-row {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
}

.verification-label {
    font-size: 28rpx;
    font-weight: bold;
    width: 160rpx;
    text-align: left;
}

.verification-code-inputs {
    display: flex;
    gap: 20rpx;

    .code-input {
        width: 80rpx;
        height: 80rpx;
        border: 1px solid #ccc;
    }
}

.verification-input {
    width: 80rpx;
    height: 80rpx;
    background: #f5f5f5;
    border-radius: 16rpx;
    text-align: center;
    font-size: 72rpx;
}

.verification-detail-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 80rpx;
}

.verification-detail {
    font-size: 28rpx;
    color: #888;
    text-align: left;
}

.verification-btn {
    width: 100%;
    height: 80rpx;
    background: #000;
    color: #fff;
    border-radius: 80rpx;
    font-size: 32rpx;
    margin-top: 80rpx;
}

.avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 20rpx;
    margin-right: 20rpx;
}

.qrcode-box-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    border-radius: 20rpx;
    overflow: hidden;
    background-color: #fff;
    padding: 40rpx ;
}

.qrcode-box {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 20rpx 0 20rpx;

    .qrcode-box-title {
        font-size: 32rpx;
        color: #000;
    }


}
.qrcode-box-content{
    font-size: 20rpx;
    color: #999;
}
.qrcode-box-text {
        font-size: 20rpx;
        color: #999;
        margin-bottom: 40rpx;
        width: 100%;
        text-align: center;
    }
</style>