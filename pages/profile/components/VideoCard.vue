<template>
    <view class="video-card">
        <image :src="video.cover" mode="widthFix" class="video-cover"></image>
        <view class="video-info">
            <text class="video-title">{{video.title}}</text>
            <text class="video-desc">{{video.desc}}</text>
        </view>
    </view>
</template>
<script setup>
const props = defineProps({
    video: {
        type: Object,
        required: true
    }
})
</script>
<style lang="scss" scoped></style>