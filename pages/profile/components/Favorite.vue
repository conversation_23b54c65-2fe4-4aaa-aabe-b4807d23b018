<template>
  <view class="waterfall-container-favorite">
      <!-- 改为网格布局，每行3个元素 -->
      <view class="grid-container">
          <view class="grid-item" v-for="(item, index) in allItems" :key="index" @click="goToDetail(item)">
              <image :src="`${HTTP_IMG}${item.img}`" mode="aspectFill" class="item-image"></image>
              <uni-icons  type="heart" size="16" color="#ffffff" class="item-heart">{{item.likeNum}}</uni-icons>
          </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
          <view class="loading-text">加载中...</view>
      </view>
      
      <!-- 没有更多数据提示 -->
      <view class="no-more-container" v-if="!hasMore && allItems.length > 0">
          <view class="no-more-text">没有更多数据了</view>
      </view>
      
      <!-- 底部触发器，用于检测是否滚动到底部 -->
      <view class="scroll-trigger" id="scrollTrigger" v-if="hasMore && !loading"></view>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { userApi } from '@/common/api'
const emit = defineEmits(['emit-swiper-height'])
import { HTTP_IMG } from '@/common/constant'
const goToDetail = (item) => {
    uni.navigateTo({
        url: `/pages/expertDetail/expert-detail?id=${item.userId}`
    })
}
// 分页和加载状态
const currentPage = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const hasMore = ref(true)

// 改为单一数组存储所有数据
const allItems = ref([])

// 滚动监听相关
let scrollTimer = null
let observer = null

// 加载数据
const distributeData = async (isLoadMore = false) => {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  
  try {
      const res = await userApi.getFavorite({
          pageNum: currentPage.value,
          pageSize: pageSize.value
      })
      
      console.log('加载数据:', res)
      
      if (res.rows && res.rows.length > 0) {
          // 如果是首次加载，清空数据
          if (!isLoadMore) {
              allItems.value = []
          }
          
          // 将新数据添加到数组中
          allItems.value.push(...res.rows)

          // 检查是否还有更多数据
          if (res.rows.length < pageSize.value) {
              hasMore.value = false
          }
          
          // 更新页码
          currentPage.value++
          
      } else {
          hasMore.value = false
      }
      
  } catch (error) {
      console.error('加载数据失败:', error)
  } finally {
      loading.value = false
      nextTick(() => {
          updateHeight()
          // 重新设置观察器
          setupScrollTrigger()
      })
  }
}

// 在图片加载完成后重新计算高度
const updateHeight = () => {
  const itemsPerRow = 3
  const rows = Math.ceil(allItems.value.length / itemsPerRow)
  const h = rows * 200 + 100 // 每行高度约200rpx
  emit('emit-swiper-height', h > 200 ? h : 200)
}


// 设置滚动触发器
const setupScrollTrigger = () => {
  // 清理之前的观察器
  if (observer) {
      observer.disconnect()
      observer = null
  }
  
  // 使用定时器方式检测底部触发器
  if (scrollTimer) {
      clearInterval(scrollTimer)
  }
  
  scrollTimer = setInterval(() => {
      if (!hasMore.value || loading.value) return
      
      uni.createSelectorQuery().select('#scrollTrigger').boundingClientRect((rect) => {
          if (rect) {
              const windowHeight = uni.getSystemInfoSync().windowHeight
              // 当触发器进入视口时加载更多
              if (rect.top <= windowHeight) {
                  clearInterval(scrollTimer)
                  scrollTimer = null
                  distributeData(true)
              }
          }
      }).exec()
  }, 100) // 每100ms检测一次
}

// 清理滚动监听
const cleanupScrollListener = () => {
  if (scrollTimer) {
      clearInterval(scrollTimer)
      scrollTimer = null
  }
  
  if (observer) {
      observer.disconnect()
      observer = null
  }
}

// 重置数据
const resetData = () => {
  currentPage.value = 1
  hasMore.value = true
  allItems.value = []
  cleanupScrollListener()
}

// 刷新数据
const refreshData = async () => {
  resetData()
  await distributeData(false)
}

// 在图片加载完成后调用
onMounted(() => {
  distributeData(false) // 首次加载
})

onUnmounted(() => {
  console.log('onUnmounted')
  cleanupScrollListener() // 清理滚动监听
})

// 暴露方法供父组件调用
defineExpose({
  refreshData,
  resetData
})
</script>

<style scoped>
.waterfall-container-favorite {
  padding: 10px;
}

/* 网格容器 */
.grid-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

/* 网格项目 - 每行3个 */
.grid-item {
  width: calc(33.333% - 7rpx); /* 3个元素，减去间距 */
  height: 200rpx;
  background: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 10rpx;
  position: relative;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  padding: 10rpx;
}

.item-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
  display: block;
}

.item-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  width: 100%;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 没有更多数据样式 */
.no-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  width: 100%;
}

.no-more-text {
  color: #ccc;
  font-size: 28rpx;
}

/* 滚动触发器 */
.scroll-trigger {
  height: 1px;
  width: 100%;
  margin-top: 40rpx;
}
.item-heart {
  position: absolute;
  bottom: 10rpx;
  left: 10rpx;
}
</style>