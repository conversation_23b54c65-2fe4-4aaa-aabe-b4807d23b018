<template>
  <div class="income-container">
    <NavigationCustorm :isFixed="false">
      <template #title>
        我的收益
      </template>
    </NavigationCustorm>
    <view class="income-content">
      <div class="card-wrapper">
        <div class="card withdrawable">
          <div class="withdrawable-content">
            <div class="label">可提现金额（元）</div>
            <div class="amount">{{ withdrawableAmount }}</div>
          </div>
          <view class="withdraw-btn"  @click="handleWithdraw">去提现</view>
        </div>
      </div>
      <div class="stats-row">
        <div class=" stat">
          <div class="label">今日服务接单</div>
          <div class="value">{{ orderCount }}</div>
        </div>
        <div class=" stat">
          <div class="label">累计收益</div>
          <div class="value">￥{{ totalIncome }}</div>
        </div>
      </div>
      <div class="record-row"  @click="handleClick">
        <div>
          <image src="@/static/images/profile/icon-income.png" mode="widthFix" class="record-icon"></image>
          <image src="@/static/images/profile/income-text.png" mode="widthFix" class=" record-icon-text"></image>
          <text class="record-text">提现到账 214.3元 · 1天前</text>
        </div>
        <uni-icons type="right" size="16" color="#999"></uni-icons>
      </div>
    </view>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { profileApi } from '@/common/api'
import { formatAmount } from '@/common/utils'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

const userStore = useUserStore()
const withdrawableAmount = ref('0.00')
const orderCount = ref(0)
const totalIncome = ref('0.00')



onMounted(async () => {

  const res = await profileApi.getMyIncome(userStore.userInfo?.userId)
  if (res.code == 200) {
    withdrawableAmount.value = formatAmount(res.data?.balance || 0)
    orderCount.value = res.data?.orderCount || 0
    totalIncome.value = formatAmount(res.data?.totalIncome || 0)
  }
})

const handleClick = () => {
  uni.navigateTo({
    url: '/pages/profile/withdrawalRecord'
  })
}

const handleWithdraw = () => {
  uni.navigateTo({
    url: '/pages/profile/withdrawal'
  })
}
</script>

<style scoped>
.income-container {
  height: 100vh;
  background-image: url('@/static/images/profile/income-bg.png');
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-position: top;

  .nav-custorm {
    background: #fff;
  }
}

.income-content {
  padding: 30rpx;
  position: relative;
}

.card-wrapper {
  background: linear-gradient(to bottom, #f0fef1, #ebf8ee);
  border-radius: 42rpx;
  padding: 2rpx;
  margin-bottom: 32rpx;
  position: absolute;
  top: 20rpx;
  left: 30rpx;
  right: 30rpx;
  box-sizing: border-box;

}

.card {
  background: linear-gradient(to bottom, #c1edcc, #f8fdfa);
  border-radius: 40rpx;
  position: relative;
  display: flex;
  padding: 36rpx 38rpx;
  justify-content: space-between;
  align-items: center;
}

.withdrawable-content {
  font-size: 36rpx;

}

.amount {
  height: 92rpx;
  line-height: 92rpx;
  font-size: 76rpx;
  font-weight: bold;
  margin-top: 16rpx;
}

.stats-row {
  height: 110rpx;
  background: #FFFFFF;
  box-shadow: 0px 4rpx 8rpx 2rpx rgba(0, 0, 0, 0.02);
  border-radius: 22rpx;
  padding: 32rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  padding: 0 40rpx;
  margin-top: 200rpx;

  .stat {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .value {
    margin-left: 60rpx;
    font-size: 24rpx;
    font-weight: bold;
    color: #FF5F54;
  }
}

.withdraw-btn {
  background: #F0FEF1;
  border-radius: 30rpx;
  border: 1px solid #FFFFFF;
  font-family: 苹方-简, 苹方-简;
  font-weight: normal;
  font-size: 26rpx;
  color: #66D47E;
  text-align: center;
  font-style: normal;
  padding: 10rpx 20rpx;
  text-transform: none;
}



.record-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  background: #FFFFFF;
  box-shadow: 0px 4rpx 8px 2px rgba(0, 0, 0, 0.02);
  border-radius: 22rpx;
  padding: 0 32rpx;
  margin-top:22rpx;
}

.record-link {
  color: #888;
  font-size: 30rpx;
  cursor: pointer;
}

.record-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}
.record-icon-text{
  width: 120rpx;
  height: 30rpx;
  margin-right: 20rpx;
}
.record-text{
  color: #999;
}
</style>