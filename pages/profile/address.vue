<template>
    <view class="address-container">
        <NavigationCustorm style="background: #fff;">
            <template #title>
                {{ title }}
            </template>
        </NavigationCustorm>
        <view class="address-content">
            <!-- 加载状态 -->
            <view v-if="loading" class="loading-container">
                <text class="loading-text">加载中...</text>
            </view>

            <!-- 错误状态 -->
            <view v-else-if="error" class="error-container">
                <text class="error-text">{{ error }}</text>
                <button class="retry-btn" @click="loadAddressList">重试</button>
            </view>

            <!-- 地址列表 -->
            <view v-else class="address-list">
                <!-- 空状态 -->
                <view v-if="addressList.length === 0" class="empty-container">
                    <image src="@/static/images/profile/no-address.png" class="empty-icon" ></image>
                    <text class="empty-text-bold">暂无地址</text>
                    <text class="empty-text">快去添加一个吧~</text>
                    <button class="address-btn" @click="addAddress">
                        添加收货地址
                    </button>
                </view>

                <!-- 地址项 -->
                <view v-for="(address, index) in addressList" :key="address.id || index" class="address-item"
                    @click="selectAddress(address)">
                    <view class="address-location">
                        {{ address.areaDetail}} <view
                            class="default-text" v-if="address.isDefault == 1">默认</view>
                    </view>
                    <view class="address-detail">
                        {{ address.detailAddress }}
                    </view>
                    <view class="address-header">
                        <text class="name">{{ address.receiverName }}</text>
                        <text class="phone">{{ address.contactNumber }}</text>
                    </view>
                    <view class="address-footer">
                        <view class="default-check" @click.stop="toggleDefault(address)">
                            <!-- <radio value="r1" :checked="address.isDefault==1?true:false" color="#66D47E" /> -->
                            <image src="@/static/images/profile/<EMAIL>" mode="widthFix" class="radio-icon"
                                v-if="address.isDefault !== 1"  ></image>
                            <image src="@/static/images/profile/<EMAIL>" mode="widthFix" class="radio-icon"
                                v-else  >
                            </image>
                            <text>{{ address.isDefault ? '已默认' : '设为默认' }}</text>
                        </view>
                        <view class="action-buttons">
                            <image src="@/static/images/profile/edit.png" mode="widthFix" class="edit-icon"
                                @click.stop="editAddress(address)"></image>
                            <image src="@/static/images/profile/del.png" mode="widthFix" class="edit-icon"
                                @click.stop="deleteAddress(address)" ></image>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 新增地址按钮 -->
             <view class="add-address-btn-wrapper">
            <button v-if="addressList.length > 0" class="add-address-btn" @click="addAddress">
                <uni-icons type="plus" size="24" color="#fff"></uni-icons>
                新增地址
            </button>
        </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted, nextTick, onActivated } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { addressApi } from '@/common/api.js'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
import cityData from '@/pages/user/components/citydata.json'

const title = ref('')
// 响应式数据
const addressList = ref([])
const loading = ref(false)
const error = ref('')

const form = ref({
    receiverName: '',
    contactNumber: '',
    provinceCode: '',
    cityCode: '',
    areaCode: '',
    detailAddress: '',
    isDefault: false,
    pickerValue: []
})
const getLabel = (provinceCode, cityCode, areaCode) => {
    const province = cityData.find(item => item.value == provinceCode)
    const city = province?.children?.find(item => item.value == cityCode)
    const area = city?.children?.find(item => item.value == areaCode)
    return `${province?.text}/${city?.text}/${area?.text}`
}

// 加载地址列表
const loadAddressList = async () => {
    try {
        loading.value = true
        error.value = ''
        const response = await addressApi.getAddressList()

        if (response.code === 200 || response.success) {
            addressList.value = response.data || response.result || []
        } else {
            error.value = response.message || '获取地址列表失败'
        }
    } catch (err) {
        addressList.value = []
        console.error('获取地址列表失败:', err)
        // error.value = '网络错误，请稍后重试'
    } finally {
        loading.value = false
    }
}

// 切换默认地址
const toggleDefault = async (address) => {
    if (address.isDefault) {
        return // 已经是默认地址，不需要操作
    }

    try {
        const response = await addressApi.setDefaultAddress({ dataId: String(address.dataId) })
        if (response.code === 200 || response.success) {
            // 更新本地数据
            loadAddressList()
            uni.showToast({
                title: '设置成功',
                icon: 'success'
            })
        } else {
            uni.showToast({
                title: response.message || '设置失败',
                icon: 'error'
            })
        }
    } catch (err) {
        console.error('设置默认地址失败:', err)
        uni.showToast({
            title: '网络错误',
            icon: 'error'
        })
    }
}

// 编辑地址
const editAddress = (address) => {
    console.log('编辑地址', address)
    uni.navigateTo({
        url: `/pages/profile/addAddress?address=${address.dataId}`
    })
    // Object.assign(form.value, {
    //     receiverName: address.receiverName,
    //     contactNumber: address.contactNumber,
    //     provinceCode: address.provinceCode,
    //     cityCode: address.cityCode,
    //     areaCode: address.areaCode,
    //     detailAddress: address.detailAddress,
    //     isDefault: address.isDefault == 1 ? true : false,
    //     dataId: address.dataId
    // })

    // if (address.provinceCode && address.cityCode && address.areaCode) {
    //     form.value.pickerValue = [String(address.provinceCode), String(address.cityCode), String(address.areaCode)]
    // } else {
    //     form.value.pickerValue = []
    // }
}

// 删除地址
const deleteAddress = async (address) => {
    uni.showModal({
        title: '确认删除',
        content: '确定要删除这个地址吗？',
        success: async (res) => {
            if (res.confirm) {
                try {
                    const response = await addressApi.deleteAddress({ dataId: address.dataId })
                    if (response.code === 200 || response.success) {
                        loadAddressList()
                        uni.showToast({
                            title: '删除成功',
                            icon: 'success'
                        })
                    } else {
                        uni.showToast({
                            title: response.message || '删除失败',
                            icon: 'error'
                        })
                    }
                } catch (err) {
                    console.error('删除地址失败:', err)
                    uni.showToast({
                        title: '网络错误',
                        icon: 'error'
                    })
                }
            }
        }
    })
}

const selectAddress = (address) => {
    // 将选中的地址保存到本地存储
    uni.setStorageSync('selectedAddress', address);
    // 返回上一页
    uni.navigateBack({
        success: () => {
            // 通过事件总线通知fillOrder页面更新地址
            uni.$emit('addressSelected', address);
        }
    });
}


// 新增地址
const addAddress = () => {
    uni.navigateTo({
        url: '/pages/profile/addAddress'
    })
}

// 页面显示时刷新数据（从编辑页面返回时）
onShow(() => {
    console.log('onShow')
    nextTick(() => {
        loadAddressList()
    })
})

// 组件挂载时加载数据
onMounted(() => {
    console.log('onMounted')
    loadAddressList()
})

onLoad((params) => {
    if (params.title == 'profile') {
        title.value = '地址管理'
    } else {
        title.value = '常用地址'
    }
})
</script>

<style lang="scss" scoped>
.address-container {
    min-height: 100vh;
    /* 背景渐变 渐变范围高度300rpx*/
    background: #F4F8FB;
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .address-content {

        padding: 222rpx 30rpx 30rpx 30rpx;
    }

}

.address-list {
    margin-bottom: 160rpx;
}

.address-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0px 2PX 4px 0px rgba(0, 0, 0, 0.08);
}

.address-header {
    display: flex;
    font-size: 26rpx;
    align-items: center;
    margin-bottom: 16rpx;
}

.name {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-right: 24rpx;
}

.phone {
    font-size: 26rpx;
    color: #000;
}

.address-location {
    font-weight: 500;
    font-size: 26rpx;
    color: #999999;
    margin-bottom: 8rpx;
}

.address-detail {
    font-size: 28rpx;
    font-weight: 500;
    font-size: 30rpx;
    color: #0D0E0F;
    line-height: 40rpx;
    margin-bottom: 8rpx;
}

.address-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.default-check {
    display: flex;
    align-items: center;
}

.default-check checkbox {
    margin-right: 16rpx;
}

.default-check text {
    font-size: 28rpx;
    color: #666;
}

.action-buttons {
    display: flex;
    gap: 24rpx;
}

.edit-btn,
.delete-btn {
    padding: 16rpx 32rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    border: none;
}

.edit-btn {
    background: #f0f0f0;
    color: #333;
}

.delete-btn {
    background: #f0f0f0;
    color: #333;
}

.address-btn {
    margin-top: 46rpx;
    background: #66D47E;
    height: 92rpx;
    line-height: 92rpx;
    border-radius: 46rpx;
    color: #fff;
    border: none;
    border-radius: 48rpx;
    font-size: 30rpx;
    padding: 0rpx 60rpx;
    font-family: 苹方-简, 苹方-简;
}

.add-address-btn-wrapper{
    height: 160rpx;
	box-sizing: border-box;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 16rpx 30rpx;
	background-color: #fff;
	z-index: 9;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    .add-address-btn{
        height: 88rpx;
        line-height: 88rpx;
      background: #66D47E;
      color: #fff;
      border: none;
      border-radius: 92rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    }

/* 加载状态样式 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 100rpx 0;
}

.loading-text {
    font-size: 28rpx;
    color: #999;
}

/* 错误状态样式 */
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 100rpx 32rpx;
}

.error-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 32rpx;
    text-align: center;
}

.retry-btn {
    background: #007aff;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
}

/* 空状态样式 */
.empty-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 300rpx 0;
}

.empty-text-bold {
    font-family: PingFangSC-Semibold, PingFangSC-Semibold;
    font-weight: normal;
    font-size: 30rpx;
    color: #000000;
    line-height: 42rpx;
    margin-top: 36rpx;
}

.empty-text {
    font-family: 苹方-简, 苹方-简;
    font-weight: normal;
    font-size: 26rpx;
    color: #666666;
    line-height: 38rpx;
    margin-bottom: 24rpx;
}

.edit-icon {
    width: 40rpx;
    height: 40rpx;
}


.form-item {
    margin-bottom: 12rpx;

    .label {
        display: block;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
    }

    input {
        width: 100%;
        padding: 20rpx;
        border-radius: 16rpx;
        background: #F5F5F5;
        border: none;
        font-size: 28rpx;
    }
}

.textarea {
    width: 100%;
    height: 100rpx;
    border-radius: 16rpx;
    padding: 20rpx 0;
    background: #F5F5F5;
}

.uni-input-input {
    background: #F5F5F5;
}

.radio-wrapper {
    display: flex;
    align-items: center;
    padding: 16rpx 0;
    cursor: pointer;

    .radio-text {
        margin-left: 16rpx;
        font-size: 28rpx;
        color: #333;
    }

}

.radio-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
}

.default-text {
    font-size: 24rpx;
    color: #66D47E;
    margin-left: 16rpx;
    background: #F0FFF1;
    display: inline-block;
}

.empty-icon {
    width: 190rpx;
    height: 190rpx
}

:deep(.uni-modal) {
    background: #FFFFFF;
    box-shadow: 0px 1 9px 1px rgba(209, 209, 209, 0.5);
    border-radius: 30rpx 30rpx 30rpx 30rpx;
    padding: 40rpx 60rpx;
    box-sizing: border-box;

    .uni-modal__title {
        font-family: 苹方-简-中黑体, 苹方-简;
        font-weight: normal;
        font-size: 30rpx;
        color: #000000;
    }

    .uni-modal__bd {
        padding: 20rpx 30rpx 46rpx 30rpx;
    }

    .uni-modal__ft {
        &:after {
            border: none;
        }
    }

    .uni-modal__btn.uni-modal__btn_default {
        background: #F5F5F5 !important;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
        font-family: 苹方-简, 苹方-简;
        font-weight: normal;
        font-size: 30rpx;
        color: #666666 !important;
        height: 92rpx;
        line-height: 92rpx;
        margin-right: 30rpx;
    }

    .uni-modal__btn.uni-modal__btn_primary {
        &:after {
            border: none;
        }
        background: #34BC4D !important;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
        font-family: 苹方-简,
        苹方-简;
        font-weight: normal;
        font-size: 30rpx;
        color: #fff !important;
        height: 92rpx;
        line-height: 92rpx;
    }
}
</style>