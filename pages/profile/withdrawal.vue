<template>
  <div class="withdrawal-container">
    <NavigationCustorm>
      <template #title>
        申请提现
      </template>
      <template #right>
        <span @click="goRule">提现规则</span>
      </template>
    </NavigationCustorm>
    <div class="withdrawal-content">
      <div class="withdrawal-content-box">
        <div class="card available">
          <div class="label">可提现金额（元）</div>
          <div class="amount"> {{ availableAmount }}</div>
        </div>
        <div class="card input-card">
          <view class="input-row">
            <span class="input-label">提现金额</span>
          </view>
          <div class="input-row line">
            <span class="input-label-money">￥</span>
            <input v-model="withdrawAmount" class="amount-input" type="number" placeholder="" />
            <span class="all-btn" @click="withdrawAll">全部提现</span>
          </div>
          <div class="input-row tips">最低可提现1元，最高5万</div>
        </div>
        <div class="card input-card">
          <view class="input-row">
            <span class="input-label-to">提现到</span>
          </view>
          <div class="input-row channel-row">
            <div class="channel-item-box">
              <view class="channel-item">
                <image src="@/static/images/profile/wechat.png" mode="widthFix" style="width: 40rpx;height: 40rpx;">
                </image>
                <span class="channel-label">微信</span>
              </view>
              <view class="channel-item">
                <span class="channel-label" style="margin-left: 48rpx;">
                  吃葡萄就吐葡萄皮(*莹)</span>
              </view>
            </div>
            <view class="channel-item">
              <uni-icons type="right" size="20" color="#999"></uni-icons>
            </view>
          </div>
        </div>
      </div>
      <button class="withdraw-btn" @click="submitWithdraw">提现申请</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { profileApi } from '@/common/api'
import { useUserStore } from '@/stores/user'
import { formatAmount } from '@/common/utils'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'


const userStore = useUserStore()
const availableAmount = ref(0)
const withdrawAmount = ref('')

const withdrawAll = () => {
  withdrawAmount.value = availableAmount.value
}

const editChannel = () => {
  // 跳转到渠道编辑页或弹窗
  uni.navigateTo({
    url: '/pages/profile/withdrawalChannel'
  })
}

const goRule = () => {
  uni.showToast({
    title: '提现规则',
    icon: 'none'
  })
}

const submitWithdraw = async () => {
  const res = await profileApi.deduct({
    amount: withdrawAmount.value * 100
  })
  if (res.code == 200) {
    uni.showToast({ title: '扣除成功', icon: 'success' })
  }
  setTimeout(() => {
    uni.navigateBack()
  }, 2000)
}

onMounted(async () => {
  const res = await profileApi.getMyIncome()
  debugger
  if (res.code == 200) {
    availableAmount.value = formatAmount(res.data?.balance || 0)
  }
})
</script>

<style lang="scss" scoped>
.withdrawal-container {
  max-width: 800rpx;
  margin: 0 auto;
  padding: 0;
  background: #F4F8FB;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .tag-page-header {
    background: #fff;
  }
}

.withdrawal-content {
  padding: 222rpx 30rpx 30rpx 30rpx;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card {
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  width: 100%;
  text-align: left;
  box-sizing: border-box;
}

.available {
  .label {
    font-size: 26rpx;
    text-align: center;
    color: #666;
  }

  .amount {
    font-size: 64rpx;
    font-weight: bold;
    margin-top: 16rpx;
    text-align: center;
  }
}

.input-row {
  display: flex;
  align-items: center;
  gap: 16rpx;

  &.line {
    border-bottom: 2rpx solid #eee;
  }

  &.tips {
    margin-top: 20rpx;
    color: #999;
    font-size: 26rpx;
  }
}

.input-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 76rpx;
}

.amount-input {
  flex: 1;
  font-size: 80rpx;
  border: none;
  background: transparent;
  outline: none;
  padding: 8rpx 0;
  margin: 0 8rpx;
}
.input-label-money{
  font-family: 苹方-简, 苹方-简;
  font-weight: normal;
  font-size: 46rpx;
  color: #000000;
  margin-top: 24rpx;
}
.input-label-to {
  font-family: 苹方-简, 苹方-简;
  font-weight: normal;
  font-size: 26rpx;
  color: #000000;
  margin-bottom: 28rpx;
}

.all-btn {
  color: #66D47E;
  font-size: 30rpx;
  cursor: pointer;
}

.channel-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.channel-item {
  display: flex;
  align-items: center;
  gap: 0rpx;
}

.channel-label {
  font-size: 32rpx;
  margin-left: 8rpx;
  font-size: 26rpx;
  color: #666666;
}

.edit-btn {
  background: #eee;
  border: none;
  border-radius: 24rpx;
  padding: 8rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
}

.info-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 16rpx;
  gap: 32rpx;
}

.info-label {
  color: #888;
  font-size: 30rpx;
  width: 120rpx;
}

.info-value {
  font-size: 32rpx;
  color: #333;
  letter-spacing: 2rpx;
}

.withdraw-btn {
  width: 80%;
  height: 92rpx;
  line-height: 92rpx;
  background: #66D47E;
  color: #fff;
  border: none;
  border-radius: 46rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  cursor: pointer;
  transition: background 0.2s;
}

.withdraw-btn:hover {
  background: #66D47E;
}

.rule-row {
  width: 100%;
  text-align: center;
  margin-top: 16rpx;
}

.rule-link {
  color: #888;
  font-size: 28rpx;
}

.input-card {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;

}
</style>
