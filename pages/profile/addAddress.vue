<template>
  <view class="add-address-page">
    <NavigationCustorm>
      <template #title>
        {{ title }}
      </template>
    </NavigationCustorm>
    <view class="add-address-content">
      <!-- <view class="smart-address">
        <uni-easyinput type="textarea" v-model="addressText" placeholder="粘贴地址信息，智能识别文本中的姓名、电话和地址" />
        <view class="smart-address-btn-wrapper">
          <button class="smart-address-btn" @click="handleSmartAddress">粘贴并识别</button>
        </view>
      </view> -->
      <view class="add-address-item">
        <view class="form-item">
          <text class="label">收货人</text>
          <input type="text" placeholder="请输入收货人" v-model="form.receiverName" />
        </view>

        <view class="form-item">
          <text class="label">手机号</text>
          <input type="text" placeholder="请输入电话" v-model="form.contactNumber" />
        </view>
        <view class="form-item">
          <text class="label">所在地</text>
          <div class="city-picker" @click="handleLocation">
            <div class="city-picker-item" :class="{ 'active': form.areaDetail }">
              {{ form.areaDetail || '请选择所在地' }}
            </div>
            <div class="city-picker-item-btn"><uni-icons type="location" size="15" color="#66D47E"></uni-icons> 定位</div>
          </div>
        </view>
        <view class="form-item flex-start">
          <text class="label">详细地址</text>
          <textarea auto-height class="textarea" v-model="form.detailAddress" placeholder="   请输入详细地址"></textarea>
        </view>

        <view class="form-item">
          <view class="radio-wrapper" @click="handleClickRadio">
            <image src="@/static/images/profile/<EMAIL>" mode="widthFix" class="radio-icon"
              v-if="!form.isDefault">
            </image>
            <image src="@/static/images/profile/<EMAIL>" mode="widthFix" class="radio-icon" v-else>
            </image>
            <text class="radio-text"></text>
          </view>
          <text class="label">设为默认</text>
        </view>
      </view>

      <view class="submit-btn-wrapper">
        <button class="submit-btn" @click="submit">确定</button>
      </view>
    </view>
  </view>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { addressApi } from '@/common/api.js'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
import cityData from '@/pages/user/components/citydata.json'

const addressText = ref('')
const form = ref({
  receiverName: '',
  contactNumber: '',
  areaDetail: '',
  detailAddress: '',
  isDefault: false,
  longitude: 0,
  latitude: 0,

})

const handleClickRadio = () => {
  form.value.isDefault = !form.value.isDefault
}
const submit = async () => {
  form.value.isDefault = form.value.isDefault ? 1 : 0
  // 校验收货人
  if (!form.value.receiverName) {
    uni.showToast({
      title: '请输入收货人',
      icon: 'none'
    })
    return
  }
  // 校验手机号必填及正则格式
  if (!form.value.contactNumber) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none'
    })
    return
  }
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(form.value.contactNumber)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return
  }
  // 校验省市区必填
  if (!form.value.areaDetail) {
    uni.showToast({
      title: '请选择省市区',
      icon: 'none'
    })
    return
  }
  // 校验详细地址必填
  if (!form.value.detailAddress) {
    uni.showToast({
      title: '请输入详细地址',
      icon: 'none'
    })
    return
  }
  if (form.value.dataId) {
    const response = await addressApi.updateAddress(form.value)
    if (response.code === 200 || response.success) {
      uni.showToast({
        title: '修改成功',
        icon: 'success'
      })
      // loadAddressList()
      uni.navigateBack()
    } else {
      uni.showToast({
        title: response.message || '修改失败',
        icon: 'error'
      })
    }
  } else {
    const response = await addressApi.addAddress(form.value)
    if (response.code === 200 || response.success) {
      uni.showToast({
        title: '新增成功',
        icon: 'success'
      })
      // loadAddressList()
      uni.navigateBack()
    } else {
      uni.showToast({
        title: response.message || '新增失败',
        icon: 'error'
      })
    }
  }
  // popup.value.close()
  // loadAddressList()
}
const handleLocation = () => {
  // sendLocationMessage('紫方大厦', '江苏省南京市江宁区秣陵街道天禧路1号')
  // sendLocationMessage('唐镇高科东路', '上海市浦东新区唐镇高科东路')
  // sendLocationMessage('大潭郊野公园', '香港特别行政区南区大潭郊野公园')
  uni.chooseLocation({
    success: res => {
      sendLocationMessage(res.name, res.address,res.longitude,res.latitude)
    }
  })
}
const sendLocationMessage = async (name, address,longitude,latitude) => {
  // form.value.detailAddress = name

  form.value.longitude = longitude
  form.value.latitude = latitude
  const addressInfo = parseAddressFromString(address)
  if (addressInfo) {
    // 更新省市区
    form.value.areaDetail = addressInfo.fullRegionName?.replace(name, '')

    // 更新详细地址（去除省市区信息）
    form.value.detailAddress = address.replace(addressInfo.fullRegionName, '')?.trim()?.replace(name, '')+name

    console.log('解析的地址信息：', form.value.areaDetail,form.value.detailAddress)
  } else {
    form.value.areaDetail = address?.replace(name, '')
    form.value.detailAddress = name
    console.log('无法解析地址信息')
  }
}

// 从地址字符串中解析省市区信息
const parseAddressFromString = (address) => {
  if (!address) return null

  // 遍历省份
  for (const province of cityData) {
    const provinceName = province.text
    if (address.includes(provinceName)) {
      // 找到省份，继续查找市
      for (const city of province.children) {
        const cityName = city.text
        if (address.includes(cityName)) {
          // 找到市，继续查找区/县
          for (const area of city.children) {
            const areaName = area.text
            if (address.includes(areaName)) {
              // 找到区/县
              return {
                provinceName: provinceName,
                cityName: cityName,
                areaName: areaName,
                provinceCode: Number(province.value),
                cityCode: Number(city.value),
                areaCode: Number(area.value),
                fullRegionName: `${provinceName}${cityName==provinceName?'':cityName}${areaName==cityName?'':areaName}`
              }
            }
          }

          // 如果没有找到区/县，返回省市信息
          return {
            provinceName: provinceName,
            cityName: cityName,
            areaName: '',
            provinceCode: Number(province.value),
            cityCode: Number(city.value),
            areaCode: 0,
            fullRegionName: `${provinceName}${cityName==provinceName?'':cityName}`
          }
        }
      }

      // 如果没有找到市，返回省份信息
      return {
        provinceName: provinceName,
        cityName: '',
        areaName: '',
        provinceCode: Number(province.value),
        cityCode: 0,
        areaCode: 0,
        fullRegionName: provinceName
      }
    }
  }

  return null
}

// 获取选中的地址文本显示
const getSelectedAddressText = () => {
  if (form.value.provinceCode && form.value.cityCode && form.value.areaCode) {
    // 根据 code 查找对应的名称
    const province = cityData.find(p => Number(p.value) === form.value.provinceCode)
    if (province) {
      const city = province.children.find(c => Number(c.value) === form.value.cityCode)
      if (city) {
        const area = city.children.find(a => Number(a.value) === form.value.areaCode)
        if (area) {
          return `${province.text} ${city.text} ${area.text}`
        }
        return `${province.text} ${city.text}`
      }
      return province.text
    }
  }
  return '请选择省市区'
}

// 智能识别粘贴的地址文本并填充到表单中
const handleSmartAddress = () => {
  if (!addressText.value.trim()) {
    uni.showToast({
      title: '请先输入或粘贴地址信息',
      icon: 'none'
    })
    return
  }

  // 解析地址信息
  const addressInfo = parseAddressFromString(addressText.value)
  if (addressInfo) {
    // 更新表单数据
    form.value.provinceCode = addressInfo.provinceCode
    form.value.cityCode = addressInfo.cityCode
    form.value.areaCode = addressInfo.areaCode
    form.value.pickerValue = [
      String(addressInfo.provinceCode),
      String(addressInfo.cityCode),
      String(addressInfo.areaCode)
    ]

    // 更新详细地址（去除省市区信息）
    const detailAddress = addressText.value.replace(addressInfo.fullRegionName, '').trim()
    form.value.detailAddress = detailAddress

    // 尝试提取姓名和电话（简单的正则匹配）
    extractNameAndPhone(addressText.value)

    // 清空智能识别文本框
    addressText.value = ''

    uni.showToast({
      title: '地址识别成功',
      icon: 'success'
    })

    console.log('智能识别结果：', addressInfo)
  } else {
    uni.showToast({
      title: '无法识别地址信息，请检查格式',
      icon: 'none'
    })
  }
}

// 从文本中提取姓名和电话号码
const extractNameAndPhone = (text) => {
  // 提取手机号码（11位数字）
  const phoneMatch = text.match(/1[3-9]\d{9}/)
  if (phoneMatch) {
    form.value.contactNumber = phoneMatch[0]
  }

  // 提取姓名（简单规则：2-4个中文字符，且不包含省市区县等地名关键词）
  const nameMatch = text.match(/[\u4e00-\u9fa5]{2,4}/)
  if (nameMatch) {
    const name = nameMatch[0]
    // 排除常见的地名关键词
    const excludeWords = ['省', '市', '区', '县', '镇', '街道', '路', '号', '村', '乡', '社区']
    if (!excludeWords.some(word => name.includes(word))) {
      form.value.receiverName = name
    }
  }
}
const getAddress = async (id) => {
  const address = await addressApi.getAddress(id)
  console.log('address', address)
  if (address.code === 200 || address.success) {
    form.value = address.data
    form.value.pickerValue = [
      String(address.data?.provinceCode),
      String(address.data?.cityCode),
      String(address.data?.areaCode)
    ]

    form.value.isDefault = address.data?.isDefault == 1 ? true : false
  }
}
const title = ref('')
onLoad((options) => {
  console.log('onLoad', options)
  if (options.address) {
    title.value = '编辑地址'
    getAddress(options.address)
  } else {
    title.value = '新增地址'
  }
})
</script>
<style lang="scss" scoped>
.add-address-page {
  background: #F4F8FB;
  height: 100vh;

  .add-address-content {
    padding: 222rpx 30rpx 30rpx 30rpx;

    .smart-address {
      background: #fff;
      border-radius: 15rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;

      .smart-address-btn-wrapper {
        display: flex;
        justify-content: flex-end;
        margin-top: 20rpx;
      }

      .smart-address-btn {
        width: 154rpx;
        height: 54rpx;
        padding: 0;
        border-radius: 27rpx;
        background: #66D47E;
        color: #fff;
        border-radius: 27rpx;
        font-size: 24rpx;
        font-family: 苹方-简, 苹方-简;
        font-weight: normal;
        line-height: 54rpx;
        text-align: center;
        margin: 0;
      }
    }

    .add-address-item {
      background: #fff;
      border-radius: 15rpx;
      padding: 20rpx;
      margin-bottom: 150rpx;
    }
  }

  :deep(.is-textarea) {
    border: none !important;
  }

  :deep(.uni-easyinput__content-textarea) {
    margin: 0 !important;
    padding-left: 0 !important;
  }

  :deep(.uni-easyinput__placeholder-class) {
    font-family: 苹方-简-中黑体, 苹方-简;
    font-weight: normal;
    font-size: 22rpx;
    color: #999999;
  }

  .radio-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
  }

  .form-item {
    margin-bottom: 12rpx;
    display: flex;
    align-items: center;
    min-height: 84rpx;
    border-bottom: 1rpx solid #eaeaea;

    .label {
      flex-shrink: 0;
      width: 170rpx;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .radio-wrapper {
      display: flex;
      align-items: center;
    }

    &.flex-start {
      align-items: flex-start;
      padding-top: 20rpx;

    }

    input {
      width: 100%;
      padding: 20rpx;
      border-radius: 16rpx;
      background: #fff;
      border: none;
      font-size: 28rpx;
    }

    .submit-btn {
      width: 100%;
      height: 88rpx;
      background: #66D47E;
      border: none;
      border-radius: 16rpx;
      color: #fff;
      font-size: 32rpx;
      margin-top: 32rpx;
    }
  }

  .submit-btn-wrapper {
    height: 160rpx;
	box-sizing: border-box;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 16rpx 30rpx;
	background-color: #fff;
	z-index: 9;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    button {
      height: 88rpx;
      line-height: 88rpx;
      background: #66D47E;
      color: #fff;
      border: none;
      border-radius: 92rpx;
    }
  }

  .city-picker {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .city-picker-item {
    width: 100%;
    display: flex;
    font-size: 28rpx;
    align-items: center;
    justify-content: space-between;
    padding-left: 20rpx;
    color: gray;

    &.active {
      color: #000;
    }
  }

  .city-picker-item-btn {
    flex-shrink: 0;
    color: #66D47E;
    font-size: 24rpx;
  }

  :deep(.selected-area.placeholder) {
    border: none;
    font-size: 28rpx;
  }

  :deep(.arrow-area) {
    display: none;
  }

  :deep(.uni-data-tree-dialog) {
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
  }

  :deep(.input-value-border) {
    border: none !important;
  }

  :deep(.dialog-close-plus) {
    color: #000 !important;
  }

  :deep(.uni-textarea-placeholder) {
    font-size: 28rpx !important;
    color: #999999 !important;
  }

  :deep(.icon-clear) {
    display: none !important;
  }

  :deep(.input-split-line) {
    opacity: 0 !important;
  }

  :deep(.text-color) {
    color: #000 !important;
  }

  :deep(.uni-textarea-textarea) {
    font-size: 28rpx !important;
    padding: 0 20rpx !important;
  }

  :deep(.selected-item-active) {
    border-bottom: 4px solid #66D47E;
  }

  :deep(.check) {
    border-color: #66D47E !important;
  }
}
</style>
