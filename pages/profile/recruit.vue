<template>
  <view class="container page-container">
    <view class="custom-nav-bar">
      <view class="nav-title">我的招募</view>
    </view>
    <!-- 搜索 -->
    <view class="search-container">
      <view class="search-input">
        <uni-icons type="search" size="20" color="#000"></uni-icons>
        <input
          type="text"
          placeholder="输入技能名称查找"
          v-model="searchValue"
          @confirm="search"
          @input="onSearchInput"
        />
        <uni-icons
          v-if="searchValue"
          type="clear"
          size="20"
          color="#999"
          @click="clearSearch"
        ></uni-icons>
      </view>
    </view>
    <view class="activity-list">
      <view
        v-for="(item, index) in list"
        :key="index"
        class="activity-item"
        @tap="handleItemClick(item)"
      >
        <!-- 顶部标签和优惠标签 -->
        <view class="item-header">
          <view class="item-header-left">
            <image class="tag-icon" src="/static/zhaomu/headerLeft.png" mode="heightFix" />
            <text class="tag-text">{{ item.title }}</text>
          </view>
          <view class="item-header-right">
            <image class="tag-icon" :src="getStatusImage(item.activityStatus)" mode="heightFix" />
          </view>
        </view>
        <!-- 活动描述 -->
        <view class="activity-content">
          <view class="activity-content-top">
            <!-- <view class="category-tag">
              <text class="tag-text">{{ getType(item.activityStatus) }}</text>
            </view> -->
            <text class="activity-title">{{ item.description }}</text>
          </view>
        </view>
        <!-- 日期时间 -->
        <view class="time-info">
          <image class="time-icon" src="/static/zhaomu/time.png" mode="heightFix" />
          <text class="time-text">{{ item.expireTime }}</text>
        </view>

        <!-- 地点 -->
        <view class="location-info">
          <image class="location-icon" src="/static/zhaomu/location.png" mode="heightFix" />
          <text class="location-text">{{ item.addr || '暂无地点' }}</text>
        </view>

        <!-- 底部信息 -->
        <view class="item-footer">
          <view class="participants-info">
            <image
              v-for="(avatar, avatarIndex) in item.recentApplicants"
              :key="avatarIndex"
              :src="getFullUrl(avatar.avatar)"
              class="participant-avatar"
              mode="aspectFill"
            />
            <text class="participant-count">{{ item.applicationCount }}人已上车</text>
          </view>
          <view class="price-info">
            <text class="price">{{ item.pricePerPerson * item.applicationCount }}贝壳币</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { shopApi } from '@/common/api'
  export default {
    // 启用下拉刷新
    enablePullDownRefresh: true,
    // 设置下拉刷新的样式
    backgroundColor: '#f8f8f8',
    backgroundTextStyle: 'dark',

    data() {
      return {
        shops: [],
        searchValue: '' // 搜索关键词
      }
    },
    onLoad() {},
    // 下拉刷新
    onPullDownRefresh() {
      console.log('下拉刷新')
      // 清空搜索
      this.searchValue = ''
      // 重置分页
      this.pageNum = 1
      this.hasMore = true
      // 重新获取店铺列表
      this.refreshShopsList()
    },
    // 上拉加载更多
    onReachBottom() {
      console.log('触底加载更多')
      // 如果正在搜索或加载更多，或者没有更多数据，则不处理
      if (this.isSearching || this.isLoadingMore || !this.hasMore) {
        return
      }

      // 如果有搜索关键词，则加载更多搜索结果
      if (this.searchValue.trim()) {
        this.loadMoreSearchResults()
      } else {
        // 否则加载更多店铺列表
        this.loadMoreShops()
      }
    },
    methods: {}
  }
</script>

<style scoped lang="scss">
  .container {
    background-color: #f8f8f8;
    min-height: 100vh;
    overflow-x: hidden;
  }
  .nav-title {
    font-weight: bold;
  }

  .search-container {
    padding: 20rpx;
  }

  .search-input {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-radius: 50rpx;
    padding: 15rpx 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  .search-input input {
    flex: 1;
    height: 60rpx;
    margin-left: 15rpx;
    font-size: 28rpx;
    color: #333;
  }

  .search-input uni-icons {
    margin-right: 0;
  }

</style>
