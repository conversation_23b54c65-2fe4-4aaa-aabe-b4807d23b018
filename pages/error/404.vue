<template>
    <view>
        <view>
            <text style="font-size: 25px;color: #333;">
                404 Page Not Found
            </text>
        </view>
        <view>
            <text style="font-size: 18px;color: #999;">
                {{errMsg}}
            </text>
        </view>
    </view>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
    setup() {
        const errMsg = ref('')

        onMounted((query) => {
            errMsg.value = query.errMsg || ''
        })

        return {
            errMsg
        }
    }
}
</script>

<style>

</style>
