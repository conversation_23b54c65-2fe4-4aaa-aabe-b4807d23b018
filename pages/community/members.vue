<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-nav-bar2">
      <view class="nav-left" @tap="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
      <view class="nav-title">群组成员({{ memberList.length }})</view>
      <view class="nav-right"></view>
    </view>

    <!-- 搜索框 -->
    <view class="search-box">
      <view class="search-input-wrapper">
        <image src="/static/message/search.png" class="search-icon" mode="aspectFit"></image>
        <input type="text" placeholder="搜索" class="search-input" v-model="searchKeyword" />
      </view>
    </view>

    <!-- 群成员列表 -->
    <scroll-view
      scroll-y
      class="member-list-container"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <!-- 群主区域 -->
      <view class="section-title">群主</view>
      <view class="member-section">
        <view class="member-item" v-for="member in ownerList" :key="member.uid">
          <image
            class="avatar"
            :src="
              member.avatar.includes('http')
                ? member.avatar
                : 'http://47.123.3.183:9000/' + member.avatar
            "
            mode="aspectFill"
            @tap="viewUserDetail(member)"
          />
          <view class="member-info">
            <view class="nickname-row">
              <text class="nickname">{{ member.name }}</text>
              <view class="gender-age">
                <text :class="['gender-icon', member.gender === 1 ? 'male' : 'female']">{{
                  member.gender === 1 ? '♂' : '♀'
                }}</text>
                <text class="age">{{ member.age || '23' }}</text>
                <image
                  class="constellation-icon"
                  :src="`/static/index/constellation/${getConstellation(member)}.png`"
                  mode="aspectFit"
                ></image>
              </view>
            </view>
            <view class="tags-row">
              <view class="tag" v-for="(tag, index) in getRandomTags(member)" :key="index">{{
                tag
              }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 群成员区域 -->
      <view class="section-title">群成员</view>
      <view class="member-section">
        <view class="member-item" v-for="member in filteredMembers" :key="member.uid">
          <image
            class="avatar"
            :src="
              member.avatar.includes('http')
                ? member.avatar
                : 'http://47.123.3.183:9000/' + member.avatar
            "
            mode="aspectFill"
            @tap="viewUserDetail(member)"
          />
          <view class="member-info">
            <view class="nickname-row">
              <text class="nickname">{{ member.name }}</text>
              <view class="gender-age">
                <text :class="['gender-icon', member.gender === 1 ? 'male' : 'female']">{{
                  member.gender === 1 ? '♂' : '♀'
                }}</text>
                <text class="age">{{ member.age || getRandomAge() }}</text>
                <image
                  class="constellation-icon"
                  :src="`/static/index/constellation/${getConstellation(member)}.png`"
                  mode="aspectFit"
                ></image>
              </view>
            </view>
            <view class="tags-row">
              <view class="tag" v-for="(tag, index) in getRandomTags(member)" :key="index">{{
                tag
              }}</view>
            </view>
          </view>
          <view class="remove-btn" @tap="removeMember(member)">移除</view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { ref, onMounted, computed } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import PageHeader from '@/components/PageHeader.vue'
  import { groupsApi } from '@/common/api'

  // 群成员列表
  const memberList = ref([])
  const groupCode = ref('')
  const searchKeyword = ref('')
  const isRefreshing = ref(false)

  // 兴趣标签库
  const interestTags = {
    sports: ['篮球', '羽毛球', '网球', '健身', '跑步', '爬山', '密室逃脱'],
    food: ['美食', '露营', 'homebar', '剧本杀'],
    constellation: [
      '水瓶座',
      '双鱼座',
      '白羊座',
      '金牛座',
      '双子座',
      '巨蟹座',
      '狮子座',
      '处女座',
      '天秤座',
      '天蝎座',
      '射手座',
      '摩羯座'
    ]
  }

  // 星座列表
  const constellations = [
    'aquarius', // 水瓶座
    'pisces', // 双鱼座
    'aries', // 白羊座
    'taurus', // 金牛座
    'gemini', // 双子座
    'cancer', // 巨蟹座
    'leo', // 狮子座
    'virgo', // 处女座
    'libra', // 天秤座
    'scorpio', // 天蝎座
    'sagittarius', // 射手座
    'capricorn' // 摩羯座
  ]

  // 获取随机年龄
  const getRandomAge = () => {
    return Math.floor(Math.random() * 10) + 21 // 21-30之间的随机数
  }

  // 获取随机星座
  const getConstellation = member => {
    if (member.constellation) {
      return member.constellation
    }
    const randomIndex = Math.floor(Math.random() * constellations.length)
    return constellations[randomIndex]
  }

  // 获取随机标签
  const getRandomTags = member => {
    // 如果成员有标签数据则使用，否则随机生成
    if (member.tags && member.tags.length > 0) {
      return member.tags
    }

    const tags = []
    // 随机选择1-3个运动标签
    const sportsCount = Math.floor(Math.random() * 3) + 1
    for (let i = 0; i < sportsCount; i++) {
      const randomIndex = Math.floor(Math.random() * interestTags.sports.length)
      if (!tags.includes(interestTags.sports[randomIndex])) {
        tags.push(interestTags.sports[randomIndex])
      }
    }

    // 随机选择0-2个食物标签
    const foodCount = Math.floor(Math.random() * 2)
    for (let i = 0; i < foodCount; i++) {
      const randomIndex = Math.floor(Math.random() * interestTags.food.length)
      if (!tags.includes(interestTags.food[randomIndex])) {
        tags.push(interestTags.food[randomIndex])
      }
    }

    return tags.slice(0, 3) // 最多返回3个标签
  }

  // 群主列表
  const ownerList = computed(() => {
    return memberList.value.filter(member => member.role === 'owner')
  })

  // 过滤后的成员列表（非群主且符合搜索关键词）
  const filteredMembers = computed(() => {
    return memberList.value
      .filter(member => member.role !== 'owner')
      .filter(member => {
        if (!searchKeyword.value) return true
        return member.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
      })
  })

  // 获取群成员列表
  const getGroupMembers = async () => {
    try {
      const res = await groupsApi.getGroupMembers({
        groupCode: groupCode.value
      })

      if (res.code === 200 && res.data) {
        // 为测试数据添加角色信息
        if (res.data.length > 0) {
          res.data[0].role = 'owner' // 第一个成员设为群主

          // 为每个成员添加性别信息（随机）
          res.data.forEach(member => {
            if (!member.gender) {
              member.gender = Math.random() > 0.5 ? 1 : 2 // 1-男性，2-女性
            }

            // 随机添加星座
            if (!member.constellation) {
              const randomIndex = Math.floor(Math.random() * constellations.length)
              member.constellation = constellations[randomIndex]
            }
          })
        }

        memberList.value = res.data
      } else {
        uni.showToast({
          title: '获取群成员失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取群成员异常:', error)
      uni.showToast({
        title: '获取群成员列表失败',
        icon: 'none'
      })
    }
  }

  // 移除成员
  const removeMember = member => {
    uni.showModal({
      title: '提示',
      content: `确定要移除成员 ${member.name} 吗？`,
      success: async res => {
        if (res.confirm) {
          try {
            // 调用移除成员API
            // const result = await groupsApi.removeGroupMember({
            //   groupCode: groupCode.value,
            //   uid: member.uid
            // })

            // 模拟API调用成功
            uni.showToast({
              title: '移除成功',
              icon: 'success'
            })

            // 从列表中移除
            const index = memberList.value.findIndex(item => item.uid === member.uid)
            if (index !== -1) {
              memberList.value.splice(index, 1)
            }
          } catch (error) {
            console.error('移除成员异常:', error)
            uni.showToast({
              title: '移除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  }

  // 返回上一页
  const goBack = () => {
    uni.navigateBack()
  }

  // 查看用户详情
  const viewUserDetail = member => {
    uni.navigateTo({
      url: `/pages/user/detail?uid=${member.uid}`
    })
  }

  // 下拉刷新
  const onRefresh = async () => {
    isRefreshing.value = true
    try {
      await getGroupMembers()
    } finally {
      setTimeout(() => {
        isRefreshing.value = false
      }, 1000)
    }
  }

  // 页面加载
  onLoad(option => {
    if (option.groupCode) {
      groupCode.value = option.groupCode
      getGroupMembers()
    } else {
      uni.showToast({
        title: '缺少必要参数',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  })
</script>

<style lang="scss" scoped>
  :deep(.uni-page-head) {
    background: transparent !important;
  }
  :deep(uni-page-body) {
    background: #f5f5f5;
  }

  :deep(uni-page) {
    background: #f5f5f5;
  }

  .custom-nav-bar2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    background-color: #ffffff;
    padding-top: var(--status-bar-height);
    height: calc(88rpx + var(--status-bar-height));

    .nav-left,
    .nav-right {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-back-icon {
      font-size: 48rpx;
      font-weight: bold;
    }

    .nav-title {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .page-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    box-sizing: border-box;
    width: 100%;
    overflow-x: hidden;
    background-color: #f4f8fb !important;
  }

  .search-box {
    padding: 20rpx;
    // background-color: #fff;
    background-color: #f4f8fb !important;

    .search-input-wrapper {
      display: flex;
      align-items: center;
      background-color: #fff;
      border-radius: 36rpx;
      padding: 10rpx 20rpx;

      .search-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }

      .search-input {
        flex: 1;
        height: 60rpx;
        font-size: 28rpx;
        color: #333;
      }
    }
  }

  .member-list-container {
    flex: 1;
    padding: 0;
    width: 100%;
    box-sizing: border-box;
  }

  .section-title {
    font-size: 28rpx;
    color: #666666;
    padding: 20rpx;
    background-color: #f4f8fb;
  }
  .member-section {
    // margin-bottom: 20rpx;
    background-color: #fff;
    padding: 0 20rpx;
  }

  .member-list {
    width: 100%;
  }

  .member-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    position: relative;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .avatar {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      margin-right: 20rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    }

    .member-info {
      flex: 1;

      .nickname-row {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;

        .nickname {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          margin-right: 10rpx;
        }

        .gender-age {
          display: flex;
          align-items: center;

          .gender-icon {
            font-size: 24rpx;
            width: 32rpx;
            height: 32rpx;
            line-height: 32rpx;
            text-align: center;
            border-radius: 50%;
            margin-right: 6rpx;

            &.male {
              background-color: rgba(64, 158, 255, 0.1);
              color: #409eff;
            }

            &.female {
              background-color: rgba(255, 109, 151, 0.1);
              color: #ff6d97;
            }
          }

          .age {
            font-size: 24rpx;
            color: #999;
            margin-right: 10rpx;
          }

          .constellation-icon {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }

      .tags-row {
        display: flex;
        flex-wrap: wrap;

        .tag {
          font-size: 24rpx;
          padding: 4rpx 16rpx;
          background-color: #f5f5f5;
          color: #666;
          border-radius: 20rpx;
          margin-right: 10rpx;
          margin-bottom: 6rpx;
        }
      }
    }

    .remove-btn {
      padding: 10rpx 20rpx;
      background-color: #ff6d6d;
      color: #fff;
      font-size: 26rpx;
      border-radius: 30rpx;
      min-width: 80rpx;
      text-align: center;

      &:active {
        opacity: 0.8;
      }
    }
  }
</style>
