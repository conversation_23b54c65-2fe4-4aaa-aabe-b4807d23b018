<template>
  <view class="search-list-container">
    <!-- 顶部搜索栏 -->
    <view class="search-header">
      <view class="back-icon" @tap="handleBack">
        <text class="icon">&#xe601;</text>
      </view>
      <view class="search-box">
        <view class="search-input-wrapper">
          <image src="/static/message/search.png" class="search-icon"></image>
          <input
            type="text"
            v-model="searchKeyword"
            placeholder="美食"
            class="search-input"
            @input="handleSearch"
            @confirm="handleSearch"
            confirm-type="search"
          />
          <text class="clear-icon" v-if="searchKeyword" @tap="clearSearch">×</text>
        </view>
      </view>
    </view>

    <!-- 搜索结果列表 -->
    <view class="search-results">
      <view
        class="community-item"
        v-for="(item, index) in searchResults"
        :key="index"
        @tap="handleJoinChannel(item)"
      >
        <image :src="item.groupImg" class="community-avatar" mode="aspectFill" />
        <view class="community-info">
          <view class="community-name-row">
            <text class="community-name">{{ item.groupName }}</text>
            <!-- <text class="member-count">{{ item.memberCount }}人</text> -->
          </view>
          <view class="community-owner">
            <text class="channel-label">频道主</text>
            <image :src="item.ownerAvatar" class="owner-avatar" mode="aspectFill" />
            <text class="owner-name">{{ item.ownerName }}</text>
          </view>
        </view>
        <view class="join-btn">
          <text>加入</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import { groupsApi } from '../../common/api'

  // 搜索关键词
  const searchKeyword = ref('')

  // 搜索结果列表
  const searchResults = ref([])

  // 页面加载
  onLoad(options => {
    console.log('SearchList页面加载，参数:', options)
    if (options && options.keyword) {
      searchKeyword.value = decodeURIComponent(options.keyword)
      handleSearch()
    }
  })

  // 处理搜索
  const handleSearch = async () => {
    if (!searchKeyword.value.trim()) {
      searchResults.value = []
      return
    }

    try {
      console.log('开始搜索频道，关键词:', searchKeyword.value)
      // 调用API获取搜索结果
      const res = await groupsApi.getGroups({
        keyword: searchKeyword.value
      })

      console.log('搜索结果:', res)

      if (res && res.code === 200 && Array.isArray(res.rows)) {
        searchResults.value = res.rows.map(group => ({
          groupCode: group.groupCode,
          groupName: group.groupName,
          groupImg:
            group.groupImg && group.groupImg.includes('http')
              ? group.groupImg
              : group.groupImg
                ? 'http://************:9000/' + group.groupImg
                : '/static/images/default-group.png',
          memberCount: group.memberCount || 0,
          description: group.description || '',
          channelId: group.channelId,
          ownerAvatar:
            group.ownerImg && group.ownerImg.includes('http')
              ? group.ownerImg
              : group.ownerImg
                ? 'http://************:9000/' + group.ownerImg
                : '/static/images/default-avatar.png',
          ownerName: group.ownerUserName || '未知用户'
        }))
      } else {
        searchResults.value = []
        if (res && res.msg) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
      }
    } catch (error) {
      console.error('搜索失败:', error)
      searchResults.value = []
      uni.showToast({
        title: '搜索失败，请稍后重试',
        icon: 'none'
      })
    }
  }

  // 清除搜索
  const clearSearch = () => {
    searchKeyword.value = ''
    searchResults.value = []
  }

  // 返回上一页
  const handleBack = () => {
    uni.navigateBack()
  }

  // 处理加入频道
  const handleJoinChannel = item => {
    uni.navigateTo({
      url: `/pages/community/join?groupCode=${item.groupCode}&groupName=${encodeURIComponent(item.groupName)}&groupImg=${encodeURIComponent(item.groupImg)}&memberCount=${item.memberCount}&channelId=${item.channelId}&ownerAvatar=${encodeURIComponent(item.ownerAvatar)}&ownerName=${encodeURIComponent(item.ownerName)}`
    })
  }
</script>

<style lang="scss" scoped>
  .search-list-container {
    min-height: 100vh;
    background-color: #f4f8fb;
    display: flex;
    flex-direction: column;
  }

  .search-header {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    background-color: #ffffff;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .back-icon {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10rpx;

      .icon {
        font-family: 'iconfont';
        font-size: 40rpx;
        color: #333;
      }
    }

    .search-box {
      flex: 1;

      .search-input-wrapper {
        display: flex;
        align-items: center;
        background-color: #f5f5f5;
        border-radius: 30rpx;
        padding: 0 20rpx;
        height: 70rpx;
        position: relative;

        .search-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 10rpx;
        }

        .search-input {
          flex: 1;
          height: 70rpx;
          font-size: 28rpx;
          color: #333;
        }

        .clear-icon {
          width: 40rpx;
          height: 40rpx;
          line-height: 40rpx;
          text-align: center;
          font-size: 32rpx;
          color: #999;
        }
      }
    }
  }

  .search-results {
    flex: 1;
    padding: 20rpx;

    .community-item {
      display: flex;
      align-items: center;
      background-color: #fff;
      border-radius: 12rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

      .community-avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 12rpx;
        margin-right: 20rpx;
      }

      .community-info {
        flex: 1;

        .community-name-row {
          display: flex;
          align-items: center;
          margin-bottom: 10rpx;

          .community-name {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
          }

          .member-count {
            font-size: 24rpx;
            color: #999;
            margin-left: 10rpx;
          }
        }

        .community-owner {
          display: flex;
          align-items: center;

          .channel-label {
            font-size: 24rpx;
            color: #999;
            margin-right: 10rpx;
          }

          .owner-avatar {
            width: 36rpx;
            height: 36rpx;
            border-radius: 50%;
            margin-right: 8rpx;
          }

          .owner-name {
            font-size: 24rpx;
            color: #666;
          }
        }
      }

      .join-btn {
        width: 100rpx;
        height: 60rpx;
        background-color: #4cd964;
        border-radius: 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        text {
          font-size: 28rpx;
          color: #fff;
        }
      }
    }
  }
</style>
