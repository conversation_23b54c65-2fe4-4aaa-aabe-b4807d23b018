<template>
  <view class="join-channel-container">
    <!-- 顶部标题 -->
    <!-- <view class="header-title">
      <text>加入已有频道</text>
    </view> -->

    <!-- 顶部图片区域 -->
    <view class="header-image-container">
      <image src="/static/message/island.png" class="island-image" mode="aspectFit"></image>
    </view>

    <!-- 搜索区域 -->
    <view class="search-container">
      <view class="search-box">
        <image src="/static/message/search.png" class="search-icon"></image>
        <input
          type="text"
          placeholder="搜索或输入邀请码"
          v-model="searchKeyword"
          @input="handleSearch"
          class="search-input"
        />
      </view>

      <!-- 查看频道按钮 -->
      <button class="view-btn" @tap="handleViewChannel">查看频道信息</button>
    </view>

    <!-- 搜索结果列表 -->
    <view class="search-results" v-if="searchResults.length > 0">
      <view
        class="community-item"
        v-for="(item, index) in searchResults"
        :key="index"
        @tap="handleCommunityClick(item)"
      >
        <image
          :src="item.groupImg || '/static/images/default-group.png'"
          class="avatar"
          mode="aspectFill"
        />
        <view class="message-content">
          <view class="message-header">
            <view class="name-container">
              <text class="name">{{ item.groupName }}</text>
              <text class="member-count">{{ item.memberCount }}人</text>
            </view>
          </view>
          <view class="message-footer">
            <text class="message">{{ item.description || '暂无社群介绍' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <!-- <view v-else class="empty-state">
      <text class="empty-tip">输入社群邀请码加入已有社群</text>
    </view> -->
  </view>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { groupsApi } from '../../common/api'

  // 搜索关键词
  const searchKeyword = ref('')

  // 搜索结果
  const searchResults = ref([])

  // 处理搜索
  const handleSearch = () => {
    if (!searchKeyword.value) {
      searchResults.value = []
      return
    }

    // 模拟搜索结果，实际开发中应该调用API
    setTimeout(() => {
      if (searchKeyword.value) {
        searchGroups()
      }
    }, 500)
  }

  // 搜索群组
  const searchGroups = async () => {
    try {
      // 调用搜索群组API
      const res = await groupsApi.getGroups({
        keyword: searchKeyword.value
      })

      if (res && res.code === 200 && Array.isArray(res.rows)) {
        searchResults.value = res.rows.map(group => ({
          groupCode: group.groupCode,
          groupName: group.groupName,
          groupImg:
            group.groupImg && group.groupImg.includes('http')
              ? group.groupImg
              : group.groupImg
                ? 'http://************:9000/' + group.groupImg
                : '/static/images/default-group.png',
          memberCount: group.memberCount || 0,
          description: group.description || '',
          channelId: group.channelId,
          ownerAvatar:
            group.ownerImg && group.ownerImg.includes('http')
              ? group.ownerImg
              : group.ownerImg
                ? 'http://************:9000/' + group.ownerImg
                : '/static/images/default-avatar.png',
          ownerName: group.ownerUserName || '未知用户'
        }))
      } else {
        searchResults.value = []
        if (res && res.msg) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
      }
    } catch (error) {
      console.error('搜索群组失败:', error)
      searchResults.value = []
      uni.showToast({
        title: '搜索失败，请稍后再试',
        icon: 'none'
      })
    }
  }

  // 处理点击社群
  const handleCommunityClick = item => {
    uni.navigateTo({
      url: `/pages/community/join?groupCode=${item.groupCode}&groupName=${encodeURIComponent(item.groupName)}&groupImg=${encodeURIComponent(item.groupImg)}&memberCount=${item.memberCount}&channelId=${item.channelId}&ownerAvatar=${encodeURIComponent(item.ownerAvatar || '/static/images/default-avatar.png')}&ownerName=${encodeURIComponent(item.ownerName || '未知用户')}`
    })
  }

  // 查看频道信息
  const handleViewChannel = () => {
    if (!searchKeyword.value) {
      uni.showToast({
        title: '请输入有效的邀请码',
        icon: 'none'
      })
      return
    }

    console.log('准备跳转到searchList页面，关键词:', searchKeyword.value)
    // 携带查询条件跳转到searchList页面
    const url = `./searchList?keyword=${encodeURIComponent(searchKeyword.value)}`
    console.log('跳转URL:', url)

    uni.navigateTo({
      url: url,
      success: () => {
        console.log('跳转成功')
      },
      fail: err => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  }
</script>

<style lang="scss" scoped>
  .join-channel-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 30rpx;
    background-color: #f4f8fb;
  }

  .header-title {
    text-align: center;
    margin-bottom: 20rpx;

    text {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .header-image-container {
    display: flex;
    justify-content: center;
    margin-bottom: 40rpx;

    .island-image {
      width: 300rpx;
      height: 300rpx;
    }
  }

  .search-container {
    margin-bottom: 30rpx;
    display: flex;
    flex-direction: column;

    .search-box {
      display: flex;
      align-items: center;
      height: 80rpx;
      background-color: #fff;
      border-radius: 40rpx;
      padding: 0 30rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      margin-bottom: 20rpx;

      .search-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 20rpx;
      }

      .search-input {
        flex: 1;
        height: 80rpx;
        font-size: 28rpx;
        color: #333;
      }
    }

    .view-btn {
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      background-color: #4cd964;
      color: #fff;
      font-size: 30rpx;
      border-radius: 40rpx;
      margin-top: 10rpx;
    }
  }

  .search-results {
    flex: 1;

    .community-item {
      display: flex;
      align-items: flex-start;
      padding: 20rpx;
      background-color: #fff;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .message-content {
        flex: 1;

        .message-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10rpx;

          .name-container {
            display: flex;
            align-items: center;

            .name {
              font-size: 32rpx;
              color: #0d0e0f;
              font-weight: 500;
            }

            .member-count {
              font-size: 24rpx;
              color: #999;
              margin-left: 10rpx;
            }
          }
        }

        .message-footer {
          .message {
            font-size: 26rpx;
            color: #666;
          }
        }
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;

    .empty-tip {
      font-size: 28rpx;
      color: #999;
    }
  }
</style>
