<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-box">
        <text class="iconfont icon-search"></text>
        <input type="text" placeholder="搜索商品" v-model="searchText" @confirm="handleSearch" />
      </view>
    </view>

    <!-- 轮播图 -->
    <swiper class="banner" circular autoplay interval="3000" duration="500">
      <swiper-item v-for="(item, index) in bannerList" :key="index">
        <image :src="item.image" mode="aspectFill" @click="navigateToBanner(item)"></image>
      </swiper-item>
    </swiper>

    <!-- 商品列表 -->
    <scroll-view
      scroll-y
      class="product-list"
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view
        class="product-item"
        v-for="(item, index) in productList"
        :key="index"
        @click="navigateToDetail(item)"
      >
        <image class="product-image" :src="item.image" mode="aspectFill"></image>
        <view class="product-info">
          <view class="info-main">
            <text class="product-name">{{ item.name }}</text>
            <text class="product-desc">{{ item.description }}</text>
            <view class="tags" v-if="item.tags && item.tags.length">
              <text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{
                tag
              }}</text>
            </view>
          </view>
          <view class="info-footer">
            <view class="price">
              <text class="symbol">¥</text>
              <text class="amount">{{ item.price }}</text>
              <text class="original" v-if="item.originalPrice">¥{{ item.originalPrice }}</text>
            </view>
            <text class="sold">已售{{ item.soldCount }}件</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useUserStore } from '@/stores/user'
  import { storeToRefs } from 'pinia'

  const searchText = ref('')
  const isRefreshing = ref(false)

  // 确保store在setup阶段就被初始化
  const store = useUserStore()
  const { userInfo } = storeToRefs(store)

  const bannerList = reactive([
    {
      id: 1,
      image: '/static/banner/banner1.jpg',
      url: '/pages/activity/new-year'
    },
    {
      id: 2,
      image: '/static/banner/banner2.jpg',
      url: '/pages/activity/discount'
    }
  ])

  const productList = reactive([
    {
      id: 1,
      name: '新鲜水果大礼包',
      description: '精选当季水果，多种搭配，新鲜美味',
      image: '/static/products/fruit.jpg',
      price: 99.9,
      originalPrice: 128,
      soldCount: 1023,
      tags: ['限时特惠', '包邮']
    },
    {
      id: 2,
      name: '进口零食大礼包',
      description: '多种进口零食，满足您的味蕾，美味随心享',
      image: '/static/products/snacks.jpg',
      price: 158,
      originalPrice: 198,
      soldCount: 836,
      tags: ['新品', '直营']
    }
  ])

  const handleSearch = () => {
    uni.showToast({
      title: '搜索功能开发中',
      icon: 'none'
    })
  }

  const loadMore = () => {
    uni.showLoading({
      title: '加载中...'
    })

    setTimeout(() => {
      uni.hideLoading()
    }, 1000)
  }

  const onRefresh = () => {
    isRefreshing.value = true
    setTimeout(() => {
      isRefreshing.value = false
      uni.showToast({
        title: '刷新成功',
        icon: 'success'
      })
    }, 1000)
  }

  const navigateToBanner = banner => {
    uni.navigateTo({
      url: banner.url
    })
  }

  const navigateToDetail = product => {
    uni.navigateTo({
      url: '/pages/product/detail?id=' + product.id
    })
  }
</script>

<style lang="scss">
  :deep(.uni-page-head) {
    background: transparent !important;
  }
  :deep(uni-page-body) {
    background: url('/static/bg.png') no-repeat center center;
    background-size: cover;
  }

  :deep(uni-page) {
    background: url('/static/bg.png') no-repeat center center;
    background-size: cover;
  }

  .container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    // background-color: #f5f5f5;

    .search-bar {
      padding: 20rpx;
      // background-color: #fff;
      position: sticky;
      top: 0;
      z-index: 100;

      .search-box {
        display: flex;
        align-items: center;
        background-color: #f5f5f5;
        padding: 10rpx 20rpx;
        border-radius: 30rpx;

        .icon-search {
          font-size: 32rpx;
          color: #999;
          margin-right: 10rpx;
        }

        input {
          flex: 1;
          font-size: 28rpx;
        }
      }
    }

    .banner {
      width: 100%;
      height: 300rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .product-list {
      flex: 1;
      padding: 20rpx;

      .product-item {
        display: flex;
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 20rpx;
        padding: 20rpx;

        .product-image {
          width: 200rpx;
          height: 200rpx;
          border-radius: 8rpx;
          margin-right: 20rpx;
        }

        .product-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .info-main {
            .product-name {
              font-size: 30rpx;
              color: #333;
              font-weight: bold;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .product-desc {
              font-size: 26rpx;
              color: #999;
              margin-top: 10rpx;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
            }

            .tags {
              display: flex;
              flex-wrap: wrap;
              margin-top: 10rpx;

              .tag {
                font-size: 22rpx;
                color: #ff6b6b;
                background: rgba(255, 107, 107, 0.1);
                padding: 4rpx 12rpx;
                border-radius: 4rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
              }
            }
          }

          .info-footer {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-top: 10rpx;

            .price {
              display: flex;
              align-items: baseline;

              .symbol {
                font-size: 24rpx;
                color: #ff6b6b;
              }

              .amount {
                font-size: 36rpx;
                color: #ff6b6b;
                font-weight: bold;
                margin-right: 10rpx;
              }

              .original {
                font-size: 24rpx;
                color: #999;
                text-decoration: line-through;
              }
            }

            .sold {
              font-size: 22rpx;
              color: #999;
            }
          }
        }
      }
    }
  }
</style>
