<template>
  <view class="join-community-container">
    <view class="content-container">
      <!-- 社群信息卡片（包含介绍） -->
      <view class="info-card">
        <view class="community-header-card">
          <image
            class="community-avatar"
            :src="groupImg || '/static/images/default-group.png'"
            mode="aspectFill"
            @error="handleImageError"
          ></image>
          <view class="community-info">
            <view class="community-name">{{ groupName || '深洲巨口-南京美食小分队' }}</view>
            <view class="community-id">频道ID {{ groupCode || '10053' }}</view>
          </view>
        </view>

        <view class="divider"></view>

        <view class="community-intro">
          <view class="card-title">社群介绍</view>
          <view class="card-content">
            {{ groupDescription }}
          </view>
        </view>
      </view>

      <!-- 社群成员 -->
      <view class="info-card" style="background-color: rgba(255, 255, 255, 0.6)">
        <view class="card-title">社群成员({{ memberCount === 'null' ? '34' : memberCount }})</view>
        <view class="members-grid">
          <view class="member-item" v-for="(item, index) in displayMembers" :key="index">
            <image class="member-avatar" :src="item.avatar" mode="aspectFill"></image>
            <view class="member-name">{{ item.name }}</view>
            <view class="member-tag" v-if="item.role === 'owner'">群主</view>
          </view>
        </view>
        <view class="view-all-members" @tap="viewAllMembers">
          <text>查看全部成员</text>
          <text class="arrow-down">&#xe6e1;</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="join-button-container">
      <button class="join-button" @tap="submitJoinRequest" :disabled="submitting">
        {{ submitting ? '加入中...' : '加入频道' }}
      </button>
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted, computed } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import { groupsApi } from '../../common/api'

  // 页面参数
  const groupCode = ref('')
  const groupName = ref('')
  const groupImg = ref('')
  const groupDescription = ref('')
  const memberCount = ref(0)
  const channelId = ref('')

  // 群主信息（从searchList页面传入）
  const ownerInfo = ref({
    ownerAvatar: '',
    ownerName: ''
  })

  // 群成员列表
  const memberList = ref([])

  // 表单数据
  const applyReason = ref('')
  const submitting = ref(false)

  // 显示的成员列表（最多显示10个）
  const displayMembers = computed(() => {
    return memberList.value.slice(0, 10)
  })

  // 加载页面数据
  onLoad(options => {
    console.log('接收到的参数:', options)

    if (options.groupCode) {
      groupCode.value = options.groupCode
      console.log('设置 groupCode:', groupCode.value)
    }
    if (options.groupName) {
      groupName.value = decodeURIComponent(options.groupName)
      console.log('设置 groupName:', groupName.value)
    }
    if (options.channelId) {
      channelId.value = options.channelId
      console.log('设置 channelId:', channelId.value)
    }
    if (options.groupImg) {
      groupImg.value = decodeURIComponent(options.groupImg)
      console.log('设置 groupImg:', groupImg.value)
    }
    if (options.memberCount) {
      memberCount.value = options.memberCount
      console.log('设置 memberCount:', memberCount.value)
    }

    // 从searchList页面传入的群主信息
    if (options.ownerAvatar) {
      ownerInfo.value.ownerAvatar = decodeURIComponent(options.ownerAvatar)
      console.log('设置 ownerAvatar:', ownerInfo.value.ownerAvatar)
    }
    if (options.ownerName) {
      ownerInfo.value.ownerName = decodeURIComponent(options.ownerName)
      console.log('设置 ownerName:', ownerInfo.value.ownerName)
    }

    console.log('群主信息:', ownerInfo.value)

    // 加载社群详情和成员
    loadGroupDetail()
    loadGroupMembers()
  })

  // 加载社群详情
  const loadGroupDetail = async () => {
    try {
      // 调用getGroupDetail获取群聊信息
      const res = await groupsApi.getGroupDetail({
        array: [groupCode.value]
      })

      console.log('群聊详情结果:', res)

      if (res && res.code === 200 && res.data && res.data.length > 0) {
        const groupInfo = res.data[0]
        if (groupInfo.attribute) {
          // attribute包含群聊信息
          groupDescription.value =
            groupInfo.attribute.description || groupInfo.attribute.intro || '暂无社群介绍'
          // 更新其他可能的群聊信息
          if (groupInfo.attribute.groupName) {
            groupName.value = groupInfo.attribute.groupName
          }
          if (groupInfo.attribute.groupImg) {
            groupImg.value = groupInfo.attribute.groupImg.includes('http')
              ? groupInfo.attribute.groupImg
              : 'http://************:9000/' + groupInfo.attribute.groupImg
          }
          if (groupInfo.attribute.memberCount) {
            memberCount.value = groupInfo.attribute.memberCount
          }
        }
      } else {
        console.log('获取群聊详情失败或数据为空')
        // 保持原有的默认描述
        groupDescription.value =
          '美食，从广义上来说，是指那些味道、口感、外观等方面具有独特魅力，能给人带来愉悦的味觉享受和感官体验的食物。'
      }
    } catch (error) {
      console.error('获取社群详情失败', error)
      // 保持原有的默认描述
      groupDescription.value =
        '美食，从广义上来说，是指那些味道、口感、外观等方面具有独特魅力，能给人带来愉悦的味觉享受和感官体验的食物。'
      uni.showToast({
        title: '获取社群信息失败',
        icon: 'none'
      })
    }
  }

  // 加载群成员列表（参考members页面）
  const loadGroupMembers = async () => {
    try {
      const res = await groupsApi.getGroupMembers({
        groupCode: groupCode.value
      })

      console.log('群成员结果:', res)

      if (res && res.code === 200 && res.data) {
        memberList.value = res.data.map(member => ({
          uid: member.uid,
          name: member.name,
          avatar:
            member.avatar && member.avatar.includes('http')
              ? member.avatar
              : member.avatar
                ? 'http://************:9000/' + member.avatar
                : '/static/images/default-avatar.png',
          role: member.role || 'member'
        }))

        // 如果从searchList传入了群主信息，确保群主在列表中并设置正确的role
        if (ownerInfo.value.ownerName) {
          // 查找是否已有群主在成员列表中
          let ownerFound = false
          memberList.value = memberList.value.map(member => {
            if (member.name === ownerInfo.value.ownerName) {
              ownerFound = true
              return { ...member, role: 'owner' }
            }
            return member
          })

          // 如果群主不在成员列表中，添加群主到列表第一位
          if (!ownerFound) {
            memberList.value.unshift({
              uid: 'owner_from_search',
              name: ownerInfo.value.ownerName,
              avatar: ownerInfo.value.ownerAvatar,
              role: 'owner'
            })
          }
        }

        // 确保群主在列表第一位
        memberList.value.sort((a, b) => {
          if (a.role === 'owner') return -1
          if (b.role === 'owner') return 1
          return 0
        })

        console.log('处理后的成员列表:', memberList.value)
        console.log(
          '群主成员:',
          memberList.value.filter(m => m.role === 'owner')
        )

        // 更新成员数量
        memberCount.value = memberList.value.length
      } else {
        console.log('获取群成员失败或数据为空，使用传入的群主信息或模拟数据')
        // 如果有群主信息就使用，否则使用模拟数据
        if (ownerInfo.value.ownerName) {
          memberList.value = [
            {
              uid: 'owner_from_search',
              name: ownerInfo.value.ownerName,
              avatar: ownerInfo.value.ownerAvatar,
              role: 'owner'
            },
            ...generateMockMembers().slice(1) // 添加一些模拟成员，但跳过第一个
          ]
        } else {
          memberList.value = generateMockMembers()
        }
      }
    } catch (error) {
      console.error('获取群成员异常:', error)
      // 如果有群主信息就使用，否则使用模拟数据
      if (ownerInfo.value.ownerName) {
        memberList.value = [
          {
            uid: 'owner_from_search',
            name: ownerInfo.value.ownerName,
            avatar: ownerInfo.value.ownerAvatar,
            role: 'owner'
          },
          ...generateMockMembers().slice(1) // 添加一些模拟成员，但跳过第一个
        ]
      } else {
        memberList.value = generateMockMembers()
      }
    }
  }

  // 生成模拟成员数据
  const generateMockMembers = () => {
    const mockNames = [
      '夏小帅',
      '王雨欣',
      '张子涵',
      '陈子轩',
      '吴雅琪',
      '杨芸琳',
      '周浩然',
      '黄诗琪',
      '赵梓涵',
      '徐峰'
    ]

    return mockNames.map((name, index) => ({
      uid: `mock_${index}`,
      name: name,
      avatar: `/static/index/avatar_${(index % 5) + 1}.png`,
      role: index === 0 ? 'owner' : 'member'
    }))
  }

  // 查看全部成员
  const viewAllMembers = () => {
    uni.navigateTo({
      url: `/pages/community/members?groupCode=${groupCode.value}`
    })
  }

  // 提交加入请求
  const submitJoinRequest = async () => {
    submitting.value = true
    try {
      // 调用加入社群API
      const res = await groupsApi.joinGroup({
        groupCode: groupCode.value,
        reason: '希望加入社群学习交流'
      })

      if (res.code === 200) {
        uni.navigateTo({
          url: `/pages/chat/index?channelID=${channelId.value}&channelType=2&nickName=${groupName.value}&groupCode=${groupCode.value}&memberCount=${memberCount.value}`
        })
      }
    } catch (error) {
      console.error('申请加入社群失败', error)
      uni.showToast({
        title: '申请失败，请稍后再试',
        icon: 'none'
      })
    } finally {
      submitting.value = false
    }
  }

  // 返回上一页
  const goBack = () => {
    uni.navigateBack()
  }

  // 处理图片加载错误
  const handleImageError = () => {
    console.log('图片加载失败，使用默认图片')
    groupImg.value = '/static/images/default-group.png'
  }
</script>

<style lang="scss" scoped>
  .join-community-container {
    min-height: 100vh;
    background-image: url('/static/message/groupDetail-bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center top;
    position: relative;
    padding-bottom: 120rpx;
  }

  .nav-header {
    display: flex;
    align-items: center;
    height: 90rpx;
    padding: 0 30rpx;
    position: relative;
    background-color: #ffffff;

    .back-icon {
      position: absolute;
      left: 30rpx;
      font-size: 40rpx;
      color: #333;
    }

    .page-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 500;
    }
  }

  .content-container {
    padding: 20rpx 30rpx;
  }

  .info-card {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
  }

  .community-header-card {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .community-avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 10rpx;
      margin-right: 20rpx;
    }

    .community-info {
      flex: 1;

      .community-name {
        font-size: 34rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
      }

      .community-id {
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .divider {
    height: 1px;
    background-color: #f0f0f0;
    margin: 20rpx 0;
  }

  .community-intro {
    .card-title {
      font-size: 30rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .card-content {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
    }
  }

  .card-title {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .members-grid {
    display: flex;
    flex-wrap: wrap;

    .member-item {
      width: 20%;
      margin-bottom: 20rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .member-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-bottom: 10rpx;
      }

      .member-name {
        font-size: 24rpx;
        color: #333;
        text-align: center;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .member-tag {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        background-color: #4cd964;
        color: white;
        font-size: 20rpx;
        padding: 2rpx 8rpx;
        border-radius: 10rpx;
        margin-top: -10rpx;
      }
    }
  }

  .view-all-members {
    text-align: center;
    color: #666;
    font-size: 28rpx;
    margin-top: 10rpx;

    .arrow-down {
      margin-left: 10rpx;
    }
  }

  .join-button-container {
    padding: 30rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: transparent;

    .join-button {
      width: 100%;
      height: 90rpx;
      background-color: #4cd964;
      color: white;
      border-radius: 45rpx;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:disabled {
        opacity: 0.6;
      }
    }
  }
</style>
