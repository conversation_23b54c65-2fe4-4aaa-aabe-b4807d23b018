<template>
  <view class="create-community-container">
    <view class="form-section">
      <view class="form-title">创建社群</view>

      <!-- 社群头像 -->
      <view class="form-item">
        <text class="form-label">社群头像</text>
        <view class="avatar-upload" @tap="chooseImage">
          <image
            v-if="formData.groupImg"
            :src="formData.groupImg"
            class="avatar-preview"
            mode="aspectFill"
          ></image>
          <view v-else class="avatar-placeholder">
            <text class="iconfont icon-camera">+</text>
            <text class="upload-text">上传图片</text>
          </view>
        </view>
      </view>

      <!-- 社群名称 -->
      <view class="form-item">
        <text class="form-label">社群名称</text>
        <input
          class="form-input"
          v-model="formData.groupName"
          placeholder="请输入社群名称（2-20个字符）"
          maxlength="20"
        />
      </view>

      <!-- 社群类型 -->
      <view class="form-item">
        <text class="form-label">社群类型</text>
        <view class="type-selector">
          <view
            class="type-item"
            :class="{ active: formData.type === 'single' }"
            @tap="formData.type = 'single'"
          >
            <text>个人社群</text>
          </view>
          <view
            class="type-item"
            :class="{ active: formData.type === 'multiple' }"
            @tap="formData.type = 'multiple'"
          >
            <text>多人共建</text>
          </view>
        </view>
      </view>

      <!-- 社群介绍 -->
      <view class="form-item">
        <text class="form-label">社群介绍</text>
        <textarea
          class="form-textarea"
          v-model="formData.description"
          placeholder="请输入社群介绍（最多200字）"
          maxlength="200"
        ></textarea>
        <text class="word-count">{{ formData.description.length }}/200</text>
      </view>

      <!-- 加入方式 -->
      <view class="form-item">
        <text class="form-label">加入方式</text>
        <view class="join-method-selector">
          <view
            class="join-method-item"
            :class="{ active: formData.joinMethod === 'free' }"
            @tap="formData.joinMethod = 'free'"
          >
            <text>自由加入</text>
          </view>
          <view
            class="join-method-item"
            :class="{ active: formData.joinMethod === 'approval' }"
            @tap="formData.joinMethod = 'approval'"
          >
            <text>需要审核</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button class="cancel-btn" @tap="goBack">取消</button>
      <button class="submit-btn" @tap="submitForm" :disabled="submitting">
        {{ submitting ? '提交中...' : '创建社群' }}
      </button>
    </view>
  </view>
</template>

<script setup>
  import { ref, reactive } from 'vue'
  import { groupsApi, fileApi } from '../../common/api'

  // 表单数据
  const formData = reactive({
    groupName: '',
    groupImg: '',
    type: 'single', // single: 个人社群, multiple: 多人共建
    description: '',
    joinMethod: 'approval' // free: 自由加入, approval: 需要审核
  })

  const submitting = ref(false)

  // 选择图片
  const chooseImage = () => {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async res => {
        const tempFilePath = res.tempFilePaths[0]

        // 显示上传中
        uni.showLoading({
          title: '上传中...'
        })

        try {
          // 上传图片
          const uploadRes = await fileApi.uploadOne(tempFilePath, 'community')
          formData.groupImg = uploadRes.url
        } catch (error) {
          console.error('上传图片失败', error)
          uni.showToast({
            title: '图片上传失败，请重试',
            icon: 'none'
          })
        } finally {
          uni.hideLoading()
        }
      }
    })
  }

  // 提交表单
  const submitForm = async () => {
    // 表单验证
    if (!formData.groupName.trim()) {
      uni.showToast({
        title: '请输入社群名称',
        icon: 'none'
      })
      return
    }

    if (!formData.groupImg) {
      uni.showToast({
        title: '请上传社群头像',
        icon: 'none'
      })
      return
    }

    submitting.value = true
    try {
      // 调用创建社群API
      const result = await groupsApi.createGroup({
        groupName: formData.groupName,
        groupImg: formData.groupImg,
        type: formData.type,
        description: formData.description,
        joinMethod: formData.joinMethod
      })

      uni.showToast({
        title: '创建成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } catch (error) {
      console.error('创建社群失败', error)
      uni.showToast({
        title: '创建失败，请稍后再试',
        icon: 'none'
      })
    } finally {
      submitting.value = false
    }
  }

  // 返回上一页
  const goBack = () => {
    uni.navigateBack()
  }
</script>

<style lang="scss" scoped>
  :deep(.uni-page-head) {
    background: transparent !important;
  }
  :deep(uni-page-body) {
    background: url('/static/bg.png') no-repeat center center;
    background-size: cover;
  }

  :deep(uni-page) {
    background: url('/static/bg.png') no-repeat center center;
    background-size: cover;
  }

  .create-community-container {
    padding: 30rpx;
    background-color: #f8f8f8;
    min-height: 100vh;
  }

  .form-section {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;

    .form-title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 40rpx;
      text-align: center;
    }
  }

  .form-item {
    margin-bottom: 40rpx;
    position: relative;

    .form-label {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
      display: block;
    }

    .form-input {
      width: 100%;
      height: 80rpx;
      border: 1px solid #e0e0e0;
      border-radius: 8rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
      box-sizing: border-box;
    }

    .form-textarea {
      width: 100%;
      height: 200rpx;
      border: 1px solid #e0e0e0;
      border-radius: 8rpx;
      padding: 20rpx;
      font-size: 28rpx;
      box-sizing: border-box;
    }

    .word-count {
      position: absolute;
      right: 20rpx;
      bottom: 20rpx;
      font-size: 24rpx;
      color: #999;
    }

    .avatar-upload {
      width: 160rpx;
      height: 160rpx;
      border-radius: 16rpx;
      overflow: hidden;
      background-color: #f5f5f5;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .avatar-preview {
        width: 100%;
        height: 100%;
      }

      .avatar-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;

        .icon-camera {
          font-size: 48rpx;
          color: #999;
          margin-bottom: 10rpx;
        }

        .upload-text {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .type-selector,
    .join-method-selector {
      display: flex;

      .type-item,
      .join-method-item {
        flex: 1;
        height: 80rpx;
        border: 1px solid #e0e0e0;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #666;
        margin-right: 20rpx;

        &:last-child {
          margin-right: 0;
        }

        &.active {
          border-color: #007aff;
          color: #007aff;
          background-color: rgba(0, 122, 255, 0.05);
        }
      }
    }
  }

  .bottom-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 60rpx;

    button {
      width: 45%;
      height: 88rpx;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    .submit-btn {
      background-color: #007aff;
      color: #fff;

      &:disabled {
        opacity: 0.6;
      }
    }
  }
</style>
