<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <view class="custom-nav-bar2">
      <view class="nav-left" @tap="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
      <view class="nav-title">聊天设置</view>
      <view class="nav-right"></view>
    </view>

    <!-- 群组详情内容 -->
    <view class="detail-container">
      <!-- 群成员模块 -->
      <view class="detail-card">
        <view class="card-header" @tap="navigateToMembers">
          <text class="card-title">群组成员 ({{ memberCount }})</text>
          <view class="card-right">
            <text class="member-count" v-if="memberCount">{{ memberCount }}位</text>
            <text class="arrow-icon">›</text>
          </view>
        </view>

        <!-- 成员头像列表 -->
        <view class="member-avatars">
          <image
            v-for="(member, index) in memberList.slice(0, 10)"
            :key="index"
            class="member-avatar"
            :src="
              member.avatar.includes('http')
                ? member.avatar
                : 'http://47.123.3.183:9000/' + member.avatar
            "
            mode="aspectFill"
          />
        </view>
      </view>

      <!-- 群设置模块 -->
      <view class="detail-card">
        <view class="setting-item">
          <text class="setting-label">接收群消息通知</text>
          <switch
            class="setting-switch"
            color="#66D47E"
            :checked="receiveNotification"
            @change="toggleNotification"
          />
        </view>

        <view class="setting-item">
          <text class="setting-label">社群邀请码</text>
          <view class="invite-code">
            <text class="code-text">{{ inviteCode }}</text>
            <view class="copy-btn" @tap="copyInviteCode">复制</view>
          </view>
        </view>
      </view>

      <!-- 退出群组按钮 -->
      <view class="exit-btn" @tap="showExitConfirm">
        <text>退出社群</text>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import PageHeader from '@/components/PageHeader.vue'
  import { groupsApi } from '@/common/api'

  // 群组基本信息
  const groupName = ref('')
  const groupCode = ref('')
  const channelId = ref('')
  const memberCount = ref(0)
  const inviteCode = ref('')
  const userRole = ref('普通群员')

  // 群设置
  const receiveNotification = ref(true)

  // 群成员列表
  const memberList = ref([])

  // 获取群组详情
  const getGroupDetail = async () => {
    try {
      // 获取群成员
      const res = await groupsApi.getGroupMembers({
        groupCode: groupCode.value
      })

      if (res.code === 200 && res.data) {
        memberList.value = res.data
        memberCount.value = res.data.length
      }
    } catch (error) {
      console.error('获取群组详情失败:', error)
      uni.showToast({
        title: '获取群组详情失败',
        icon: 'none'
      })
    }
  }

  // 跳转到成员列表页面
  const navigateToMembers = () => {
    uni.navigateTo({
      url: `/pages/community/members?groupCode=${groupCode.value}`
    })
  }

  // 切换通知设置
  const toggleNotification = e => {
    receiveNotification.value = e.detail.value
    // TODO: 调用接口保存设置
  }

  // 复制邀请码
  const copyInviteCode = () => {
    uni.setClipboardData({
      data: inviteCode.value,
      success: () => {
        uni.showToast({
          title: '邀请码已复制',
          icon: 'success'
        })
      }
    })
  }

  // 显示退出确认对话框
  const showExitConfirm = () => {
    uni.showModal({
      title: '确认退出',
      content: '确定要退出该社群吗？',
      confirmColor: '#e64340',
      success: res => {
        if (res.confirm) {
          exitGroup()
        }
      }
    })
  }

  // 退出群组
  const exitGroup = async () => {
    try {
      const res = await groupsApi.leaveGroup({
        groupCode: groupCode.value
      })

      if (res.code === 200) {
        uni.showToast({
          title: '已退出社群',
          icon: 'success'
        })

        // 延迟返回
        setTimeout(() => {
          uni.navigateBack({
            delta: 2 // 返回到会话列表
          })
        }, 1500)
      } else {
        uni.showToast({
          title: res.msg || '退出失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('退出社群失败:', error)
      uni.showToast({
        title: '退出社群失败',
        icon: 'none'
      })
    }
  }

  // 返回上一页
  const goBack = () => {
    uni.navigateBack()
  }

  // 页面加载
  onLoad(option => {
    if (option.groupCode) {
      groupCode.value = option.groupCode
      groupName.value = decodeURIComponent(option.groupName || '')
      channelId.value = option.channelId || ''
      inviteCode.value = option.inviteCode || `PD${Math.floor(10000 + Math.random() * 90000)}`
      getGroupDetail()
    } else {
      uni.showToast({
        title: '缺少必要参数',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  })
</script>

<style lang="scss" scoped>
  :deep(.uni-page-head) {
    background: transparent !important;
  }
  :deep(uni-page-body) {
    background-color: #f4f8fb;
  }

  :deep(uni-page) {
    background-color: #f4f8fb;
  }

  .custom-nav-bar2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    background-color: #ffffff;
    border-bottom: 1rpx solid #eaeaea;
    padding-top: var(--status-bar-height);
    height: calc(88rpx + var(--status-bar-height));

    .nav-left,
    .nav-right {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-back-icon {
      font-size: 48rpx;
      font-weight: bold;
    }

    .nav-menu-icon {
      font-size: 24rpx;
      font-weight: bold;
      color: #999;
    }

    .nav-title {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .page-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--status-bar-height) - 44px);
    background-color: #f4f8fb;
    box-sizing: border-box;
    width: 100%;
    overflow-x: hidden;
  }

  .detail-container {
    flex: 1;
    padding: 20rpx;
    background-color: #f4f8fb;
  }

  .detail-card {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .card-title {
        font-size: 30rpx;
        font-weight: 500;
        color: #333;
      }

      .card-right {
        display: flex;
        align-items: center;

        .member-count {
          font-size: 26rpx;
          color: #999;
          margin-right: 10rpx;
        }

        .arrow-icon {
          font-size: 36rpx;
          color: #999;
        }
      }
    }

    .member-avatars {
      display: flex;
      flex-wrap: wrap;

      .member-avatar {
        width: 70rpx;
        height: 70rpx;
        border-radius: 50%;
        margin-right: 15rpx;
        margin-bottom: 15rpx;
        background-color: #eee;
      }
    }

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;

      .setting-label {
        font-size: 28rpx;
        color: #333;
      }

      .invite-code {
        display: flex;
        align-items: center;

        .code-text {
          font-size: 28rpx;
          color: #333;
          margin-right: 20rpx;
        }

        .copy-btn {
          font-size: 24rpx;
          color: #fff;
          background-color: #66d47e;
          padding: 6rpx 20rpx;
          border-radius: 30rpx;
        }
      }
    }
  }

  .exit-btn {
    background-color: #66d47e;
    color: #fff;
    height: 90rpx;
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    margin: 40rpx 0;
  }

  .role-tag {
    text-align: center;
    margin: 30rpx 0;

    text {
      font-size: 28rpx;
      color: #66d47e;
    }
  }
</style>
