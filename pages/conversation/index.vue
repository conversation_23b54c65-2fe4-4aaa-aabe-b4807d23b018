<template>
  <view class="page-container">
    <!-- 状态栏占位 -->
    <view class="status-bar-placeholder"></view>

    <!-- 连接状态指示器 -->
    <!-- <view class="connection-status" v-if="showConnectionStatus" :class="connectionStatus">
      <text class="status-icon">{{ connectionStatusIcon }}</text>
      <text class="status-text">{{ connectionStatusText }}</text>
    </view> -->
    <!-- 自定义导航栏 -->
    <view class="custom-nav-bar">
      <view class="nav-title">消息</view>
    </view>
    <!-- 顶部导航栏 -->
    <view class="nav-tabs">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-item', { active: currentTab === index }]"
        @click="switchTab(index)"
      >
        <image
          v-if="currentTab === index"
          :src="currentTab === index ? tabIcons[index].active : ''"
          class="tab-icon"
          mode="aspectFit"
        ></image>
        <text v-if="currentTab !== index" class="tab-text">{{ tab }}</text>
      </view>
      <view class="zan-item" @click="goZan">
        <image src="/static/message/zan.png" class="tab-icon"></image>
        <text class="tab-text">获赞</text>
      </view>
    </view>
    <!-- 通知权限提示，仅在未开启通知且未手动关闭时显示 -->
    <!-- <view class="notify" v-if="!notificationEnabled && showNotifyTip"> -->
    <view class="notify" v-if="currentTab === 0 && !notificationEnabled && showNotifyTip">
      <view class="notify-content">
        <span>打开推送通知</span>
        <span>及时接受最新消息</span>
      </view>
      <button class="notify-btn" @click="goToNotificationSettings">去打开</button>
      <text class="close-btn" @click="closeNotifyTip">×</text>
      <image src="/static/message/notice.png" mode="aspectFit" />
    </view>
    <!-- 消息列表区域 -->
    <scroll-view
      scroll-y
      class="scroll-container"
      @scrolltolower="handleLoadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="handleRefresh"
    >
      <!-- 使用组件 -->
      <MessageTab v-if="currentTab === 0" ref="messageTabRef" />
      <OfficialTab v-else-if="currentTab === 1" />
      <CommunityTab v-else-if="currentTab === 2" ref="communityTabRef" />
      <!-- <ContactsTab v-else-if="currentTab === 3" ref="contactsTabRef" /> -->

      <uni-load-more :status="loadMoreStatus" />
    </scroll-view>
  </view>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
  import { onShow } from '@dcloudio/uni-app'
  import WKIMManager from '@/utils/wukongim'
  import { useUserStore } from '@/stores/user'
  import { useConversationStore } from '@/stores/conversation'
  import { useGroupStore } from '@/stores/group'
  import { storeToRefs } from 'pinia'

  // 导入组件
  import MessageTab from './components/MessageTab.vue'
  import OfficialTab from './components/OfficialTab.vue'
  import CommunityTab from './components/CommunityTab.vue'
  import ContactsTab from './components/ContactsTab.vue'

  // 初始化store
  const store = useUserStore()
  const conversationStore = useConversationStore()
  const { userInfo } = storeToRefs(store)

  // 系统信息
  const systemInfo = uni.getSystemInfoSync()
  const isAndroid = systemInfo.platform === 'android'
  const statusBarHeight = systemInfo.statusBarHeight || 0

  // 设置自定义导航栏高度
  const setCustomNavBarHeight = () => {
    // 动态设置CSS变量
    // #ifdef H5
    document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight}px`)
    document.documentElement.style.setProperty(
      '--window-bottom',
      `${systemInfo.windowBottom || 0}px`
    )

    // 针对刘海屏做特殊处理
    if (isAndroid && systemInfo.safeArea) {
      const safeAreaTop = systemInfo.safeArea.top
      if (safeAreaTop > statusBarHeight) {
        document.documentElement.style.setProperty(
          '--safe-area-inset-top',
          `${safeAreaTop - statusBarHeight}px`
        )
      }
    }
    // #endif

    // 非H5环境下，直接使用变量，在样式中通过计算处理
    console.log('系统信息:', systemInfo)
    console.log('状态栏高度:', statusBarHeight)
    console.log('是否安卓:', isAndroid)
    if (isAndroid && systemInfo.safeArea) {
      console.log('安全区域:', systemInfo.safeArea)
    }
  }

  // 组件引用
  const messageTabRef = ref(null)
  const communityTabRef = ref(null)
  const contactsTabRef = ref(null)

  // 响应式状态
  const currentTab = ref(0)
  const tabs = reactive(['消息', '官方', '社群'])
  const tabIcons = reactive([
    {
      active: '/static/message/message.png',
      inactive: '/static/images/tabbar/message.png'
    },
    {
      active: '/static/message/offical.png',
      inactive: '/static/images/tabbar/msg.png'
    },
    {
      active: '/static/message/community.png',
      inactive: '/static/images/tabbar/home.png'
    }
  ])
  const refreshing = ref(false)
  const loadMoreStatus = ref('more')
  const connectionStatus = ref('disconnected')
  const showConnectionStatus = ref(false)
  // 添加通知权限状态
  const notificationEnabled = ref(true)
  // 添加通知提示显示状态
  const showNotifyTip = ref(true)

  // 计算属性
  const connectionStatusIcon = computed(() => {
    switch (connectionStatus.value) {
      case 'connected':
        return '●'
      case 'connecting':
        return '○'
      case 'disconnected':
      default:
        return '●'
    }
  })

  const connectionStatusText = computed(() => {
    switch (connectionStatus.value) {
      case 'connected':
        return '消息服务已连接'
      case 'connecting':
        return '正在连接消息服务...'
      case 'disconnected':
      default:
        return '消息服务连接失败'
    }
  })

  // 方法
  const switchTab = index => {
    currentTab.value = index
  }

  // 加载更多
  const handleLoadMore = () => {
    // 实现加载更多逻辑
  }

  // 刷新
  const handleRefresh = async () => {
    refreshing.value = true
    if (currentTab.value === 0) {
      await loadConversations()
    } else if (currentTab.value === 2) {
      await communityTabRef.value?.loadCommunityList()
    } else if (currentTab.value === 3) {
      await contactsTabRef.value?.loadContacts()
    }
    refreshing.value = false
  }

  // 加载会话列表
  const loadConversations = async () => {
    try {
      console.log('开始加载会话列表*******', userInfo.value?.nickname)
      let list = await WKIMManager.getConversationList()
      console.log('获取到会话列表********:', list)
      conversationStore.setConversationList(list || [])

      // 置顶会话处理已经在 WKIMManager 中完成，不需要再次获取
    } catch (error) {
      console.error('Failed to load conversations:', error)
      uni.showToast({
        title: '加载会话列表失败',
        icon: 'none'
      })
      conversationStore.setConversationList([])
    }
  }

  // 检查通知权限状态
  const checkNotificationPermission = () => {
    // 先检查是否已经手动关闭了通知提示
    const notifyTipClosed = uni.getStorageSync('notifyTipClosed')
    if (notifyTipClosed) {
      showNotifyTip.value = false
      return
    }

    // 仅在APP环境下检查通知权限
    // #ifdef APP-PLUS
    uni.getAppAuthorizeSetting({
      success: res => {
        console.log('授权状态：', res)
        // 根据平台获取通知权限状态
        // #ifdef APP-PLUS
        if (uni.getSystemInfoSync().platform === 'android') {
          notificationEnabled.value = res.notificationAuthorized
        } else {
          notificationEnabled.value = res.notificationAuthorized
        }
        // #endif
      },
      fail: err => {
        console.error('获取授权状态失败：', err)
        // 获取失败时默认为已开启，避免错误地显示通知提示
        notificationEnabled.value = true
      }
    })
    // #endif

    // #ifdef H5 || MP-WEIXIN
    // 在H5或微信小程序环境下，无法直接获取通知权限
    // 可以通过本地存储来记录用户是否已经同意过通知
    const hasAgreedNotification = uni.getStorageSync('hasAgreedNotification')
    notificationEnabled.value = !!hasAgreedNotification
    // #endif
  }

  // 打开通知设置
  const goToNotificationSettings = () => {
    // #ifdef APP-PLUS
    // 打开应用设置页面
    uni.openAppAuthorizeSetting({
      success: res => {
        console.log('打开授权设置页面成功')
        // 设置完成后重新检查权限
        setTimeout(() => {
          checkNotificationPermission()
        }, 1000)
      },
      fail: err => {
        console.error('打开授权设置页面失败：', err)
      }
    })
    // #endif

    // #ifdef H5 || MP-WEIXIN
    // 在H5或微信小程序环境下，记录用户已同意通知
    uni.setStorageSync('hasAgreedNotification', true)
    notificationEnabled.value = true
    // #endif
  }

  // 关闭通知提示
  const closeNotifyTip = () => {
    showNotifyTip.value = false
    // 记录用户已关闭通知提示，避免再次显示
    uni.setStorageSync('notifyTipClosed', true)
  }

  // 跳转到获赞页面
  const goZan = () => {
    uni.navigateTo({
      url: '/pages/message/likes'
    })
  }

  // 生命周期钩子
  onMounted(async () => {
    console.log('会话页面加载，初始化数据')
    // 设置自定义导航栏高度
    setCustomNavBarHeight()

    // 检查悟空IM连接状态
    const status = WKIMManager.getConnectionStatus()
    connectionStatus.value = status

    // 如果未连接，尝试连接
    if (status !== 'connected') {
      try {
        await WKIMManager.initConnect()
        connectionStatus.value = 'connected'
      } catch (error) {
        console.error('连接消息服务失败:', error)
        connectionStatus.value = 'disconnected'
      }
    }

    // 加载会话列表
    await loadConversations()

    // 检查通知权限
    checkNotificationPermission()
  })

  // 添加 onShow 生命周期钩子
  onShow(async () => {
    console.log('会话列表页面显示，重新加载会话列表')
    // 重新加载会话列表
    await loadConversations()

    // 更新未读消息数量
    updateUnreadBadge()

    // 每次页面显示时重新检查通知权限
    checkNotificationPermission()
  })

  // 更新未读消息数量
  const updateUnreadBadge = () => {
    try {
      // 获取消息列表未读数量
      let messageUnreadCount = 0
      if (conversationStore.conversationList && conversationStore.conversationList.length > 0) {
        messageUnreadCount = conversationStore.conversationList.reduce((total, conv) => {
          return total + (conv.unread || 0)
        }, 0)
      }

      // 获取群组未读消息数量
      const groupStore = useGroupStore()
      let groupUnreadCount = 0
      if (groupStore.groupHistoryList && groupStore.groupHistoryList.length > 0) {
        groupUnreadCount = groupStore.groupHistoryList.reduce((total, group) => {
          // 优先使用unread字段
          if (group.unread !== undefined) {
            return total + group.unread
          }
          // 如果有原始群组数据，尝试从中获取
          if (group.groupRes && group.groupRes.unread !== undefined) {
            return total + group.groupRes.unread
          }
          return total
        }, 0)
      }

      // 计算总未读数量
      const totalUnreadCount = messageUnreadCount + groupUnreadCount
      console.log(
        '未读消息总数:',
        totalUnreadCount,
        '(消息:',
        messageUnreadCount,
        ', 群组:',
        groupUnreadCount,
        ')'
      )

      // 设置TabBar角标
      if (totalUnreadCount > 0) {
        uni.setTabBarBadge({
          index: 2, // 消息选项卡的索引
          text: totalUnreadCount > 99 ? '99+' : totalUnreadCount.toString(),
          success: () => {
            console.log('设置TabBar角标成功')
          },
          fail: err => {
            console.error('设置TabBar角标失败:', err)
          }
        })
      } else {
        // 如果没有未读消息，移除角标
        uni.removeTabBarBadge({
          index: 2
        })
      }
    } catch (error) {
      console.error('更新未读消息角标失败:', error)
    }
  }

  onUnmounted(() => {
    // 清理工作，如移除监听器等
  })
</script>

<style lang="scss" scoped>
  .notify {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 90px;
    position: relative;
    margin-bottom: 10px;
    width: 100%;

    .notify-content {
      position: absolute;
      top: 55%;
      left: 24%;
      transform: translateY(-50%);
      z-index: 1;
      display: flex;
      flex-direction: column;

      span {
        color: #fff;
        font-size: 14px;
        line-height: 20px;

        &:first-child {
          font-weight: bold;
          font-size: 18px;
          margin-bottom: 10px;
        }
      }
    }

    .notify-btn {
      position: absolute;
      right: 70rpx;
      top: 55%;
      transform: translateY(-50%);
      z-index: 1;
      background-color: #ffffff;
      color: #333;
      font-size: 14px;
      padding: 5px 15px;
      border-radius: 20px;
      border: none;
      height: 36px;
      line-height: 26px;
      font-weight: bold;
    }

    .close-btn {
      position: absolute;
      top: 24rpx;
      right: 40rpx;
      color: #000;
      font-size: 16px;
      font-weight: bold;
      z-index: 2;
      width: 24px;
      height: 24x;
      line-height: 24px;
      text-align: center;
      border-radius: 50%;
      span {
        font-size: 12px;
      }
    }

    :deep(uni-image) {
      width: 90%;
      height: 90px;
      object-fit: cover;
    }
  }

  .status-bar-placeholder {
    width: 100%;
    height: var(--status-bar-height, 20px);
  }

  .custom-nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    height: 90rpx; /* 增加高度 */
    background: transparent !important;
    border: none !important;
    padding-top: 0; /* 移除顶部内边距，因为已经有占位视图 */
    box-sizing: border-box;
    margin-top: 0;
    position: relative;
    z-index: 10;

    .nav-left,
    .nav-right {
      width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-back-icon {
      font-size: 48rpx;
      font-weight: bold;
    }

    .nav-menu-icon {
      font-size: 32rpx;
      color: #999;
    }

    .nav-title {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .page-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: url('/static/bg.png') no-repeat center center;
    background-size: cover;
    box-sizing: border-box;
    min-height: 100vh;
    padding-top: 0; /* 移除顶部内边距，因为已经有占位视图 */
    padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
    padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */
    position: fixed;
    width: 100vw;
  }
  :deep(.uni-page-head) {
    background: transparent !important;
  }
  :deep(uni-page-body) {
    background: url('/static/bg.png') no-repeat center center;
    background-size: cover;
  }

  :deep(uni-page) {
    background: url('/static/bg.png') no-repeat center center;
    background-size: cover;
  }

  .connection-status {
    padding: 10rpx 20rpx;
    font-size: 24rpx;
    text-align: center;

    &.connected {
      background-color: rgba(0, 179, 138, 0.1);
      color: #00b38a;
    }

    &.connecting {
      background-color: rgba(255, 153, 0, 0.1);
      color: #ff9900;
    }

    &.disconnected {
      background-color: rgba(255, 59, 48, 0.1);
      color: #ff3b30;
    }

    .status-icon {
      margin-right: 10rpx;
    }
  }

  .nav-tabs {
    display: flex;
    // background-color: #fff;
    padding: 20rpx 20rpx 0 20rpx;
    position: sticky;
    top: 0;
    z-index: 1;

    .tab-item {
      // flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 10rpx 0;
      position: relative;
      width: 136rpx;

      .tab-icon {
        width: 136rpx;
        height: 64rpx;
        margin-bottom: 10rpx;
      }

      .tab-text {
        width: 32px;
        height: 24px;
        margin-left: 10px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #000000;
        line-height: 24px;
        text-align: left;
        font-style: normal;
      }

      &.active {
        .tab-icon {
          width: 136rpx;
          height: 64rpx;
        }
      }
    }
    .zan-item {
      // flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx 0;
      position: relative;
      width: 136rpx;
      height: 64rpx;
      position: absolute;
      right: 10px;
      justify-content: center;
      top: 16px;
      .tab-icon {
        width: 40rpx;
        height: 40rpx;
        margin-bottom: 10rpx;
      }

      .tab-text {
        height: 24px;
        margin-left: 10px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #000000;
        line-height: 24px;
        text-align: left;
        font-style: normal;
      }
    }
  }

  .scroll-container {
    flex: 1;
    height: 0;
    padding: 0;
    box-sizing: border-box;
  }
</style>
