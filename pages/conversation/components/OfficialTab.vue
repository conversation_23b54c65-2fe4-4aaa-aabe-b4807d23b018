<template>
  <view>
    <view class="message-item official" v-for="(item, index) in officialList" :key="index" @click="handleItemClick(item)">
      <image :src="item.icon" class="message-icon"></image>
      <view class="message-content">
        <view class="message-header">
          <text class="title">{{ item.title }}</text>
          <text class="time">{{ formatTime(item.time) }}</text>
        </view>
        <text class="desc">{{ item.description }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { reactive } from 'vue'
  import { formatListTime } from '@/utils/date'

  // 格式化时间
  const formatTime = timestamp => {
    if (!timestamp) return ''
    return formatListTime(timestamp)
  }

  // 处理项目点击
  const handleItemClick = (item) => {
    if (item.title === '预约订单') {
      uni.navigateTo({
        url: '/pages/reservationOrder/notifications'
      })
    } else if (item.title === '订单通知') {
      uni.navigateTo({
        url: '/pages/orders/notifications'
      })
    }
  }

  // 官方消息列表
  const officialList = reactive([
    // {
    //   icon: '/static/message/offical-worker.png',
    //   title: '官方客服',
    //   description: '解答小岛屿问题中遇到的使用问题',
    //   time: Date.now() - 1000 * 60 * 30 // 30分钟前
    // },
    {
      icon: '/static/message/bell.png',
      title: '官方通知',
      description: '官方最新消息通知',
      time: Date.now() - 1000 * 60 * 60 * 3 // 3小时前
    },
    {
      icon: '/static/message/order.png',
      title: '预约订单',
      description: '最新预约订单通知',
      time: Date.now() - 1000 * 60 * 60 * 24 // 1天前
    },
    {
      icon: '/static/message/order-notice.png',
      title: '订单通知',
      description: '最新订单通知',
      time: Date.now() - 1000 * 60 * 60 * 24 * 3 // 3天前
    }
  ])
</script>

<style lang="scss" scoped>
  .message-item {
    display: flex;
    align-items: flex-start;
    padding: 20rpx;
    // background-color: #fff;
    // border-radius: 12rpx;
    // margin-bottom: 20rpx;
    cursor: pointer;

    &.official {
      .message-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .message-content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .message-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10rpx;

          .title {
            font-size: 32rpx;
            color: #0d0e0f;
            font-weight: 500;
          }

          .time {
            font-size: 24rpx;
            color: #999;
          }
        }

        .desc {
          font-size: 26rpx;
          color: #999;
        }
      }
    }
  }
</style>
