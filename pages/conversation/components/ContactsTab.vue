<template>
  <view>
    <view class="contacts-empty" v-if="!contactsList.length">
      <image src="/static/empty-contacts.png" class="empty-icon"></image>
      <text class="empty-text">暂无联系人</text>
    </view>

    <view v-else>
      <!-- 索引列表 -->
      <view class="contact-list">
        <view class="index-group" v-for="(group, index) in contactsGroups" :key="index">
          <view class="index-title">{{ group.letter }}</view>
          <view
            class="contact-item"
            v-for="contact in group.contacts"
            :key="contact.id"
            @tap="handleContactClick(contact)"
          >
            <image :src="contact.avatar || '/static/default-avatar.png'" class="avatar"></image>
            <view class="info">
              <text class="name">{{ contact.name }}</text>
              <text class="status" v-if="contact.status">{{ contact.status }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 右侧索引栏 -->
      <view class="index-bar">
        <text
          class="index-item"
          v-for="letter in indexLetters"
          :key="letter"
          @tap="scrollToLetter(letter)"
        >
          {{ letter }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'

  // 联系人列表
  const contactsList = reactive([
    // 这里可以添加示例联系人数据
    // 实际应用中应该从API获取
  ])

  // 索引字母
  const indexLetters = [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    '#'
  ]

  // 按字母分组的联系人
  const contactsGroups = computed(() => {
    const groups = []

    // 按首字母分组
    indexLetters.forEach(letter => {
      const contacts = contactsList.filter(contact => {
        const firstChar = contact.name.charAt(0).toUpperCase()
        if (letter === '#') {
          // 非字母开头的联系人归入#组
          return !/^[A-Za-z]/.test(firstChar)
        }
        return firstChar === letter
      })

      if (contacts.length > 0) {
        groups.push({
          letter,
          contacts
        })
      }
    })

    return groups
  })

  // 处理点击联系人
  const handleContactClick = contact => {
    uni.navigateTo({
      url: `/pages/chat/index?channelID=${contact.id}&channelType=1&nickName=${contact.name}`
    })
  }

  // 滚动到指定字母的联系人区域
  const scrollToLetter = letter => {
    // 实现滚动到指定字母区域的逻辑
    uni.showToast({
      title: letter,
      icon: 'none',
      duration: 500
    })
  }

  // 加载联系人列表
  const loadContacts = async () => {
    // TODO: 实现加载联系人列表的逻辑
  }

  defineExpose({
    loadContacts
  })
</script>

<style lang="scss" scoped>
  .contacts-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;

    .empty-icon {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .contact-list {
    padding-bottom: 30rpx;

    .index-group {
      .index-title {
        padding: 10rpx 30rpx;
        font-size: 24rpx;
        color: #999;
        background-color: #f5f5f5;
      }

      .contact-item {
        display: flex;
        align-items: center;
        padding: 20rpx 30rpx;
        background-color: #fff;
        border-bottom: 1px solid #eee;

        .avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .info {
          flex: 1;

          .name {
            font-size: 32rpx;
            color: #333;
          }

          .status {
            font-size: 24rpx;
            color: #999;
            margin-top: 4rpx;
          }
        }
      }
    }
  }

  .index-bar {
    position: fixed;
    right: 10rpx;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;

    .index-item {
      padding: 6rpx;
      font-size: 24rpx;
      color: #007aff;
      text-align: center;
    }
  }
</style>
