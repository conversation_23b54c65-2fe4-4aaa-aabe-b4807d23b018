<template>
  <view>
    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <image src="/static/message/search.png" class="search-icon"></image>
        <input
          type="text"
          placeholder="搜索社群"
          v-model="searchKeyword"
          @input="handleSearch"
          class="search-input"
        />
      </view>
      <image
        src="/static/message/join.png"
        class="add-icon"
        @tap="handleAddCommunity"
        mode="aspectFill"
      />
    </view>

    <uni-swipe-action ref="swipeActionRef">
      <uni-swipe-action-item
        v-for="(item, index) in filteredCommunityList"
        :key="index"
        :show-option="false"
        :right-options="getSwipeOptions(item)"
        @click="e => handleSwipeClick(e, item)"
        :ref="el => setSwipeItemRef(el, item.channelId)"
      >
        <view class="message-item" @tap="handleCommunityClick(item)">
          <view class="avatar-container">
            <image
              :src="item.groupImg || item.groupRes?.groupImg || '/static/images/default-group.png'"
              class="avatar"
              mode="aspectFill"
            />
          </view>
          <view class="message-content">
            <view class="message-header">
              <text class="name"
                >{{ item.groupName || item.groupRes?.groupName || '未命名群组' }}
                <text class="member-count"
                  >({{ item.memberCount || item.groupRes?.memberCount || 0 }}人)</text
                ></text
              >
              <text class="time">{{ formatTime(item.lastMessageTime) }}</text>
            </view>
            <view class="message-footer">
              <text class="message">{{ formatMessage(item.lastMessage) }}</text>
              <view class="badge" v-if="getUnreadCount(item) > 0">
                {{ getUnreadCount(item) > 99 ? '99+' : getUnreadCount(item) }}
              </view>
              <view class="status" :class="getStatusClass(item)" v-if="getApplyStatus(item) !== 1">
                {{ getStatusText(getApplyStatus(item)) }}
              </view>
            </view>
          </view>
        </view>
      </uni-swipe-action-item>
    </uni-swipe-action>

    <view v-if="filteredCommunityList.length === 0" class="empty-tip">
      <text>暂无社群</text>
    </view>
  </view>
</template>

<script setup>
  import { computed, onMounted, ref, nextTick } from 'vue'
  import { groupsApi } from '../../../common/api'
  import { onLoad, onShow, onReady, onUnload } from '@dcloudio/uni-app'
  import { useGroupStore } from '@/stores/group'
  import { formatListTime } from '@/utils/date'

  // 导入 uni-swipe-action 组件
  import uniSwipeAction from '@/uni_modules/uni-swipe-action/components/uni-swipe-action/uni-swipe-action.vue'
  import uniSwipeActionItem from '@/uni_modules/uni-swipe-action/components/uni-swipe-action-item/uni-swipe-action-item.vue'

  // 搜索关键词
  const searchKeyword = ref('')

  // 标记是否正在处理滑动操作
  const isHandlingSwipe = ref(false)

  // swipe-action 实例引用
  const swipeActionRef = ref(null)
  // 存储每个 swipe-action-item 的引用
  const swipeItemRefs = ref({})

  // 设置 swipe-action-item 引用
  const setSwipeItemRef = (el, id) => {
    if (el) {
      swipeItemRefs.value[id] = el
    }
  }

  // 社群列表
  const communityList = computed(() => {
    const groupStore = useGroupStore()
    const list = groupStore.getGroupHistoryList
    console.log('社群列表数据:', list)
    return list || []
  })

  // 过滤后的社群列表
  const filteredCommunityList = computed(() => {
    if (!searchKeyword.value) {
      return communityList.value
    }

    return communityList.value.filter(item => {
      const groupName = item.groupName || item.groupRes?.groupName || ''
      return groupName.toLowerCase().includes(searchKeyword.value.toLowerCase())
    })
  })

  // 处理搜索
  const handleSearch = () => {
    // 实时搜索，无需额外处理，依赖computed属性自动过滤
  }

  // 获取未读消息数量
  const getUnreadCount = item => {
    // 优先使用unread字段
    if (item.unread !== undefined) {
      return item.unread
    }

    // 如果有原始群组数据，尝试从中获取
    if (item.groupRes && item.groupRes.unread !== undefined) {
      return item.groupRes.unread
    }

    return 0
  }

  // 格式化时间
  const formatTime = timestamp => {
    if (!timestamp) return ''
    return formatListTime(timestamp)
  }

  // 获取最后一条消息内容
  // 格式化最后一条消息
  const formatMessage = message => {
    console.log('message--->', message)
    if (!message) return ''

    // 获取发送者信息
    let senderName = ''
    if (message.fromUID) {
      const groupStore = useGroupStore()
      const groupCode = communityList.value.find(
        item => item.lastMessage && item.lastMessage.messageID === message.messageID
      )?.groupCode

      if (groupCode) {
        const memberInfo = groupStore.getMemberByGroupAndUid(groupCode, message.fromUID)
        if (memberInfo) {
          senderName = memberInfo.name + ': '
        }
      }
    }

    let content = ''
    switch (message.type) {
      case 'text':
        content = message.content
        break
      case 'image':
        content = '[图片]'
        break
      case 'voice':
        content = '[语音]'
        break
      case 'location':
        content = '[位置]'
        break
      default:
        content = '[未知消息类型]'
    }

    return senderName + content
  }

  // 获取申请状态
  const getApplyStatus = item => {
    if (item.applyStatus !== undefined) {
      return item.applyStatus
    }

    if (item.groupRes && item.groupRes.applyStatus !== undefined) {
      return item.groupRes.applyStatus
    }

    return 1 // 默认为正常状态
  }

  // 获取状态样式类
  const getStatusClass = item => {
    const status = getApplyStatus(item)
    if (status === 2) return 'reviewing'
    if (status === 3) return 'rejected'
    return ''
  }

  // 根据会话状态获取滑动操作按钮配置
  const getSwipeOptions = item => {
    return [
      // {
      //   text: '标记已读',
      //   style: {
      //     backgroundColor: '#007aff'
      //   }
      // },
      // {
      //   text: '删除',
      //   style: {
      //     backgroundColor: '#ff3b30'
      //   }
      // }
    ]
  }

  // 处理左滑操作点击
  const handleSwipeClick = async (e, item) => {
    const { position, content, index } = e
    if (position !== 'right') return

    // 设置标记，防止触发列表点击
    isHandlingSwipe.value = true

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation()

    switch (index) {
      case 0: // 已读
        markAsRead(item)
        break
      case 1: // 删除
        deleteCommunity(item)
        break
    }

    // 手动关闭滑动操作菜单
    closeSwipe(item.channelId)

    // 延迟重置标记，确保列表点击事件不会被触发
    setTimeout(() => {
      isHandlingSwipe.value = false
    }, 300)
  }

  // 关闭指定的滑动操作菜单
  const closeSwipe = channelId => {
    nextTick(() => {
      // 关闭所有滑动操作菜单
      if (swipeActionRef.value) {
        swipeActionRef.value.closeAll()
      }

      // 如果有特定项的引用，也尝试关闭它
      if (swipeItemRefs.value[channelId]) {
        swipeItemRefs.value[channelId].close()
      }
    })
  }

  // 标记为已读
  const markAsRead = item => {
    const unreadCount = getUnreadCount(item)
    if (unreadCount > 0) {
      const groupStore = useGroupStore()
      groupStore.clearGroupUnread(item.channelId)
      uni.showToast({
        title: '已标记为已读',
        icon: 'none'
      })
    }
  }

  // 删除社群
  const deleteCommunity = item => {
    uni.showModal({
      title: '确认删除',
      content: `确定要删除"${item.groupName || item.groupRes?.groupName || '未命名群组'}"社群吗？`,
      success: async res => {
        if (res.confirm) {
          const groupStore = useGroupStore()
          const success = groupStore.removeGroup(item.channelId)
          if (success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        }
      }
    })
  }

  // 社群相关方法
  const loadCommunityList = async () => {
    try {
      // 尝试从WuKongIM获取群组历史
      const groupStore = useGroupStore()

      // 如果已有数据，不需要重新加载
      if (groupStore.getGroupHistoryList.length > 0) {
        console.log('已有社群列表数据，无需重新加载')
        return
      }

      console.log('开始加载社群列表...')
      // 从API获取群组列表
      const groupRes = await groupsApi.getGroups({})
      console.log('获取到群组列表:', groupRes)

      if (groupRes && Array.isArray(groupRes)) {
        // 格式化群组数据
        const formattedGroups = groupRes.map(group => ({
          channel: { channelID: group.channelId, channelType: '2' },
          groupName: group.groupName,
          groupImg:
            group.groupImg && group.groupImg.includes('http')
              ? group.groupImg
              : group.groupImg
                ? 'http://47.123.3.183:9000/' + group.groupImg
                : '/static/images/default-group.png',
          groupCode: group.groupCode,
          channelId: group.channelId,
          memberCount: group.memberCount,
          applyStatus: group.applyStatus,
          lastMessage: group.lastMsg || '',
          lastMessageTime: group.lastMsgTime ? new Date(group.lastMsgTime).getTime() : Date.now(),
          lastSpeaker: group.ownerUserName || '',
          timestamp: new Date(group.enterDate || Date.now()).getTime(),
          unread: group.unread || 0,
          groupRes: group
        }))

        // 更新Store
        groupStore.setGroupHistoryList(formattedGroups)
        console.log('社群列表已更新:', formattedGroups)
      }
    } catch (error) {
      console.error('加载社群列表失败:', error)
    }
  }

  const handleCommunityClick = item => {
    // 如果正在处理滑动操作，则不触发点击事件
    if (isHandlingSwipe.value) {
      return
    }

    const applyStatus = getApplyStatus(item)

    // 处理不同的申请状态
    if (applyStatus === 2) {
      uni.showToast({
        title: '社群正在审核中',
        icon: 'none'
      })
      return
    }
    if (applyStatus === 3) {
      uni.showToast({
        title: '社群审核未通过',
        icon: 'none'
      })
      return
    }

    // 获取必要参数
    const groupName = item.groupName || item.groupRes?.groupName || ''
    const groupImg = item.groupImg || item.groupRes?.groupImg || ''
    const groupCode = item.groupCode || item.groupRes?.groupCode || ''
    const channelId = item.channelId || item.channel?.channelID || ''
    const memberCount = item.memberCount || item.groupRes?.memberCount || 0

    // 如果未申请，打开加入社群页面
    if (applyStatus === 0) {
      uni.navigateTo({
        url: `/pages/community/join?groupCode=${groupCode}&groupName=${groupName}&groupImg=${groupImg}&memberCount=${memberCount}&channelId=${channelId}`
      })
      return
    }

    // 正常状态(applyStatus === 1)，进入聊天页面
    uni.navigateTo({
      url: `/pages/chat/index?channelID=${channelId}&channelType=2&nickName=${groupName}&groupCode=${groupCode}&memberCount=${memberCount}&groupImg=${groupImg}`
    })
  }

  const handleAddCommunity = () => {
    uni.navigateTo({
      url: '/pages/community/join-channel'
    })
  }

  const getStatusText = status => {
    const statusMap = {
      0: '未申请',
      1: '正常',
      2: '审核中',
      3: '审核失败'
    }
    return statusMap[status] || ''
  }

  onLoad(params => {
    loadCommunityList()
  })

  // 每次显示页面时刷新数据
  onShow(() => {
    loadCommunityList()
  })

  defineExpose({
    loadCommunityList
  })
</script>

<style lang="scss" scoped>
  // 搜索框样式
  .search-container {
    display: flex;
    align-items: center;
    padding: 0 20rpx 20rpx 20rpx;
    // background-color: #fff;

    .search-box {
      flex: 1;
      display: flex;
      align-items: center;
      height: 70rpx;
      background-color: #fff;
      border-radius: 35rpx;
      padding: 0 20rpx;
      margin-right: 20rpx;

      .search-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }

      .search-input {
        flex: 1;
        height: 70rpx;
        font-size: 28rpx;
        color: #333;
      }
    }

    .add-icon {
      width: 60rpx;
      height: 60rpx;
    }
  }

  :deep(.uni-swipe-action__container) {
    position: relative;
    width: 100%;
  }

  :deep(.uni-swipe-action--btn) {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100% !important;
    padding: 0 30rpx;
  }

  .message-item {
    display: flex;
    align-items: flex-start;
    padding: 20rpx 30rpx;
    width: 100%;
    box-sizing: border-box;
    height: 100%;

    .avatar-container {
      position: relative;
      margin-right: 20rpx;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }
    }

    .message-content {
      flex: 1;
      padding-bottom: 20rpx;
      // border-bottom: 1rpx solid #eee;

      .message-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10rpx;

        .name {
          font-size: 32rpx;
          color: #0d0e0f;
          font-weight: 500;
        }

        .member-count {
          font-size: 24rpx;
          color: #999;
          font-weight: normal;
        }

        .time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .message-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .message {
          flex: 1;
          font-size: 28rpx;
          color: #666;
          @include text-ellipsis;
        }

        .badge {
          min-width: 36rpx;
          height: 36rpx;
          padding: 0 8rpx;
          background-color: #ff3b30;
          color: #fff;
          font-size: 24rpx;
          border-radius: 18rpx;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .status {
          padding: 4rpx 12rpx;
          font-size: 24rpx;
          border-radius: 4rpx;
          margin-left: 10rpx;

          &.reviewing {
            color: #ff9900;
            background-color: rgba(255, 153, 0, 0.1);
          }

          &.rejected {
            color: #ff3b30;
            background-color: rgba(255, 59, 48, 0.1);
          }
        }
      }
    }
  }

  .empty-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200rpx;
    color: #999;
    font-size: 28rpx;
  }
</style>
