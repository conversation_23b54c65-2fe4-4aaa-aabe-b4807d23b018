<template>
  <view>
    <uni-swipe-action ref="swipeActionRef">
      <uni-swipe-action-item
        v-for="conversation in conversations"
        :key="conversation.channel.channelID"
        :show-option="false"
        :right-options="getSwipeOptions(conversation)"
        @click="e => handleSwipeClick(e, conversation)"
        :ref="el => setSwipeItemRef(el, conversation.channel.channelID)"
      >
        <view
          class="message-item"
          :class="{ pinned: conversation.isPinned }"
          @click.stop="handleConversationClick(conversation)"
        >
          <view class="avatar-container">
            <image
              :src="conversation.channel.avatar || '/static/avatar1.png'"
              class="avatar"
            ></image>
            <view
              class="status-indicator"
              :class="{
                online: conversation.channel.orgData && conversation.channel.orgData.isOnline === 1
              }"
            ></view>
          </view>
          <view class="message-content">
            <view class="message-header">
              <text class="name">{{ conversation.channel.title }}</text>
              <text class="time">{{ formatListTime(conversation.lastMessage.timestamp) }}</text>
            </view>
            <view class="message-footer">
              <text class="message">{{ formatMessage(conversation.lastMessage) }}</text>
              <view class="badge" v-if="conversation.unread > 0">
                {{ conversation.unread > 99 ? '99+' : conversation.unread }}
              </view>
            </view>
          </view>
        </view>
      </uni-swipe-action-item>
    </uni-swipe-action>
  </view>
</template>

<script setup>
  import { computed, ref, nextTick } from 'vue'
  import { formatListTime } from '@/utils/date'
  import { useConversationStore } from '@/stores/conversation'
  import { useUserStore } from '@/stores/user'
  import { messageApi } from '@/common/api'
  import { storeToRefs } from 'pinia'

  // 导入 uni-swipe-action 组件
  import uniSwipeAction from '@/uni_modules/uni-swipe-action/components/uni-swipe-action/uni-swipe-action.vue'
  import uniSwipeActionItem from '@/uni_modules/uni-swipe-action/components/uni-swipe-action-item/uni-swipe-action-item.vue'
  const userStore = useUserStore()
  const conversationStore = useConversationStore()
  const { userInfo } = storeToRefs(userStore)

  // 标记是否正在处理滑动操作
  const isHandlingSwipe = ref(false)

  // swipe-action 实例引用
  const swipeActionRef = ref(null)
  // 存储每个 swipe-action-item 的引用
  const swipeItemRefs = ref({})

  // 设置 swipe-action-item 引用
  const setSwipeItemRef = (el, id) => {
    if (el) {
      swipeItemRefs.value[id] = el
    }
  }

  // 根据会话状态获取滑动操作按钮配置
  const getSwipeOptions = conversation => {
    return [
      {
        text: '标记已读',
        style: {
          backgroundColor: '#007aff'
        }
      },
      {
        text: conversation.isPinned ? '取消置顶' : '置顶聊天',
        style: {
          backgroundColor: '#ff9500'
        }
      },
      {
        text: '删除',
        style: {
          backgroundColor: '#ff3b30'
        }
      }
    ]
  }

  // 使用全局会话列表
  const conversations = computed(() => {
    return conversationStore.conversationList
  })

  // 格式化最后一条消息
  const formatMessage = message => {
    if (!message) return ''

    switch (message.type) {
      case 'text':
        return message.content
      case 'image':
        return '[图片]'
      case 'voice':
        return '[语音]'
      case 'location':
        return '[位置]'
      default:
        return '[未知消息类型]'
    }
  }

  // 处理左滑操作点击
  const handleSwipeClick = async (e, conversation) => {
    const { position, content, index } = e
    if (position !== 'right') return

    // 设置标记，防止触发列表点击
    isHandlingSwipe.value = true

    // 阻止事件冒泡
    e.stopPropagation && e.stopPropagation()

    switch (index) {
      case 0: // 已读
        markAsRead(conversation)
        break
      case 1: // 置顶/取消置顶
        await togglePin(conversation)
        break
      case 2: // 删除
        deleteConversation(conversation)
        break
    }

    // 手动关闭滑动操作菜单
    closeSwipe(conversation.channel.channelID)

    // 延迟重置标记，确保列表点击事件不会被触发
    setTimeout(() => {
      isHandlingSwipe.value = false
    }, 300)
  }

  // 关闭指定的滑动操作菜单
  const closeSwipe = channelID => {
    nextTick(() => {
      // 关闭所有滑动操作菜单
      if (swipeActionRef.value) {
        swipeActionRef.value.closeAll()
      }

      // 如果有特定项的引用，也尝试关闭它
      if (swipeItemRefs.value[channelID]) {
        swipeItemRefs.value[channelID].close()
      }
    })
  }

  // 标记为已读
  const markAsRead = conversation => {
    if (conversation.unread > 0) {
      conversationStore.clearUnread(
        conversation.channel.channelID,
        conversation.channel.channelType
      )
      uni.showToast({
        title: '已标记为已读',
        icon: 'none'
      })
    }
  }

  // 置顶/取消置顶会话
  const togglePin = async conversation => {
    const isPinned = await conversationStore.togglePin(
      conversation.channel.channelID,
      conversation.channel.channelType
    )
    uni.showToast({
      title: isPinned ? '已置顶' : '已取消置顶',
      icon: 'none'
    })
  }

  // 删除会话
  const deleteConversation = conversation => {
    uni.showModal({
      title: '确认删除',
      content: `确定要删除与"${conversation.channel.title}"的会话吗？`,
      success: async res => {
        if (res.confirm) {
          const success = conversationStore.deleteConversation(
            conversation.channel.channelID,
            conversation.channel.channelType
          )
          await messageApi.deleteConversation({
            uid: userInfo.value.userId,
            channel_id: conversation.channel.channelID,
            channel_type: 1
          })
          if (success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        }
      }
    })
  }

  // 点击会话
  const handleConversationClick = conversation => {
    // 如果正在处理滑动操作，则不触发点击事件
    if (isHandlingSwipe.value) {
      return
    }

    // 存储当前会话数据
    conversationStore.setCurrentConversation(conversation)
    // 设置历史对话列表
    conversationStore.setConversationHistory(conversation.recents || [])
    // 跳转到聊天页面
    uni.navigateTo({
      url: `/pages/chat/index?channelID=${conversation.channel.channelID}&channelType=${conversation.channel.channelType}&nickName=${conversation.channel.title}`
    })
  }

  defineExpose({
    handleConversationClick
  })
</script>

<style lang="scss" scoped>
  .scroll-container {
    padding: 0 !important;
  }
  :deep(.uni-swipe-action__container) {
    position: relative;
    width: 100%;
  }

  :deep(.uni-swipe-action--btn) {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100% !important;
    padding: 0 30rpx;
  }
  :deep(.button-group--right) {
    // bottom: 20rpx;
  }

  .custom-swipe-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 30rpx;
    height: 100%;

    .swipe-icon {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 10rpx;
    }

    .swipe-text {
      font-size: 24rpx;
      color: #fff;
    }
  }

  .message-item {
    display: flex;
    align-items: flex-start;
    padding: 20rpx 0 0 30rpx;
    // background-color: #fff;
    width: 100%;
    box-sizing: border-box;
    height: 100%;

    // 添加置顶消息的样式
    &.pinned {
      background-color: rgb(231 250 236); // 淡橙色背景
      position: relative;

      // &::before {
      //   content: '';
      //   position: absolute;
      //   left: 0;
      //   top: 0;
      //   bottom: 0;
      //   width: 6rpx;
      //   background-color: #ff9500; // 橙色左侧边框
      // }
    }

    .avatar-container {
      position: relative;
      margin-right: 20rpx;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }

      .status-indicator {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        background-color: #ccc;
        position: absolute;
        top: 4rpx;
        right: 4rpx;
        border: 2rpx solid #fff;

        &.online {
          background-color: #07c160;
        }
      }
    }

    .message-content {
      flex: 1;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #eee;
      .message-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10rpx;

        .name {
          font-size: 32rpx;
          color: #0d0e0f;
          font-weight: 500;
        }

        .time {
          font-size: 24rpx;
          color: #999;
          margin-right: 30rpx;
        }
      }

      .message-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .message {
          flex: 1;
          font-size: 28rpx;
          color: #666;
          @include text-ellipsis;
        }

        .badge {
          min-width: 36rpx;
          height: 36rpx;
          padding: 0 8rpx;
          background-color: #ff3b30;
          color: #fff;
          font-size: 24rpx;
          border-radius: 18rpx;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 30rpx;
        }
      }
    }
  }
</style>
