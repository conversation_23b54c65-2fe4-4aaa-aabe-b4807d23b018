<template>
	<view class="container">
		<!-- 话题信息 -->
		<view class="topic-header">
			<view class="topic-info">
				<image 
					class="cover" 
					:src="topic.cover || '/static/images/topic-default.png'" 
					mode="aspectFill"
				></image>
				<view class="info-content">
					<text class="name">#{{topic.name}}#</text>
					<view class="stats">
						<text class="stat-item">{{formatNumber(topic.discussCount)}}讨论</text>
						<text class="stat-item">{{formatNumber(topic.viewCount)}}浏览</text>
					</view>
				</view>
				<button 
					class="follow-btn" 
					:class="{active: topic.isFollowing}"
					@tap="toggleFollow"
				>{{topic.isFollowing ? '已关注' : '关注'}}</button>
			</view>
			<text class="description">{{topic.description}}</text>
		</view>
		
		<!-- 帖子筛选 -->
		<view class="filter-bar">
			<view class="sort-tabs">
				<text 
					class="tab-item"
					:class="{active: sortType === 'hot'}"
					@tap="changeSort('hot')"
				>热门</text>
				<text 
					class="tab-item"
					:class="{active: sortType === 'new'}"
					@tap="changeSort('new')"
				>最新</text>
			</view>
		</view>
		
		<!-- 帖子列表 -->
		<view class="post-list">
			<view 
				class="post-item"
				v-for="post in posts"
				:key="post.id"
				@tap="navigateToPost(post)"
			>
				<!-- 用户信息 -->
				<view class="user-info">
					<image class="avatar" :src="post.user.avatar" mode="aspectFill"></image>
					<view class="user-detail">
						<text class="username">{{post.user.nickname}}</text>
						<text class="time">{{formatTime(post.createTime)}}</text>
					</view>
				</view>
				
				<!-- 帖子内容 -->
				<text class="content">{{post.content}}</text>
				
				<!-- 图片列表 -->
				<view class="image-list" v-if="post.images && post.images.length > 0">
					<image 
						v-for="(image, index) in post.images.slice(0, 3)"
						:key="index"
						:src="image"
						mode="aspectFill"
						:class="['post-image', `count-${post.images.length}`]"
					></image>
					<view class="image-count" v-if="post.images.length > 3">
						+{{post.images.length - 3}}
					</view>
				</view>
				
				<!-- 互动数据 -->
				<view class="interaction-bar">
					<view class="interaction-item">
						<text class="iconfont icon-like"></text>
						<text class="count">{{formatNumber(post.likeCount)}}</text>
					</view>
					<view class="interaction-item">
						<text class="iconfont icon-comment"></text>
						<text class="count">{{formatNumber(post.commentCount)}}</text>
					</view>
					<view class="interaction-item">
						<text class="iconfont icon-share"></text>
						<text class="count">{{formatNumber(post.shareCount)}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<uni-load-more :status="loadMoreStatus"></uni-load-more>
		
		<!-- 发帖按钮 -->
		<view class="post-btn" @tap="createPost">
			<text class="iconfont icon-edit"></text>
			<text>发帖</text>
		</view>
	</view>
</template>

<script>
import { ref, onMounted } from 'vue'
import { formatTime } from '@/utils/format'

export default {
	setup() {
		const topicId = ref('')
		const topic = ref({
			name: '',
			cover: '',
			description: '',
			discussCount: 0,
			viewCount: 0,
			isFollowing: false
		})
		const sortType = ref('hot')
		const posts = ref([])
		const page = ref(1)
		const loadMoreStatus = ref('more')
		const isLoading = ref(false)

		// 格式化数字
		const formatNumber = (num) => {
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w'
			}
			return num
		}

		// 加载话题详情
		const loadTopicDetail = async () => {
			try {
				const topicData = await this.$api.getTopicDetail(topicId.value)
				topic.value = topicData
			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			}
		}

		// 加载帖子列表
		const loadPosts = async () => {
			if (isLoading.value) return

			isLoading.value = true
			loadMoreStatus.value = 'loading'

			try {
				const { list, total } = await this.$api.getTopicPosts({
					topicId: topicId.value,
					page: page.value,
					sortType: sortType.value
				})

				if (page.value === 1) {
					posts.value = list
				} else {
					posts.value = [...posts.value, ...list]
				}

				loadMoreStatus.value = posts.value.length >= total ? 'noMore' : 'more'
				page.value++
			} catch (error) {
				loadMoreStatus.value = 'more'
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				isLoading.value = false
			}
		}

		// 切换排序方式
		const changeSortType = (type) => {
			if (sortType.value === type) return

			sortType.value = type
			page.value = 1
			loadPosts()
		}

		// 关注/取消关注话题
		const toggleFollow = async () => {
			try {
				if (topic.value.isFollowing) {
					await this.$api.unfollowTopic(topicId.value)
				} else {
					await this.$api.followTopic(topicId.value)
				}
				topic.value.isFollowing = !topic.value.isFollowing
			} catch (error) {
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				})
			}
		}

		// 跳转到发布页面
		const navigateToPublish = () => {
			uni.navigateTo({
				url: `/pages/publish/publish?topicId=${topicId.value}&topicName=${topic.value.name}`
			})
		}

		// 跳转到帖子详情
		const navigateToPostDetail = (post) => {
			uni.navigateTo({
				url: `/pages/post/detail?id=${post.id}`
			})
		}

		onMounted(() => {
			loadTopicDetail()
			loadPosts()
		})

		return {
			topic,
			sortType,
			posts,
			loadMoreStatus,
			formatTime,
			formatNumber,
			changeSortType,
			toggleFollow,
			navigateToPublish,
			navigateToPostDetail,
			loadTopicDetail,
			loadPosts,
			page,
			topicId
		}
	},

	onLoad(options) {
		this.topicId = options.id
		this.loadTopicDetail()
		this.loadPosts()
	},

	onPullDownRefresh() {
		this.page = 1
		Promise.all([
			this.loadTopicDetail(),
			this.loadPosts()
		]).then(() => {
			uni.stopPullDownRefresh()
		})
	},

	onReachBottom() {
		if (this.loadMoreStatus === 'more') {
			this.loadPosts()
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f8f8f8;
	padding-bottom: 120rpx;
}

.topic-header {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
	
	.topic-info {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		
		.cover {
			width: 120rpx;
			height: 120rpx;
			border-radius: 12rpx;
			margin-right: 20rpx;
		}
		
		.info-content {
			flex: 1;
			
			.name {
				font-size: 36rpx;
				color: #333;
				font-weight: 600;
				margin-bottom: 10rpx;
			}
			
			.stats {
				display: flex;
				align-items: center;
				
				.stat-item {
					font-size: 26rpx;
					color: #666;
					margin-right: 30rpx;
				}
			}
		}
		
		.follow-btn {
			height: 64rpx;
			line-height: 64rpx;
			padding: 0 40rpx;
			font-size: 28rpx;
			color: $color-primary;
			background: rgba($color-primary, 0.1);
			border-radius: 32rpx;
			
			&.active {
				color: #fff;
				background: $color-primary;
			}
		}
	}
	
	.description {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}
}

.filter-bar {
	background-color: #fff;
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	margin-bottom: 20rpx;
	
	.sort-tabs {
		display: flex;
		align-items: center;
		
		.tab-item {
			font-size: 30rpx;
			color: #666;
			margin-right: 40rpx;
			position: relative;
			
			&.active {
				color: #333;
				font-weight: 500;
				
				&::after {
					content: '';
					position: absolute;
					left: 50%;
					bottom: -20rpx;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background: $color-primary;
					border-radius: 2rpx;
				}
			}
		}
	}
}

.post-list {
	.post-item {
		background-color: #fff;
		padding: 30rpx;
		margin-bottom: 20rpx;
		
		.user-info {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			
			.avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}
			
			.user-detail {
				flex: 1;
				
				.username {
					font-size: 30rpx;
					color: #333;
					font-weight: 500;
				}
				
				.time {
					font-size: 24rpx;
					color: #999;
					margin-top: 4rpx;
				}
			}
		}
		
		.content {
			font-size: 30rpx;
			color: #333;
			line-height: 1.6;
			margin-bottom: 20rpx;
		}
		
		.image-list {
			display: flex;
			margin: 0 -5rpx 20rpx;
			position: relative;
			
			.post-image {
				flex: 1;
				height: 200rpx;
				margin: 0 5rpx;
				border-radius: 8rpx;
			}
			
			.image-count {
				position: absolute;
				right: 5rpx;
				bottom: 0;
				background: rgba(0, 0, 0, 0.5);
				color: #fff;
				font-size: 24rpx;
				padding: 4rpx 12rpx;
				border-radius: 8rpx;
			}
		}
		
		.interaction-bar {
			display: flex;
			justify-content: space-around;
			border-top: 1rpx solid #eee;
			padding-top: 20rpx;
			
			.interaction-item {
				display: flex;
				align-items: center;
				
				.iconfont {
					font-size: 36rpx;
					color: #666;
					margin-right: 8rpx;
				}
				
				.count {
					font-size: 26rpx;
					color: #666;
				}
			}
		}
	}
}

.post-btn {
	position: fixed;
	right: 40rpx;
	bottom: 40rpx;
	display: flex;
	align-items: center;
	height: 88rpx;
	padding: 0 40rpx;
	background: $color-primary;
	color: #fff;
	border-radius: 44rpx;
	box-shadow: 0 4rpx 16rpx rgba($color-primary, 0.3);
	
	.iconfont {
		font-size: 36rpx;
		margin-right: 10rpx;
	}
	
	text {
		font-size: 30rpx;
	}
}
</style> 