<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-box">
			<view class="search-bar">
				<text class="iconfont icon-search"></text>
				<input
					class="search-input"
					v-model="searchKeyword"
					placeholder="搜索话题"
					@confirm="searchTopics"
					confirm-type="search"
				/>
			</view>
		</view>
		
		<!-- 分类标签 -->
		<scroll-view class="category-scroll" scroll-x>
			<view class="category-list">
				<text 
					class="category-item"
					:class="{active: currentCategory === category.id}"
					v-for="category in categories"
					:key="category.id"
					@tap="selectCategory(category)"
				>{{category.name}}</text>
			</view>
		</scroll-view>
		
		<!-- 热门话题 -->
		<view class="section" v-if="currentCategory === 'all'">
			<view class="section-header">
				<text class="title">热门话题</text>
				<text class="more" @tap="navigateToMore('hot')">查看更多</text>
			</view>
			<view class="topic-list">
				<view 
					class="topic-item hot"
					v-for="(topic, index) in hotTopics"
					:key="topic.id"
					@tap="navigateToDetail(topic)"
				>
					<view class="rank-badge" :class="`rank-${index + 1}`">#{{index + 1}}</view>
					<view class="topic-info">
						<view class="topic-main">
							<text class="name">#{{topic.name}}#</text>
							<text class="count">{{formatNumber(topic.discussCount)}}讨论</text>
						</view>
						<text class="desc">{{topic.description}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 全部话题列表 -->
		<view class="topic-list">
			<view 
				class="topic-item"
				v-for="topic in topicList"
				:key="topic.id"
				@tap="navigateToDetail(topic)"
			>
				<image 
					class="cover" 
					:src="topic.cover || '/static/images/topic-default.png'" 
					mode="aspectFill"
				></image>
				<view class="topic-info">
					<view class="topic-main">
						<text class="name">#{{topic.name}}#</text>
						<text class="count">{{formatNumber(topic.discussCount)}}讨论</text>
					</view>
					<text class="desc">{{topic.description}}</text>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<uni-load-more :status="loadMoreStatus"></uni-load-more>
	</view>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
	setup() {
		const searchKeyword = ref('')
		const currentCategory = ref('all')
		const categories = ref([
			{ id: 'all', name: '全部' },
			{ id: 'entertainment', name: '娱乐' },
			{ id: 'life', name: '生活' },
			{ id: 'tech', name: '科技' },
			{ id: 'sports', name: '运动' },
			{ id: 'food', name: '美食' },
			{ id: 'travel', name: '旅行' },
			{ id: 'education', name: '教育' }
		])
		const hotTopics = ref([])
		const topicList = ref([])
		const page = ref(1)
		const loadMoreStatus = ref('more')
		const isLoading = ref(false)

		// 格式化数字
		const formatNumber = (num) => {
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w'
			}
			return num
		}

		// 加载热门话题
		const loadHotTopics = async () => {
			try {
				const { list } = await this.$api.getHotTopics()
				hotTopics.value = list
			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			}
		}

		// 加载话题列表
		const loadTopics = async () => {
			if (isLoading.value) return

			isLoading.value = true
			loadMoreStatus.value = 'loading'

			try {
				const { list, total } = await this.$api.getTopics({
					page: page.value,
					category: currentCategory.value === 'all' ? '' : currentCategory.value,
					keyword: searchKeyword.value
				})

				if (page.value === 1) {
					topicList.value = list
				} else {
					topicList.value = [...topicList.value, ...list]
				}

				loadMoreStatus.value = topicList.value.length >= total ? 'noMore' : 'more'
				page.value++
			} catch (error) {
				loadMoreStatus.value = 'more'
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				isLoading.value = false
			}
		}

		// 选择分类
		const selectCategory = (category) => {
			if (currentCategory.value === category.id) return

			currentCategory.value = category.id
			page.value = 1
			loadTopics()
		}

		// 搜索话题
		const searchTopics = () => {
			page.value = 1
			loadTopics()
		}

		// 跳转到话题详情
		const navigateToDetail = (topic) => {
			uni.navigateTo({
				url: `/pages/topic/detail?id=${topic.id}`
			})
		}

		// 跳转到更多页面
		const navigateToMore = (type) => {
			uni.navigateTo({
				url: `/pages/topic/more?type=${type}`
			})
		}

		onMounted(() => {
			loadHotTopics()
			loadTopics()
		})

		return {
			searchKeyword,
			currentCategory,
			categories,
			hotTopics,
			topicList,
			loadMoreStatus,
			formatNumber,
			selectCategory,
			searchTopics,
			navigateToDetail,
			navigateToMore,
			loadHotTopics,
			loadTopics,
			page
		}
	},

	onPullDownRefresh() {
		this.page = 1
		Promise.all([
			this.loadHotTopics(),
			this.loadTopics()
		]).then(() => {
			uni.stopPullDownRefresh()
		})
	},

	onReachBottom() {
		if (this.loadMoreStatus === 'more') {
			this.loadTopics()
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.search-box {
	background-color: #fff;
	padding: 20rpx 30rpx;
	
	.search-bar {
		display: flex;
		align-items: center;
		height: 72rpx;
		background: #f5f5f5;
		border-radius: 36rpx;
		padding: 0 30rpx;
		
		.iconfont {
			font-size: 32rpx;
			color: #999;
			margin-right: 10rpx;
		}
		
		.search-input {
			flex: 1;
			height: 100%;
			font-size: 28rpx;
		}
	}
}

.category-scroll {
	background-color: #fff;
	white-space: nowrap;
	padding: 0 20rpx;
	border-bottom: 1rpx solid #eee;
	
	.category-list {
		display: inline-block;
		padding: 20rpx 0;
		
		.category-item {
			display: inline-block;
			height: 56rpx;
			line-height: 56rpx;
			padding: 0 30rpx;
			font-size: 28rpx;
			color: #666;
			margin-right: 20rpx;
			border-radius: 28rpx;
			background: #f5f5f5;
			
			&.active {
				color: #fff;
				background: $color-primary;
			}
		}
	}
}

.section {
	background-color: #fff;
	margin-bottom: 20rpx;
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
		
		.title {
			font-size: 32rpx;
			color: #333;
			font-weight: 500;
		}
		
		.more {
			font-size: 26rpx;
			color: #999;
		}
	}
}

.topic-list {
	background-color: #fff;
	
	.topic-item {
		display: flex;
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
		
		&.hot {
			padding-left: 90rpx;
			position: relative;
			
			.rank-badge {
				position: absolute;
				left: 30rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 40rpx;
				height: 40rpx;
				line-height: 40rpx;
				text-align: center;
				font-size: 24rpx;
				color: #fff;
				background: #999;
				border-radius: 6rpx;
				
				&.rank-1 {
					background: #ff6b6b;
				}
				
				&.rank-2 {
					background: #ff8f6b;
				}
				
				&.rank-3 {
					background: #ffb36b;
				}
			}
		}
		
		.cover {
			width: 120rpx;
			height: 120rpx;
			border-radius: 12rpx;
			margin-right: 20rpx;
		}
		
		.topic-info {
			flex: 1;
			
			.topic-main {
				display: flex;
				align-items: center;
				margin-bottom: 10rpx;
				
				.name {
					font-size: 32rpx;
					color: #333;
					font-weight: 500;
					margin-right: 20rpx;
				}
				
				.count {
					font-size: 24rpx;
					color: #999;
				}
			}
			
			.desc {
				font-size: 26rpx;
				color: #666;
				line-height: 1.5;
				@include text-ellipsis(2);
			}
		}
	}
}
</style> 