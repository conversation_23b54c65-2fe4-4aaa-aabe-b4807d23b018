<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-box">
			<view class="search-bar">
				<text class="iconfont icon-search"></text>
				<input
					class="search-input"
					v-model="searchKeyword"
					placeholder="搜索话题"
					@confirm="searchTopics"
					confirm-type="search"
				/>
			</view>
		</view>
		
		<!-- 常用话题 -->
		<view class="section" v-if="!searchKeyword">
			<view class="section-header">
				<text class="title">常用话题</text>
			</view>
			<view class="topic-grid">
				<view 
					class="topic-item"
					v-for="topic in frequentTopics"
					:key="topic.id"
					@tap="selectTopic(topic)"
				>
					<image 
						class="cover" 
						:src="topic.cover || '/static/images/topic-default.png'" 
						mode="aspectFill"
					></image>
					<text class="name">#{{topic.name}}#</text>
					<text class="count">{{formatNumber(topic.discussCount)}}讨论</text>
				</view>
			</view>
		</view>
		
		<!-- 热门话题 -->
		<view class="section" v-if="!searchKeyword">
			<view class="section-header">
				<text class="title">热门话题</text>
			</view>
			<view class="topic-list">
				<view 
					class="topic-item"
					v-for="(topic, index) in hotTopics"
					:key="topic.id"
					@tap="selectTopic(topic)"
				>
					<view class="rank-badge" :class="`rank-${index + 1}`">#{{index + 1}}</view>
					<view class="topic-info">
						<text class="name">#{{topic.name}}#</text>
						<text class="count">{{formatNumber(topic.discussCount)}}讨论</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 搜索结果 -->
		<view class="topic-list search-result" v-else>
			<view 
				class="topic-item"
				v-for="topic in searchResults"
				:key="topic.id"
				@tap="selectTopic(topic)"
			>
				<image 
					class="cover" 
					:src="topic.cover || '/static/images/topic-default.png'" 
					mode="aspectFill"
				></image>
				<view class="topic-info">
					<text class="name">#{{topic.name}}#</text>
					<text class="count">{{formatNumber(topic.discussCount)}}讨论</text>
					<text class="desc">{{topic.description}}</text>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<uni-load-more :status="loadMoreStatus" v-if="searchKeyword"></uni-load-more>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			frequentTopics: [],
			hotTopics: [],
			searchResults: [],
			page: 1,
			loadMoreStatus: 'more',
			isLoading: false
		}
	},
	
	onLoad() {
		this.loadFrequentTopics();
		this.loadHotTopics();
	},
	
	onReachBottom() {
		if (this.searchKeyword && this.loadMoreStatus === 'more') {
			this.searchTopics();
		}
	},
	
	methods: {
		// 格式化数字
		formatNumber(num) {
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + 'w';
			}
			return num;
		},
		
		// 加载常用话题
		async loadFrequentTopics() {
			try {
				const { list } = await this.$api.getFrequentTopics();
				this.frequentTopics = list;
			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			}
		},
		
		// 加载热门话题
		async loadHotTopics() {
			try {
				const { list } = await this.$api.getHotTopics();
				this.hotTopics = list;
			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			}
		},
		
		// 搜索话题
		async searchTopics() {
			if (this.isLoading) return;
			
			this.isLoading = true;
			this.loadMoreStatus = 'loading';
			
			try {
				const { list, total } = await this.$api.searchTopics({
					keyword: this.searchKeyword,
					page: this.page
				});
				
				if (this.page === 1) {
					this.searchResults = list;
				} else {
					this.searchResults = [...this.searchResults, ...list];
				}
				
				this.loadMoreStatus = this.searchResults.length >= total ? 'noMore' : 'more';
				this.page++;
			} catch (error) {
				this.loadMoreStatus = 'more';
				uni.showToast({
					title: '搜索失败',
					icon: 'none'
				});
			} finally {
				this.isLoading = false;
			}
		},
		
		// 选择话题
		selectTopic(topic) {
			const pages = getCurrentPages();
			const prevPage = pages[pages.length - 2];
			
			// 更新上一页的话题数据
			if (prevPage) {
				prevPage.$vm.setTopic({
					id: topic.id,
					name: topic.name
				});
			}
			
			uni.navigateBack();
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.search-box {
	background-color: #fff;
	padding: 20rpx 30rpx;
	
	.search-bar {
		display: flex;
		align-items: center;
		height: 72rpx;
		background: #f5f5f5;
		border-radius: 36rpx;
		padding: 0 30rpx;
		
		.iconfont {
			font-size: 32rpx;
			color: #999;
			margin-right: 10rpx;
		}
		
		.search-input {
			flex: 1;
			height: 100%;
			font-size: 28rpx;
		}
	}
}

.section {
	background-color: #fff;
	margin-top: 20rpx;
	
	.section-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
		
		.title {
			font-size: 32rpx;
			color: #333;
			font-weight: 500;
		}
	}
}

.topic-grid {
	display: flex;
	flex-wrap: wrap;
	padding: 20rpx;
	
	.topic-item {
		width: 33.33%;
		padding: 10rpx;
		text-align: center;
		
		.cover {
			width: 120rpx;
			height: 120rpx;
			border-radius: 12rpx;
			margin-bottom: 10rpx;
		}
		
		.name {
			font-size: 26rpx;
			color: #333;
			margin-bottom: 4rpx;
		}
		
		.count {
			font-size: 22rpx;
			color: #999;
		}
	}
}

.topic-list {
	background-color: #fff;
	
	&.search-result {
		margin-top: 20rpx;
	}
	
	.topic-item {
		display: flex;
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
		
		.rank-badge {
			width: 40rpx;
			height: 40rpx;
			line-height: 40rpx;
			text-align: center;
			font-size: 24rpx;
			color: #fff;
			background: #999;
			border-radius: 6rpx;
			margin-right: 20rpx;
			
			&.rank-1 {
				background: #ff6b6b;
			}
			
			&.rank-2 {
				background: #ff8f6b;
			}
			
			&.rank-3 {
				background: #ffb36b;
			}
		}
		
		.cover {
			width: 100rpx;
			height: 100rpx;
			border-radius: 12rpx;
			margin-right: 20rpx;
		}
		
		.topic-info {
			flex: 1;
			
			.name {
				font-size: 30rpx;
				color: #333;
				font-weight: 500;
				margin-bottom: 8rpx;
			}
			
			.count {
				font-size: 24rpx;
				color: #999;
				margin-bottom: 8rpx;
			}
			
			.desc {
				font-size: 26rpx;
				color: #666;
				line-height: 1.5;
				@include text-ellipsis(2);
			}
		}
	}
}
</style> 