<template>
	<view class="evaluate-container">
		<view class="evaluate-item" v-for="(item, idx) in list" :key="idx">
			<view class="evaluate-header">
				<image class="avatar" :src="item.avatar" mode="aspectFill" />
				<view class="user-info">
					<view class="user-phone">{{ item.phone }}</view>
					<view class="stars">
						<uni-icons
							v-for="i in 5"
							:key="i"
							:type="i <= item.star ? 'star-filled' : 'star'"
							size="20"
							:color="i <= item.star ? '#FFD700' : '#eee'"
						/>
					</view>
				</view>
				<view class="evaluate-date">发表于{{ item.date }}</view>
			</view>
			<view class="evaluate-content">{{ item.content }}</view>
			<view class="evaluate-imgs" v-if="item.imgs && item.imgs.length">
				<image
					v-for="(img, imgIdx) in item.imgs"
					:key="imgIdx"
					:src="img"
					class="evaluate-img"
					mode="aspectFill"
					@click="previewImg(item.imgs, imgIdx)"
				>
					<view class="img-placeholder">评论图</view>
				</image>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [
					{
						avatar: 'https://via.placeholder.com/60x60?text=头像',
						phone: '189****7055',
						star: 3,
						date: '2024年07月23日',
						content: '用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价',
						imgs: []
					},
					{
						avatar: 'https://via.placeholder.com/60x60?text=头像',
						phone: '189****7055',
						star: 3,
						date: '2024年07月23日',
						content: '用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价',
						imgs: [
							'https://via.placeholder.com/120x120?text=评论图',
							'https://via.placeholder.com/120x120?text=评论图'
						]
					},
					{
						avatar: 'https://via.placeholder.com/60x60?text=头像',
						phone: '189****7055',
						star: 3,
						date: '2024年07月23日',
						content: '用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价用户评价',
						imgs: [
							'https://via.placeholder.com/120x120?text=评论图'
						]
					}
				]
			}
		},
		methods: {
			previewImg(imgs, idx) {
				uni.previewImage({
					urls: imgs,
					current: imgs[idx]
				});
			}
		}
	}
</script>

<style>
	.evaluate-container {
		background: #f5f5f5;
		min-height: 100vh;
		padding: 0 0 20rpx 0;
	}
	.evaluate-item {
		background: #fff;
		border-radius: 12rpx;
		margin: 20rpx 16rpx 0 16rpx;
		padding: 20rpx 20rpx 16rpx 20rpx;
		box-shadow: 0 2rpx 8rpx #f0f0f0;
	}
	.evaluate-header {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	.avatar {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background: #eee;
		margin-right: 16rpx;
	}
	.user-info {
		flex: 1;
	}
	.user-phone {
		font-size: 26rpx;
		color: #333;
		margin-bottom: 2rpx;
	}
	.stars {
		display: flex;
		align-items: center;
	}
	.evaluate-date {
		color: #bbb;
		font-size: 22rpx;
		margin-left: 10rpx;
		white-space: nowrap;
	}
	.evaluate-content {
		color: #666;
		font-size: 26rpx;
		margin-bottom: 10rpx;
		line-height: 1.5;
	}
	.evaluate-imgs {
		display: flex;
		gap: 16rpx;
		margin-top: 8rpx;
	}
	.evaluate-img {
		width: 120rpx;
		height: 120rpx;
		background: #eee;
		border-radius: 8rpx;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
	}
	.img-placeholder {
		position: absolute;
		left: 0; right: 0; top: 0; bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #999;
		font-size: 24rpx;
		background: rgba(255,255,255,0.7);
	}
</style>
