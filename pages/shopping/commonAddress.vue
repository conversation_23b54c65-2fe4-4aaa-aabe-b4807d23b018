<template>
	<view class="container">
		<view class="address-list">
			<view class="address-card" v-for="(item, index) in addressList" :key="index" @click="selectAddress(item)">
				<view class="address-details">
					<view class="address-line-1">
						<text>{{item.province}} {{item.city}} {{item.district}} {{item.street}}</text>
						<text class="default-tag" v-if="item.isDefault">默认</text>
					</view>
					<view class="address-line-2">
						{{item.address}}
					</view>
					<view class="user-info">
						<text>{{item.name}}</text>
						<text>{{item.phone}}</text>
					</view>
				</view>
				<view class="address-actions">
					<view class="set-default">
						<view v-if="item.isDefault" class="default-checked">
							<uni-icons type="checkbox-filled" color="#4CAF50" size="20"></uni-icons>
							<text>已默认</text>
						</view>
						<view v-else class="default-unchecked" @click.stop="setDefault(item.id)">
							<view class="radio-circle"></view>
							<text>设为默认</text>
						</view>
					</view>
					<view class="action-buttons">
						<uni-icons type="compose" size="22" color="#666"></uni-icons>
						<uni-icons type="trash" size="22" color="#666" style="margin-left: 40rpx;"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<view class="add-address-wrapper">
			<view class="add-address-btn">
				<uni-icons type="plus" size="20" color="#fff" style="margin-right: 10rpx;"></uni-icons>
				<text>新增地址</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				addressList: [{
						id: 1,
						province: '江苏省',
						city: '南京市',
						district: '江宁区',
						street: '百家湖街道',
						address: '双龙大道1698号景枫中心写字楼B4收发室',
						name: '张三',
						phone: '18909870987',
						isDefault: true,
					},
					{
						id: 2,
						province: '江苏省',
						city: '南京市',
						district: '江宁区',
						street: '秣林街道',
						address: '双龙大道388号义务小商品28栋3楼301-303、324-328',
						name: '张三',
						phone: '18909870987',
						isDefault: false,
					},
					{
						id: 3,
						province: '江苏省',
						city: '南京市',
						district: '江宁区',
						street: '百家湖街道',
						address: '双龙大道1698号景枫中心写字楼B4收发室',
						name: '张三',
						phone: '18909870987',
						isDefault: false,
					}
				]
			}
		},
		methods: {
			/**
			 * @description 设置为默认地址
			 * @param { number } id
			 */
			setDefault(id) {
				this.addressList.forEach(item => {
					item.isDefault = item.id === id;
				});
			},
			
			/**
			 * @description 选择地址并回填到订单页面
			 * @param {Object} address - 选中的地址对象
			 */
			selectAddress(address) {
				// 构建回填到fillOrder的地址对象
				const selectedAddress = {
					address: address.province + address.city + address.district + address.street + address.address,
					receiverName: address.name,
					contactNumber: address.phone,
					// 添加其他可能需要的地址信息
					provinceName: address.province,
					cityName: address.city,
					areaName: address.district,
					detailAddress: address.address
				};
				
				// 将选中的地址保存到本地存储
				uni.setStorageSync('selectedAddress', selectedAddress);
				
				// 返回上一页
				uni.navigateBack({
					success: () => {
						// 通过事件总线通知fillOrder页面更新地址
						uni.$emit('addressSelected', selectedAddress);
					}
				});
			}
		}
	}
</script>

<style>
	.container {
		background-color: #f8f8f8;
		min-height: 100vh;
		padding-bottom: 180rpx;
	}

	.address-list {
		padding: 20rpx;
	}

	.address-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}

	.address-details {
		border-bottom: 1rpx solid #f5f5f5;
		padding-bottom: 20rpx;
		margin-bottom: 20rpx;
	}

	.address-line-1 {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 12rpx;
	}
	
	.default-tag {
		color: #4CAF50;
		margin-left: 10rpx;
		font-weight: bold;
	}

	.address-line-2 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 12rpx;
	}

	.user-info text {
		font-size: 28rpx;
		color: #333;
	}

	.user-info text:last-child {
		margin-left: 20rpx;
	}

	.address-actions {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.set-default {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #666;
	}

	.default-checked {
		display: flex;
		align-items: center;
		color: #4CAF50;
	}

	.default-checked text {
		margin-left: 10rpx;
	}

	.default-unchecked {
		display: flex;
		align-items: center;
	}

	.radio-circle {
		width: 36rpx;
		height: 36rpx;
		border: 1rpx solid #ccc;
		border-radius: 50%;
		margin-right: 10rpx;
	}

	.action-buttons {
		display: flex;
		align-items: center;
	}
	
	.add-address-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #f8f8f8;
		padding: 20rpx 40rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	}

	.add-address-btn {
		background-color: #4CAF50;
		color: #fff;
		font-size: 32rpx;
		text-align: center;
		padding: 24rpx 0;
		border-radius: 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
