<template>
	<view class="container">
		<!-- 配送方式选择 -->
		<view class="section">
			<view class="section-title">请选择收货方式</view>
			<view class="delivery-options">
				<view 
					:class="['delivery-option', deliveryType === 'pickup' ? 'active' : '']"
					@click="selectDelivery('pickup')"
				>
					<view class="option-icon">
						<uni-icons v-if="deliveryType === 'pickup'" type="checkmarkempty" size="20" color="#fff" />
					</view>
					<text>自提</text>
				</view>
				<view 
					:class="['delivery-option', deliveryType === 'delivery' ? 'active' : '']"
					@click="selectDelivery('delivery')"
				>
					<view class="option-icon">
						<uni-icons v-if="deliveryType === 'delivery'" type="checkmarkempty" size="20" color="#fff" />
					</view>
					<text>配送</text>
				</view>
			</view>
		</view>

		<!-- 店铺信息 -->
		<view class="store-section">
			<view class="store-header">
				<uni-icons type="shop" size="24" color="#666" />
				<text class="store-name">xxxxxx直营店铺</text>
			</view>
		</view>

		<!-- 商品信息 -->
		<view class="goods-section">
			<view class="goods-item" v-for="(item, idx) in goodsList" :key="idx">
				<view class="goods-img">
					<text class="img-placeholder">商品图</text>
				</view>
				<view class="goods-info">
					<view class="goods-title">{{ item.title }}</view>
					<view class="goods-spec">{{ item.spec }}</view>
					<view class="goods-price-row">
						<text class="goods-price">￥{{ item.price }}</text>
						<text class="goods-count">x {{ item.count }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 留言 -->
		<view class="message-section">
			<view class="message-label">留言</view>
			<input 
				class="message-input" 
				placeholder="可留写您的要求" 
				v-model="message"
				maxlength="100"
			/>
		</view>

		<!-- 支付方式 -->
		<view class="payment-section">
			<view class="payment-item" @click="selectPayment('account')">
				<view class="payment-info">
					<text class="payment-name">账户余额</text>
					<text class="payment-balance">0余币</text>
				</view>
				<view class="payment-radio">
					<view :class="['radio-btn', paymentType === 'account' ? 'active' : '']"></view>
				</view>
			</view>
			
			<view class="payment-item" @click="selectPayment('wechat')">
				<view class="payment-info">
					<text class="payment-name">微信</text>
				</view>
				<view class="payment-radio">
					<view :class="['radio-btn', paymentType === 'wechat' ? 'active' : '']"></view>
				</view>
			</view>
			
			<view class="payment-item" @click="selectPayment('alipay')">
				<view class="payment-info">
					<text class="payment-name">支付宝</text>
				</view>
				<view class="payment-radio">
					<view :class="['radio-btn', paymentType === 'alipay' ? 'active' : '']"></view>
				</view>
			</view>
		</view>

		<!-- 底部支付栏 -->
		<view class="bottom-bar">
			<view class="total-info">
				<text class="total-price">￥{{ totalPrice }}</text>
				<text class="total-count">共{{ totalCount }}件</text>
			</view>
			<button class="pay-btn" @click="submitOrder">立即支付</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				deliveryType: 'pickup', // pickup: 自提, delivery: 配送
				paymentType: 'account', // account: 账户余额, wechat: 微信, alipay: 支付宝
				message: '',
				goodsList: [
					{
						id: 1,
						title: '手工奶茶XXXXX',
						spec: '中杯',
						price: '90.00',
						count: 4
					}
				]
			}
		},
		computed: {
			totalPrice() {
				return this.goodsList.reduce((total, item) => {
					return total + parseFloat(item.price) * item.count;
				}, 0).toFixed(2);
			},
			totalCount() {
				return this.goodsList.reduce((total, item) => {
					return total + item.count;
				}, 0);
			}
		},
		methods: {
			selectDelivery(type) {
				this.deliveryType = type;
			},
			selectPayment(type) {
				this.paymentType = type;
			},
			submitOrder() {
				if (!this.paymentType) {
					uni.showToast({
						title: '请选择支付方式',
						icon: 'none'
					});
					return;
				}
				
				uni.showLoading({
					title: '支付中...'
				});
				
				// 模拟提交订单和支付过程
				setTimeout(() => {
					uni.hideLoading();
					
					// 生成模拟订单ID
					const orderId = 'ORDER' + Date.now().toString().slice(-8);
					
					// 模拟随机支付结果，80%成功，20%失败
					const isSuccess = Math.random() < 0.8;
					
					if (isSuccess) {
						// 支付成功
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							duration: 1500
						});
						
						// 跳转到支付成功页面
						setTimeout(() => {
							uni.redirectTo({
								url: `/pages/shopping/success?orderId=${orderId}`
							});
						}, 1500);
					} else {
						// 支付失败
						uni.showToast({
							title: '支付失败',
							icon: 'error',
							duration: 1500
						});
						
						// 跳转到支付失败页面
						setTimeout(() => {
							uni.redirectTo({
								url: `/pages/shopping/error?orderId=${orderId}`
							});
						}, 1500);
					}
				}, 2000);
			}
		}
	}
</script>

<style>
.container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 配送方式 */
.section {
	background: #fff;
	padding: 24rpx 20rpx;
	margin-bottom: 16rpx;
}
.section-title {
	font-size: 28rpx;
	color: #222;
	margin-bottom: 20rpx;
}
.delivery-options {
	display: flex;
	gap: 20rpx;
}
.delivery-option {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 60rpx;
	border: 2rpx solid #eee;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
	position: relative;
}
.delivery-option.active {
	border-color: #222;
	color: #222;
}
.option-icon {
	position: absolute;
	left: 16rpx;
	width: 32rpx;
	height: 32rpx;
	background: #222;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.delivery-option:not(.active) .option-icon {
	background: #eee;
}

/* 店铺信息 */
.store-section {
	background: #fff;
	padding: 20rpx;
	margin-bottom: 16rpx;
}
.store-header {
	display: flex;
	align-items: center;
}
.store-name {
	font-size: 26rpx;
	color: #333;
	margin-left: 12rpx;
}

/* 商品信息 */
.goods-section {
	background: #fff;
	padding: 20rpx;
	margin-bottom: 16rpx;
}
.goods-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.goods-item:last-child {
	margin-bottom: 0;
}
.goods-img {
	width: 80rpx;
	height: 80rpx;
	background: #eee;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 16rpx;
}
.img-placeholder {
	color: #999;
	font-size: 22rpx;
}
.goods-info {
	flex: 1;
}
.goods-title {
	font-size: 26rpx;
	color: #222;
	margin-bottom: 4rpx;
}
.goods-spec {
	font-size: 22rpx;
	color: #888;
	margin-bottom: 8rpx;
}
.goods-price-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.goods-price {
	color: #ff9800;
	font-size: 26rpx;
	font-weight: bold;
}
.goods-count {
	color: #666;
	font-size: 24rpx;
}

/* 留言 */
.message-section {
	background: #fff;
	padding: 20rpx;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
}
.message-label {
	font-size: 26rpx;
	color: #333;
	margin-right: 20rpx;
	width: 80rpx;
}
.message-input {
	flex: 1;
	font-size: 26rpx;
	color: #333;
}

/* 支付方式 */
.payment-section {
	background: #fff;
	margin-bottom: 16rpx;
}
.payment-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 20rpx;
	border-bottom: 1rpx solid #f5f5f5;
}
.payment-item:last-child {
	border-bottom: none;
}
.payment-info {
	flex: 1;
}
.payment-name {
	font-size: 26rpx;
	color: #333;
	margin-right: 12rpx;
}
.payment-balance {
	font-size: 22rpx;
	color: #ff9800;
}
.payment-radio {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.radio-btn {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #ddd;
	border-radius: 50%;
	position: relative;
}
.radio-btn.active {
	border-color: #007aff;
	background: #007aff;
}
.radio-btn.active::after {
	content: '';
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 12rpx;
	height: 12rpx;
	background: #fff;
	border-radius: 50%;
}

/* 底部支付栏 */
.bottom-bar {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	box-shadow: 0 -2rpx 8rpx #eee;
	z-index: 100;
}
.total-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}
.total-price {
	color: #ff9800;
	font-size: 32rpx;
	font-weight: bold;
}
.total-count {
	color: #888;
	font-size: 22rpx;
}
.pay-btn {
	background: #222;
	color: #fff;
	font-size: 28rpx;
	border-radius: 40rpx;
	padding: 0 40rpx;
	height: 68rpx;
	line-height: 68rpx;
	border: none;
}
</style>
