<template>
	<view class="success-container">
		<image class="success-image" src="/static/images/shoping/success.png" mode="aspectFit"></image>
		<view class="success-title">支付成功</view>
		<view class="success-desc">您的订单已支付成功</view>
		<view class="btn-group">
			<button class="btn view-btn" @click="viewOrder">查看订单详情</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderId: ''
			}
		},
		onLoad(options) {
			// 获取订单ID
			if (options.orderId) {
				this.orderId = options.orderId;
			}
		},
		methods: {
			/**
			 * 查看订单详情
			 */
			viewOrder() {
				if (this.orderId) {
					uni.navigateTo({
						url: `/pages/orders/details?orderId=${this.orderId}`
					});
				}
			},
			
			/**
			 * 继续购物
			 */
			continueShopping() {
				// 返回首页或商城首页
				uni.switchTab({
					url: '/pages/index/index'
				});
			}
		}
	}
</script>

<style lang="scss">
.success-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 190rpx;
}

.success-image {
	width: 273rpx;
	height: 190rpx;
}

.success-title {
	font-size: 35rpx;
	font-weight: 500;
	margin-top: 60rpx;
	color: #000;
}

.success-desc {
	font-size: 24rpx;
	color: #666;
	margin-top: 20rpx;
	margin-bottom: 60rpx;
}

.btn-group {
	display: flex;
	width: 100%;
	padding: 0 40rpx;
	justify-content: space-between;
}

.btn {
	width: 328rpx;
	height: 91rpx;
	line-height: 91rpx;
	border-radius: 46rpx;
	font-size: 30rpx;
}

.view-btn {
	background-color: #66D47E;
	color: #fff;
}
</style>
