<template>
	<view class="container page-container">
		<view class="custom-nav-bar">
			<view class="nav-left" @tap="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
      <view class="nav-title">填写订单</view>
    </view>
		<!-- 配送方式切换 -->
		<view class="delivery-toggle-wrapper">
			<view class="delivery-toggle">
				<view class="toggle-option" :class="{ active: deliveryType === 1 }" @click="deliveryType = 1">
					<image v-if="deliveryType === 1" src="/static/images/shoping/spact.png" class="toggle-bg"></image>
					<text>商家配送</text>
				</view>
				<view class="toggle-option" :class="{ active: deliveryType === 2 }" @click="deliveryType = 2">
					<image v-if="deliveryType === 2" src="/static/images/shoping/ztact.png" class="toggle-bg"></image>
					<text>到店自提</text>
				</view>
			</view>
		</view>

		<!-- 商家配送信息 -->
		<view class="card" style="border-radius: 0 0 16rpx 16rpx;" v-if="deliveryType === 1">
			<view class="delivery-address" @click="goToCommonAddress">
				<view class="address-main">
					<view class="address-text">{{address?.areaDetail || '请选择地址'}}</view>
					<view class="user-info">{{address.receiverName || ''}} {{ address.contactNumber || '' }}</view>
				</view>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
			<!-- <view class="delivery-time">
				<text>立即送出</text>
				<text class="time">大约 {{ estimatedDeliveryTime }} 送达</text>
			</view> -->
			<view class="delivery-note">
				<image src="/static/images/shoping/info.png" mode="widthFix" class="info-icon"></image>
				<text>仅支持配送范围1km以内</text>
			</view>
		</view>

		<!-- 到店自提信息 -->
		<view class="card" style="border-radius: 0 0 16rpx 16rpx;" v-if="deliveryType === 2">
			<view class="pickup-info">
				<view class="pickup-row address-row" style="padding-top: 0;">
					<view class="header-row">
						<text class="label">商家地址</text>
						<view class="distance-tag">
							<uni-icons type="location-filled" size="14" color="#999"></uni-icons>
							<text class="distance-text">距你0.55km</text>
						</view>
					</view>
					<view class="address-content">
						<text class="address-text">{{storeDetail.address}}</text>
					</view>
				</view>
				<!-- <view class="pickup-row">
					<text class="label">自取时间</text>
					<view class="content">
						<text v-if="!isEditingTime" class="value" @click="showTimeSelector">{{ pickupTime || '请选择时间' }}</text>
						<picker 
							v-else
							mode="time"
							:value="pickupTime"
							start="08:00"
							end="20:00"
							@change="onTimeChange"
							@cancel="hideTimeSelector"
						>
							<view class="picker-view">
								{{ pickupTime || '请选择时间' }}
							</view>
						</picker>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>
				<view class="pickup-row">
					<text class="label">预留电话</text>
					<view class="content">
						<text v-if="!isEditingPhone" class="value" @click="showPhoneInput">{{ phoneNumber || '点击输入手机号码' }}</text>
						<input 
							v-else
							class="phone-input" 
							v-model="phoneNumber" 
							placeholder="请输入手机号码" 
							type="number"
							maxlength="11"
							focus
							@blur="hidePhoneInput"
							@confirm="hidePhoneInput"
						/>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view> -->
			</view>
		</view>

		<!-- 订单商品信息 -->
		<view class="card">
			<view class="shop-name">{{ storeDetail.merchantName }}</view>
			<view class="product-item">
				<image class="product-image"
					:src="img">
				</image>
				<view class="product-info">
					<view class="product-name">{{ productName }}</view>
					<view class="product-spec">{{ productSpec }}</view>
					<view class="product-quantity">x{{ amount }}</view>
				</view>
				<view class="price-info">
					<text class="old-price" v-if="discountPrice">¥{{ (price / 100) * amount }}</text>
					<text class="new-price">¥{{ ((discountPrice || price) / 100) * amount }}</text>
				</view>
			</view>
			<view class="promo-info" v-if="discountPrice && discountPrice !== 'null'">
				<text class="yh-box">优惠说明</text>
				<text class="promo-amount"><text class="yyh-box">已优惠</text>&nbsp;&nbsp;<text class="je-box">¥{{ ((price - discountPrice) / 100) * amount }}</text> </text>
			</view>
		</view>

		<!-- 留言 -->
		<view class="card">
			<view class="message-row">
				<text>留言</text>
				<view class="message-content" @click="showMessageInput" v-if="!isEditingMessage">
					<text>{{orderMessage || '点击输入留言'}}</text>
					<uni-icons type="right" size="16" color="#999"></uni-icons>
				</view>
				<view class="message-input-container" v-else>
					<input 
						class="message-input" 
						v-model="orderMessage" 
						placeholder="请输入留言内容" 
						focus 
						@blur="hideMessageInput"
						@confirm="hideMessageInput"
					/>
				</view>
			</view>
		</view>


		<!-- 支付方式 -->
		<view class="card">
			<view class="payment-method" v-for="(method, index) in paymentMethods" :key="index"
				@click="handlePaymentSelect(method.id)">
				<view class="method-info">
					<image :src="'/static/images/shoping/'+method.icon" class="method-icon"></image>
					<text>{{ method.name }}</text>
					<text class="balance" v-if="method.id === 1">{{ method.balance }} <text class="balance-text">贝壳币</text></text>
				</view>
				<view class="radio" :class="{ checked: selectedPayment === method.id }">
					<uni-icons v-if="selectedPayment === method.id" type="checkmarkempty" color="#fff" size="12">
					</uni-icons>
				</view>
			</view>
		</view>


		<!-- 底部合计与支付 -->
		<view class="bottom-bar">
			<view class="total-section">
				<view class="total-price">
					<text class="total-price-text">合计</text>
					<text class="price-symbol">¥</text>
					<text class="price-amount">{{ ((discountPrice || price) / 100) * amount }}</text>
				</view>
				<view class="total-details" v-if="discountPrice && discountPrice !== 'null'">
					共{{amount}}件, 已优惠 ¥{{(price - discountPrice) / 100 * amount}}
				</view>
			</view>
			<view class="pay-btn" @click="handlePayNow">
				立即支付
			</view>
		</view>
		<uni-popup ref="customModal" mode="center" :mask-click="false">
			<view class="confirm-popup-content">
				<view class="title">{{modalTitle}}</view>
				<view class="content">{{modalMessage}}</view>
				<view class="btn-box">
					<view class="cancel" @click="closeModal">取消</view>
					<view class="confirm" @click="confirmModal">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import { shopApi, addressApi, payApi, orderApi  } from '@/common/api'
	import cityData from '@/pages/user/components/citydata.json'
	export default {
		data() {
			return {
				deliveryType: 2, // 1: 商家配送, 2: 到店自提
				selectedPayment: 3, // 默认选中微信支付
				storeDetail: {},
				address: {},
				productId: '',
				skuId: '',
				amount: 1,
				productName: '',
				productSpec: '',
				price: 0,
				discountPrice: '',
				phoneNumber: '', // 预留电话号码
				isEditingPhone: false, // 是否正在编辑电话号码
				orderMessage: '', // 默认留言内容
				isEditingMessage: false, // 是否正在编辑留言
				img: '',
				pickupTime: '', // 自取时间
				isEditingTime: false, // 是否正在编辑自取时间
				paymentMethods: [
					{
						id: 3,
						name: '微信支付',
						balance: null,
						icon: 'wx.png'
					},
					{
						id: 1,
						name: '账户余额',
						balance: '',
						icon: 'ye.png'
					},
					// {
					// 	id: 2,
					// 	name: '支付宝',
					// 	balance: null,
					// 	icon: 'zfb.png'
					// },
				],
				// 自定义弹窗相关
				modalTitle: '',
				modalMessage: '',
				confirmCallback: null
			}
		},
		computed: {
			estimatedDeliveryTime() {
				const now = new Date();
				// 当前时间加45分钟
				const deliveryTime = new Date(now.getTime() + 45 * 60 * 1000);
				// 格式化时间为 HH:MM 格式
				const hours = String(deliveryTime.getHours()).padStart(2, '0');
				const minutes = String(deliveryTime.getMinutes()).padStart(2, '0');
				return `${hours}:${minutes}`;
			}
		},
		onLoad(options) {
			this.storeDetail = uni.getStorageSync('storeInfo');
			this.productId = options.productId;
			this.skuId = options.skuId;
			this.amount = options.amount;
			this.productName = options.productName;
			this.productSpec = options.productSpec;
			this.price = options.price;
			this.discountPrice = options.discountPrice !== 'null' ? options.discountPrice : '';
			this.img = options.image;
			// 检查是否有本地存储的地址信息
			const selectedAddress = uni.getStorageSync('selectedAddress');
			if (selectedAddress) {
				this.address = selectedAddress;
			} else {
				this.getAddressList();
			}
			this.getBalance();
			// 监听地址选择事件
			uni.$on('addressSelected', this.updateAddress);
		},
		onUnload() {
			// 页面卸载时移除事件监听
			uni.$off('addressSelected', this.updateAddress);
			// 离开页面时删除selectedAddress缓存
			uni.removeStorageSync('selectedAddress');
		},
		onReady() {
			// 页面渲染完成后，确保弹窗组件已挂载
			this.$nextTick(() => {
				if (!this.$refs.customModal) {
					console.warn('弹窗组件未正确挂载');
				}
			});
		},
		methods: {
			getLabel(provinceCode,cityCode,areaCode) {
				const province = cityData.find(item => item.value == provinceCode)
				const city = province?.children?.find(item => item.value == cityCode)
				const area = city?.children?.find(item => item.value == areaCode)
				return `${province?.text}${city?.text}${area?.text}`
			},
			/**
			 * @description 更新选中的地址
			 * @param {Object} address - 选中的地址对象
			 */
			updateAddress(address) {
				this.address = address
			},
			getBalance(){
				payApi.getWallet().then(res => {
					this.paymentMethods[1].balance = res.data.walletAccount;
				})
			},
			async getAddressList(){
				const res = await addressApi.getAddressList()
				this.address = res.data?.[0] || {}
			},
			/**
			 * 跳转到常用地址页面
			 */
			goToCommonAddress() {
				uni.navigateTo({
					// url: '/pages/shopping/commonAddress'
					url: '/pages/profile/address'
				});
			},
			/**
			 * 处理支付方式选择
			 * @param {number} paymentId - 支付方式ID
			 */
			handlePaymentSelect(paymentId) {
				this.selectedPayment = paymentId;
			},
			async addOrder(){
				const address = this.address?.areaDetail;
				let params = {}
				if(this.deliveryType === 1){
					params = {
						receiverName: this.address.receiverName,
						receiverPhone: this.address.contactNumber,
						deliveryAddress: address,
					}
				} else {
					params = {
						// pickupTime: this.formatDate(new Date()) + ' ' + this.pickupTime + ':00',
						reservePhone: this.phoneNumber
					}
				}
				const payParams = await this.getWxPayParams({
					...params,
					"merchantId": this.storeDetail.id,
					"deliveryType": this.deliveryType,
					"orderMessage": this.orderMessage,
					"goodsList": [
						{
							"goodsId": this.productId,
							"skuId": this.skuId,
							"quantity": this.amount,
							"unitPrice": this.price
						}
					]
				});
				if(this.selectedPayment === 3){
					uni.requestPayment({
						provider: 'wxpay',
						orderInfo: {
							"appid": payParams.appId,  // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
							"noncestr": payParams.nonceStr, // 随机字符串
							"package": "Sign=WXPay",        // 固定值
							"partnerid": payParams.mchId,      // 微信支付商户号
							"prepayid": payParams.prepayId, // 统一下单订单号
							"timestamp": payParams.timeStamp,        // 时间戳（单位：秒）
							"sign": payParams.sign // 签名，这里用的 MD5/RSA 签名
						}, // 微信支付订单信息
						success: () => {
							this.handlePaySuccess(payParams);
						},
						fail: (err) => {
							console.log('err',err);
							this.handlePayError(payParams);
						}
					});
				} else if(this.selectedPayment === 1) {
					if(payParams.payOk || payParams.orderStatus === "1"){
						this.handlePaySuccess(payParams);
						return;
					} else {
						this.handlePayError(payParams);
						return;
					}
				}
			},
			async getWxPayParams(createOrderRequest) {
				const res = await orderApi.createOrder({
					...createOrderRequest,
					payMethod: this.selectedPayment === 3 ? 1 : 5
				})
				return {...res.data.paymentResp, orderStatus: res.data.orderStatus}
			},
			/**
			 * 处理微信支付
			 */
			handleWechatPay() {
				// 检查是否在微信小程序环境
				// #ifdef MP-WEIXIN
				this.requestWechatPay();
				// #endif
				
				// #ifdef APP-PLUS
				this.requestWechatPay();
				// #endif
				
				// #ifdef H5
				uni.showToast({
					title: 'H5环境暂不支持微信支付',
					icon: 'none'
				});
				// #endif
			},
			
			/**
			 * 请求微信支付
			 */
			requestWechatPay() {
				uni.showLoading({
					title: '正在发起支付...'
				});
				
				// 这里需要调用后端接口获取支付参数
				// 实际项目中需要根据您的后端API进行调整
				this.getWechatPayParams().then(payParams => {
					uni.hideLoading();
					this.callWechatPay(payParams);
				}).catch(error => {
					uni.hideLoading();
					uni.showToast({
						title: '获取支付参数失败',
						icon: 'none'
					});
					console.error('获取微信支付参数失败:', error);
				});
			},
			
			/**
			 * 获取微信支付参数
			 * @returns {Promise} 支付参数
			 */
			getWechatPayParams() {
				return new Promise((resolve, reject) => {
					// 这里需要调用您的后端API获取微信支付参数
					// 示例代码，请根据实际API调整
					uni.request({
						url: 'your-api-url/wechat/pay',
						method: 'POST',
						data: {
							orderId: 'your-order-id', // 订单ID
							amount: 23.0, // 支付金额
							description: 'The Daily Brew 咖啡馆订单' // 商品描述
						},
						success: (res) => {
							if (res.data.code === 0) {
								resolve(res.data.data);
							} else {
								reject(new Error(res.data.message));
							}
						},
						fail: (error) => {
							reject(error);
						}
					});
				});
			},
			
			/**
			 * 调用微信支付
			 * @param {Object} payParams - 支付参数
			 */
			callWechatPay(payParams) {
				// #ifdef MP-WEIXIN
				// 微信小程序支付
				uni.requestPayment({
					timeStamp: payParams.timeStamp,
					nonceStr: payParams.nonceStr,
					package: payParams.package,
					signType: payParams.signType,
					paySign: payParams.paySign,
					success: (res) => {
						console.log('微信支付成功:', res);
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						});
						// 支付成功后的处理逻辑
						this.handlePaySuccess();
					},
					fail: (err) => {
						console.log('微信支付失败:', err);
						if (err.errMsg.includes('cancel')) {
							uni.showToast({
								title: '支付已取消',
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: '支付失败',
								icon: 'none'
							});
						}
					}
				});
				// #endif
				
				// #ifdef APP-PLUS
				// APP端微信支付
				uni.requestPayment({
					provider: 'wxpay',
					orderInfo: payParams.orderInfo, // 微信支付订单信息
					success: (res) => {
						console.log('微信支付成功:', res);
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						});
						// 支付成功后的处理逻辑
						this.handlePaySuccess();
					},
					fail: (err) => {
						console.log('微信支付失败:', err);
						if (err.errMsg.includes('cancel')) {
							uni.showToast({
								title: '支付已取消',
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: '支付失败',
								icon: 'none'
							});
						}
					}
				});
				// #endif
			},
			
			/**
			 * 处理支付成功
			 */
			handlePaySuccess(payParams) {
				// 支付成功后的处理逻辑
				// 例如：跳转到支付成功页面、更新订单状态等
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/shopping/success?orderId=' + payParams.orderNo
					});
				}, 1500);
			},
			handlePayError(payParams) {
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/shopping/error?orderId=' + payParams.orderNo
					});
				}, 1500);
			},
			/**
			 * 立即支付按钮点击事件
			 */
			async handlePayNow() {
				// 检查当前时间是否在营业时间内
				const currentTime = new Date();
				const currentHour = currentTime.getHours();
				const currentMinute = currentTime.getMinutes();
				
				// 解析营业时间
				const openTimeParts = this.storeDetail.openTime ? this.storeDetail.openTime.split(':') : ['00', '00'];
				const closeTimeParts = this.storeDetail.closeTime ? this.storeDetail.closeTime.split(':') : ['23', '59'];
				
				const openHour = parseInt(openTimeParts[0]);
				const openMinute = parseInt(openTimeParts[1] || 0);
				const closeHour = parseInt(closeTimeParts[0]);
				const closeMinute = parseInt(closeTimeParts[1] || 0);
				
				// 转换为分钟进行比较
				const currentTotalMinutes = currentHour * 60 + currentMinute;
				const openTotalMinutes = openHour * 60 + openMinute;
				const closeTotalMinutes = closeHour * 60 + closeMinute;
				
				// 检查是否在营业时间内
				if (!(currentTotalMinutes >= openTotalMinutes && currentTotalMinutes <= closeTotalMinutes)) {
					// 不在营业时间内，提示用户
					uni.showToast({
						title: `非营业时间，营业时间为${this.storeDetail.openTime}-${this.storeDetail.closeTime}`,
						icon: 'none',
						duration: 2000
					});
					return;
				}
				const address = this.address?.areaDetail;
				if(this.deliveryType === 1 && !address){
					uni.showToast({
						title: '请选择地址',
						icon: 'none'
					});
					return;
				}
				// if(this.deliveryType === 2 && !this.pickupTime){
				// 	uni.showToast({
				// 		title: '请选择自取时间',
				// 		icon: 'none'
				// 	});
				// 	return;
				// }
				// if(this.deliveryType === 2 && !this.phoneNumber){
				// 	uni.showToast({
				// 		title: '请输入预留电话',
				// 		icon: 'none'
				// 	});
				// 	return;
				// }
				if (this.selectedPayment === 3) {
					// 微信支付
					const result = await this.showConfirmDialog(`是否使用微信支付 ¥${((this.discountPrice || this.price) / 100) * this.amount}？商家已接单，不可退款！`, '确认支付');
					if (!result) return;
					this.addOrder();
				} else if (this.selectedPayment === 1) {
					// 账户余额支付
					const balance = parseFloat(this.paymentMethods[1].balance) || 0;
					const amount = ((this.discountPrice || this.price) / 10) * this.amount;
					
					if (balance < amount) {
						uni.showToast({
							title: `余额不足，当前余额 ${balance}贝壳币，需要 ${amount}贝壳币`,
							icon: 'none',
							duration: 3000
						});
						return;
					}
					
					const result = await this.showConfirmDialog(`是否使用账户余额支付 ${amount}贝壳币？商家已接单，不可退款！`, '确认支付');
					if (!result) return;
					this.addOrder();
				} else if (this.selectedPayment === 2) {
					// 支付宝支付
					this.handleAlipayPay();
				}
			},
			
		/**
		 * 显示确认对话框
		 */
		showConfirmDialog(content, title = '提示') {
			this.modalTitle = title;
			this.modalMessage = content;
			// 使用 nextTick 确保 DOM 更新后再打开弹窗
			this.$nextTick(() => {
				if (this.$refs.customModal) {
					this.$refs.customModal.open();
				} else {
					console.warn('弹窗组件未找到，使用备用方案');
					// 备用方案：使用 uni.showModal
					uni.showModal({
						title: title,
						content: content,
						success: (res) => {
							if (this.confirmCallback) {
								this.confirmCallback(res.confirm);
							}
						}
					});
				}
			});
			this.confirmCallback = null; // 清空之前的回调
			return new Promise((resolve) => {
				this.confirmCallback = resolve;
			});
		},

		/**
		 * 关闭自定义弹窗
		 */
		closeModal() {
			if (this.$refs.customModal) {
				this.$refs.customModal.close();
			}
			this.modalTitle = '';
			this.modalMessage = '';
			this.confirmCallback = null;
		},

		/**
		 * 确认自定义弹窗
		 */
		confirmModal() {
			if (this.confirmCallback) {
				this.confirmCallback(true);
			}
			this.closeModal();
		},
			/**
			 * 支付宝支付
			 */
			handleAlipayPay() {
				// #ifdef APP-PLUS
				uni.requestPayment({
					provider: 'alipay',
					orderInfo: 'your-alipay-order-info', // 支付宝订单信息
					success: (res) => {
						console.log('支付宝支付成功:', res);
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						});
						this.handlePaySuccess();
					},
					fail: (err) => {
						console.log('支付宝支付失败:', err);
						if (err.errMsg.includes('cancel')) {
							uni.showToast({
								title: '支付已取消',
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: '支付失败',
								icon: 'none'
							});
						}
					}
				});
				// #endif
				
				// #ifdef H5
				uni.showToast({
					title: 'H5环境暂不支持支付宝支付',
					icon: 'none'
				});
				// #endif
			},
			/**
			 * 显示电话号码输入框
			 */
			showPhoneInput() {
				this.isEditingPhone = true;
			},
			
			/**
			 * 隐藏电话号码输入框
			 */
			hidePhoneInput() {
				this.isEditingPhone = false;
			},
			
			showMessageInput() {
				this.isEditingMessage = true;
			},
			hideMessageInput() {
				this.isEditingMessage = false;
			},
			/**
			 * 显示自取时间选择器
			 */
			showTimeSelector() {
				this.isEditingTime = true;
			},
			/**
			 * 隐藏自取时间选择器
			 */
			hideTimeSelector() {
				this.isEditingTime = false;
			},
			/**
			 * 格式化日期为 YYYY-MM-DD 格式
			 * @param {Date} date - 日期对象
			 * @returns {string} 格式化后的日期字符串
			 */
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			/**
			 * 自取时间选择器变化
			 * @param {Object} e - 选择器事件对象
			 */
			onTimeChange(e) {
				this.pickupTime = e.detail.value;
				this.isEditingTime = false;
			},
			goBack(){
				uni.navigateBack();
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f8f8f8;
		min-height: calc(100vh - 120rpx) !important;
		max-height: calc(100vh - 120rpx) !important;
		padding: 20rpx;
		padding-bottom: 130rpx;
		overflow-y: auto;
	}
	.custom-nav-bar .nav-title{
		font-weight: bold;
	}
	.delivery-toggle-wrapper {
		display: flex;
		justify-content: center;
		padding-bottom: 20rpx;
		margin-top: 20rpx;
		background: #fff;
	}

	.delivery-toggle {
		display: flex;
		width: 100%;
		// max-width: 500rpx;
		justify-content: space-between;
	}

	.toggle-option {
		position: relative;
		width: 100%;
		height: 70rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.toggle-bg {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 1;
	}

	.toggle-option text {
		position: relative;
		z-index: 2;
		font-size: 31rpx;
		font-weight: 600;
		color: #0D0E0F;
	}

	.toggle-option.active text {
		color: #000;
		font-size: 27rpx;
	}

	.card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0,0,0,0.08);
	}

	/* 配送地址 */
	.delivery-address {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 20rpx;
		margin-bottom: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.address-text {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.user-info {
		font-weight: 500;
		font-size: 27rpx;
		color: #999999;
	}

	.delivery-time {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		margin-bottom: 10rpx;
	}

	.delivery-time .time {
		font-weight: 500;
		font-size: 27rpx;
		color: #4BAD63;
	}

	.delivery-note {
		display: flex;
		align-items: center;
		.info-icon{
			width: 27rpx;
			margin-right: 10rpx;
		}
		text {
			font-weight: 500;
			font-size: 23rpx;
			color: #F9AF25;
		}
	}


	/* 自提信息 */
	.pickup-row {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		font-size: 28rpx;
		padding: 16rpx 0 0 0;
	}

	.pickup-row .label {
		flex-shrink: 0;
		margin-right: 40rpx;
		font-weight: 500;
		font-size: 27rpx;
		color: #333333;
	}

	.pickup-row .content {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		text-align: right;
		.value{
			font-weight: 500;
			font-size: 27rpx;
			color: #4BAD63;
		}
	}

	.pickup-row .content .address-text {
		margin-bottom: 0;
		font-weight: 500;
		font-size: 31rpx;
		color: #0D0E0F;
	}

	.pickup-row .content .distance {
		font-size: 24rpx;
		color: #999;
		margin-top: 8rpx;
	}

	.pickup-row .content .distance text {
		margin-left: 4rpx;
	}
	
	.pickup-row .value {
		margin-right: 10rpx;
	}
	
	.phone-input {
		border: none;
		border-bottom: 1rpx solid #eee;
		padding: 10rpx;
		width: 300rpx;
		text-align: right;
		font-size: 28rpx;
	}
	
	/* 时间选择器样式 */
	.picker-view {
		font-weight: 500;
		font-size: 27rpx;
		color: #4BAD63;
		padding: 10rpx 0;
		text-align: right;
		margin-right: 10rpx;
	}

	/* 新增样式 */
	.address-container {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}

	.distance-tag {
		font-weight: 400;
		font-size: 23rpx;
		color: #666666;
	}

	.distance-text {
		margin-left: 4rpx;
		font-size: 24rpx;
		color: #999;
		font-weight: 400;
	}
	
	/* 商家地址新样式 */
	.address-row {
		flex-direction: column;
		align-items: flex-start;
	}
	
	.header-row {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
		.label{
			font-weight: 400;
			font-size: 23rpx;
			color: #666666;
		}
	}
	
	.address-content {
		width: 100%;
		box-sizing: border-box;
		.address-text {
			font-weight: 500;
			font-size: 31rpx;
			color: #0D0E0F;
		}
	}
	

	/* 商品信息 */
	.shop-name {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}

	.product-item {
		display: flex;
		align-items: center;
	}

	.product-image {
		width: 90rpx;
		height: 90rpx;
		border-radius: 12rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
    justify-content: space-between;
		height: 90rpx;
	}

	.product-name {
		font-family: PingFangSC, PingFang SC;
		font-weight: 700;
		font-size: 30rpx;
		color: #000000;
		text-align: left;
		font-style: normal;
	}

	.product-spec,
	.product-quantity {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 23rpx;
		color: #999999;
		text-align: left;
		font-style: normal;
	}

	.price-info {
		text-align: right;
		display: flex;
	}

	.old-price {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 23rpx;
		color: #999999;
		text-align: left;
		font-style: normal;
		text-decoration-line: line-through;
		margin-right: 10rpx;
	}

	.new-price {
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 27rpx;
		color: #000000;
		text-align: left;
		font-style: normal;
	}

	.promo-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 26rpx;
		margin-top: 20rpx;
		padding-top: 20rpx;
		border-top: 1rpx solid #f5f5f5;
	}

	.promo-amount {
		color: #e54d42;
	}

	/* 留言 */
	.message-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
	}

	.message-content {
		display: flex;
		align-items: center;
		color: #999;
	}

	.message-content text {
		margin-right: 10rpx;
	}
	
	.message-input-container {
		flex: 1;
		display: flex;
		justify-content: flex-end;
	}
	
	.message-input {
		border: none;
		border-bottom: 1rpx solid #eee;
		padding: 10rpx;
		width: 400rpx;
		text-align: right;
		font-size: 28rpx;
	}

	/* 支付方式 */
	.payment-method {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
	}

	.method-info {
		display: flex;
		align-items: center;
		font-size: 28rpx;
	}

	.method-icon {
		width: 48rpx;
		height: 48rpx;
		margin-right: 20rpx;
	}

	.balance {
		font-size: 22rpx;
		color: #F7532E;
		margin-left: 30rpx;
		.balance-text{
			font-weight: 500;
			font-size: 27rpx;
			color: #F7532E;
		}
	}

	.radio {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		border: 1rpx solid #ccc;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.radio.checked {
		background-color: #4CAF50;
		border-color: #4CAF50;
	}

	/* 底部栏 */
	.bottom-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 40rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		border-top: 1rpx solid #f0f0f0;
	}

	.total-price {
		display: flex;
		align-items: baseline;
		font-size: 28rpx;
		.total-price-text{
			font-weight: 500;
			font-size: 23rpx;
			color: #F7532E;
		}
		.price-symbol {
			font-size: 23rpx;
			color: #F7532E;
			margin-left: 8rpx;
		}
		.price-amount{
			font-size: 38rpx;
			color: #F7532E;
			margin-left: 8rpx;
		}
	}

	.total-details {
		font-weight: 400;
		font-size: 23rpx;
		color: #666666;
		text-align: left;
	}

	.pay-btn {
		background-color: #4CAF50;
		color: #fff;
		font-size: 32rpx;
		padding: 24rpx 80rpx;
		border-radius: 50rpx;
	}
	
	text{
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 27rpx;
		color: #000000;
		line-height: 42rpx;
		text-align: left;
		font-style: normal;
	}
	
	.yh-box{
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 23rpx;
		color: #999999;
		line-height: 38rpx;
		text-align: left;
		font-style: normal;
	}
	
	.yyh-box{
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 23rpx;
		color: #666666;
		line-height: 23rpx;
		text-align: left;
		font-style: normal;
	}
	
	.je-box{
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 23rpx;
		color: #F7532E;
		line-height: 23rpx;
		text-align: left;
		font-style: normal;
	}
	.confirm-popup-content {
		width: 540rpx;
		background-color: #fff;
		border-radius: 30rpx;
		padding: 40rpx 60rpx;
		box-sizing: border-box;

		.title {
			font-family: 苹方-简, 苹方-简;
			font-weight: normal;
			font-size: 30rpx;
			color: #000000;
			line-height: 42rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
			margin-bottom: 30rpx;
		}

		.content {
			font-family: 苹方-简, 苹方-简;
			font-weight: normal;
			font-size: 24rpx;
			color: #666666;
			line-height: 36rpx;
			text-align: center;
		}

		.btn-box {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;

			.cancel,
			.confirm {
				width: 200rpx;
				height: 80rpx;
				border-radius: 40rpx;
			}
		}

		.cancel {
			width: 200rpx;
			height: 80rpx;
			background-color: #F5F5F5;
			border-radius: 40rpx;
			color: #666666;
			text-align: center;
			line-height: 80rpx;
		}

		.confirm {
			width: 200rpx;
			height: 80rpx;
			background: #34BC4D;
			border-radius: 40rpx;
			color: #fff;
			text-align: center;
			line-height: 80rpx;
		}
	}
</style>
