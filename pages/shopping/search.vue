<template>
	<view class="container">
		<!-- 顶部搜索栏 -->
		<view class="search-header">
			<view class="search-bar">
				<uni-icons type="search" size="20" color="#ccc" />
				<input
					class="search-input"
					placeholder="输入服务名称/店铺名称"
					v-model="searchValue"
				/>
			</view>
			<text class="cancel-btn" @click="onCancel">取消</text>
		</view>

		<!-- 搜索历史 -->
		<view class="history-section">
			<view class="history-title-row">
				<text class="history-title">搜索历史</text>
				<uni-icons type="trash" size="22" color="#bbb" class="clear-icon" @click="clearHistory" />
			</view>
			<view class="history-tags">
				<view
					v-for="(item, idx) in historyList"
					:key="idx"
					class="history-tag"
				>
					{{ item }}
				</view>
			</view>
			<text class="history-tip">历史搜索最大保留十个</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchValue: '',
			historyList: [
				'历史一',
				'历史二',
				'历史xxxxxxxxx',
				'历史xxxxxxxxx',
				'历史搜索最大保留十个'
			]
		}
	},
	methods: {
		onCancel() {
			uni.navigateBack();
		},
		clearHistory() {
			this.historyList = [];
		}
	}
}
</script>

<style>
.container {
	background: #fff;
	min-height: 100vh;
	padding: 0 24rpx;
	box-sizing: border-box;
}
.search-header {
	display: flex;
	align-items: center;
	padding: 24rpx 0 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}
.search-bar {
	flex: 1;
	display: flex;
	align-items: center;
	background: #fafafa;
	border-radius: 32rpx;
	padding: 0 20rpx;
	height: 56rpx;
	border: 1rpx solid #eee;
}
.search-input {
	flex: 1;
	border: none;
	background: transparent;
	margin-left: 10rpx;
	font-size: 28rpx;
	height: 56rpx;
	line-height: 56rpx;
}
.cancel-btn {
	color: #1890ff;
	font-size: 28rpx;
	margin-left: 20rpx;
}
.history-section {
	margin-top: 32rpx;
}
.history-title-row {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}
.history-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-right: 12rpx;
}
.clear-icon {
	margin-left: auto;
}
.history-tags {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 16rpx;
	
	.history-tag {
		margin-right: 20rpx;
		margin-bottom: 16rpx;
		
		&:nth-child(3n) {
			margin-right: 0;
		}
	}
}
.history-tag {
	background: #fafafa;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	padding: 10rpx 24rpx;
	font-size: 26rpx;
	color: #333;
	margin-bottom: 8rpx;
}
.history-tip {
	color: #999;
	font-size: 24rpx;
	margin-top: 8rpx;
	display: block;
}
</style>