<template>
	<view class="container page-container">
		<view class="custom-nav-bar">
      <view class="nav-left" @tap="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
    </view>
		<!-- 顶部轮播图 -->
		<swiper class="store-banner" circular autoplay interval="3000" duration="500" indicator-dots indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#FFFFFF">
			<swiper-item v-for="(item, index) in carouselImages && carouselImages.length > 0 ? carouselImages : [storeInfo.imgUrl]" :key="index">
				<image class="swiper-image" :src="item" mode="aspectFill" />
			</swiper-item>
		</swiper>

		<!-- 店铺信息 -->
		<view class="store-info">
			<view class="store-title-row">
				<text class="store-name">{{ storeInfo.merchantName }}</text>
				<view class="store-tags">
					<text class="tag" v-for="tag in storeInfo.tags" :key="tag">{{ tag }}</text>
				</view>
			</view>
			<view class="store-desc">{{ storeInfo.merchantDesc }}</view>
			<view class="store-meta">
				<view>
					<text class="meta">{{ storeInfo.categoryDesc }}</text>
					<text class="meta">{{ storeInfo.avgConsumption }}条</text>
					<text class="meta">¥{{ storeInfo.avgConsumption }}/人</text>
				</view>
				<text class="meta">营业： {{ storeInfo.openTime }} - {{ storeInfo.closeTime }}</text>
			</view>
			<view class="shop-rank">
				<text class="rank-badge">榜</text>
				<text class="rank-text">{{ storeInfo.rankingInfo }}</text>
			</view>
			<view class="store-address-row">
				<view class="address-row">
					<view class="address-row-top">
						<text class="address">{{ storeInfo.address }}</text>
						<uni-icons type="arrowright" size="16" color="#000" />
					</view>
					<view class="address-row-bottom">
						<text class="distance">距地铁1号线百家湖站4口步行360m</text>
					</view>
				</view>
				<!-- <text class="distance">{{ storeInfo.distance }}</text> -->
				<view class="phone-btn" @click="callStore">
					<image src="/static/images/shoping/phone.png" mode="aspectFill" style="width: 40rpx;height: 40rpx;" />
					电话
				</view>
			</view>
		</view>

		<!-- Tab切换 -->
		<view class="tab-bar">
			<view class="tab" @click="tabActive = 0" :class="{'active': tabActive === 0}">商品
				<view class="tab-line" v-if="tabActive === 0"></view>
			</view>
			<view class="tab" @click="tabActive = 1" :class="{'active': tabActive === 1}">
				评价
				<text class="comment-count">{{ comments.length }}</text>
				<view class="tab-line" v-if="tabActive === 1"></view>
			</view>
		</view>

		<!-- 商品列表 -->
		<view v-if="tabActive === 0" class="goods-section">
			<view class="goods-grid">
				<view class="goods-item" v-for="(item, idx) in goodsList" :key="idx" @click="goToDetail(item)">
					<text class="goods-tag">推荐</text>
					<image class="goods-img" :src="item.mainPic" mode="aspectFill" />
					<view class="goods-info">
						<view class="goods-title">{{ item.goodsName }}</view>
						<view class="price-row">
							<text class="current-price">￥{{ item.price / 100 }}</text>
							<text class="old-price" v-if="item.discountPrice">￥{{ item.discountPrice / 100 }}</text>
						</view>
						<view class="goods-else-info">
							<view class="goods-zk">55折</view>
							<view class="goods-ys">累计销售 51</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 评价入口 -->
			<!-- <view class="evaluate-entry" @click="tabActive = 1">
				<text>查看全部评价 ></text>
			</view> -->
		</view>

		<!-- 评价列表 -->
		<view v-else class="comment-section">
			<view class="comment-item" v-for="(item, idx) in comments" :key="idx">
				<image class="avatar" :src="item.userAvatar" mode="aspectFill" />
				<view class="comment-main">
					<view class="comment-header">
						<view class="comment-header-left">
							<text class="nick">{{ item.userNickname }}</text>
							<view class="stars">
								<uni-icons v-for="i in 5" :key="i" :type="'star-filled'" size="16" :color="i <= item.rating ? '#FFD700' : '#ccc'" />
							</view>
						</view>
						<text class="date">{{ item.reviewTime }}</text>
					</view>
					<view class="content">{{ item.reviewContent }}</view>
					<view class="comment-imgs" v-if="item.reviewImagesArray && item.reviewImagesArray.length">
						<image v-for="(img, i) in item.reviewImagesArray" :key="i" :src="img" class="comment-img" mode="aspectFill" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import {
		shopApi
	} from '@/common/api'
	export default {
		data() {
			return {
				tabActive: 0,
				storeInfo: {},
				goodsList: [],
				comments: [],
				id: ''
			}
		},
		onLoad(options){
			console.log(options)
			this.id = options.id;
			this.storeInfo = uni.getStorageSync('storeInfo');
			this.getGoodList(this.id);
			this.getMerchantList(this.id);
			this.carouselImages = uni.getStorageSync('storeInfo').carouselImages && uni.getStorageSync('storeInfo').carouselImages.split(',');
		},
		methods: {
			goBack(){
				uni.navigateBack()
			},
			async getGoodList(id){
				const res = await shopApi.getGoodList({
					merchantId: id,
					pageNum: 1,
					pageSize: 10
				})
				this.goodsList = res.rows;
			},
			async getMerchantList(id){
				const res = await shopApi.getMerchantList({
					merchantId: id,
					pageNum: 1,
					pageSize: 10,
				})
				this.comments = res.rows;
			},
			/**
			 * 拨打店铺电话
			 */
			callStore() {
				uni.makePhoneCall({ phoneNumber: this.storeInfo.merchantTel });
			},
			/**
			 * 跳转商品详情
			 * @param {Object} item 商品对象
			 */
			goToDetail(item) {
				// 检查当前时间是否在营业时间内
				const currentTime = new Date();
				const currentHour = currentTime.getHours();
				const currentMinute = currentTime.getMinutes();
				
				// 解析营业时间
				const openTimeParts = this.storeInfo.openTime ? this.storeInfo.openTime.split(':') : ['00', '00'];
				const closeTimeParts = this.storeInfo.closeTime ? this.storeInfo.closeTime.split(':') : ['23', '59'];
				
				const openHour = parseInt(openTimeParts[0]);
				const openMinute = parseInt(openTimeParts[1] || 0);
				const closeHour = parseInt(closeTimeParts[0]);
				const closeMinute = parseInt(closeTimeParts[1] || 0);
				
				// 转换为分钟进行比较
				const currentTotalMinutes = currentHour * 60 + currentMinute;
				const openTotalMinutes = openHour * 60 + openMinute;
				const closeTotalMinutes = closeHour * 60 + closeMinute;
				
				// 检查是否在营业时间内
				if (currentTotalMinutes >= openTotalMinutes && currentTotalMinutes <= closeTotalMinutes) {
					// 在营业时间内，允许跳转
					uni.navigateTo({ url: `/pages/shopping/productDetails?merchantId=${this.id}&productId=${item.id}` });
				} else {
					// 不在营业时间内，提示用户
					uni.showToast({
						title: `非营业时间，营业时间为${this.storeInfo.openTime}-${this.storeInfo.closeTime}`,
						icon: 'none',
						duration: 2000
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">

	.custom-nav-bar{
		position: absolute;
		z-index: 99;
	}
	.container {
		background: #f5f5f5;
		min-height: 100vh;
	}
	.store-banner {
		width: 100vw;
		height: 500rpx;
		border-bottom-left-radius: 32rpx;
		border-bottom-right-radius: 32rpx;
		box-shadow: 0 4rpx 24rpx #eee;
		overflow: hidden;
	}
	::v-deep .uni-swiper-dots{
		bottom: 80rpx;
	}
	.swiper-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	.store-info {
		background: #fff;
		margin: -32rpx 0 0 0;
		border-radius: 24rpx;
		box-shadow: 0 2rpx 12rpx #f0f0f0;
		padding: 28rpx 24rpx 18rpx 24rpx;
		position: relative;
		z-index: 2;
	}
	.store-title-row {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}
	.store-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #222;
		margin-right: 12rpx;
	}
	.store-tags {
		display: flex;
		gap: 8rpx;
	}
	.tag {
		background: #e6f7ff;
		color: #1890ff;
		font-size: 20rpx;
		border-radius: 8rpx;
		padding: 2rpx 10rpx;
	}
	.store-desc {
		font-size: 24rpx;
		color: #888;
		margin-bottom: 8rpx;
	}
	.store-meta {
		display: flex;
		justify-content: space-between;
		gap: 18rpx;
		font-size: 23rpx;
		color: #000;
		margin-bottom: 8rpx;
	}
	.meta {
		margin-right: 8rpx;
	}
	.store-address-row {
		display: flex;
		align-items: center;
		margin-bottom: 2rpx;
		border-top: 1px solid rgba(209, 209, 209, 0.5);
		margin-top: 22rpx;
		padding-top: 22rpx;
		.address-row-top{
			display: flex;
			align-items: center;
		}
	}
	.address-row{
		flex: 1;
	}
	.address-row-bottom{
		font-weight: 400;
		margin-top: 5rpx;
		font-size: 20rpx;
		color: #666666;
		line-height: 27rpx;
	}
	.address {
		font-size: 24rpx;
		color: #000;
	}
	.uniui-arrowright{
		color: #000;
		margin-left: 2rpx;
	}
	.phone-btn {
		display: flex;
		justify-content: center;
    flex-direction: column;
		align-items: center;
		font-size: 20rpx;
		color: #666666;
		font-weight: 400;
	}
	.tab-bar {
		display: flex;
		background: #fff;
		margin: 18rpx 0 0 0;
		overflow: hidden;
		position: relative;
		justify-content: flex-start;
		.tab {
			flex: 0 0 auto;
			text-align: center;
			font-size: 27rpx;
			color: #000;
			padding: 24rpx 0 6rpx 0;
			font-weight: 400;
			border-bottom: 4rpx solid transparent;
			position: relative;
			margin: 0 40rpx;
			.tab-line{
				position: absolute;
				bottom: 6rpx;
				left: 0;
				width: 100%;
				height: 8rpx;
				background: #91E02A;
			}
		}
		.tab.active {
			color: #000;
			font-size: 31rpx;
			font-weight: 600;
			// border-bottom: 4rpx solid #91E02A;
		}
		
	}
	.shop-rank {
		display: flex;
		align-items: center;
	}

	.rank-badge {
		background-color: #ffc107;
		color: #fff;
		font-size: 20rpx;
		padding: 2rpx 8rpx;
		border-radius: 4rpx;
		margin-right: 8rpx;
	}

	.rank-text {
		font-size: 22rpx;
		color: #999;
	}
	.comment-count {
		position: absolute;
		top: 14rpx;
		margin-left: 4rpx;
		font-family: HelveticaNeue;
		font-size: 19rpx;
		color: #999999;
	}
	.goods-section {
		background: #fff;
		padding: 20rpx 20rpx 0 20rpx;
	}
	.goods-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}
	.goods-item {
		width: 48%;
		background: #fff;
		border-radius: 15rpx;
		margin-bottom: 20rpx;
		position: relative;
		box-sizing: border-box;
		border: 2rpx solid rgba(209,209,209,0.5);;
		box-shadow: 0 2rpx 8rpx #f5f5f5;
	}
	.goods-img {
		width: 100%;
		height: 200rpx;
		/* background: #ddd; */
		border-radius: 15rpx 15rpx 0rpx 0rpx;
		display: block;
		margin-bottom: 12rpx;
	}
	.goods-info {
		margin-bottom: 8rpx;
		padding: 0 16rpx 8rpx;
	}
	.goods-title {
		font-size: 26rpx;
		margin-bottom: 8rpx;
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		font-weight: 600;
		color: #000000;
	}
	.goods-else-info{
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 10rpx;
	}
	.goods-zk{
		font-weight: 500;
		font-size: 20rpx;
		color: #F7532E;
		border-radius: 2rpx;
		border: 2rpx solid #F7532E;
		padding: 2rpx 4rpx;
	}
	.goods-ys{
		font-weight: 400;
		font-size: 20rpx;
		color: #666666;
	}
	.price-row {
		display: flex;
		align-items: center;
	}
	.current-price {
		color: #F7532E;
		font-size: 20rpx;
		font-weight: 500;
		margin-right: 10rpx;
	}
	.old-price {
		color: #999;
		font-size: 20rpx;
		font-weight: 400;
		text-decoration: line-through;
	}
	.goods-tag {
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		font-size: 20rpx;
		padding: 6rpx 8rpx;
		background: linear-gradient( 45deg, #66D47E 0%, #91E02A 100%);
		border-radius: 15rpx 0rpx 15rpx 0rpx;
		font-weight: 500;
		font-size: 23rpx;
		color: #FFFFFF;
		z-index: 99;
	}
	.evaluate-entry {
		text-align: center;
		color: #1890ff;
		font-size: 26rpx;
		padding: 18rpx 0 10rpx 0;
		border-top: 1rpx solid #f0f0f0;
		margin-top: 8rpx;
		cursor: pointer;
	}
	.comment-section {
		background: #fff;
		padding: 20rpx 20rpx 0 20rpx;
		max-height: 700rpx;
		overflow-y: auto;
	}
	.comment-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 24rpx;
		border-bottom: 1rpx solid #f5f5f5;
		padding-bottom: 18rpx;
	}
	.avatar {
		width: 61rpx;
		min-width: 61rpx;
		height: 61rpx;
		border-radius: 50%;
		margin-right: 14rpx;
		background: #eee;
	}
	.comment-main {
		flex: 1;
		width: 100%;
	}
	.comment-header {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		margin-bottom: 6rpx;
	}
	.comment-header-left {
		display: flex;
		flex-direction: column;
	}
	.nick {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 6rpx;
	}
	.stars {
		display: flex;
		align-items: center;
	}
	.date {
		color: #999;
		font-size: 24rpx;
		margin-right: 20rpx;
	}
	.content {
		color: #333;
		font-size: 26rpx;
		line-height: 1.5;
		margin: 12rpx 0;
	}
	.comment-imgs {
		display: flex;
		gap: 8rpx;
	}
	.comment-img {
		width: 80rpx;
		height: 80rpx;
		border-radius: 8rpx;
		object-fit: cover;
	}
</style>
