<template>
	<view class="container page-container">
		<view class="custom-nav-bar">
      <view class="nav-title">直营店铺</view>
    </view>
		<!-- 搜索 -->
		<view class="search-container">
			<view class="search-input">
				<uni-icons type="search" size="20" color="#000"></uni-icons>
				<input type="text" placeholder="搜索店铺名称" v-model="searchValue" @confirm="search" @input="onSearchInput">
				<uni-icons v-if="searchValue" type="clear" size="20" color="#999" @click="clearSearch"></uni-icons>
			</view>
		</view>

		<!-- 搜索历史 -->
		<!-- <view class="search-history" v-if="searchHistory.length > 0 && searchValue === '' && !isSearching">
			<view class="history-header">
				<text class="history-title">搜索历史</text>
				<uni-icons type="trash" size="18" color="#999" @click="clearSearchHistory"></uni-icons>
			</view>
			<view class="history-list">
				<view class="history-item" v-for="(item, index) in searchHistory" :key="index" @click="useHistorySearch(item)">
					<uni-icons type="search" size="16" color="#999"></uni-icons>
					<text class="history-text">{{ item }}</text>
				</view>
			</view>
		</view> -->

		<!-- 轮播图 -->
		<view class="banner-swiper" v-if="!isSearching && !searchValue">
			<swiper class="swiper" :indicator-dots="false" :autoplay="true" :interval="5000" :duration="500" circular @change="onSwiperChange">
				<swiper-item v-for="(banner, index) in bannerList" :key="index">
					<image :src="banner.imageUrl" class="swiper-image" mode="aspectFill" @click="handleBannerClick(banner)"></image>
				</swiper-item>
			</swiper>
			<view class="swiper-indicator" v-if="bannerList.length > 0">
				<view 
					v-for="(item, index) in bannerList" 
					:key="index" 
					class="indicator-dot" 
					:class="{ active: index === currentSwiperIndex }"
				></view>
			</view>
		</view>

		<!-- 搜索结果为空提示 -->
		<view class="empty-result" v-if="searchValue && shops.length === 0 && !isSearching">
			<image class="empty-image" src="/static/images/empty-search.png" mode="aspectFit"></image>
			<text class="empty-text">没有找到相关店铺</text>
		</view>
		
		<!-- 加载中提示 -->
		<view class="loading-container" v-if="isSearching">
			<uni-icons type="spinner-cycle" size="30" color="#4CAF50"></uni-icons>
			<text class="loading-text">正在搜索...</text>
		</view>

		<!-- 店铺列表 -->
		<view class="shop-list" v-if="shops.length > 0">
			<view class="shop-card" v-for="(shop, index) in shops" :key="index">
				<!-- 店铺头部信息 -->
				<view class="shop-header" @click="goToStore(shop)">
					<view class="shop-logo-container">
						<image class="shop-logo" :src="shop.imgUrl" mode="aspectFill"></image>
					</view>
					<view class="shop-info">
						<text class="shop-name">{{ shop.merchantName }}</text>
						<view class="shop-meta">
							<text>{{ shop.reviews || 14 }}条 &nbsp; ¥{{ shop.avgConsumption || 100 }}/人</text>
						</view>
						<view class="shop-category">
							<text>{{ shop.categoryDesc }} &nbsp; {{ shop.businessArea }}</text>
						</view>
						<view class="shop-rank">
							<text class="rank-badge">榜</text>
							<text class="rank-text">{{ shop.rankingInfo }}</text>
							<text class="rank-mt">秒提</text>
							<text class="rank-mt">在线点单</text>
						</view>
					</view>
					<view class="shop-actions">
						<view class="to-store-btn">到店</view>
						<text v-if="location.longitude && location.latitude" class="distance">{{ formatDistanceNumber(shop.distance) }}km</text>
					</view>
				</view>

				<!-- 商品滚动列表 -->
				<!-- <view class="product-container" v-if="shop.products && shop.products.length">
					<view class="product-left-space"></view>
					<scroll-view class="product-scroll" scroll-x="true" show-scrollbar="false">
						<view class="product-scroll-inner">
							<view class="product-item" @click="goToProductDetails(product, shop)" v-for="(product, pIndex) in shop.products" :key="pIndex">
								<image class="product-image" :src="product.mainPic" mode="aspectFill"></image>
								<view class="product-name">{{ product.goodsName }}</view>
								<view class="price-container">
									<text class="new-price">¥{{ product.price }}</text>
									<text class="old-price">¥{{ product.discountPrice }}</text>
								</view>
							</view>
						</view>
					</scroll-view>
				</view> -->

				<!-- 优惠活动区域 -->
				<!-- <view class="promo-section" v-if="shop.promos && shop.promos.length">
					<view class="countdown" v-if="shop.countdown">
						<text class="countdown-time">{{ shop.countdown }}</text>
					</view>
					<view class="promo-list-container">
						<view class="promo-list" :class="{'promo-expanded': isPromoExpanded(shop)}">
							<view class="promo-item" v-for="(promo, pIndex) in shop.promos" :key="pIndex">
								<view class="promo-left">
									<text class="promo-price-tag">惠</text>
									<text class="promo-price-value">¥{{ promo.price }}</text>
									<text class="promo-desc">{{ promo.text }}</text>
								</view>
								<text class="promo-sales">年售{{ promo.sale_info }}</text>
							</view>
						</view>
						<view class="more-promos" @click="togglePromoExpand(shop)" v-if="shop.promos && shop.promos.length > 1">
							<text>{{ isPromoExpanded(shop) ? '收起优惠' : `更多优惠(${shop.more_promos_count}个)` }}</text>
							<uni-icons :type="isPromoExpanded(shop) ? 'top' : 'bottom'" size="12" color="#999" />
						</view>
					</view>
				</view> -->
				<view class="border-line" v-if="index != shops.length - 1"></view>
			</view>
			
			<!-- 底部加载状态 -->
			<view class="loading-more" v-if="shops.length > 0">
				<view v-if="isLoadingMore" class="loading-more-content">
					<uni-icons type="spinner-cycle" size="20" color="#999"></uni-icons>
					<text class="loading-more-text">加载中...</text>
				</view>
				<text v-else-if="!hasMore" class="no-more-text">没有更多店铺了</text>
				<view v-else class="loading-more-content">
					<text class="loading-more-text">上拉加载更多</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		shopApi
	} from '@/common/api'
	export default {
		// 启用下拉刷新
		enablePullDownRefresh: true,
		// 设置下拉刷新的样式
		backgroundColor: "#f8f8f8",
		backgroundTextStyle: "dark",
		
		data() {
			return {
				shops: [],
				searchValue: '', // 搜索关键词
				isSearching: false, // 是否正在搜索
				searchHistory: [], // 搜索历史
				searchTimer: null, // 搜索防抖定时器
				expandedShops: {}, // 存储已展开优惠的店铺ID
				bannerList: [
					{
						id: 1,
						imageUrl: 'http://************:9000/merchant/xbk.jpg',
						linkUrl: '',
						title: '小白卡'
					},
					{
						id: 2,
						imageUrl: 'http://************:9000/merchant/mdl.jpg',
						linkUrl: '',
						title: '限时优惠'
					},
					{
						id: 3,
						imageUrl: 'http://************:9000/merchant/hdl.jpg',
						linkUrl: '',
						title: '新店开业'
					},
					{
						id: 4,
						imageUrl: 'http://************:9000/merchant/blf.jpg',
						linkUrl: '',
						title: '新店开业'
					},
					{
						id: 5,
						imageUrl: 'http://************:9000/merchant/hmsx.jpg',
						linkUrl: '',
						title: '新店开业'
					}
				], // 轮播图列表
				currentSwiperIndex: 0,
				// 分页相关
				pageNum: 1,
				pageSize: 10,
				hasMore: true, // 是否还有更多数据
				isLoadingMore: false, // 是否正在加载更多
				location: {
					longitude: null,
					latitude: null
				}
			}
		},
		onLoad() {
			uni.getLocation({
				type: 'gcj02',
				success: res => {
					console.log('经纬度', res)
					this.location.longitude = res.longitude
					this.location.latitude = res.latitude
					this.getShopsList();
				},
				fail: err => {
					console.log('获取经纬度失败', err)
					this.getShopsList();
				}
			})
			// 从本地存储获取搜索历史
			const history = uni.getStorageSync('shop_search_history');
			if (history) {
				this.searchHistory = JSON.parse(history);
			}
			
			// 获取轮播图数据
			this.getBannerList();
		},
		// 下拉刷新
		onPullDownRefresh() {
			console.log('下拉刷新');
			// 清空搜索
			this.searchValue = '';
			// 重置分页
			this.pageNum = 1;
			this.hasMore = true;
			// 重新获取店铺列表
			uni.getLocation({
				type: 'gcj02',
				success: res => {
					console.log('经纬度', res)
					this.location.longitude = res.longitude
					this.location.latitude = res.latitude
					this.refreshShopsList();
				},
				fail: err => {
					console.log('获取经纬度失败', err)
					this.refreshShopsList();
				}
			})
		},
		// 上拉加载更多
		onReachBottom() {
			console.log('触底加载更多');
			// 如果正在搜索或加载更多，或者没有更多数据，则不处理
			if (this.isSearching || this.isLoadingMore || !this.hasMore) {
				return;
			}
			
			// 如果有搜索关键词，则加载更多搜索结果
			if (this.searchValue.trim()) {
				this.loadMoreSearchResults();
			} else {
				// 否则加载更多店铺列表
				this.loadMoreShops();
			}
		},
		methods: {
			/**
			 * 处理距离数字，保留一位小数并进位
			 * @param {string|number} distance - 距离值
			 * @return {string} - 格式化后的距离字符串
			 */
			formatDistanceNumber(distance) {
				if (!distance && distance !== 0) return '0.0';
				
				// 将距离转换为数字
				let numDistance = parseFloat(distance);
				
				// 如果不是有效数字，返回默认值
				if (isNaN(numDistance)) return '0.0';
				
				// 保留一位小数并进位（向上取整到一位小数）
				numDistance = Math.ceil(numDistance * 10) / 10;
				
				// 格式化为一位小数
				return numDistance.toFixed(1);
			},
			
			// 加载更多店铺
			loadMoreShops() {
				this.isLoadingMore = true;
				// 页码加1
				this.pageNum++;
				
				// 显示加载中
				uni.showLoading({
					title: '加载中...'
				});
		
				// 调用API获取更多数据
				shopApi.getShopsList({
					page: this.pageNum,
					pageSize: this.pageSize,
					orderByColumn: 'createTime',
					isAsc: 'asc',
					userLatitude: this.location.longitude,
					userLongitude: this.location.latitude
				}).then(res => {
					// 如果返回的数据少于pageSize，说明没有更多数据了
					if (res.data.length < this.pageSize) {
						this.hasMore = false;
					}
					
					// 检查是否有新数据
					if (res.data.length === 0) {
						this.hasMore = false;
						uni.hideLoading();
						return;
					}
					
					// 检查是否有重复数据
					const newShops = res.data.filter(newShop => {
						// 检查新店铺是否已存在于当前列表中
						return !this.shops.some(existingShop => existingShop.id === newShop.id);
					});
					
					// 如果过滤后没有新数据，说明全是重复数据，不再加载更多
					if (newShops.length === 0) {
						this.hasMore = false;
						uni.hideLoading();
						uni.showToast({
							title: '已加载全部店铺',
							icon: 'none'
						});
						return;
					}
					
					// 将新数据添加到列表末尾
					this.shops = [...this.shops, ...newShops];
					
					this.isLoadingMore = false;
					uni.hideLoading();
				}).catch(err => {
					console.error('加载更多失败:', err);
					this.isLoadingMore = false;
					uni.hideLoading();
					uni.showToast({
						title: '加载失败，请重试',
						icon: 'none'
					});
					// 加载失败时，页码减回去
					this.pageNum--;
				});
			},
			
			// 加载更多搜索结果
			loadMoreSearchResults() {
				this.isLoadingMore = true;
				// 页码加1
				this.pageNum++;
				
				// 显示加载中
				uni.showLoading({
					title: '加载中...'
				});
				
				// 调用搜索API
				shopApi.getShopsList({
					page: this.pageNum,
					pageSize: this.pageSize,
					merchantName: this.searchValue.trim(),
					userLatitude: this.location.longitude,
					userLongitude: this.location.latitude
				}).then(res => {
					// 如果返回的数据少于pageSize，说明没有更多数据了
					if (res.data.length < this.pageSize) {
						this.hasMore = false;
					}
					
					// 检查是否有新数据
					if (res.data.length === 0) {
						this.hasMore = false;
						uni.hideLoading();
						return;
					}
					
					// 检查是否有重复数据
					const newShops = res.data.filter(newShop => {
						// 检查新店铺是否已存在于当前列表中
						return !this.shops.some(existingShop => existingShop.id === newShop.id);
					});
					
					// 如果过滤后没有新数据，说明全是重复数据，不再加载更多
					if (newShops.length === 0) {
						this.hasMore = false;
						uni.hideLoading();
						uni.showToast({
							title: '已加载全部店铺',
							icon: 'none'
						});
						return;
					}
					
					// 将新数据添加到列表末尾
					this.shops = [...this.shops, ...newShops];
					
					this.isLoadingMore = false;
					uni.hideLoading();
				}).catch(err => {
					console.error('加载更多搜索结果失败:', err);
					this.isLoadingMore = false;
					uni.hideLoading();
					uni.showToast({
						title: '加载失败，请重试',
						icon: 'none'
					});
					// 加载失败时，页码减回去
					this.pageNum--;
				});
			},
			
			// 刷新店铺列表
			refreshShopsList() {
				this.isSearching = true;
				
				// 调用API获取最新数据
				shopApi.getShopsList({
					page: 1,
					pageSize: this.pageSize,
					orderByColumn: 'createTime',
					isAsc: 'asc',
					userLatitude: this.location.longitude,
					userLongitude: this.location.latitude
				}).then(res => {
					this.shops = res.data;
					this.isSearching = false;
					
					// 如果返回的数据少于pageSize，说明没有更多数据了
					if (res.data.length < this.pageSize) {
						this.hasMore = false;
					} else {
						// 检查是否真的有更多数据
						shopApi.getShopsList({
							page: 2,
							pageSize: this.pageSize,
							orderByColumn: 'createTime',
							isAsc: 'asc',
							userLatitude: this.location.longitude,
							userLongitude: this.location.latitude
						}).then(nextPageRes => {
							// 检查第二页是否有新数据（不是重复的）
							const hasNewData = nextPageRes.data.some(newShop => {
								return !this.shops.some(existingShop => existingShop.id === newShop.id);
							});
							
							// 如果第二页没有新数据，则标记为没有更多数据
							if (!hasNewData) {
								this.hasMore = false;
							}
						}).catch(() => {
							// 出错时假设没有更多数据
							this.hasMore = false;
						});
					}
					
					// 停止下拉刷新动画
					uni.stopPullDownRefresh();
					
					// 显示刷新成功提示
					// uni.showToast({
					// 	title: '刷新成功',
					// 	icon: 'success',
					// 	duration: 1500
					// });
				}).catch(err => {
					console.error('刷新失败:', err);
					this.isSearching = false;
					
					// 停止下拉刷新动画
					uni.stopPullDownRefresh();
					
					// 显示刷新失败提示
					uni.showToast({
						title: '刷新失败，请重试',
						icon: 'none',
						duration: 1500
					});
				});
			},
			goToStore(shop) {
				uni.setStorageSync('storeInfo', shop);
				uni.navigateTo({
					url: `/pages/shopping/storeDetails?id=${shop.id}`
				});
			},
			goToSearch() {
				uni.navigateTo({
					url: '/pages/shopping/search'
				});
			},
			goToProductDetails(item, shop) {
				uni.setStorageSync('storeInfo', shop);
				uni.navigateTo({
					url: `/pages/shopping/productDetails?merchantId=${shop.id}&productId=${item.id}`
				});
			},
			/**
			 * 输入搜索关键词时触发
			 */
			onSearchInput() {
				// 清除之前的定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}
				
				// 如果搜索框为空，恢复默认列表
				if (!this.searchValue.trim()) {
					this.getShopsList();
					return;
				}
				
				// 设置防抖定时器，500ms后执行搜索
				this.searchTimer = setTimeout(() => {
					this.search();
				}, 500);
			},
			/**
			 * 搜索店铺
			 */
			search() {
				if (!this.searchValue.trim()) {
					// 如果搜索框为空，获取所有店铺列表
					this.getShopsList();
					return;
				}
				
				// 保存搜索历史
				this.saveSearchHistory(this.searchValue.trim());
				
				// 重置分页
				this.pageNum = 1;
				this.hasMore = true;
				
				this.isSearching = true;
				uni.showLoading({
					title: '搜索中...'
				});
				
				// 调用搜索API
				shopApi.getShopsList({
					page: 1,
					pageSize: this.pageSize,
					merchantName: this.searchValue.trim(),
					userLatitude: this.location.longitude,
					userLongitude: this.location.latitude
				}).then(res => {
					this.shops = res.data;
					
					// 如果返回的数据少于pageSize，说明没有更多数据了
					if (res.data.length < this.pageSize) {
						this.hasMore = false;
					}
					
					// 检查是否真的有更多数据
					shopApi.getShopsList({
						page: 2,
						pageSize: this.pageSize,
						merchantName: this.searchValue.trim(),
						userLatitude: this.location.longitude,
						userLongitude: this.location.latitude
					}).then(nextPageRes => {
						// 检查第二页是否有新数据（不是重复的）
						const hasNewData = nextPageRes.data.some(newShop => {
							return !this.shops.some(existingShop => existingShop.id === newShop.id);
						});
						
						// 如果第二页没有新数据，则标记为没有更多数据
						if (!hasNewData) {
							this.hasMore = false;
						}
					}).catch(() => {
						// 出错时假设没有更多数据
						this.hasMore = false;
					});
					
					this.isSearching = false;
					uni.hideLoading();
				}).catch(err => {
					console.error('搜索失败:', err);
					this.isSearching = false;
					uni.hideLoading();
					uni.showToast({
						title: '搜索失败，请重试',
						icon: 'none'
					});
				});
			},
			async getShopsList() {
				// 重置分页
				this.pageNum = 1;
				this.hasMore = true;
				
				const res = await shopApi.getShopsList({
					page: this.pageNum,
					pageSize: this.pageSize,
					orderByColumn: 'createTime',
					isAsc: 'asc',
					userLatitude: this.location.longitude,
					userLongitude: this.location.latitude
				})
				this.shops = res.data;
				
				// 如果返回的数据少于pageSize，或者总数据量很少，说明没有更多数据了
				if (res.data.length < this.pageSize || res.total <= this.pageSize) {
					this.hasMore = false;
				}
				
				// 尝试获取第二页数据来检查是否真的有更多数据
				try {
					const nextPageRes = await shopApi.getShopsList({
						page: this.pageNum + 1,
						pageSize: this.pageSize,
						orderByColumn: 'createTime',
						isAsc: 'asc',
						userLatitude: this.location.longitude,
						userLongitude: this.location.latitude
					});
					
					// 检查第二页是否有新数据（不是重复的）
					const hasNewData = nextPageRes.data.some(newShop => {
						return !this.shops.some(existingShop => existingShop.id === newShop.id);
					});
					
					// 如果第二页没有新数据，则标记为没有更多数据
					if (!hasNewData) {
						this.hasMore = false;
					}
				} catch (err) {
					console.error('检查更多数据失败:', err);
					// 出错时假设没有更多数据
					this.hasMore = false;
				}
				
				// // 为每个店铺添加模拟的商品数据和优惠活动数据
				// this.shops.forEach((shop, index) => {
				// 	// 添加模拟商品数据
				// 	if(index == 0){
				// 		shop.products = [
				// 			{
				// 					"pageNum": null,
				// 					"pageSize": null,
				// 					"orderByColumn": null,
				// 					"isAsc": null,
				// 					"createBy": "system",
				// 					"createTime": "2025-06-23 10:36:34",
				// 					"updateBy": "system",
				// 					"updateTime": "2025-07-05 21:31:41",
				// 					"remark": null,
				// 					"id": 1,
				// 					"goodsCode": "GOODS001",
				// 					"goodsName": "新鲜三文鱼",
				// 					"goodsType": 1,
				// 					"category": "生鲜海产",
				// 					"description": "挪威进口新鲜三文鱼，肉质鲜美，营养丰富",
				// 					"detailContent": "挪威进口新鲜三文鱼，采用冷链运输，保证新鲜度。富含优质蛋白质和Omega-3脂肪酸，适合刺身、煎烤等多种烹饪方式。",
				// 					"brand": "盒马",
				// 					"origin": "挪威",
				// 					"merchantCode": "MERCHANT005",
				// 					"price": 88,
				// 					"discountPrice": 78,
				// 					"status": 1,
				// 					"dataStatus": 1,
				// 					"mainPic": "http://************:9000/product/sanwenyu.jpg"
				// 			},
				// 			{
				// 					"pageNum": null,
				// 					"pageSize": null,
				// 					"orderByColumn": null,
				// 					"isAsc": null,
				// 					"createBy": "system",
				// 					"createTime": "2025-06-23 10:36:34",
				// 					"updateBy": "system",
				// 					"updateTime": "2025-07-05 21:31:49",
				// 					"remark": null,
				// 					"id": 2,
				// 					"goodsCode": "GOODS002",
				// 					"goodsName": "有机蔬菜礼盒",
				// 					"goodsType": 1,
				// 					"category": "有机蔬菜",
				// 					"description": "精选有机蔬菜组合装，健康营养",
				// 					"detailContent": "精选多种有机认证蔬菜，包含时令蔬菜搭配，无农药残留，口感清甜。适合全家日常食用，营养均衡。",
				// 					"brand": "盒马",
				// 					"origin": "山东",
				// 					"merchantCode": "MERCHANT005",
				// 					"price": 45,
				// 					"discountPrice": 39,
				// 					"status": 1,
				// 					"dataStatus": 1,
				// 					"mainPic": "http://************:9000/product/sucai.jpeg"
				// 			},
				// 			{
				// 					"pageNum": null,
				// 					"pageSize": null,
				// 					"orderByColumn": null,
				// 					"isAsc": null,
				// 					"createBy": "system",
				// 					"createTime": "2025-06-23 10:36:34",
				// 					"updateBy": "system",
				// 					"updateTime": "2025-07-05 21:31:41",
				// 					"remark": null,
				// 					"id": 1,
				// 					"goodsCode": "GOODS001",
				// 					"goodsName": "新鲜三文鱼",
				// 					"goodsType": 1,
				// 					"category": "生鲜海产",
				// 					"description": "挪威进口新鲜三文鱼，肉质鲜美，营养丰富",
				// 					"detailContent": "挪威进口新鲜三文鱼，采用冷链运输，保证新鲜度。富含优质蛋白质和Omega-3脂肪酸，适合刺身、煎烤等多种烹饪方式。",
				// 					"brand": "盒马",
				// 					"origin": "挪威",
				// 					"merchantCode": "MERCHANT005",
				// 					"price": 88,
				// 					"discountPrice": 78,
				// 					"status": 1,
				// 					"dataStatus": 1,
				// 					"mainPic": "http://************:9000/product/sanwenyu.jpg"
				// 			},
				// 			{
				// 					"pageNum": null,
				// 					"pageSize": null,
				// 					"orderByColumn": null,
				// 					"isAsc": null,
				// 					"createBy": "system",
				// 					"createTime": "2025-06-23 10:36:34",
				// 					"updateBy": "system",
				// 					"updateTime": "2025-07-05 21:31:49",
				// 					"remark": null,
				// 					"id": 2,
				// 					"goodsCode": "GOODS002",
				// 					"goodsName": "有机蔬菜礼盒",
				// 					"goodsType": 1,
				// 					"category": "有机蔬菜",
				// 					"description": "精选有机蔬菜组合装，健康营养",
				// 					"detailContent": "精选多种有机认证蔬菜，包含时令蔬菜搭配，无农药残留，口感清甜。适合全家日常食用，营养均衡。",
				// 					"brand": "盒马",
				// 					"origin": "山东",
				// 					"merchantCode": "MERCHANT005",
				// 					"price": 45,
				// 					"discountPrice": 39,
				// 					"status": 1,
				// 					"dataStatus": 1,
				// 					"mainPic": "http://************:9000/product/sucai.jpeg"
				// 			},
				// 			{
				// 					"pageNum": null,
				// 					"pageSize": null,
				// 					"orderByColumn": null,
				// 					"isAsc": null,
				// 					"createBy": "system",
				// 					"createTime": "2025-06-23 10:36:34",
				// 					"updateBy": "system",
				// 					"updateTime": "2025-07-05 21:31:41",
				// 					"remark": null,
				// 					"id": 1,
				// 					"goodsCode": "GOODS001",
				// 					"goodsName": "新鲜三文鱼",
				// 					"goodsType": 1,
				// 					"category": "生鲜海产",
				// 					"description": "挪威进口新鲜三文鱼，肉质鲜美，营养丰富",
				// 					"detailContent": "挪威进口新鲜三文鱼，采用冷链运输，保证新鲜度。富含优质蛋白质和Omega-3脂肪酸，适合刺身、煎烤等多种烹饪方式。",
				// 					"brand": "盒马",
				// 					"origin": "挪威",
				// 					"merchantCode": "MERCHANT005",
				// 					"price": 88,
				// 					"discountPrice": 78,
				// 					"status": 1,
				// 					"dataStatus": 1,
				// 					"mainPic": "http://************:9000/product/sanwenyu.jpg"
				// 			},
				// 			{
				// 					"pageNum": null,
				// 					"pageSize": null,
				// 					"orderByColumn": null,
				// 					"isAsc": null,
				// 					"createBy": "system",
				// 					"createTime": "2025-06-23 10:36:34",
				// 					"updateBy": "system",
				// 					"updateTime": "2025-07-05 21:31:49",
				// 					"remark": null,
				// 					"id": 2,
				// 					"goodsCode": "GOODS002",
				// 					"goodsName": "有机蔬菜礼盒",
				// 					"goodsType": 1,
				// 					"category": "有机蔬菜",
				// 					"description": "精选有机蔬菜组合装，健康营养",
				// 					"detailContent": "精选多种有机认证蔬菜，包含时令蔬菜搭配，无农药残留，口感清甜。适合全家日常食用，营养均衡。",
				// 					"brand": "盒马",
				// 					"origin": "山东",
				// 					"merchantCode": "MERCHANT005",
				// 					"price": 45,
				// 					"discountPrice": 39,
				// 					"status": 1,
				// 					"dataStatus": 1,
				// 					"mainPic": "http://************:9000/product/sucai.jpeg"
				// 			}
				// 		];
				// 	}else{
				// 		// 添加模拟优惠活动数据
				// 		shop.promos = [
				// 			{
				// 				id: 1,
				// 				price: '148.0',
				// 				text: '[周末女神畅饮] 女生鸡尾酒调酒...',
				// 				sale_info: '90+'
				// 			},
				// 			{
				// 				id: 2,
				// 				price: '196.0',
				// 				text: '[周末帅哥畅饮] 帅哥鸡尾酒调酒...',
				// 				sale_info: '120+'
				// 			}
				// 		];
					// }
					
					
				// 	// 添加更多优惠数量和倒计时
				// 	shop.more_promos_count = Math.floor(Math.random() * 5) + 1;
				// 	shop.countdown = '剩余02:57:21';
				// });
			},
			clearSearch() {
				this.searchValue = '';
				this.getShopsList();
			},
			/**
			 * 保存搜索历史
			 * @param {string} keyword - 搜索关键词
			 */
			saveSearchHistory(keyword) {
				// 如果已存在相同关键词，先移除
				const index = this.searchHistory.indexOf(keyword);
				if (index > -1) {
					this.searchHistory.splice(index, 1);
				}
				
				// 添加到历史记录开头
				this.searchHistory.unshift(keyword);
				
				// 限制历史记录最多10条
				if (this.searchHistory.length > 10) {
					this.searchHistory.pop();
				}
				
				// 保存到本地存储
				uni.setStorageSync('shop_search_history', JSON.stringify(this.searchHistory));
			},
			/**
			 * 使用历史记录进行搜索
			 * @param {string} keyword - 搜索关键词
			 */
			useHistorySearch(keyword) {
				this.searchValue = keyword;
				this.search();
			},
			/**
			 * 清除搜索历史
			 */
			clearSearchHistory() {
				this.searchHistory = [];
				uni.removeStorageSync('shop_search_history');
				uni.showToast({
					title: '搜索历史已清除',
					icon: 'none'
				});
			},
			/**
			 * 获取轮播图数据
			 */
			getBannerList() {
				// 这里可以调用API获取轮播图数据
				// 目前使用本地数据，如果有API可以替换为API调用
				// shopApi.getBannerList().then(res => {
				//   this.bannerList = res.data;
				// });
			},
			/**
			 * 处理轮播图点击事件
			 */
			handleBannerClick(banner) {
				console.log('Banner clicked:', banner);
				if (banner.linkUrl) {
					// 如果有链接，跳转到对应页面
					uni.navigateTo({
						url: banner.linkUrl
					});
				} else if (banner.id) {
					// 如果有ID，可以跳转到详情页
					uni.navigateTo({
						url: `/pages/shopping/bannerDetail?id=${banner.id}`
					});
				}
			},
			onSwiperChange(e) {
				this.currentSwiperIndex = e.detail.current;
			},
			/**
			 * 切换优惠展开/收起状态
			 * @param {Object} shop - 店铺对象
			 */
			togglePromoExpand(shop) {
				// 如果店铺ID已存在，则删除（收起）；否则添加（展开）
				if (this.expandedShops[shop.id]) {
					this.$set(this.expandedShops, shop.id, false);
				} else {
					this.$set(this.expandedShops, shop.id, true);
				}
				
				// 强制更新视图
				this.$forceUpdate();
			},
			
			/**
			 * 判断店铺优惠是否已展开
			 * @param {Object} shop - 店铺对象
			 * @return {Boolean} - 是否已展开
			 */
			isPromoExpanded(shop) {
				return !!this.expandedShops[shop.id];
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		background-color: #f8f8f8;
		min-height: 100vh;
		overflow-x: hidden;
	}
	.nav-title{
		font-weight: bold;
	}
	
	.search-container {
		padding: 20rpx;
	}
	
	.search-input {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 50rpx;
		padding: 15rpx 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.search-input input {
		flex: 1;
		height: 60rpx;
		margin-left: 15rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.search-input uni-icons {
		margin-right: 0;
	}

	.banner-swiper {
		padding: 20rpx;
		position: relative;
	}
	
	.swiper {
		width: 100%;
		height: 300rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}
	
	.swiper-image {
		width: 100%;
		height: 100%;
	}
	
	/* 自定义指示点样式 */
	.uni-swiper-dot {
		width: 12rpx !important;
		height: 12rpx !important;
		border-radius: 50% !important;
		background-color: rgba(255, 255, 255, 0.5) !important;
		margin: 0 8rpx !important;
	}
	
	.uni-swiper-dot-active {
		width: 24rpx !important;
		border-radius: 12rpx !important;
		background-color: #fff !important;
	}

	.shop-list {
		padding: 20rpx 20rpx 140rpx 20rpx;
		background: #fff;
		.border-line{
			background: #D1D1D1;
			height: 2rpx;
			margin-left: 165rpx;
		}
	}

	.shop-card {
		background-color: #fff;
		padding: 24rpx 24rpx 0 24rpx;
		overflow: hidden; /* 确保内部元素不会溢出 */
	}

	.shop-header {
		display: flex;
		align-items: flex-start;
		margin-bottom: 20rpx;
		position: relative;
	}

	.shop-logo {
		width: 145rpx;
		height: 145rpx;
		border-radius: 12rpx;
		flex-shrink: 0;
		margin-right: 0;
	}
	
	.shop-logo-container {
		margin-right: 20rpx;
		width: 145rpx;
		height: 145rpx;
	}
	
	.shop-info {
		flex: 1;
		min-width: 0;
	}

	.shop-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 3rpx;
	}

	.shop-meta {
		display: flex;
		align-items: center;
		font-weight: 400;
		font-size: 23rpx;
		margin-bottom: 3rpx;
		color: #000000;
	}
	
	.shop-category {
		font-weight: 400;
		font-size: 23rpx;
		color: #666666;
		margin-bottom: 3rpx;
	}

	.shop-rank {
		display: flex;
		align-items: center;
	}

	.rank-badge {
		padding: 2rpx 8rpx;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 19rpx;
		color: #7D541F;
		background: linear-gradient( 45deg, #CA914A 0%, #EBD39F 100%);
		order-radius: 2rpx 0rpx 0rpx 2rpx;
	}

	.rank-text {
		background: linear-gradient( 45deg, rgba(202, 145, 74, 0.2) 0%, rgba(235, 211, 159, 0.2) 100%);
		width: 193rpx;
		height: 27rpx;
		font-weight: 400;
		font-size: 19rpx;
		line-height: 27rpx;
		color: #333333;
		padding: 2rpx 2rpx 2rpx 10rpx;
	}
	.rank-mt {
		background: rgba(0, 0, 0, 0.05);
		border-radius: 2rpx;
		padding: 2rpx 2rpx 2rpx 2rpx;		
		font-weight: 400;
		font-size: 19rpx;
		color: #666666;
		margin-left: 10rpx;
	}

	.shop-actions {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		margin-left: 20rpx;
		position: absolute;
		right: 0;
		top: 0;
	}

	.to-store-btn {
		background: #66D47E;
		color: #FFFFFF;
		padding: 4rpx 10rpx;
		border-radius: 2rpx;
		font-size: 20rpx;
		/* font-weight: bold; */
		margin-bottom: 8rpx;
	}

	.distance {
		font-size: 23rpx;
		color: #000;
		font-weight: 400;
	}

	/* Product Scroll */
	.product-container {
		margin-top: 20rpx;
		margin-bottom: 10rpx;
		display: flex;
		position: relative;
	}
	
	.product-left-space {
		width: 165rpx;
		flex-shrink: 0;
	}
	
	.product-scroll {
		flex: 1;
		white-space: nowrap;
		overflow-x: auto;
		/* 隐藏滚动条 */
		scrollbar-width: none; /* Firefox */
		-ms-overflow-style: none; /* IE and Edge */
		// margin-right: -24rpx; /* 让右侧内容可以滚动到容器外 */
		padding-right: 24rpx; /* 右侧内边距 */
	}
	
	.product-scroll::-webkit-scrollbar {
		display: none; /* Chrome, Safari, Opera */
	}
	
	.product-scroll-inner {
		display: inline-flex;
	}

	.product-item {
		display: inline-block;
		width: 145rpx;
		margin-right: 20rpx;
		vertical-align: top;
	}
	
	.product-item:last-child {
		margin-right: 0;
	}

	.product-image {
		width: 145rpx;
		height: 145rpx;
		background: #D8D8D8;
		border-radius: 12rpx;
	}

	.product-name {
		font-weight: 400;
		font-size: 20rpx;
		color: #000000;
	}

	.price-container {
		margin-top: 4rpx;
	}

	.new-price {
		font-weight: 500;
		font-size: 20rpx;
		color: #F7532E;
	}

	.old-price {
		text-decoration: line-through;
		margin-left: 10rpx;
		font-weight: 400;
		font-size: 20rpx;
		color: #999999;
	}

	/* Promo Section */
	.promo-section {
		display: flex;
		align-items: center;
		.countdown {
			min-width: 165rpx;
			max-width: 165rpx;
			.countdown-time {
				background: rgba(247, 83, 46, 0.1);
				border-radius: 2rpx;
				padding: 2rpx 2rpx 2rpx 2rpx;
				color: #F7532E;
				font-weight: 400;
				font-size: 19rpx;
			}
		}
	}

	
	.promo-list-container{
		width: calc(100% - 165rpx);
	}
	
	.promo-list {
		max-height: 40rpx; /* 只显示一个优惠项目的高度 */
		overflow: hidden;
		transition: max-height 0.3s ease;
	}
	
	.promo-expanded {
		max-height: 500rpx !important; /* 足够大的高度以显示所有优惠 */
	}

	.promo-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 12rpx;
		width: 100%;
	}
	
	.promo-sales {
		color: #999;
		min-width: 100rpx;
		font-weight: 400;
		font-size: 19rpx;
		color: #999999;
		text-align: right;
		flex-shrink: 0;
	}

	.promo-left {
		display: flex;
		align-items: center;
		flex: 1;
		overflow: hidden;
		margin-right: 10rpx;
	}

	.promo-price-tag {
		background-color: rgba(247, 83, 46, 0.1);
		font-weight: 400;
		font-size: 20rpx;
		color: #F7532E;
		padding: 4rpx;
		border-radius: 4rpx;
		margin-right: 8rpx;
		flex-shrink: 0;
	}
	
	.promo-price-value {
		font-weight: 500;
		font-size: 20rpx;
		color: #F7532E;
		margin-right: 8rpx;
	}

	.promo-desc {
		font-weight: 400;
		font-size: 20rpx;
		color: #000000;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		flex: 1;
	}
	
	.more-promos {
		font-size: 24rpx;
		color: #999;
		display: flex;
		align-items: center;
		justify-content: center;
		padding-top: 10rpx;
		padding-bottom: 12rpx;
		cursor: pointer;
	}
	
	.more-promos text {
		margin-right: 4rpx;
	}

	.empty-result {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 40rpx;
	}

	.empty-image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 20rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}

	/* Search History */
	.search-history {
		margin: 20rpx;
		padding: 20rpx;
		background-color: #fff;
		border-radius: 16rpx;
	}

	.history-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16rpx;
	}

	.history-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}

	.history-list {
		display: flex;
		flex-direction: column;
	}

	.history-item {
		display: flex;
		align-items: center;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.history-item:last-child {
		border-bottom: none;
	}

	.history-text {
		font-size: 28rpx;
		color: #333;
		margin-left: 10rpx;
	}

	/* Loading Container */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 40rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #999;
		margin-top: 20rpx;
	}

	.swiper-indicator {
		position: absolute;
		bottom: 20rpx;
		left: 0;
		right: 0;
		padding: 15rpx 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
	}
	
	.indicator-dot {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		background-color: rgba(0, 0, 0, 0.5);
		margin: 0 8rpx;
		transition: all 0.3s;
	}
	
	.indicator-dot.active {
		width: 24rpx;
		border-radius: 12rpx;
		background-color: #000;
	}
	
	/* 底部加载更多 */
	.loading-more {
		text-align: center;
		padding: 30rpx 0;
	}
	
	.loading-more-content {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.loading-more-text {
		font-size: 24rpx;
		color: #999;
		margin-left: 10rpx;
	}
	
	.no-more-text {
		font-size: 24rpx;
		color: #999;
	}
</style>

