<template>
	<view class="container">
		<!-- 顶部标题和搜索栏 -->
		<view class="header">
			<view class="search-bar" @click="goToSearch">
				<uni-icons type="search" size="20" color="#ccc" />
				<input class="search-input" placeholder="输入商品名称/店铺名称" disabled />
			</view>
		</view>

		<!-- 分类标签 -->
		<scroll-view class="tabs" scroll-x show-scrollbar>
			<view class="tabs-inner">
				<view
					v-for="(tab, idx) in tabs"
					:key="tab"
					:class="['tab', idx === activeTab ? 'active' : '']"
					@click="activeTab = idx"
				>
					{{ tab }}
				</view>
			</view>
		</scroll-view>

		<!-- 商品列表 -->
		<view class="goods-list">
			<view class="goods-card" v-for="(item, idx) in goods" :key="idx" @click="goToProductDetails(item)">
				<view class="goods-img">商品图片</view>
				<view class="goods-title">{{ item.title }}</view>
				<view class="goods-tags">
					<text class="tag" v-if="item.tag">{{ item.tag }}</text>
				</view>
				<view class="goods-info">
					<text class="price">￥{{ item.price }}</text>
					<text class="old-price" v-if="item.oldPrice">￥{{ item.oldPrice }}</text>
				</view>
			</view>
		</view>

		<!-- 底部购物车 -->
		<view class="cart-float">
			<uni-icons type="cart" size="36" />
			<view class="cart-badge">99</view>
			<text class="cart-text">购物车</text>
		</view>
	</view>
</template>

<script>
	import { shopApi } from '@/common/api'
	export default {
		data() {
			return {
				tabs: [
					'推荐', '超市用品', '数码电子', '休闲娱乐', '母婴',
					'美妆', '家电', '服饰', '运动', '汽车', '图书', '家居'
				],
				activeTab: 0,
				goods: [
					{ title: '服务名称服务名称服务名称服务名称', price: 1099, oldPrice: 2000, tag: '推荐' },
					{ title: '服务名称服务名称服务名称服务名称', price: 1099, oldPrice: 2000, tag: '' },
					{ title: '服务名称服务名称服务名称服务名称', price: 1099, oldPrice: 2000, tag: '推荐' },
					{ title: '服务名称服务名称服务名称服务名称', price: 1099, oldPrice: 2000, tag: '' }
				]
			}
		},
		onLoad() {
			this.getShopsList()
		},
		methods: {
			goToSearch() {
				uni.navigateTo({ url: '/pages/shopping/search' });
			},
			goToProductDetails(item) {
				uni.navigateTo({ url: `/pages/shopping/productDetails?id=${item.id}` });
			},
			async getShopsList() {
				const res = await shopApi.getShopsList({
					page: 1,
					pageSize: 10
				})
			}
		}
	}
</script>

<style>
	.container {
		background: #fff;
		min-height: 100vh;
		padding-bottom: 60rpx;
		overflow-x: hidden;
	}
	.header {
		padding: 20rpx 20rpx 0 20rpx;
		background: #fff;
	}
	.title {
		font-size: 36rpx;
		font-weight: bold;
		text-align: center;
		display: block;
		margin-bottom: 20rpx;
	}
	.search-bar {
		display: flex;
		align-items: center;
		background: #f5f5f5;
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
	}
	.search-input {
		flex: 1;
		border: none;
		background: transparent;
		margin-left: 10rpx;
		font-size: 28rpx;
	}
	.tabs {
		background: #fff;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
	}
	.tabs-inner {
		display: flex;
		flex-direction: row;
	}
	.tab {
		padding: 20rpx 30rpx 10rpx 30rpx;
		font-size: 28rpx;
		color: #666;
		border-bottom: 4rpx solid transparent;
		white-space: nowrap;
	}
	.tab.active {
		color: #000;
		font-weight: bold;
		border-bottom: 4rpx solid #000;
	}
	.goods-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0 20rpx;
	}
	.goods-card {
		width: 48%;
		background: #f5f5f5;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-sizing: border-box;
	}
	.goods-img {
		width: 100%;
		height: 180rpx;
		background: #bdbdbd;
		color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
		margin-bottom: 16rpx;
		font-size: 28rpx;
	}
	.goods-title {
		font-size: 26rpx;
		color: #222;
		margin-bottom: 8rpx;
		height: 70rpx;
		overflow: hidden;
	}
	.goods-tags .tag {
		background: #ffb74d;
		color: #fff;
		font-size: 20rpx;
		border-radius: 8rpx;
		padding: 2rpx 10rpx;
		margin-bottom: 8rpx;
		display: inline-block;
	}
	.goods-info {
		display: flex;
		align-items: center;
		margin-top: 8rpx;
	}
	.price {
		color: #ff9800;
		font-size: 28rpx;
		font-weight: bold;
		margin-right: 10rpx;
	}
	.old-price {
		color: #bdbdbd;
		font-size: 22rpx;
		text-decoration: line-through;
	}
	.cart-float {
		position: fixed;
		left: 32rpx;
		bottom: 48rpx;
		width: 90rpx;
		height: 90rpx;
		/* background: #fff; */
		border-radius: 50%;
		/* box-shadow: 0 2rpx 16rpx #eee; */
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		z-index: 100;
	}
	.cart-float .cart-badge {
		position: absolute;
		top: -10rpx;
		right: 12rpx;
		background: #ff3b30;
		color: #fff;
		border-radius: 50%;
		font-size: 20rpx;
		width: 32rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 101;
	}
	.cart-float .cart-text {
		margin-top: 8rpx;
		font-size: 22rpx;
		color: #333;
	}
</style>
