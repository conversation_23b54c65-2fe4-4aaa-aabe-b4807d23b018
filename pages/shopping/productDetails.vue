<template>
	<view class="product-detail-container page-container">
		<view class="custom-nav-bar">
      <view class="nav-left" @tap="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
		</view>
		<!-- 顶部图片轮播 -->
		<view class="carousel-box">
			<swiper class="carousel" indicator-dots autoplay circular @change="onSwiperChange">
				<swiper-item>
					<image :src="goodDetail.mainPic" class="carousel-img" mode="aspectFill" />
				</swiper-item>
			</swiper>
			<view class="carousel-indicator">{{ current + 1 }}/{{ 1 }}</view>
		</view>

		<!-- 价格、销量、标题、描述 -->
		<view class="main-info">
			<view class="price-row">
				<text class="price">￥{{ (goodDetail.discountPrice || 0) / 100 }}</text>
				<text class="old-price">￥{{ (goodDetail.price || 0) / 100 }}</text>
				<text class="sold">已售{{ goodDetail.salesVolume || 0 }}件</text>
			</view>
			<view class="title-row">
				<text class="tag">推荐</text>
				<text class="title">{{ goodDetail.goodsName }}</text>
			</view>
			<view class="desc">
				{{ goodDetail.description }}
			</view>
		</view>

		<!-- 配送方式、已选规格、服务 -->
		<view class="cell-group">
			<!-- <view class="cell">
				<view class="cell-left">
					<image src="/static/images/shoping/kd.png" class="cell-icon" />
					<text class="cell-text">当前仅支持自提</text>
				</view>
				<view class="cell-right">
					<uni-icons type="right" size="18" color="#bbb" />
				</view>
			</view> -->
			<view class="cell">
				<view class="cell-left">
					<image src="/static/images/shoping/fw.png" class="cell-icon" />
					<view class="service-tags">
						<view class="service-tag-item">
							<text class="service-tag">专业认证</text>
						</view>
						<view class="service-tag-item">
							<text class="service-tag">购后无忧</text>
						</view>
						<view class="service-tag-item">
							<text class="service-tag">爽约险</text>
						</view>
						<view class="service-tag-item">
							<text class="service-tag">取消预约说明</text>
						</view>
					</view>
				</view>
				<view class="cell-right" @click="openServicePopup">
					<uni-icons type="right" size="18" color="#bbb" />
				</view>
			</view>
			<view class="cell">
				<view class="cell-left">
					<image src="/static/images/shoping/gg.png" class="cell-icon" />
					<text class="spec-text">{{ skuList.filter(sku => sku.id === selectedSku)?.[0]?.specName || '请选择' }}</text>
				</view>
				<view class="cell-right" @click="openSkuPopup('1')">
					<text>{{skuList && skuList.length || 0}}款可选</text>
					<uni-icons type="right" size="18" color="#bbb" />
				</view>
			</view>
		</view>

		<!-- 店铺信息 -->
		<view class="shop-box">
			<image class="shop-logo-img" :src="storeInfo.imgUrl" mode="aspectFill" />
			<view class="shop-info">
				<view class="shop-title">{{ storeInfo.merchantName }}</view>
				<view class="shop-desc">{{ storeInfo.address }}</view>
				<view class="shop-goods-title">在售商品<view class="shop-goods-count">10</view>件</view>
			</view>

			<button class="shop-btn" size="mini" @click="goToShop">进入店铺</button>
		</view>

		<!-- 评价 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">评价 ({{commentsTotal > 200 ? '200+' : commentsTotal}})
					<text class="goodvalute">好评人数200+</text>
				</text>
				<text class="section-more" @click="openEvaluatePopup">查看全部 ></text>
			</view>
			<view class="comment-list">
				<view class="comment" v-for="(item, idx) in evaluateList" :key="idx">
					<view class="comment-user">
						<image class="avatar" :src="item.userAvatar" mode="aspectFill" />
						<view class="user-info">
							<text class="user-nick">{{ item.userNickname }}</text>
							<view class="stars">
								<uni-icons v-for="i in 5" :key="i" :type="i <= item.rating ? 'star-filled' : 'star'" size="14" :color="i <= item.rating ? '#FFD700' : '#ccc'" />
								<text class="star-count">{{ item.rating }}星</text>
							</view>
						</view>
						<text class="comment-time">{{ idx === 0 ? '9小时前' : '昨天' }}</text>
					</view>
					<view class="comment-content-wrapper">
						<view class="comment-tag" v-if="item.tag">{{ item.tag }}</view>
						<view class="comment-text">{{ item.reviewContent }}</view>
						<view class="comment-imgs" v-if="item.reviewImagesArray && item.reviewImagesArray.length">
							<image v-for="(img, i) in item.reviewImagesArray" :key="i" :src="img" class="comment-img" mode="aspectFill" />
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 商品详情 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">商品详情</text>
			</view>
			<view class="detail-content">
				{{ goodDetail.detailContent }}
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-bar">
			<view class="bar-item" @click="openServiceDialog">
				<image src="/static/images/shoping/kf.png" mode="aspectFill" style="width: 40rpx;height: 40rpx;" />
				<text class="bar-text">客服</text>
			</view>
			<!-- <view class="bar-item">
				<uni-icons type="cart" size="28" />
				<view class="cart-badge">1</view>
				<text class="bar-text">购物车</text>
			</view> -->
			<!-- <button class="bar-btn add-cart" @click="openSkuPopup('1')">加入购物车</button> -->
			<button class="bar-btn buy-now" @click="openSkuPopup('2')">立即购买</button>
		</view>

		<!-- 规格选择弹窗 -->
		<view v-if="showSkuPopup" class="popup-mask" @click.self="closeSkuPopup">
			<view class="sku-popup">
				<view class="sku-header">
					<text>选择商品</text>
					<uni-icons type="closeempty" size="28" class="close-icon" @click="closeSkuPopup" />
				</view>
				<view class="sku-main">
					<image class="sku-img" :src="goodDetail.mainPic" mode="aspectFill" />
					<view class="sku-info">
						<view class="sku-title">{{ goodDetail.goodsName }}</view>
						<view class="sku-price-row">
							<text class="sku-price">￥{{ ((skuList.filter(sku => sku.id === selectedSku)?.[0]?.discountPrice) || (skuList.filter(sku => sku.id === selectedSku)?.[0]?.specPrice)) / 100 }}</text>
							<text class="sku-old-price" v-if="(skuList.filter(sku => sku.id === selectedSku)?.[0]?.discountPrice)">￥{{(skuList.filter(sku => sku.id === selectedSku)?.[0]?.specPrice) / 100}}</text>
						</view>
						<view class="sku-stock">
							<view class="sku-amount">
								<button class="sku-btn" @click="changeAmount(-1)" :disabled="amount<=1">-</button>
								<text class="sku-num">{{ amount }}</text>
								<button class="sku-btn" :disabled="amount>=skuList.filter(sku => sku.id === selectedSku)?.[0]?.stock" @click="changeAmount(1)">+</button>
							</view>
							<view class="sku-stock-text">
								剩余库存{{ skuList.filter(sku => sku.id === selectedSku)?.[0]?.stock }}件
							</view>
						</view>
					</view>
				</view>
				<view class="sku-section">
					<view class="sku-section-title">商品规格</view>
					<view class="sku-tags">
						<view
							v-for="(sku, idx) in skuList"
							:key="idx"
							:class="['sku-tag', sku.id === selectedSku ? 'active' : '']"
							@click="selectSku(sku.id)"
						>{{ sku.specName }}</view>
					</view>
				</view>
				<!-- <view class="sku-section">
					<view class="sku-section-title">数量</view>
					<view class="sku-amount">
						<button class="sku-btn" @click="changeAmount(-1)" :disabled="amount<=1">-</button>
						<text class="sku-num">{{ amount }}</text>
						<button class="sku-btn" @click="changeAmount(1)">+</button>
					</view>
				</view> -->
				<button class="sku-buy-btn" @click="buyNow">{{skuType === '1' ? '立即购买' : '立即购买'}}</button>
			</view>
		</view>

		<!-- 商品服务弹窗 -->
		<view v-if="showServicePopup" class="popup-mask" @click.self="closeServicePopup">
			<view class="service-popup">
				<view class="service-header">
					<text>商品服务</text>
					<uni-icons type="closeempty" size="20" class="close-icon" @click="closeServicePopup" />
				</view>
				<view class="service-content">
					<view class="service-block" v-for="(srv, i) in serviceList" :key="i">
						<view class="service-title"><image src="/static/images/shoping/fwbz.png" class="service-icon" />{{ srv.title }}</view>
						<view class="service-desc">{{ srv.desc }}</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 客服弹窗 -->
		<view v-if="showServiceDialog" class="popup-mask" @click.self="closeServiceDialog">
			<view class="service-dialog">
				<view class="service-header">
					<text>商城客服</text>
					<uni-icons type="closeempty" size="20" class="close-icon" @click="closeServiceDialog" />
				</view>
				<view class="service-time">服务时段：每日 {{ storeInfo.openTime }} - {{ storeInfo.closeTime }}</view>
				<!-- <view class="service-phone" v-for="phone in servicePhones" :key="phone" @click="callPhone(phone)">
					{{ phone }}
				</view> -->
				<view class="service-phone" @click="callPhone(storeInfo.merchantTel)">
					{{ storeInfo.merchantTel }}
				</view>
				<view class="zwf"></view>
				<view class="service-cancel" @click="closeServiceDialog">取消</view>
			</view>
		</view>

		<!-- 评价弹窗 -->
		<view v-if="showEvaluatePopup" class="popup-mask" @click.self="closeEvaluatePopup">
			<view class="evaluate-popup">
				<view class="evaluate-header">
					<text>评价</text>
					<uni-icons type="closeempty" size="20" class="close-icon" @click="closeEvaluatePopup" />
				</view>
				<scroll-view class="evaluate-content" scroll-y>
					<view class="evaluate-list">
						<view class="evaluate-item" v-for="(item, idx) in evaluateList" :key="idx">
							<view class="evaluate-user">
								<image class="evaluate-avatar" :src="item.userAvatar" mode="aspectFill" />
								<view class="evaluate-user-info">
									<text class="evaluate-user-name">{{ item.userNickname }}</text>
									<view class="evaluate-stars">
										<uni-icons v-for="i in 5" :key="i" :type="i <= item.rating ? 'star-filled' : 'star'" size="14" :color="i <= item.rating ? '#FFD700' : '#ccc'" />
										<text class="evaluate-star-count">{{ item.rating }}星</text>
									</view>
								</view>
								<text class="evaluate-time">{{ item.reviewTime }}</text>
							</view>
							<view class="evaluate-content-wrapper">
								<view class="evaluate-tag" v-if="item.tag">{{ item.tag }}</view>
								<view class="evaluate-desc">{{ item.reviewContent }}</view>
								<view class="evaluate-images" v-if="item.reviewImagesArray && item.reviewImagesArray.length">
									<image v-for="(img, i) in item.reviewImagesArray" :key="i" :src="img" class="evaluate-image" mode="aspectFill" />
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		shopApi
	} from '@/common/api'
	export default {
		data() {
			return {
				images: [
					'https://cdn.uviewui.com/uview/swiper/1.jpg',
					'https://cdn.uviewui.com/uview/swiper/2.jpg',
					'https://cdn.uviewui.com/uview/swiper/3.jpg'
				],
				current: 0,
				showSkuPopup: false,
				showServicePopup: false,
				showServiceDialog: false,
				commentsTotal: 0,
				skuList: [],
				goodDetail: {},
				selectedSku: 0,
				amount: 1,
				skuType: '1',
				servicePhones: ['025 81688888', '189 0987 0987'],
				serviceList: [
					{ title: '专业认证', desc: '我们严格审核服务提供者的资质，确保每一位服务人员都经过专业培训并通过相关认证。无论是技能、经验还是服务态度，我们都力求为用户提供高质量、可信赖的服务。专业认证不仅保障了服务的质量，也增强了用户对平台的信任感。' },
					{ title: '购后无忧', desc: '为保障用户的消费权益，我们提供“购后无忧”服务。一旦用户对服务或产品不满意，可在规定时间内申请退换或补偿。我们致力于为用户提供安心、放心的消费体验，确保每一次消费都物有所值。' },
					{ title: '爽约险', desc: '为保障用户的消费权益，我们提供“购后无忧”服务。一旦用户对服务或产品不满意，可在规定时间内申请退换或补偿。我们致力于为用户提供安心、放心的消费体验，确保每一次消费都物有所值。' },
					{ title: '取消预约说明', desc: '用户在预约服务后如需取消，需根据平台规定提前一定时间操作。若在规定时间内取消，系统将全额或部分退款；若在规定时间后取消，可能无法退款或需支付一定违约金。具体的取消规则可在预约页面查看，我们建议用户提前安排好时间，以避免不必要的损失。' }
				],
				storeInfo: {},
				comments: [
					{
						userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
						userNickname: '小帅',
						rating: 4,
						reviewContent: '咖啡很棒，是我喜欢的那一款，巧克力与果香的风味，超级喜欢~',
						reviewImagesArray: []
					},
					{
						userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
						userNickname: 'Olivia_lily',
						rating: 4,
						reviewContent: '口感干净，包装精心。配送也很快！下次还会点。',
						reviewImagesArray: ['https://cdn.uviewui.com/uview/swiper/2.jpg']
					},
					{
						userAvatar: 'https://randomuser.me/api/portraits/men/65.jpg',
						userNickname: '王明宇',
						rating: 5,
						reviewContent: '冷萃咖啡很棒，酸度适中，清爽宜人，适合夏天！',
						reviewImagesArray: []
					}
				],
				showEvaluatePopup: false,
				evaluateList: [
					{
						userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
						userNickname: '小帅',
						rating: 4,
						reviewContent: '咖啡很棒，是我喜欢的那一款，巧克力与果香的风味，超级喜欢~',
						reviewImagesArray: [],
						reviewTime: '9小时前',
						tag: '冷【标准杯】'
					},
					{
						userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
						userNickname: 'Olivia_lily',
						rating: 4,
						reviewContent: '口感干净，包装精心。配送也很快！下次还会点。',
						reviewImagesArray: [
							'http://************:9000/product/sanwenyu.jpg',
							'http://************:9000/product/sanwenyu.jpg'
						],
						reviewTime: '昨天',
						tag: '冷【标准杯】'
					},
					{
						userAvatar: 'https://randomuser.me/api/portraits/men/65.jpg',
						userNickname: '王明宇',
						rating: 4,
						reviewContent: '冷萃咖啡很棒，细品来很有层次感，适合夏天！',
						reviewImagesArray: [],
						reviewTime: '昨天',
						tag: '冷【标准杯】'
					},
					{
						userAvatar: 'https://randomuser.me/api/portraits/women/22.jpg',
						userNickname: '赵子豪',
						rating: 4,
						reviewContent: '性价比高，价格实惠，推荐试试',
						reviewImagesArray: [
							'https://cdn.uviewui.com/uview/swiper/3.jpg'
						],
						reviewTime: '昨天',
						tag: '冷【标准杯】'
					}
				]
			}
		},
		onLoad(options){
			this.storeInfo = uni.getStorageSync('storeInfo');
			this.merchantId = options.merchantId;
			this.productId = options.productId;
			this.getGoodDetail();
			this.getEvaluateList();
		},
		methods: {
			goBack(){
				uni.navigateBack()
			},
			async getGoodDetail(){
				const res = await shopApi.getGoodDetail({
					merchantId: this.merchantId,
					productId: this.productId
				})
				this.skuList = res.data.skuList || [];
				this.goodDetail = res.data.tgood;
				this.selectedSku = this.skuList[0]?.id;
			},
			/**
			 * 轮播图切换事件
			 * @param {Object} e swiper事件对象
			 */
			onSwiperChange(e) {
				this.current = e.detail.current;
			},
			/**
			 * 打开规格选择弹窗
			 * @param {string} type 类型 1-加购 2-立即购买
			 */
			openSkuPopup(type) {
				this.skuType = type;
				this.showSkuPopup = true;
			},
			/**
			 * 关闭规格选择弹窗
			 */
			closeSkuPopup() {
				this.showSkuPopup = false;
			},
			/**
			 * 打开商品服务弹窗
			 */
			openServicePopup() {
				this.showServicePopup = true;
			},
			/**
			 * 关闭商品服务弹窗
			 */
			closeServicePopup() {
				this.showServicePopup = false;
			},
			/**
			 * 选择规格
			 * @param {number} idx 规格索引
			 */
			selectSku(idx) {
				this.selectedSku = idx;
			},
			/**
			 * 修改购买数量
			 * @param {number} delta 增减数量
			 */
			changeAmount(delta) {
				if (this.amount + delta < 1) return;
				this.amount += delta;
			},
			/**
			 * 立即购买/加入购物车
			 */
			buyNow() {
				this.closeSkuPopup();
				const price = this.skuList.filter(sku => sku.id === this.selectedSku)?.[0]?.specPrice;	
				const discountPrice = this.skuList.filter(sku => sku.id === this.selectedSku)?.[0]?.discountPrice;
				uni.navigateTo({ url: '/pages/shopping/fillOrder?merchantId=' + this.merchantId + '&productId=' + this.productId + '&skuId=' + this.selectedSku + '&amount=' + this.amount + '&productName=' + this.goodDetail.goodsName + '&productSpec=' + this.skuList.filter(sku => sku.id === this.selectedSku)?.[0]?.specName + '&price=' + price + '&image=' + this.goodDetail.mainPic + '&discountPrice=' + discountPrice});
			},
			/**
			 * 打开客服弹窗
			 */
			openServiceDialog() {
				this.showServiceDialog = true;
			},
			/**
			 * 关闭客服弹窗
			 */
			closeServiceDialog() {
				this.showServiceDialog = false;
			},
			/**
			 * 拨打客服电话
			 * @param {string} phone 电话号码
			 */
			callPhone(phone) {
				uni.makePhoneCall({ phoneNumber: phone });
			},
			/**
			 * 跳转评价页
			 */
			goToEvaluate() {
				uni.navigateTo({ url: '/pages/shopping/evaluate' });
			},
			/**
			 * 跳转店铺页
			 */
			goToShop() {
				uni.navigateTo({ url: '/pages/shopping/storeDetails?id=' + this.merchantId });
			},
			/**
			 * 打开评价弹窗
			 */
			openEvaluatePopup() {
				this.showEvaluatePopup = true;
			},
			/**
			 * 关闭评价弹窗
			 */
			closeEvaluatePopup() {
				this.showEvaluatePopup = false;
			},
			async getEvaluateList(){
				const res = await shopApi.getMerchantList({
					merchantId: this.merchantId,
					goodsId: this.productId,
					pageNum: 1,
					pageSize: 10
				})
				this.evaluateList = res.rows;
				this.commentsTotal = res.total;
			}
		}
	}
</script>

<style scoped lang="scss">
.custom-nav-bar{
		position: absolute;
		z-index: 99;
	}
	.product-detail-container {
		background: #f7f7f7;
		min-height: 100vh;
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
		/* 兼容旧版本 */
		padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
	}
	.carousel-box {
		position: relative;
		background: #fff;
		height: 33vh;
		display: flex;
		align-items: center;
		justify-content: center;
		// border-radius: 0 0 32rpx 32rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 24rpx #eee;
	}
	.carousel {
		width: 100vw;
		height: 33vh;
	}
	.carousel-img {
		width: 100vw;
		height: 33vh;
		object-fit: cover;
	}
	.carousel-indicator {
		position: absolute;
		right: 32rpx;
		bottom: 24rpx;
		background: rgba(0,0,0,0.4);
		color: #fff;
		font-size: 22rpx;
		border-radius: 20rpx;
		padding: 4rpx 18rpx;
	}
	.main-info {
		background: #fff;
		margin: 0 0 18rpx 0;
		padding: 32rpx 28rpx 18rpx 28rpx;
		// border-radius: 0 0 32rpx 32rpx;
		box-shadow: 0 4rpx 24rpx #eee;
	}
	.price-row {
		display: flex;
		align-items: baseline;
		margin-bottom: 12rpx;
	}
	.price {
		color: #F7532E;
		font-size: 53rpx;
		font-weight: bold;
		margin-right: 16rpx;
	}
	.old-price {
		color: #999;
		font-size: 27rpx;
		text-decoration: line-through;
		margin-right: 18rpx;
	}
	.sold {
		color: #999;
		font-size: 27rpx;
		margin-left: auto;
	}
	.title-row {
		display: flex;
		align-items: baseline;
		margin-bottom: 10rpx;
	}
	.tag {
		background: #52c41a;
		color: #fff;
		font-size: 22rpx;
		border-radius: 8rpx;
		padding: 2rpx 12rpx;
		margin-right: 12rpx;
	}
	.title {
		font-size: 31rpx;
		color: #000;
		font-weight: 600;
		flex: 1;
	}
	.desc {
		color: #666;
		font-size: 23rpx;
		margin-bottom: 6rpx;
		line-height: 1.6;
	}
	.cell-group {
		background: #f8f8f8;
		margin: 20rpx 0;
		// border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	.cell {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;
		background: #fff;
		margin-bottom: 2rpx;
		position: relative;
	}
	.cell::after {
		content: '';
		position: absolute;
		left: 30rpx;
		right: 30rpx;
		bottom: 0;
		height: 1rpx;
		background-color: rgba(209, 209, 209, 0.5);
	}
	.cell:last-child::after {
		display: none;
	}
	.cell-left {
		display: flex;
		align-items: center;
		.cell-text{
			font-weight: 400;
			font-size: 23rpx;
			color: #666666;
		}
	}
	// .cell-left text {
	// 	font-size: 28rpx;
	// 	color: #333;
	// 	font-weight: 500;
	// }
	.cell-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 12rpx;
	}
	.cell-right {
		display: flex;
		align-items: center;
		color: #666;
		font-size: 26rpx;
	}
	.spec-text {
		font-weight: 600;
		font-size: 23rpx;
		color: #000000;
	}
	.service-tags {
		display: flex;
		align-items: center;
		margin-right: 8rpx;
	}
	.service-tag-item {
		display: flex;
		align-items: center;
		margin-right: 10rpx;
	}
	.service-tag {
		padding: 4rpx 12rpx;
		background: #FBF5ED;
		border-radius: 4rpx;
		font-weight: 400;
		font-size: 23rpx;
		color: #995D21;
	}
	.service-tag.green {
		background: #e8f5e9;
		color: #4caf50;
	}
	.service-tag.orange {
		background: #fff3e0;
		color: #ff9800;
	}
	.service-tag.blue {
		background: #e3f2fd;
		color: #2196f3;
	}
	.service-link {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;
	}
	.service-link-content {
		display: flex;
		align-items: center;
	}
	.service-link-text {
		color: #333;
		font-size: 28rpx;
		margin-left: 8rpx;
		font-weight: 400;
	}
	.shop-box {
		display: flex;
		align-items: center;
		background: #fff;
		padding: 24rpx 28rpx;
		// border-radius: 24rpx;
		margin: 18rpx 0 0 0;
		box-shadow: 0 2rpx 12rpx #f0f0f0;
	}
	.shop-logo-img {
		width: 95rpx;
		height: 95rpx;
		border-radius: 8rpx;
		margin-right: 18rpx;
	}
	.shop-info {
		flex: 1;
	}
	.shop-title {
		margin-bottom: 8rpx;
		font-weight: 600;
		font-size: 23rpx;
		color: #000000;
	}
	.shop-desc {
		margin-bottom: 8rpx;
		font-weight: 400;
		font-size: 19rpx;
		color: #666666;
	}
	.shop-btn {
		background: #66D47E;
		color: #fff;
		border: none;
		border-radius: 4rpx;
		padding: 0 24rpx;
		height: 53rpx;
		line-height: 53rpx;
		margin-left: 16rpx;
		font-weight: 400;
		font-size: 23rpx;
		color: #FFFFFF;
	}
	.shop-goods-title{
		font-weight: 400;
		font-size: 19rpx;
		color: #666666;
		display: flex;
		align-items: center;
		.shop-goods-count{
			color: #000;
			font-weight: 600;
		}
	}
	.section {
		background: #fff;
		margin-top: 18rpx;
		padding: 0 28rpx 20rpx 28rpx;
		// border-radius: 24rpx;
		box-shadow: 0 2rpx 12rpx #f0f0f0;
	}
	.section-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 0 10rpx 0;
	}
	.section-title {
		font-weight: 600;
		font-size: 27rpx;
		color: #333333;
		.goodvalute{
			font-weight: 400;
			font-size: 23rpx;
			color: #995D21;
			margin-left: 10rpx;
		}
	}
	.section-more {
		font-weight: 400;
		font-size: 23rpx;
		color: #666666;
	}
	.comment-list {
		margin-top: 8rpx;
		max-height: 700rpx;
		overflow-y: auto;
	}
	.comment {
		background: #fafafa;
		border-radius: 16rpx;
		padding: 18rpx 16rpx 16rpx 16rpx;
		margin-bottom: 16rpx;
		box-shadow: 0 2rpx 8rpx #f5f5f5;
		position: relative;
	}
	.comment-user {
		display: flex;
		align-items: flex-start;
		margin-bottom: 12rpx;
	}
	.avatar {
		width: 61rpx;
		height: 61rpx;
		background: #eee;
		border-radius: 50%;
		margin-right: 12rpx;
		flex-shrink: 0;
	}
	.user-info {
		display: flex;
		flex-direction: column;
		flex: 1;
	}
	.user-nick {
		font-size: 28rpx;
		color: #000;
		font-weight: 600;
		margin-bottom: 4rpx;
	}
	.stars {
		display: flex;
		align-items: center;
	}
	.stars .uni-icons {
		font-size: 24rpx;
		margin-right: 2rpx;
	}
	.star-count {
		font-size: 20rpx;
		color: #999;
		margin-left: 8rpx;
	}
	.comment-time {
		font-size: 23rpx;
		color: #999;
		position: absolute;
		right: 16rpx;
		top: 18rpx;
	}
	.comment-content-wrapper {
		padding-left: 73rpx; /* 头像宽度61rpx + 右边距12rpx */
	}
	.comment-tag {
		display: inline-block;
		font-weight: 400;
		font-size: 23rpx;
		color: #999;
		margin-bottom: 12rpx;
	}
	.comment-text {
		font-size: 26rpx;
		color: #000;
		line-height: 1.5;
		font-weight: 400;
		margin: 12rpx 0;
	}
	.comment-imgs {
		display: flex;
		flex-wrap: wrap;
		gap: 10rpx;
	}
	.comment-img {
		width: 190rpx;
		height: 190rpx;
		border-radius: 8rpx;
		object-fit: cover;
	}
	.detail-content {
		color: #888;
		font-size: 24rpx;
		padding: 16rpx 0 0 0;
	}
	.bottom-bar {
		position: fixed;
		left: 0;
		bottom: 0;
		width: calc(100vw - 32rpx);
		height: 120rpx;
		background: #fff;
		display: flex;
		align-items: center;
		z-index: 100;
		padding: 0 16rpx;
		/* 添加安全距离 */
		padding-bottom: env(safe-area-inset-bottom);
		/* 兼容旧版本 */
		padding-bottom: constant(safe-area-inset-bottom);
	}
	.bar-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 80rpx;
		position: relative;
		margin-right: 12rpx;

	}
	.bar-text {
		font-size: 23rpx;
		color: #666;
		margin-top: 8rpx;
	}
	.cart-badge {
		position: absolute;
		top: 0;
		right: 8rpx;
		background: #ff4d4f;
		color: #fff;
		border-radius: 50%;
		font-size: 18rpx;
		width: 28rpx;
		height: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1;
	}
	.bar-btn {
		flex: 1;
		height: 92rpx;
		line-height: 92rpx;
		font-size: 31rpx;
		border-radius: 46rpx;
		margin: 0 10rpx;
		font-weight: 500;
		border: none;
	}
	.add-cart {
		background: #fffbe6;
		color: #faad14;
		border: 1rpx solid #faad14;
	}
	.buy-now {
		background: #66D47E;
		color: #fff;
	}
	/* 弹窗遮罩 */
	.popup-mask {
		position: fixed;
		left: 0; top: 0; right: 0; bottom: 0;
		background: rgba(0,0,0,0.3);
		z-index: 999;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		/* 禁止滚动 */
		overflow: hidden;
	}
	/* 规格弹窗 */
	.sku-popup {
		width: 100vw;
		background: #fff;
		border-radius: 24rpx 24rpx 0 0;
		padding: 32rpx 24rpx 24rpx 24rpx;
		box-sizing: border-box;
		animation: popupIn .2s;
	}
	@keyframes popupIn { from { transform: translateY(100%); } to { transform: translateY(0); } }
	.sku-header {
		padding: 0rpx 0 18rpx 0;
		// border-botwidth: 122rpx;
		font-weight: 600;
		font-size: 31rpx;
		color: #000000;
		text-align: center;
		position: relative;
		.close-icon{
			position: absolute;
			right: 0rpx;
			top: -6rpx;
		}
	}
	.close-icon { color: #bbb; }
	.sku-main { display: flex;margin-top: 24rpx; }
	.sku-img {
		width: 161rpx; height: 161rpx; background: #eee; border-radius: 12rpx;
		display: flex; align-items: center; justify-content: center; margin-right: 18rpx;
	}
	.sku-info { flex: 1; }
	.sku-title { font-size: 31rpx; color: #000; font-weight: 600; margin-bottom: 4rpx; }
	.sku-price-row { display: flex; align-items: center; margin-bottom: 2rpx; align-items: baseline;margin-top: -4rpx;}
	.sku-price { color: #F7532E; font-size: 53rpx; font-weight: bold; margin-right: 10rpx; }
	.sku-old-price { color: #999; font-size: 27rpx; text-decoration: line-through; }
	.sku-stock { color: #999; font-size: 27rpx; align-items: baseline; display: flex; }
	.sku-section { margin-top: 24rpx; }
	.sku-section-title { font-size: 27rpx;font-weight: 600; color: #333; margin-bottom: 22rpx; }
	.sku-tags { display: flex; flex-wrap: wrap; gap: 16rpx 18rpx; }
	.sku-tag {
		background: #F6F7FB;border-radius: 15rpx; padding: 20rpx 32rpx;
		font-size: 27rpx; color: #333; margin-bottom: 8rpx;
	}
	.sku-tag.active { background: #F0FEF1; color: #66D47E; }
	.sku-amount { display: flex; align-items: center;margin-right: 75rpx; }
	.sku-btn {
		height: 53rpx; width: 53rpx; line-height: 48rpx; border-radius: 8rpx; background: #F6F7FB;
		color: #000; font-size: 40rpx; text-align: center;padding: 0;
	}
	.sku-btn:after{
		display: none;
	}
	.sku-num { padding: 0 24rpx; text-align: center; font-size: 26rpx; }
	.sku-buy-btn {
		background: #66D47E;
		border-radius: 46rpx;
		line-height: 92rpx;
		height: 92rpx;
		font-weight: 500;
		font-size: 31rpx;
		color: #FFFFFF;
		margin-top: 24rpx;
	}
	/* 服务弹窗 */
	.service-popup {
		width: 100vw;
		background: #fff;
		border-radius: 24rpx 24rpx 0 0;
		padding: 0 24rpx 24rpx 24rpx;
		box-sizing: border-box;
		animation: popupIn .2s;
		.service-header {
			padding: 32rpx 0 18rpx 0;
			// border-botwidth: 122rpx;
			font-weight: 600;
			font-size: 31rpx;
			color: #000000;
			text-align: center;
			position: relative;
			.close-icon{
				position: absolute;
				right: 0rpx;
				top: 32rpx;
			}
		}
		.service-content { padding: 18rpx 0 0 0; }
		.service-block { margin-bottom: 32rpx; }
		.service-title { 
			display: flex;
			align-items: center;
			font-weight: 600;
			font-size: 27rpx;
			color: #333333;
			.service-icon{
				width: 45rpx;
				height: 45rpx;
				margin-right: 10rpx;
			}
		 }
		.service-desc {
			font-weight: 400;
			font-size: 23rpx;
			color: #666666;
			line-height: 38rpx;
			padding-left: 56rpx;
		}
	}
	/* 客服弹窗 */
	.service-dialog {
		width: 100vw;
		background: #fff;
		border-radius: 24rpx 24rpx 0 0;
		// padding: 0 24rpx 24rpx 24rpx;
		box-sizing: border-box;
		animation: popupIn .2s;
		.service-header {
			padding: 32rpx 24rpx 18rpx 24rpx;
			// border-botwidth: 122rpx;
			font-weight: 600;
			font-size: 31rpx;
			color: #000000;
			text-align: center;
			position: relative;
			.close-icon{
				position: absolute;
				right: 24rpx;
				top: 32rpx;
			}
		}
		.service-title {
			font-size: 30rpx;
			font-weight: bold;
			color: #222;
			margin-bottom: 10rpx;
			text-align: center;
		}
		.service-time {
			text-align: center;
			margin-bottom: 30rpx;
			font-weight: 400;
			font-size: 31rpx;
			color: #000000;
			line-height: 46rpx;
		}
		.service-phone {
			text-align: center;
			color: #007AFF;
			font-size: 31rpx;
			padding: 28rpx 0;
			border-top: 1rpx solid #eee;
		}
		.service-phone:first-of-type {
			border-top: none;
		}
		.zwf{
			background: #F4F8FB;
			height: 23rpx;
		}
		.service-cancel {
			text-align: center;
			height: 100rpx;
			line-height: 100rpx;
			background: #fff;
			font-weight: 500;
			font-size: 31rpx;
			color: #007AFF;
			text-align: center;
		}
	}
	/* 评价弹窗 */
	.evaluate-popup {
		width: 100vw;
		background: #fff;
		border-radius: 24rpx 24rpx 0 0;
		box-sizing: border-box;
		animation: popupIn .2s;
		.evaluate-header {
			padding: 32rpx 24rpx 18rpx 24rpx;
			// border-botwidth: 122rpx;
			font-weight: 600;
			font-size: 31rpx;
			color: #000000;
			text-align: center;
			position: relative;
			.close-icon{
				position: absolute;
				right: 24rpx;
				top: 32rpx;
			}
		}
		.evaluate-content {
			max-height: 800rpx; /* Adjust height as needed */
			// padding-bottom: 24rpx;
		}
		.evaluate-list {
			padding-top: 18rpx;
		}
		.evaluate-item {
			background: #fff;
			border-radius: 0;
			padding: 24rpx 24rpx;
			margin-bottom: 0;
			box-shadow: none;
			border-bottom: 20rpx solid #F4F8FB;
			position: relative;
		}
		.evaluate-user {
			display: flex;
			align-items: center;
			margin-bottom: 12rpx;
		}
		.evaluate-avatar {
			width: 61rpx;
			height: 61rpx;
			background: #eee;
			border-radius: 50%;
			margin-right: 12rpx;
			flex-shrink: 0;
		}
		.evaluate-user-info {
			display: flex;
			flex-direction: column;
			flex: 1;
		}
		.evaluate-user-name {
			font-size: 28rpx;
			color: #000;
			font-weight: 600;
			margin-bottom: 4rpx;
		}
		.evaluate-stars {
			display: flex;
			align-items: center;
			margin-bottom: 10rpx;
		}
		.evaluate-star-count {
			font-size: 20rpx;
			color: #999;
			margin-left: 8rpx;
		}
		.evaluate-time {
			font-size: 23rpx;
			color: #999;
			position: absolute;
			right: 24rpx;
			top: 24rpx;
		}
		.evaluate-content-wrapper {
			margin-top: 10rpx;
			padding-left: 73rpx; /* 头像宽度61rpx + 右边距12rpx */
		}
		.evaluate-images {
			display: flex;
			flex-wrap: wrap;
			gap: 10rpx;
			.evaluate-image {
				width: 190rpx;
				height: 190rpx;
				border-radius: 8rpx;
				object-fit: cover;
			}
		}
		.comment-tag {
			display: inline-block;
			font-weight: 400;
			font-size: 23rpx;
			color: #999;
			margin-bottom: 12rpx;
		}
		.comment-text {
			font-size: 26rpx;
			color: #000;
			line-height: 1.5;
			font-weight: 400;
			margin: 12rpx 0;
		}
		.comment-imgs {
			display: flex;
			flex-wrap: wrap;
			gap: 10rpx;
		}
		.comment-img {
			width: 190rpx;
			height: 190rpx;
			border-radius: 8rpx;
			object-fit: cover;
		}
	}
</style>
