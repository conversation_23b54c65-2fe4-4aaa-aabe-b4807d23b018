<template>
	<view class="error-container">
		<image class="error-image" src="/static/images/shoping/error.png" mode="aspectFit"></image>
		<view class="error-title">支付失败</view>
		<view class="error-desc">支付遇到问题，请稍试重新支付</view>
		<view class="btn-group">
			<button class="btn return-btn" @click="returnOrder">返回订单</button>
			<button class="btn return-btn" @click="retryShopping">继续支付</button>
		</view>
	</view>
</template>

<script>
	import { payApi } from '@/common/api'
	export default {
		data() {
			return {
				orderId: ''
			}
		},
		onLoad(options) {
			// 获取订单ID
			if (options.orderId) {
				this.orderId = options.orderId;
			}
		},
		methods: {
			/**
			 * 返回订单页面
			 */
			returnOrder() {
				uni.navigateTo({
					url: '/pages/orders/details?orderId=' + this.orderId
				});
			},
			
			/**
			 * 重新尝试支付
			 */
			async retryShopping() {
				const res = await payApi.initiatePaymentById({
					orderNo: this.orderId,
					payMethod: 1
				});
				const payParams = res.data;
				uni.requestPayment({
					provider: 'wxpay',
					orderInfo: {
						"appid": payParams.appId,  // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
						"noncestr": payParams.nonceStr, // 随机字符串
						"package": "Sign=WXPay",        // 固定值
						"partnerid": payParams.mchId,      // 微信支付商户号
						"prepayid": payParams.prepayId, // 统一下单订单号
						"timestamp": payParams.timeStamp,        // 时间戳（单位：秒）
						"sign": payParams.sign // 签名，这里用的 MD5/RSA 签名
					}, // 微信支付订单信息
					success: () => {
						console.log('success');
						uni.showToast({ title: '支付成功', icon: 'success' });
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages/shopping/success?orderId=' + this.orderId
							});
						}, 1000);
					},
					fail: (err) => {
						console.log('err',err);
						uni.showToast({ title: '支付失败', icon: 'none' });
					}
				});
			}
		}
	}
</script>

<style lang="scss">
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 190rpx;
}

.error-image {
	width: 273rpx;
	height: 190rpx;
}

.error-title {
	font-size: 35rpx;
	font-weight: 500;
	margin-top: 60rpx;
	color: #000;
}

.error-desc {
	font-size: 24rpx;
	color: #666;
	margin-top: 20rpx;
	margin-bottom: 60rpx;
}

.btn-group {
	display: flex;
	width: 100%;
	padding: 0 40rpx;
	justify-content: space-between;
}

.btn {
	width: 328rpx;
	height: 91rpx;
	line-height: 91rpx;
	border-radius: 46rpx;
	font-size: 30rpx;
}

.return-btn {
	background-color: #fff;
	color: #000;
	border: 1px solid #D1D1D1;
}
</style>
