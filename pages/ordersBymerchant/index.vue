<template>
	<view class="container">
		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<uni-icons type="search" size="20" color="#999" />
				<input 
					class="search-input" 
					placeholder="请输入用户手机号或商品名称查找" 
					v-model="searchKeyword"
					@confirm="onSearch"
				/>
			</view>
			<!-- 日期筛选按钮 -->
			<view class="date-filter-btn" @click="showDatePicker">
				<image src="/static/images/order/datetime.png" mode="aspectFit"></image>
			</view>
		</view>

		<!-- 状态标签栏 -->
		<view class="tab-container">
			<view 
				v-for="(tab, index) in tabs" 
				:key="index"
				:class="['tab-item', currentTab === index ? 'active' : '']"
				@click="switchTab(index)"
			>
				<text class="tab-text">{{ tab.name }}</text>
				<text v-if="tab.count > 0">({{ tab.count }})</text>
			</view>
		</view>

		<!-- 订单列表 -->
		<scroll-view 
			class="order-list" 
			scroll-y 
			@scrolltolower="onReachBottom"
			lower-threshold="50"
		>
			<view v-if="orderList.length === 0" class="empty-state">
				<image class="empty-image" src="/static/images/order/nodata.png" mode="aspectFit"></image>
				<text class="empty-text">暂无相关订单</text>
			</view>
			
			<view v-for="order in orderList" :key="order.id" class="order-item">
				<!-- 订单头部：订单号和状态 -->
				<view class="order-header">
					<text class="order-number">
						<image class="ztIMG" src="/static/images/order/zt.png" mode="aspectFill" v-if="order.deliveryType === 2"></image>
						<image class="psIMG" src="/static/images/order/ps.png" mode="aspectFill" v-else></image>
						{{order.deliveryType === 2 ? '自提订单' : '配送订单'}}</text>
					<text class="order-status" :class="getStatusClass(order.orderStatus)">{{ order.orderStatusDesc }}</text>
				</view>

				<!-- 商家信息 -->
				<view class="shop-info">
					<uni-icons type="shop" size="16" color="#666" />
					<text class="shop-name">{{ order.merchantInfo.merchantName }}</text>
				</view>

				<!-- 商品信息 -->
				<view class="product-info">
					<image 
						class="product-image" 
						:src="order.merchantInfo.merchantAvatar"
						mode="aspectFill"
					/>
					<view class="product-detail">
						<text class="product-name text-ellipsis">{{ order.goodsList[0].goodsName }}</text>
						<text class="product-spec">{{ order.goodsList[0].specName }}</text>
						<view class="product-quantity">
							<text class="">×{{ order.goodsList[0].quantity }}</text>
							<text class="order-price" v-if="order.payMethod === '5'">{{ order.totalAmount / 10 }}贝壳币</text>
							<text class="order-price" v-else>¥ {{ order.totalAmount / 100 }}</text>
						</view>
					</view>
				</view>

				<!-- 价格信息 -->
				<!-- <view class="price-info">
					<text class="price-label">价格：</text>
					<text class="order-price">¥ {{ order.totalAmount / 100 }}</text>
				</view> -->

				<view class="order-info">
					<text class="order-info-label">留言</text>
					<text class="order-info-value">{{ order.orderMessage }}</text>
				</view>
				<view class="order-info" v-if="order.deliveryType === 1">
					<text class="order-info-label">配送地点</text>
					<text class="order-info-value">{{ order.deliveryAddress }}</text>
				</view>
				<view class="order-info">
					<text class="order-info-label">下单时间</text>
					<text class="order-info-value">{{ order.orderDate }}</text>
				</view>
				<view class="order-info" v-if="order.refundApplicationId">
					<text class="order-info-label">申请时间</text>
					<text class="order-info-value">{{ order.updateTime }}</text>
				</view>
				<view class="order-info" v-if="order.orderStatus === '3'">
					<text class="order-info-label">核销时间</text>
					<text class="order-info-value">{{ order.updateTime }}</text>
				</view>
				<!-- <view class="order-info" v-if="order.orderStatus === '1'">
					<text class="order-info-label">核销码</text>
					<text class="order-info-value hxm">{{ order.verificationCode }}</text>
				</view> -->

				<!-- 操作按钮 -->
				<view class="order-actions" v-if="['1', '4', '5'].includes(order.orderStatus)">
					<view class="action-buttons">
						<!-- <button 
							v-if="order.orderStatus === '0'" 
							class="action-btn cancel-btn" 
							@click="cancelOrder(order)"
						>
							取消订单
						</button> -->
						<button 
							v-if="order.deliveryType === 1" 
							class="action-btn cancel-btn" 
							@click="contactCustomer(order)"
						>
							联系顾客
						</button>
						<button 
							v-if="order.orderStatus === '1'" 
							class="action-btn cancel-btn" 
							@click="cancelOrder(order)"
						>
							取消订单
						</button>
						<button 
							v-if="order.orderStatus === '1'" 
							class="action-btn primary-btn" 
							@click="confirmOrder(order)"
						>
							接单
						</button>
						<button 
							v-if="order.orderStatus === '4'" 
							class="action-btn primary-btn" 
							@click="workedOrder(order)"
						>
							制作完成
						</button>
						<!-- <button 
							v-if="order.orderStatus === '-6'" 
							class="action-btn cancel-btn" 
							@click="refundOrder(order, 'tk')"
						>
							拒绝退款
						</button>
						<button 
							v-if="order.orderStatus === '-6'" 
							class="action-btn cancel-btn" 
							@click="refundOrder(order)"
						>
							同意退款
						</button> -->
						<button 
							v-if="order.orderStatus === '5'" 
							class="action-btn primary-btn" 
							@click="confirmReceive(order)"
						>
							核销
						</button>
					</view>
				</view>
			</view>

			<!-- 加载状态 -->
			<view class="loading-state" v-if="orderList.length > 0">
				<view v-if="isLoading" class="loading-more">
					<text class="loading-text">加载中...</text>
				</view>
				<view v-else-if="!hasMore" class="no-more">
					<text class="no-more-text">没有更多数据了</text>
				</view>
			</view>
		</scroll-view>

		<!-- 自定义确认弹窗 -->
		<uni-popup ref="customModal" mode="center" :mask-click="false">
			<view class="confirm-popup-content">
				<view class="title">{{modalTitle}}</view>
				<view class="content">{{modalMessage}}</view>
				<view class="btn-box">
					<view class="cancel" @click="closeModal">取消</view>
					<view class="confirm" @click="confirmModal">确定</view>
				</view>
			</view>
		</uni-popup>

		<!-- 日期选择器 -->
		<DateRangePicker 
			:visible.sync="showDatePickerVisible"
			@confirm="onDateRangeConfirm"
			@update:visible="showDatePickerVisible = $event"
		/>
		<uni-popup ref="popup" style="width: 660rpx;" class="referral-popup">
			<view class="popup-content">
				<view class="popup-top">
					<image src="/static/images/profile/popup-top.png" class="popup-top-image" mode="widthFix"></image>
				</view>
				<view class="popup-main">
					<view class="popup-title">输入核销码</view>
					<view class="input-container">
						<input 
							v-model="referralCodeInput"
							placeholder="请输入核销码"
							placeholder-class="input-placeholder"
							class="referral-input"
							maxlength="20"
						/>
					</view>
					<view class="btn-row">
						<view class="btn-cancel" @click="handleCancel">取消</view>
						<view class="btn-confirm" @click="handleConfirm">确认核销</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { orderApi, payApi } from '@/common/api';
import DateRangePicker from '@/components/DateRangePicker/DateRangePicker.vue';

export default {
	components: {
		DateRangePicker
	},
	data() {
		return {
			// 搜索关键词
			searchKeyword: '',
			// 当前选中的标签
			currentTab: 0,
			// 标签配置
			tabs: [
				{ name: '全部', count: 0, status: '' },
				{ name: '待核销', count: 0, status: '1' },
				{ name: '已完成', count: 0, status: '3' },
				{ name: '售后/退款', count: 0, status: '99' }
			],
			// 订单列表
			orderList: [],
			// 分页参数
			pageNum: 1,
			pageSize: 10,
			hasMore: true,
			isLoading: false,
			// 自定义弹窗相关
			modalTitle: '',
			modalMessage: '',
			confirmCallback: null,
			// 日期选择器相关
			showDatePickerVisible: false,
			dateRange: {
				startDate: '',
				endDate: ''
			},
			// 核销弹窗相关
			currentOrder: null,
			referralCodeInput: ''
		}
	},
	
	onLoad(options) {
		// 获取传入的状态参数
		if (options.status) {
			const tabIndex = this.tabs.findIndex(tab => tab.status === options.status);
			if (tabIndex !== -1) {
				this.currentTab = tabIndex;
			}
		}
		this.loadOrderList();
	},
	
	// 监听页面触底事件
	onReachBottom() {
		this.loadMore();
	},
	
	onReady() {
		// 页面渲染完成后，确保弹窗组件已挂载
		this.$nextTick(() => {
			if (!this.$refs.customModal) {
				console.warn('弹窗组件未正确挂载');
			}
		});
	},

	methods: {
		async getOrderCount() {
			const res = await orderApi.getOrderCount();
			this.tabs[0].count = res.data.allCount;
			this.tabs[1].count = res.data.waitVerifyCount;
			this.tabs[2].count = res.data.doneCount;
			this.tabs[3].count = res.data.refundCount;
		},
		async workedOrder(order) {
			const result = await this.showConfirmDialog('确定要制作完成吗？', '温馨提示');
			if (!result) return;
			const res = await orderApi.workedOrder({
				orderNo: order.orderCode
			});
			if(res.code === 200){
				uni.showToast({
					title: '制作完成',
					icon: 'none'
				});
				this.refreshList();
			}
		},
		async confirmOrder(order) {
			const result = await this.showConfirmDialog('确定要接单吗？', '温馨提示');
			if (!result) return;
			const res = await orderApi.confirmOrder({
				orderNo: order.orderCode
			});
			if(res.code === 200){
				uni.showToast({
					title: '接单成功',
					icon: 'none'
				});
				this.refreshList();
			}
		},
		async refundOrder(order, type) {
			if(type === 'tk'){
				uni.navigateTo({
					url: `/pages/ordersBymerchant/refund?orderId=${order.refundApplicationId}`
				});
				return;
			}
			const result = await this.showConfirmDialog('确定要退款吗？', '温馨提示');
			if (!result) return;
			const res = await orderApi.refundProcess({
					refundApplicationId: order.refundApplicationId,
					processResult: '1',
					merchantReply: '1',
					merchantOperator: 'admin'
				});
			if(res.code === 200){
				uni.showToast({
					title: '退款成功',
					icon: 'none'
				});
			}
			this.refreshList();
		},
		contactCustomer(order) {
			const phone = order.receiverPhone || order.reservePhone;
			uni.makePhoneCall({
				phoneNumber: phone
			});
		},
		/**
		 * 搜索订单
		 */
		onSearch() {
			this.pageNum = 1;
			this.orderList = [];
			this.hasMore = true;
			this.loadOrderList();
		},

		/**
		 * 切换标签
		 */
		switchTab(index) {
			if (this.currentTab === index) return;
			
			this.currentTab = index;
			this.pageNum = 1;
			this.orderList = [];
			this.hasMore = true;
			this.loadOrderList();
		},

		/**
		 * 加载订单列表
		 */
		async loadOrderList() {
			this.getOrderCount();
			if (this.isLoading || !this.hasMore) return;
			
			this.isLoading = true;
			
			try {
				const params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize,
					orderStatus: this.tabs[this.currentTab].status,
					goodsName: this.searchKeyword,
					merchantId: '5'
				};

				// 添加日期范围参数
				if (this.dateRange.startDate && this.dateRange.endDate) {
					params.startDate = this.dateRange.startDate;
					params.endDate = this.dateRange.endDate;
				}

				// 模拟数据，实际项目中调用API
				// const mockData = this.getMockOrderData();
				
				const res = await orderApi.getOrderList(params);
				const { rows, total } = res;

				if (this.pageNum === 1) {
					this.orderList = rows;
				} else {
					this.orderList.push(...rows);
				}
				
				// 判断是否还有更多数据
				this.hasMore = this.orderList.length < total;
				
			} catch (error) {
				console.error('加载订单列表失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.isLoading = false;
			}
		},

		/**
		 * 刷新列表
		 */
		refreshList() {
			this.pageNum = 1;
			this.orderList = [];
			this.hasMore = true;
			this.loadOrderList();
		},

		/**
		 * 触底加载更多
		 */
		onReachBottom() {
			this.loadMore();
		},

		/**
		 * 加载更多
		 */
		loadMore() {
			if (!this.hasMore || this.isLoading) return;
			
			this.pageNum++;
			this.loadOrderList();
		},

		/**
		 * 跳转到订单详情
		 */
		goToOrderDetail(order) {
			// 导航到详情页，同时传递订单ID作为参数
			uni.navigateTo({
				url: `/pages/orders/details?id=${order.orderCode}`
			});
		},
		
		/**
		 * 支付订单
		 */
		async payOrder(order) {
			const res = await payApi.initiatePaymentById({
				// orderId: order.id,
				orderNo: order.orderCode,
				payMethod: 1
			});
			const payParams = res.data;
			uni.requestPayment({
					provider: 'wxpay',
					orderInfo: {
						"appid": payParams.appId,  // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
						"noncestr": payParams.nonceStr, // 随机字符串
						"package": "Sign=WXPay",        // 固定值
						"partnerid": payParams.mchId,      // 微信支付商户号
						"prepayid": payParams.prepayId, // 统一下单订单号
						"timestamp": payParams.timeStamp,        // 时间戳（单位：秒）
						"sign": payParams.sign // 签名，这里用的 MD5/RSA 签名
					}, // 微信支付订单信息
					success: () => {
						console.log('success');
						uni.showToast({ title: '支付成功', icon: 'none' });
						// 支付成功后刷新订单列表
						this.refreshList();
					},
					fail: (err) => {
						console.log('err',err);
						uni.showToast({ title: '支付失败', icon: 'none' });
					}
				});
		},

		/**
		 * 取消订单
		 */
		async cancelOrder(order) {
			uni.navigateTo({
				url: `/pages/ordersBymerchant/cancal?orderId=${order.orderCode}`
			});
		},

		/**
		 * 确认核销
		 */
		async confirmReceive(order) {
			// 打开弹窗
			this.currentOrder = order;
			this.referralCodeInput = '';
			this.$refs.popup.open('center');
		},

		/**
		 * 处理取消按钮点击
		 */
		handleCancel() {
			this.$refs.popup.close();
			this.referralCodeInput = '';
		},

		/**
		 * 处理确认按钮点击
		 */
		async handleConfirm() {
			try {
				if (!this.referralCodeInput.trim()) {
					uni.showToast({
						title: '请输入核销码',
						icon: 'none'
					});
					return;
				}
				
				const res = await orderApi.verifyOrder({
					orderCode: this.currentOrder.orderCode,
					verificationCode: this.referralCodeInput.trim()
				});
				
				if(res.code === 200){
					uni.showToast({
						title: '核销成功',
						icon: 'none'
					});
					this.$refs.popup.close();
					this.refreshList();
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('核销失败:', error);
				uni.showToast({
					title: '核销失败',
					icon: 'none'
				});
			}
		},

		/**
		 * 删除订单
		 */
		async deleteOrder(order) {
			const result = await this.showConfirmDialog('确定要删除这个订单吗？删除后无法恢复。', '温馨提示');
			if (!result) return;
			
			try {
				uni.showLoading({ title: '删除中...' });
				
				// 实际项目中调用删除API
				// await orderApi.deleteOrder(order.id);
				
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '删除成功',
						icon: 'none'
					});
					this.refreshList(); // 刷新列表
				}, 1000);
				
			} catch (error) {
				uni.hideLoading();
				console.error('删除订单失败:', error);
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				});
			}
		},

		/**
		 * 评价订单
		 */
		evaluateOrder(order) {
			uni.navigateTo({
				url: `/pages/shopping/evaluate?orderId=${order.id}`
			});
		},

		/**
		 * 查看退款详情
		 */
		viewRefundDetail(order) {
			uni.navigateTo({
				url: `/pages/orders/refundDetail?orderId=${order.id}`
			});
		},

		/**
		 * 获取状态样式类
		 */
		getStatusClass(status) {
			const statusMap = {
				'0': 'status-pending',
				'1': 'status-paid',
				'2': 'status-completed',
				'3': 'status-refund'
			};
			return statusMap[status] || '';
		},

		/**
		 * 获取状态文本
		 */
		getStatusText(status) {
			const statusMap = {
				'0': '待支付',
				'1': '待核销',
				'2': '已完成',
				'3': '售后/退款'
			};
			return statusMap[status] || '未知状态';
		},

		/**
		 * 显示确认对话框
		 */
		showConfirmDialog(content, title = '提示') {
			this.modalTitle = title;
			this.modalMessage = content;
			// 使用 nextTick 确保 DOM 更新后再打开弹窗
			this.$nextTick(() => {
				if (this.$refs.customModal) {
					this.$refs.customModal.open();
				} else {
					console.warn('弹窗组件未找到，使用备用方案');
					// 备用方案：使用 uni.showModal
					uni.showModal({
						title: title,
						content: content,
						success: (res) => {
							if (this.confirmCallback) {
								this.confirmCallback(res.confirm);
							}
						}
					});
				}
			});
			this.confirmCallback = null; // 清空之前的回调
			return new Promise((resolve) => {
				this.confirmCallback = resolve;
			});
		},

		/**
		 * 关闭自定义弹窗
		 */
		closeModal() {
			if (this.$refs.customModal) {
				this.$refs.customModal.close();
			}
			this.modalTitle = '';
			this.modalMessage = '';
			this.confirmCallback = null;
		},

		/**
		 * 确认自定义弹窗
		 */
		confirmModal() {
			if (this.confirmCallback) {
				this.confirmCallback(true);
			}
			this.closeModal();
		},

		/**
		 * 显示日期选择器
		 */
		showDatePicker() {
			console.log('显示日期选择器');
			this.showDatePickerVisible = true;
		},

		/**
		 * 日期范围确认回调
		 */
		onDateRangeConfirm(dateRange) {
			this.dateRange = dateRange;
			this.pageNum = 1;
			this.orderList = [];
			this.hasMore = true;
			this.loadOrderList();
		},

		/**
		 * 获取日期筛选文本
		 */
		getDateFilterText() {
			if (this.dateRange.startDate && this.dateRange.endDate) {
				return `${this.dateRange.startDate} 至 ${this.dateRange.endDate}`;
			}
			return '选择日期';
		},

		/**
		 * 获取模拟数据
		 */
		getMockOrderData() {
			const mockOrders = [
				{
					id: '1',
					orderNumber: 'E240820120TXXXX',
					shopName: 'xxxxx直营店',
					productImage: '/static/images/default-product.png',
					productName: '手工编织XXXXXXX',
					quantity: 1,
					totalAmount: '188.88',
					status: '0'
				},
				{
					id: '2',
					orderNumber: 'E240820120TXXXX',
					shopName: 'xxxxx直营店',
					productImage: '/static/images/default-product.png',
					productName: '手工编织',
					quantity: 1,
					totalAmount: '150.00',
					status: '1'
				},
				{
					id: '3',
					orderNumber: 'E240820120TXXXX',
					shopName: 'xxxxx直营店',
					productImage: '/static/images/default-product.png',
					productName: '手工编织',
					quantity: 1,
					totalAmount: '150.00',
					status: '2'
				},
				{
					id: '4',
					orderNumber: 'E240820120TXXXX',
					shopName: 'xxxxx直营店',
					productImage: '/static/images/default-product.png',
					productName: '手工编织',
					quantity: 1,
					totalAmount: '150.00',
					status: '3'
				},
				{
					id: '5',
					orderNumber: 'E240820120TXXXX',
					shopName: 'xxxxx直营店',
					productImage: '/static/images/default-product.png',
					productName: '手工编织',
					quantity: 1,
					totalAmount: '150.00',
					status: '3'
				},
				{
					id: '6',
					orderNumber: 'E240820120TXXXX',
					shopName: 'xxxxx直营店',
					productImage: '/static/images/default-product.png',
					productName: '手工编织',
					quantity: 1,
					totalAmount: '150.00',
					status: '3'
				},
				{
					id: '7',
					orderNumber: 'E240820120TXXXX',
					shopName: 'xxxxx直营店',
					productImage: '/static/images/default-product.png',
					productName: '手工编织',
					quantity: 1,
					totalAmount: '150.00',
					status: '3'
				}
			];

			// 根据当前标签过滤数据
			const currentStatus = this.tabs[this.currentTab].status;
			let filteredOrders = mockOrders;
			
			if (currentStatus !== 'all') {
				filteredOrders = mockOrders.filter(order => order.status === currentStatus);
			}

			// 根据搜索关键词过滤
			if (this.searchKeyword) {
				filteredOrders = filteredOrders.filter(order => 
					order.productName.includes(this.searchKeyword) ||
					order.orderNumber.includes(this.searchKeyword)
				);
			}

			return {
				list: filteredOrders,
				total: filteredOrders.length
			};
		}
	}
}
</script>

<style lang="scss" scoped>
::v-deep .uni-page-head{
	background-color: #fff !important;
}
.container {
	background: #F4F8FB;
	min-height: 100vh;
	overflow-x: hidden; /* 防止横向滚动 */
	width: 100%;
	box-sizing: border-box;
}

/* 搜索框样式 */
.search-container {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	width: 100%;
	box-sizing: border-box;
}

.search-box {
	display: flex;
	align-items: center;
	background: #fff;
	border-radius: 30rpx;
	padding: 0 24rpx;
	height: 70rpx;
	flex: 1;
	box-sizing: border-box;
}

.search-input {
	flex: 1;
	margin-left: 16rpx;
	font-size: 28rpx;
	color: #333;
}

/* 日期筛选按钮样式 */
.date-filter-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	flex-shrink: 0;
}

.date-filter-btn image {
	width: 32rpx;
	height: 32rpx;
}

/* 标签栏样式 */
.tab-container {
	display: flex;
	// background: #fff;
	// border-bottom: 1rpx solid #eee;
	width: 100%;
	box-sizing: border-box;
	overflow-x: auto;
	overflow-y: hidden;
	-webkit-overflow-scrolling: touch;
}

.tab-item {
	position: relative;
	flex: none;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 88rpx;
	border-bottom: 4rpx solid transparent;
	white-space: nowrap;
	min-width: 120rpx;
	padding: 0 20rpx;
	
	&.active {
		border-bottom-color: #66D47E;
		
		.tab-text {
			color: #000;
			font-weight: bold;
		}
	}
}

.tab-text {
	font-size: 26rpx;
	color: #666;
	white-space: nowrap;
}

.tab-badge {
	position: absolute;
	top: 10rpx;
	right: 20rpx;
	background: #ff4757;
	color: #fff;
	font-size: 20rpx;
	border-radius: 20rpx;
	padding: 4rpx 12rpx;
	min-width: 32rpx;
	text-align: center;
	line-height: 1;
}

/* 订单列表样式 */
.order-list {
	flex: 1;
	padding: 20rpx;
	box-sizing: border-box;
	width: 100%;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400rpx;
	padding: 40rpx 0;
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-weight: 600;
	font-size: 27rpx;
	color: #666666;
}

.order-item {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
	width: 100%;
	box-sizing: border-box;
}

/* 订单头部 */
.order-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
	border-bottom: 1px solid #f5f5f5;
	padding-bottom: 16rpx;
	width: 100%;
}

.order-number {
	font-size: 26rpx;
	color: #333;
	max-width: 70%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: flex;
	align-items: center;
	.psIMG{
		width: 35rpx;
		height: 27rpx;
		margin-right: 10rpx;
	}
	.ztIMG{
		width: 25rpx;
		height: 31rpx;
		margin-right: 10rpx;
	}
	image{
		position: relative;
		top: 5rpx;
	}
}

.order-status {
	padding: 4rpx 12rpx;
	border-radius: 4rpx;
	font-weight: 600;
	font-size: 28rpx;
	
	&.status-pending {
		color: #FF5F54;
	}
	
	&.status-paid {
		color: #66D47E;
	}
	
	&.status-completed {
		color: #666666;
	}
	
	&.status-refund {
		color: #F9AF25;
	}
}

/* 商家信息 */
.shop-info {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	width: 100%;
}

.shop-name {
	margin-left: 8rpx;
	font-size: 24rpx;
	color: #666;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.settings-icon {
	margin-left: auto;
}

/* 商品信息 */
.product-info {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	width: 100%;
	box-sizing: border-box;
}

.product-image {
	width: 100rpx;
	height: 100rpx;
	border-radius: 8rpx;
	background: #f0f0f0;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.product-detail {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.product-name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 8rpx;
	line-height: 1.4;
}

.text-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.product-spec {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 4rpx;
}

.product-quantity {
	font-size: 24rpx;
	color: #999;
	display: flex;
	justify-content: space-between;
}

/* 价格信息 */
.price-info {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	margin-bottom: 24rpx;
	width: 100%;
}

.price-label {
	font-size: 24rpx;
	color: #666;
}

.order-price {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

/* 操作按钮 */
.order-actions {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	flex-wrap: wrap;
	width: 100%;
	border-top: 1px solid #f5f5f5;
	padding-top: 16rpx;
}

.action-left {
	min-width: 0;
}

.countdown-info {
	font-weight: 500;
	font-size: 24rpx;
	color: #000000;
}

.countdown {
	font-weight: 500;
	font-size: 24rpx;
	color: #000000;
	.num{
		color: #FF5F54;
	}
}

.delivery-info {
	font-size: 24rpx;
	color: #999;
}

.action-buttons {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	justify-content: flex-end;
}

.action-btn {
	font-size: 24rpx;
	border-radius: 30rpx;
	padding: 0 24rpx;
	height: 60rpx;
	line-height: 60rpx;
	margin-left: 16rpx;
	// border: 1rpx solid #ddd;
	background: #fff;
	color: #666;
	
	&.primary-btn {
		background: #66D47E;
		color: #fff;
		border-color: #66D47E;
	}
	
	&.cancel-btn {
		border: 1px solid #ddd;
		color: #666;
	}
}
uni-button:after{
	display: none;
}
/* 加载状态 */
.loading-state {
	padding: 20rpx 0;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	width: 100%;
}

.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
}

.loading-text {
	font-size: 24rpx;
	color: #999;
}

.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
}

.no-more-text {
	font-size: 24rpx;
	color: #999;
	position: relative;
	padding: 0 30rpx;
	
	&::before,
	&::after {
		content: '';
		position: absolute;
		top: 50%;
		width: 80rpx;
		height: 1px;
		background: #ddd;
	}
	
	&::before {
		left: -60rpx;
	}
	
	&::after {
		right: -60rpx;
	}
}
.order-info{
	display: flex;
	align-items: flex-start;
	justify-content: flex-end;
	width: 100%;
	padding-bottom: 16rpx;
	.order-info-label{
		font-weight: 500;
		font-size: 23rpx;
		color: #999999;
		min-width: 120rpx;
	}
	.order-info-value{
		font-weight: 500;
		font-size: 23rpx;
		color: #000000;
		flex: 1;
		text-align: right;
	}
	.hxm{
		font-family: FontName;
		font-size: 32rpx;
		color: #66D47E;
	}
}

/* 自定义弹窗样式 */
.confirm-popup-content {
		width: 540rpx;
		background-color: #fff;
		border-radius: 30rpx;
		padding: 40rpx 60rpx;
		box-sizing: border-box;

		.title {
			font-family: 苹方-简, 苹方-简;
			font-weight: normal;
			font-size: 30rpx;
			color: #000000;
			line-height: 42rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
			margin-bottom: 30rpx;
		}

		.content {
			font-family: 苹方-简, 苹方-简;
			font-weight: normal;
			font-size: 24rpx;
			color: #666666;
			line-height: 36rpx;
			text-align: center;
		}

		.btn-box {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 30rpx;

			.cancel,
			.confirm {
				width: 200rpx;
				height: 80rpx;
				border-radius: 40rpx;
			}
		}

		.cancel {
			width: 200rpx;
			height: 80rpx;
			background-color: #F5F5F5;
			border-radius: 40rpx;
			color: #666666;
			text-align: center;
			line-height: 80rpx;
		}

		.confirm {
			width: 200rpx;
			height: 80rpx;
			background: #34BC4D;
			border-radius: 40rpx;
			color: #fff;
			text-align: center;
			line-height: 80rpx;
		}
	}

/* 核销码弹窗样式 */
.referral-popup{
  z-index: 100;
}

.popup-content{
  width: 660rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 200;
}

.popup-top{
  position: relative;
  top:130rpx;
  z-index: 2;
  
  .popup-top-image{
    width: 508rpx;
    height: auto;
    display: block;
  }
}

.popup-main{
  width: 708rpx;
  min-height: 580rpx;
  background-image: url('/static/images/profile/referral-code-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 0 0 32rpx 32rpx;
  padding: 200rpx 80rpx 80rpx 80rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -10px;
  position: relative;
  z-index: 1;
}

.popup-title{
  font-family: 苹方-简-中粗体, 苹方-简;
  font-weight: normal;
  font-size: 46rpx;
  color: #000000;
  line-height: 46rpx;
  margin-bottom: 60rpx;
  text-align: center;
}

.input-container{
  width: 100%;
  margin-bottom: 40px;
}

.referral-input{
  width: 100%;
  height: 100rpx;
  background: #FFFFFF;
  border: 1px solid #CCCCCC;
  border-radius: 16rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  
  &:focus{
    border-color: #4CAF50;
    background: rgba(255, 255, 255, 1);
  }
}

.input-placeholder{
  color: #bbb;
  font-size: 16px;
}

.btn-row{
  display: flex;
  width: 100%;
  justify-content: space-between;
  gap: 15px;
}

.btn-cancel{
  width: 260rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #F5F5F5;
  border-radius: 40rpx;
  font-size: 32rpx;
  color: #000;
  text-align: center;
}

.btn-confirm{
  width: 260rpx;
  height: 80rpx;
  background: #34BC4D;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
}
</style>
