<template>
	<view class="refund-container">
		<!-- 退款原因输入区域 -->
		<view class="refund-reason-section">
			<textarea 
				class="reason-textarea" 
				placeholder="请简述原因" 
				v-model="refundReason" 
				maxlength="200"
			></textarea>
		</view>
		<!-- 提交按钮 -->
		<view class="submit-btn-wrapper">
			<button class="submit-btn" :disabled="!refundReason.trim()" @click="submitRefund">提交</button>
		</view>
	</view>
</template>

<script>
import { orderApi } from '@/common/api';
	export default {
		data() {
			return {
				refundReason: '', // 退款原因
				orderId: '', // 订单ID
			}
		},
		onLoad(options) {
			// 获取传递过来的订单ID
			if (options.orderId) {
				this.orderId = options.orderId;
			}
		},
		methods: {
			/**
			 * 提交退款申请
			 */
			async submitRefund() {
				// 表单验证
				if (!this.refundReason.trim()) {
					uni.showToast({
						title: '请输入原因',
						icon: 'none'
					});
					return;
				}
				const res = await orderApi.refundProcess({
					refundApplicationId: this.orderId,
					processResult: '2',
					merchantReply: this.refundReason,
					merchantOperator: 'admin'
				});
				if(res.code === 200){	
					uni.showToast({
						title: '拒绝退款成功',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack({
							delta: 1
						});
					}, 1500);
				} else {
					uni.showToast({
						title: res.msg || '拒绝退款失败',
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style lang="scss">
::v-deep .uni-page-head{
	background-color: #fff !important;
}
.refund-container {
	min-height: 100vh;
	background-color: #F4F8FB;
	padding: 0 0 40rpx;
	box-sizing: border-box;
}

/* 退款原因区域 */
.refund-reason-section {
	background-color: #FFFFFF;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 10rpx;
	font-weight: Semibold;
}

.reason-textarea {
	width: 100%;
	height: 200rpx;
	background-color: #F4F8FB;
	border-radius: 12rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333333;
	box-sizing: border-box;
}

/* 退款金额区域 */
.refund-amount-section {
	background-color: #FFFFFF;
	padding: 30rpx;
	margin-bottom: 40rpx;
}

.amount-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #D1D1D1;
}

.location-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.amount-text {
	font-size: 28rpx;
	color: #000;
	font-weight: 600;
}

/* 提交按钮 */
.submit-btn-wrapper {
	padding: 0 30rpx;
}

.submit-btn {
	width: 100%;
	height: 92rpx;
	line-height: 92rpx;
	background-color: #66D47E;
	color: #FFFFFF;
	font-size: 32rpx;
	font-weight: 500;
	border-radius: 44rpx;
	text-align: center;
	border: none;
}

.submit-btn[disabled] {
	background-color: #BEEDC5 !important;
	border: none !important;
	color: #FFFFFF !important;
}
</style>
