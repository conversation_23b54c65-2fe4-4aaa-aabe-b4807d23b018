# 创建新的附近人页面
<template>
	<view class="container">
		<!-- 未授权状态 -->
		<view class="no-permission" v-if="!hasLocationPermission">
			<image class="permission-image" src="/static/images/location.png" mode="aspectFit"></image>
			<text class="permission-title">获取位置失败</text>
			<text class="permission-desc">开启位置权限，查看附近的人</text>
			<button class="permission-btn" @click="requestLocationPermission">一键开启位置权限</button>
		</view>
		
		<!-- 已授权状态 -->
		<view class="nearby-list" v-else>
			<!-- 距离筛选 -->
			<view class="filter-bar">
				<text 
					class="filter-item" 
					v-for="(item, index) in distanceFilters" 
					:key="index"
					:class="{active: currentDistance === item.value}"
					@tap="changeDistance(item.value)"
				>
					{{item.label}}
				</text>
			</view>
			
			<!-- 用户列表 -->
			<scroll-view 
				scroll-y 
				class="user-list"
				@scrolltolower="loadMore"
				:refresher-enabled="true"
				:refresher-triggered="isRefreshing"
				@refresherrefresh="onRefresh"
			>
				<view 
					class="user-item"
					v-for="(item, index) in nearbyUsers"
					:key="index"
					@tap="navigateToUser(item)"
				>
					<image class="avatar" :src="item.avatar" mode="aspectFill"></image>
					<view class="user-info">
						<view class="info-header">
							<text class="nickname">{{item.nickname}}</text>
							<text class="distance">{{formatDistance(item.distance)}}</text>
						</view>
						<view class="info-content">
							<text class="location">{{item.location}}</text>
							<text class="time">{{formatTime(item.lastActiveTime)}}</text>
						</view>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<uni-load-more :status="loadMoreStatus"></uni-load-more>
			</scroll-view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式状态
const hasLocationPermission = ref(false)
const currentDistance = ref(1000)
const isRefreshing = ref(false)
const loadMoreStatus = ref('more')
const nearbyUsers = ref([])
const currentLocation = ref(null)
const page = ref(1)

// 距离筛选选项
const distanceFilters = [
	{ label: '1km', value: 1000 },
	{ label: '3km', value: 3000 },
	{ label: '5km', value: 5000 },
	{ label: '10km', value: 10000 }
]

// 生命周期钩子
onMounted(() => {
	checkLocationPermission()
})

// 检查位置权限
async function checkLocationPermission() {
	try {
		const res = await uni.getSetting()
		hasLocationPermission.value = res.authSetting['scope.userLocation']
		if (hasLocationPermission.value) {
			getCurrentLocation()
		}
	} catch (error) {
		console.error('获取权限设置失败：', error)
	}
}

// 请求位置权限
async function requestLocationPermission() {
	try {
		await uni.authorize({
			scope: 'scope.userLocation'
		})
		hasLocationPermission.value = true
		getCurrentLocation()
	} catch (error) {
		console.error('授权失败：', error)
		// 引导用户去设置页面开启权限
		uni.showModal({
			title: '提示',
			content: '需要您手动开启位置权限',
			confirmText: '去设置',
			success: (res) => {
				if (res.confirm) {
					uni.openSetting()
				}
			}
		})
	}
}

// 获取当前位置
async function getCurrentLocation() {
	try {
		uni.showLoading({
			title: '定位中...'
		})
		const res = await uni.getLocation({
			type: 'gcj02'
		})
		currentLocation.value = {
			latitude: res.latitude,
			longitude: res.longitude
		}
		loadNearbyUsers()
	} catch (error) {
		console.error('获取位置失败：', error)
		uni.showToast({
			title: '获取位置失败',
			icon: 'none'
		})
	} finally {
		uni.hideLoading()
	}
}

// 加载附近的人
async function loadNearbyUsers() {
	if (!currentLocation.value) return
	
	try {
		if (page.value === 1) {
			uni.showLoading({
				title: '加载中...'
			})
		}
		
		// 这里替换为实际的API调用
		const res = await uni.$api.getNearbyUsers({
			latitude: currentLocation.value.latitude,
			longitude: currentLocation.value.longitude,
			distance: currentDistance.value,
			page: page.value
		})
		
		if (page.value === 1) {
			nearbyUsers.value = res.list
		} else {
			nearbyUsers.value = [...nearbyUsers.value, ...res.list]
		}
		
		loadMoreStatus.value = nearbyUsers.value.length >= res.total ? 'noMore' : 'more'
		page.value++
	} catch (error) {
		console.error('加载附近的人失败：', error)
		uni.showToast({
			title: '加载失败',
			icon: 'none'
		})
	} finally {
		uni.hideLoading()
	}
}

// 切换距离
function changeDistance(distance) {
	if (currentDistance.value === distance) return
	currentDistance.value = distance
	page.value = 1
	loadNearbyUsers()
}

// 下拉刷新
async function onRefresh() {
	isRefreshing.value = true
	page.value = 1
	await getCurrentLocation()
	isRefreshing.value = false
}

// 加载更多
function loadMore() {
	if (loadMoreStatus.value === 'more') {
		loadNearbyUsers()
	}
}

// 跳转到用户主页
function navigateToUser(user) {
	uni.navigateTo({
		url: `/pages/user/profile?id=${user.id}`
	})
}

// 格式化距离
function formatDistance(meters) {
	if (meters < 1000) {
		return `${meters}m`
	}
	return `${(meters / 1000).toFixed(1)}km`
}

// 格式化时间
function formatTime(timestamp) {
	// 这里可以使用日期格式化库，如dayjs
	return '10分钟前' // 示例返回
}
</script>

<style lang="scss">
@import '@/uni.scss';

.container {
	min-height: 100vh;
	background-color: #f5f5f5;
	
	.no-permission {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 200rpx;
		
		.permission-image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 40rpx;
		}
		
		.permission-title {
			font-size: 32rpx;
			color: #333;
			font-weight: bold;
			margin-bottom: 20rpx;
		}
		
		.permission-desc {
			font-size: 28rpx;
			color: #999;
			margin-bottom: 60rpx;
		}
		
		.permission-btn {
			width: 400rpx;
			height: 80rpx;
			line-height: 80rpx;
			background: $color-primary;
			color: #fff;
			border-radius: 40rpx;
			font-size: 28rpx;
		}
	}
	
	.nearby-list {
		.filter-bar {
			display: flex;
			background-color: #fff;
			padding: 20rpx;
			
			.filter-item {
				padding: 10rpx 30rpx;
				font-size: 26rpx;
				color: #666;
				background: #f5f5f5;
				border-radius: 26rpx;
				margin-right: 20rpx;
				
				&.active {
					color: #fff;
					background: $color-primary;
				}
			}
		}
		
		.user-list {
			.user-item {
				display: flex;
				padding: 30rpx;
				background-color: #fff;
				margin-bottom: 2rpx;
				
				.avatar {
					width: 100rpx;
					height: 100rpx;
					border-radius: 50%;
					margin-right: 20rpx;
				}
				
				.user-info {
					flex: 1;
					
					.info-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 10rpx;
						
						.nickname {
							font-size: 30rpx;
							color: #333;
							font-weight: 500;
						}
						
						.distance {
							font-size: 24rpx;
							color: $color-primary;
						}
					}
					
					.info-content {
						display: flex;
						justify-content: space-between;
						align-items: center;
						
						.location {
							font-size: 24rpx;
							color: #666;
						}
						
						.time {
							font-size: 24rpx;
							color: #999;
						}
					}
				}
			}
		}
	}
}
</style> 