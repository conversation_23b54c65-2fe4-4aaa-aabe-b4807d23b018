<template>
	<view class="expert-order-page">
		<view class="appointed-addr" @click="selectAddr">
			<text class="txt" v-if="!addr">选择预约地址</text>
			<view v-else class="addr-wrap">
				<uni-icons type="location" size="20" class="icon-location"></uni-icons>
				<view class="addr-user">
					<view class="addr">{{ addr }}</view>
					<view class="userinfo">
						<text>{{ name }}</text>
						<text class="pl40">{{ phoneNumber }}</text>
					</view>
				</view>
			</view>
			<uni-icons type="right" size="20" color="#999"></uni-icons>
		</view>
		<view class="pannel period">
			<view class="time">
				<image class="food-guide" src="/static/images/expertOrder/foodGuide.png"></image>
				<view class="price">
					<view class="txt1">美食向导</view>
					<text class="txt2">400 贝壳币/小时</text>
				</view>
				<view class="number-wrap">
					<view :class="['minus', {disabled: hours <= 2}]" @click="minus"></view>
					<view class="hours">{{ hours }}</view>
					<view :class="['plus', {disabled: hours >= 12}]" @click="plus"></view>
				</view>
			</view>
			<view class="message-wrap">
				<text class="txt1">留言</text>
				<text class="txt2">{{ remark }}</text>
				<view class="icon-edit" @click="editRemark"></view>
			</view>
		</view>
		<view class="pannel datetime-wrap">
			<text class="txt1">预约时间</text>
			<text class="txt2" @click="selectDateTime">{{ dateInfo }}</text>
			<uni-icons type="right" size="20" color="#999" @click="selectDateTime"></uni-icons>
		</view>
		<view class="fee-wrap">
			<view class="item">
				<text class="type">订单金额</text>
				<text class="account">400 贝壳币</text>
			</view>
			<view class="item">
				<text class="type">交通费细则</text>
			  <uni-icons class="ml6" type="info" size="20" color="#999" @click="showTips('fare')"></uni-icons>
				<text class="account">10 贝壳币</text>
			</view>
			<view class="item">
				<text class="type">保险细则</text>
			  <uni-icons class="ml6" type="info" size="20" color="#999" @click="showTips('insurance')"></uni-icons>
				<text class="account">2 贝壳币</text>
				<image
				  class="icon"
				  :src="isCheckedInsurance ? '/static/images/profile/<EMAIL>' : '/static/images/profile/<EMAIL>'"
				  @click="swicthInsurance"
				></image>
			</view>
		</view>
		<view class="pay-type-wrap">
			<view class="item">
				<image class="pay-icon" src="/static/images/shoping/ye.png"></image>
				<text>账户余额</text>
				<text class="balance fg"><text class="color">450</text> 贝壳币</text>
				<image
				  class="icon"
				  :src="payType === 0 ? '/static/images/profile/<EMAIL>' : '/static/images/profile/<EMAIL>'"
				  @click="swicthPayType(0)"
				></image>
			</view>
			<view class="item">
				<image class="pay-icon" src="/static/images/shoping/zfb.png"></image>
				<text class="fg">支付宝</text>
				<image
				  class="icon"
				  :src="payType === 1 ? '/static/images/profile/<EMAIL>' : '/static/images/profile/<EMAIL>'"
				  @click="swicthPayType(1)"
				></image>
			</view>
			<view class="item">
				<image class="pay-icon wechat" src="/static/images/shoping/wx.png"></image>
				<text class="fg">微信支付</text>
				<image
				  class="icon"
				  :src="payType === 2 ? '/static/images/profile/<EMAIL>' : '/static/images/profile/<EMAIL>'"
				  @click="swicthPayType(2)"
				></image>
			</view>
		</view>
		<view class="pay-wrap">
			<view class="rule-wrap">
				<image
				  class="icon"
				  :src="isAgreeRules ? '/static/images/profile/<EMAIL>' : '/static/images/profile/<EMAIL>'"
				  @click="agreeRule"
				></image>
				<text class="fg">我已阅读并同意</text>
				<text class="rule">《用户交易规则》</text>
			</view>
			<view class="goto-pay">
				<text>共1件 总计：</text>
				<text class="total">410</text>
				<text class="txt">贝壳币</text>
				<button :class="['pay-btn', {disabled: !isAgreeRules}]">立即支付</button>
			</view>
		</view>
		<uni-popup class="pop-mask" ref="tips">
			<view class="popup-content">
				<text>{{ tips }}</text>
			</view>
		</uni-popup>
		<uni-popup ref="remark" type="bottom">
			<view class="remark-wrap">
				<view class="title">请填写备注</view>
				<uni-icons type="closeempty" size="20" class="icon-close"></uni-icons>
				<textarea placeholder="" maxlength="50" class="area" v-model="remarkBackups"></textarea>
				<view class="left">{{ leftWords }}</view>
				<view class="separate"></view>
				<view
				  :class="['btn-submit', {disabled: !remarkBackups.length}]"
					@click="setRemark"
				>提交</view>
			</view>
		</uni-popup>
		<uni-popup type="bottom" ref="dateTime">
			<view><Dt @close="closeBottomPop" @sendDateTime="sendDateTime"></Dt></view>
		</uni-popup>
	</view>
</template>

<script>
	import { toRaw } from 'vue'
	import Dt from './components/DateTime.vue'
	export default {
		components: {
			Dt
		},
		data() {
			return {
				hours: 2,
				isCheckedInsurance: false,
				payType: 0, // 余额-0 支付宝-1 微信-2
				isAgreeRules: false,
				selectDate: '',
				selectSatrtTime: '',
				addr: '',
				name: '',
				phoneNumber: '',
				// addr: '我是地址我是地址我是地址我是地址我是地址我是地址我是地址我是地址我是地址我是地址我是地址我是地址我是地址我是地址我是地址我是地址',
				tips: '',
				remark: '',
				remarkBackups: ''
			}
		},
		computed: {
			dateInfo() {
				if (!this.selectDate) {
					return '请选择'
				}
				let h = Number(this.selectSatrtTime.split(':')[0])
				let endHour = h + this.hours
				if (endHour === 24) {
					endHour = '00'
				}
				if (endHour > 24) {
					endHour -= 24
				}
				if (endHour < 10) {
					endHour = '0' + endHour
				}
				endHour += ':00'
				return `${this.selectDate} ${this.selectSatrtTime}-${endHour}`
			},
			leftWords() {
				return `${this.remark.length}/50`
			}
		},
		onLoad() {
		  uni.$on('addressSelected', addr => {
				console.log(toRaw(addr))
				this.addr = addr.detailAddress;
				this.name = addr.receiverName;
				this.phoneNumber = addr.contactNumber;
			})	
		},
		beforeDestroy() {
			uni.$off('addressSelected')
		},
		methods: {
			selectAddr() {
				uni.navigateTo({
					url: '/pages/profile/address'
				})
			},
			minus() {
				if (this.hours <= 2) {
					return
				}
				this.hours--
			},
			plus() {
				if (this.hours >= 12) {
					return
				}
				this.hours++
			},
			swicthInsurance() {
				this.isCheckedInsurance = !this.isCheckedInsurance
			},
			swicthPayType(type) {
				this.payType = type
			},
			agreeRule() {
				this.isAgreeRules = !this.isAgreeRules
			},
			selectDateTime() {
				this.$refs.dateTime.open()
			},
			closeBottomPop() {
				this.$refs.dateTime.close()
			},
			sendDateTime(payload) {
				console.log(payload)
				this.selectDate = payload.day
				this.selectSatrtTime = payload.time
				this.closeBottomPop()
			},
			showTips(type) {
				if (type === 'fare') {
					this.tips = '交通费细则'
				}
				if (type === 'insurance') {
					this.tips = '保险细则保险细则保险细则保险细则保险细则保险细则保险细则'
				}
				this.$refs.tips.open()
			},
			editRemark() {
				this.remarkBackups = this.remark;
				this.$refs.remark.open()
			},
			setRemark() {
				if (!this.remarkBackups.length) {
					return
				}
				this.remark = this.remarkBackups
				this.$refs.remark.close()
			}
		}
	}
</script>

<style scoped lang="scss">
.expert-order-page {
	padding: 0 32rpx;
	background-color: #f4f8fb;
	height: 100%;
	overflow-y: scroll;
	.appointed-addr {
		min-height: 144rpx;
		background-color: #fff;
		margin-top: 16rpx;
		padding: 0 24rpx;
		display: flex;
		align-items: center;
		border-radius: 24rpx;
		justify-content: space-between;
		.txt {
			font-size: 28rpx;
			color: #999
		}
		.addr-wrap {
			flex-grow: 1;
			display: flex;
			padding: 32rpx 0;
			.icon-location {
				margin-right: 20rpx;
				margin-top: 2rpx;
			}
			.addr-user {
				flex-grow: 1;
				padding-right: 12rpx;
			}
			.addr {
				font-size: 32rpx;
				line-height: 48rpx;
				color: #000;
			}
			.userinfo {
				margin-top: 10rpx;
				color: #666;
				font-size: 28rpx;
			}
			.pl40 {
				padding-left: 40rpx;
			}
		}
	}
	.pannel {
		margin-top: 24rpx;
		border-radius: 24rpx;
		padding: 32rpx 24rpx;
		box-sizing: border-box;
		background-color: #fff;
	}
	.period {
		height: 264rpx;
		.time {
			height: 96rpx;
			display: flex;
			// justify-content: space-between;
			align-items: flex-end;
			margin-bottom: 64rpx;
			.food-guide {
				height: 96rpx;
				width: 96rpx;
				flex-shrink: 0;
				margin-right: 16rpx;
			}
			.price {
				flex-grow: 1;
				padding-right: 16rpx;
				display: flex;
				min-width: 0;
				flex-direction: column;
				.txt1 {
					color: #000;
					font-weight: bold;
					font-size: 28rpx;
					height:48rpx;
					line-height: 48rpx;
					margin-bottom: 4rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
				.txt2 {
					font-size: 24rpx;
					color: #999;
					height: 28rpx;
					line-height: 28rpx;
					margin-bottom: 10rpx;
				}
			}
			.number-wrap {
				flex-shrink: 0;
				width: 180rpx;
				height: 56rpx;
				background-color: #f6f7fb;
				border-radius: 40rpx;
				display: flex;
				justify-content: space-between;
				.hours {
					height: 56rpx;
					line-height: 56rpx;
					font-size: 28rpx;
					color: #000;
				}
				.minus, .plus {
					width: 52rpx;
					height: 48rpx;
					margin-top: 4rpx;
					position: relative;
					&::before {
						content: "";
						background-color: #333;
						position: absolute;
						width: 20rpx;
						height: 4rpx;
						top: 22rpx;
						left: 16rpx;
					}
				}
				.plus::after {
						content: "";
						background-color: #333;
						position: absolute;
						width: 4rpx;
						height: 20rpx;
						top: 14rpx;
						left: 24rpx;
				}
				.disabled::before, .disabled::after {
					opacity: 0.4;
				}
			}
		}
	}
  .message-wrap, .datetime-wrap {
  	height: 40rpx;
		align-items: center;
  	display: flex;
  	.txt1 {
  		font-size: 28rpx;
  		flex-shrink: 0;
  	}
  	.txt2 {
  		font-size: 28rpx;
  		color: #333;
  		text-align: right;
  		padding-left: 20rpx;
			padding-right: 12rpx;
  		flex-grow: 1;
  		overflow: hidden;
  		text-overflow: ellipsis;
  		white-space: nowrap;
  	}
		.icon-edit {
			flex-shrink: 0;
			width: 32rpx;
			height: 32rpx;
			background: url('/static/images/expertOrder/ic_edit.png') no-repeat;
			background-size: 100% 100%;
		}
  }
	.datetime-wrap {
		height: 104rpx;
	}
	.fee-wrap, .pay-type-wrap {
		background-color: #fff;
		padding: 0 24rpx;
		margin-top: 24rpx;
		border-radius: 24rpx;
		.item {
			height: 104rpx;
			display: flex;
			align-items: center;
			color: #000;
			font-size: 28rpx;
			.ml6 {
				margin-left: 6rpx;
			}
			.account {
				flex-grow: 1;
				text-align: right;
			}
			.icon {
				height: 40rpx;
				width: 40rpx;
				margin-left: 20rpx;
			}
		}
	}
	.pay-type-wrap {
		margin-bottom: 360rpx;
		.pay-icon {
			width: 48rpx;
			height: 48rpx;
			margin-right: 16rpx;
			&.wechat {
				height: 40rpx;
			}
		}
		.fg {
			flex-grow: 1
		}
		.balance {
			padding-left: 32rpx;
			.color {
				color: #f7532e
			}
		}
	}
	.pay-wrap {
		height: 248rpx;
		padding: 0 32rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		.rule-wrap {
			height: 80rpx;
			display: flex;
			align-items: center;
			font-size: 28rpx;
			color: #333;
			.icon {
				height: 32rpx;
				width: 32rpx;
				margin-right: 6rpx;
			}
			.rule {
				color: #66d47e
			}
		}
		.goto-pay {
			padding-top: 16rpx;
			height: 112rpx;
			display: flex;
			align-items: center;
			font-size: 28rpx;
			color: #000;
			.total {
				font-size: 40rpx;
				color: #f7532e;
			}
			.txt {
				color: #f7532e;
				font-size: 24rpx;
				padding-left: 16rpx;
				flex-grow: 1;
			}
			.pay-btn {
				width: 304rpx;
				height: 96rpx;
				line-height: 96rpx;
				background-color: #66d47e;
				border-radius: 48rpx;
				font-size: 32rpx;
				color: #fff;
				&.disabled {
					opacity: 0.4;
				}
			}
		}
	}
	.popup-mask {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.popup-content {
		background-color: #fff;
		padding:32rpx;
		border-radius: 16rpx;
	}
	.remark-wrap {
		height: 550rpx;
		border-radius: 32rpx 32rpx 0 0;
		box-sizing: border-box;
		padding: 32rpx 32rpx 0;
		background-color: #fff;
		.title {
			height: 44rpx;
			line-height: 44rpx;
			color: #000;
			font-size: 32rpx;
			margin-bottom: 40rpx;
			text-align: center;
		}
		.icon-close {
			width: 28rpx;
			height: 28rpx;
			position: absolute;
			top: 36rpx;
			right: 32rpx;
		}
		.area {
			height: 240rpx;
			line-height: 44rpx;
			width: 100%;
			padding: 32rpx 24rpx;
			box-sizing: border-box;
			border-radius: 24rpx;
			background-color: rgba(243,243,243, .7);
			margin-bottom: 20rpx;
		}
		.left {
			position: absolute;
			top: 300rpx;
			right: 56rpx;
			color: #999;
		}
		.separate {
			height: 2rpx;
			position: absolute;
			top: 374rpx;
			background-color: #dedede;
			left: 0;
			right: 0;
		}
		.btn-submit {
			margin-top: 36rpx;
			height: 96rpx;
			line-height: 96rpx;
			background-color: #66d47e;
			border-radius: 48rpx;
			text-align: center;
			font-size: 32rpx;
			color: #fff;
			&.disabled {
				opacity: 0.4;
			}
		}
	}
}
</style>;
