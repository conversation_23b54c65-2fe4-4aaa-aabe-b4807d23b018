<template>
	<view class="date-time-wrap">
		<uni-icons class="closePop" type="closeempty" size="20" color="#333" @click="close"></uni-icons>
	  <view class="title">选择服务时间</view>
		<view class="select-date-time">
			<view class="date">
			  <view :class="['item', {curr: idx === currDateIdx}]" v-for="(item, idx) in dataList" @click="selectDate(idx)">
					<text class="txt">{{ item.date }}</text>
				</view>
			</view>
			<view class="time">
				<view :class="['item', {selected: item.selected, disabled: item.isDisabled}]" v-for="(item, idx) in dataList[currDateIdx].timeList">
					<text class="txt">{{ item.time }}</text>
					<image
						class="icon"
						:src="getImageSrc(item)"
						@click="selectTime(item, idx)"
					></image>
				</view>
			</view>
		</view>
		<view class="submit-wrap">
			<button :class="['btn', {disabled: isDisabled}]" @click="selectDateTime">确定</button>
		</view>
	</view>
</template>

<script>
	import data from './dataList.json'
	export default {
		data() {
			return {
				dataList: data.dataList,
				currDateIdx: 0,
				selectedDateIdx: undefined,
				currTimeIdx: undefined,
				selectedTimeIdx: undefined,
			}
		},
		computed: {
			getImageSrc() {
				return item => {
					let src = '';
					if (item.selected) {
						src = '/static/images/profile/<EMAIL>';
					} else if (item.isDisabled) {
						src = '/static/images/profile/<EMAIL>'; 
					} else {
						src = '/static/images/profile/<EMAIL>';
					}
					return src
				}
			},
			isDisabled() {
				return this.selectedDateIdx === undefined
			}
		},
		methods: {
			close() {
				this.$emit('close')
				// console.log(this.$parent)
				// this.$parent.$refs.dateTime.close()
			},
			selectTime(item, idx) {
				if (item.isDisabled) {
					return;
				}
				if (this.selectedDateIdx !== undefined) {
					console.log('进来啦', this.selectedDateIdx)
				  this.dataList[this.selectedDateIdx].timeList[this.selectedTimeIdx].selected = false
				}
				this.selectedDateIdx = this.currDateIdx;
				this.currTimeIdx = idx;
				this.selectedTimeIdx = idx;
				this.dataList[this.selectedDateIdx].timeList[idx].selected = true
			},
			selectDate(idx) {
				this.currDateIdx = idx
			},
			selectDateTime() {
				if (this.selectedDateIdx === undefined) {
					return;
				}
				let day = this.dataList[this.selectedDateIdx].date;
				let time = this.dataList[this.selectedDateIdx].timeList[this.selectedTimeIdx].time;
				this.$emit("sendDateTime", {day, time})
			}
		}
	}
</script>

<style lang="scss" scoped>
.date-time-wrap {
	position: relative;
	height: 1076rpx;
	max-height: 80vh;
	background-color: #fff;
	border-radius: 32rpx 32rpx 0 0;
	.closePop {
		position: absolute;
		top: 32rpx;
		right: 32rpx;
	}
	.title {
		height: 44rpx;
		color: #000;
		font-size: 32rpx;
		text-align: center;
		padding: 32rpx 0 40rpx;
	}
	.select-date-time {
		display: flex;
		height: 790rpx;
		overflow: hidden;
		.date {
			overflow-y: scroll;
			width:316rpx;
			height: 790rpx;
			background-color: #f6f7fb;
			.item {
				height: 100rpx;
				color: #000;
				font-size: 28rpx;
				line-height: 100rpx;
				padding-left: 32rpx;
				&.curr {
					color: #66d47e;
					background-color: #fff;
				}
			}
		}
		.time {
			flex-grow: 1;
			height: 790rpx;
			padding-bottom: 168rpx;
			overflow-y: scroll;
			.item {
				margin: 0 24rpx;
				height: 100rpx;
				border-bottom: 1rpx solid rgba(209,209,209, .4);
				display: flex;
				justify-content: space-between;
				align-items: center;
				&:last-child {
					border-bottom: 0;
				}
				.icon {
					width: 40rpx;
					height: 40rpx;
				}
				&.disabled {
					.txt {
						opacity: 0.5;
					}
				}
			}
		}
	}
	.submit-wrap {
		height: 168rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0 32rpx;
		border-top: 1rpx solid rgba(222,222,222, .5);
		.btn {
			background-color: #66d47e;
			width: 100%;
			height: 96rpx;
			line-height: 96rpx;
			border-radius: 48rpx;
			color: #fff;
			font-size: 32rpx;
			&.disabled {
				opacity: 0.4;
			}
		}
	}
}
</style>