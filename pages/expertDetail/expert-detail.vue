<template>
  <view class="expert-detail-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text>加载中...</text>
    </view>

    <!-- 主要内容 -->
    <view v-else>
      <!-- 图片轮播组件 - 通顶展示 -->
      <view class="carousel-section">
        <ImageCarousel :imageList="albumImages" :autoplay="false" @change="onCarouselChange" @imageClick="onImageClick" />
        <!-- 返回按钮 -->
        <view class="back-button" @click="goBack" :style="{ top: statusBarHeight + 20 + 'px' }">
          <image class="back-icon" src="/static/icon/back.png" />
        </view>
        <!-- 分享按钮 -->
        <view class="share-button" @click="showShareDialog" :style="{ top: statusBarHeight + 20 + 'px' }">
          <view class="three-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
        </view>
      </view>

      <!-- 吸顶组件 -->
      <StickyHeader 
        :avatarUrl="avatarUrl" 
        :nickname="userInfo.nickname"
        :stickyTop="120"
        :scrollTop="pageScrollTop"
        :statusBarHeight="statusBarHeight"
      />
      <!-- 用户基本信息 -->
      <view class="header">
        <view class="base-info">
					<view class="nickname-row">
						<view v-if="userInfo.sex || age" class="gender-age-tag" :class="userInfo.sex === 2 ? 'female-bg' : 'male-bg'">
						  <image v-if="userInfo.sex" class="gender-icon-img" :src="userInfo.sex === 2 ? '/static/index/icon/female.png' : '/static/index/icon/male.png'" />
						  <text class="age-text">{{ age }}</text>
						</view>
						<view v-if="userInfo.isReal === 1" class="verified">已实名</view>
					</view>
          <view class="location-row">
            <text class="location">
              <image class="distance-icon" src="/static/index/icon/<EMAIL>" />
              {{ userInfo.location }}
            </text>
            <text class="online">{{ formatDistance(userInfo.distance) }}</text>
            <view class="online-status" :class="{ offline: !userInfo.isOnline }">· {{ userInfo.isOnline ? '在线' : '离线' }}</view>
            <text class="online">· {{ userInfo.likeCount || 0 }}点赞</text>
            <text class="online">· {{ userInfo.followerCount || 0 }}粉丝</text>
          </view>
        </view>
      </view>

      <!-- Tab导航 -->
      <view class="tab-bar">
        <view class="tab" :class="{ active: activeTab === 'about' }" @click="switchTab('about')">{{ aboutTabText }}</view>
        <view class="tab" :class="{ active: activeTab === 'moments' }" @click="switchTab('moments')">动态{{ userInfo.userMomentCount }}</view>
        <view class="tab" :class="{ active: activeTab === 'skills' }" @click="switchTab('skills')">技能</view>
        <view class="tab" :class="{ active: activeTab === 'showcase' }" @click="switchTab('showcase')">橱窗</view>
      </view>

      <!-- 关于她内容 -->
      <view v-if="activeTab === 'about'" class="tab-content">
        <AboutTab :userInfo="userInfo" />
      </view>

      <!-- 动态内容 -->
      <view v-if="activeTab === 'moments'" class="tab-content">
        <MomentsTab :userInfo="userInfo" />
      </view>

      <!-- 技能内容 -->
      <view v-if="activeTab === 'skills'" class="tab-content">
        <SkillsTab :userInfo="userInfo" />
      </view>

      <!-- 技能内容 -->
      <view v-if="activeTab === 'showcase'" class="tab-content">
        <ShowcaseTab :userInfo="userInfo" />
      </view>
			
      <!-- 底部操作栏 - 从首页进入时显示 -->
      <view v-if="fromIndex" class="bottom-bar">
        <view class="action-buttons">
          <view class="action-item" @click="handleFollow">
            <view class="action-btn" :class="followButtonClass">
              <image class="btn-icon" :src="isFollowed ? '/static/index/expert/gz-active.png' : '/static/index/expert/gz.png'" />
            </view>
            <text class="btn-text" :class="followButtonClass">关注</text>
          </view>
          <view class="action-item" @click="handleCollect">
            <view class="action-btn" :class="collectButtonClass">
              <image class="btn-icon" :src="isCollected ? '/static/index/expert/sc-active.png' : '/static/index/expert/sc.png'" />
            </view>
            <text class="btn-text" :class="collectButtonClass">收藏</text>
          </view>
          <view class="action-item" @click="handleLike">
            <view class="action-btn" :class="likeButtonClass">
              <image class="btn-icon" :src="isLiked ? '/static/index/expert/like.png' : '/static/index/expert/dislike.png'" />
            </view>
            <text class="btn-text" :class="likeButtonClass">喜欢</text>
          </view>
        </view>
        <view class="message-btn" @click="handleMessage">
          <text class="message-text">发消息</text>
        </view>
      </view>

      <!-- 底部操作栏 - 非首页进入时显示 -->
      <view v-else class="bottom-bar">
        <view class="profile-actions">
          <view class="profile-action-btn edit-btn" @click="handleEditProfile">
            <text class="profile-btn-text">编辑资料</text>
          </view>
          <view class="profile-action-btn post-btn" @click="handlePostDynamic">
            <text class="profile-btn-text">发动态</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分享弹框 -->
    <view v-if="showShare" class="share-overlay" @click="hideShareDialog">
      <view class="share-dialog" @click.stop>
        <view class="share-header">
          <text class="share-title">分享</text>
          <view class="share-close" @click="hideShareDialog">×</view>
        </view>
        <view class="share-content">
          <view class="share-section">
            <view class="share-item" @click="shareToWeChatMoments">
              <view class="share-icon wechat-moments">
                <image class="share-icon-img" src="/static/index/expert/fr.png" mode="aspectFill" />
              </view>
              <text class="share-text">微信朋友圈</text>
            </view>
            <view class="share-item" @click="shareToWeChatFriends">
                <view class="share-icon wechat-friends">
                  <image class="share-icon-img" src="/static/index/expert/wx.png" mode="aspectFill" />
              </view>
              <text class="share-text">微信好友</text>
            </view>
          </view>
          <view class="share-divider"></view>
          <view class="share-section">
            <view class="share-item" @click="reportUser">
              <view class="share-icon report">
                <image class="share-icon-img" src="/static/index/expert/up.png" mode="aspectFill" />
              </view>
              <text class="share-text">举报</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 举报弹框 -->
    <ReportDialog 
      :visible="showReport"
      :targetUserId="userInfo.userId"
      @close="hideReportDialog"
    />
  </view>
</template>

<script>
import ImageCarousel from './components/ImageCarousel.vue'
import AboutTab from './components/AboutTab.vue'
import MomentsTab from './components/MomentsTab.vue'
import SkillsTab from './components/SkillsTab.vue'
import ShowcaseTab from './components/Showcase.vue'
import StickyHeader from './components/StickyHeader.vue'
import ReportDialog from './components/ReportDialog.vue'
import { userApi, postApi } from '../../common/api';
import { HTTP_IMG } from '../../common/constant.js';


export default {
  name: 'ExpertDetail',
  components: {
    ImageCarousel,
    AboutTab,
    MomentsTab,
    SkillsTab,
		ShowcaseTab,
    StickyHeader,
    ReportDialog
  },
  data() {
    return {
      loading: true,
      activeTab: 'about', // 当前激活的tab：about-关于她, moments-动态, skills-技能
      isFollowed: false, // 关注状态
      isLiked: false, // 喜欢状态
      isCollected: false, // 收藏状态
      statusBarHeight: 0, // 状态栏高度
      fromIndex: false, // 是否从首页跳转过来
      userInfo: {
        userId: '',
        accountCode: '',
        nickname: '',
        avatar: '',
        signature: '',
        sex: 1,
        birthday: '',
        location: '',
        height: '',
        weight: '',
        occupation: '',
        feature: [],
        albumPhotos: [],
        followerCount: 0,
        followingCount: 0,
        friendCount: 0,
        visitorCount: 0,
        constellation: '',
        likeCount: 0,
        distance: 0,
        isReal: null, // 实名认证状态 1实名 0否
        isOnline: false,
        vipStatus: null,
        talentLevel: null,
        showOfflineVerifyButton: null,
      },
      showShare: false, // 控制分享弹框的显示
      showReport: false, // 控制举报弹框的显示
      pageScrollTop: 0, // 页面滚动距离
    }
  },
  computed: {
    // 计算年龄
    age() {
      if (!this.userInfo.birthday) return 0
      const birthDate = new Date(this.userInfo.birthday)
      const today = new Date()
      let age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }
      return age
    },
    // 性别文本
    genderText() {
      return this.userInfo.sex === 1 ? '男' : '女'
    },
    // 相册图片
    albumImages() {
      if (this.userInfo.albumPhotos && this.userInfo.albumPhotos.length > 0) {
        // 为相册图片添加域名
        return this.userInfo.albumPhotos.map(photo => {
          if (photo.startsWith('http://') || photo.startsWith('https://')) {
            return photo
          }
          return HTTP_IMG + photo
        })
      }
      // 如果没有相册图片，使用头像作为默认图片
      return [this.avatarUrl]
    },
    // 头像URL（添加域名）
    avatarUrl() {
      if (!this.userInfo.avatar) return ''
      // 如果已经是完整URL，直接返回
      if (this.userInfo.avatar.startsWith('http://') || this.userInfo.avatar.startsWith('https://')) {
        return this.userInfo.avatar
      }
      // 否则添加域名前缀
      return HTTP_IMG + this.userInfo.avatar
    },
    // 关注按钮文本
    followButtonText() {
      return this.isFollowed ? '已关注' : '关注'
    },
    // 关注按钮样式类
    followButtonClass() {
      return this.isFollowed ? 'followed' : 'follow'
    },
    // 喜欢按钮文本
    likeButtonText() {
      return this.isLiked ? '已喜欢' : '喜欢'
    },
    // 喜欢按钮样式类
    likeButtonClass() {
      return this.isLiked ? 'liked' : 'like'
    },
    // 收藏按钮文本
    collectButtonText() {
      return this.isCollected ? '已收藏' : '收藏'
    },
    // 收藏按钮样式类
    collectButtonClass() {
      return this.isCollected ? 'collected' : 'collect'
    },
    // 关于她/他tab文本
    aboutTabText() {
      return this.genderText === '女' ? '关于她' : '关于他'
    }
  },
  // 页面滚动监听
  onPageScroll(e) {
    this.pageScrollTop = e.scrollTop
  },
  onLoad(options) {
    // 获取系统信息，包括状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 0
    

    
    // 获取路由参数中的用户ID
    const userId = options.id || '1e36a0cfa01b4ba29c2537fa2fcb2010'
    // 获取是否从首页跳转的标识
    this.fromIndex = options.fromIndex === '1'
    // 获取是否需要显示技能tab的标识
    const showSkills = options.showSkills === '1'
    
    // 如果是通过"找TA"进入的，主动展示技能tab
    if (showSkills) {
      this.activeTab = 'skills'
    }
    
    if (userId) {
      this.fetchUserProfile(userId)
    } else {
      uni.showToast({
        title: '缺少用户ID参数',
        icon: 'none'
      })
    }
  },
  methods: {
    // 格式化距离显示
    formatDistance(distance) {
      if (!distance) return ''
      
      if (distance < 1000) {
        return `${Math.round(distance)}m`
      } else {
        return `${(distance / 1000).toFixed(2)}km`
      }
    },
    // 记录浏览行为
    async recordBrowseView(userId) {
      try {
        // 调用浏览记录接口，传递被浏览的用户ID
        await postApi.uploadBrowse({ browseUserId: userId });
        console.log('浏览记录成功');
      } catch (error) {
        console.error('记录浏览失败:', error);
        // 记录失败不影响页面逻辑，静默处理
      }
    },

    // 获取用户资料
    async fetchUserProfile(userId) {
      try {
        this.loading = true

        // 调用API接口
        const res = await userApi.getUserProfile(userId);
        this.userInfo = res.data;
        
        // 初始化用户交互状态
        if (res.data) {
          // 设置关注状态
          this.isFollowed = res.data.isFollowing || false;
          // 设置喜欢状态
          this.isLiked = res.data.isLiked || false;
          // 设置收藏状态
          this.isCollected = res.data.isFavorited || false;
        }
        
        // 获取用户在线状态
        try {
          const onlineStatusRes = await userApi.getOnlineStatus([userId]);
          if (onlineStatusRes.code === 200 && onlineStatusRes.data) {
            const onlineStatusMap = {};
            onlineStatusRes.data.forEach(item => {
              if (item.uid && typeof item.online === 'number') {
                // online: 1为在线，0为离线
                onlineStatusMap[item.uid] = item.online === 1;
              }
            });
            
            // 更新用户的在线状态
            this.userInfo.isOnline = onlineStatusMap[userId] === true;
          } else {
            this.userInfo.isOnline = false;
          }
        } catch (error) {
          console.error('获取在线状态失败:', error);
          this.userInfo.isOnline = false;
        }

        // 记录浏览行为（异步调用，不影响页面逻辑）
        this.recordBrowseView(userId);
        
      } catch (error) {
        console.error('获取用户资料失败:', error)
        uni.showToast({
          title: '获取用户资料失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    onCarouselChange(e) {
      // 轮播图切换事件处理
    },

    onImageClick(e) {
      // 图片预览功能
      uni.previewImage({
        current: e.index,
        urls: this.albumImages
      })
    },

    // 处理关注/取消关注
    async handleFollow() {
      try {
        // 根据当前关注状态决定调用哪个接口
        let url;
        if (this.isFollowed) {
          url = userApi.unfollowUser(this.userInfo.userId)
        } else {
          url = userApi.followUser(this.userInfo.userId)
        }
        const response = await url;
        if (response.code === 200) {
          // 切换关注状态
          this.isFollowed = !this.isFollowed

          // 显示成功提示
          uni.showToast({
            title: this.isFollowed ? '关注成功' : '取消关注成功',
            icon: 'success'
          })
        } else {
          throw new Error(response.msg||'请求失败')
        }
              } catch (error) {
          console.error('关注操作失败:', error)
          uni.showToast({
            title: error.message||'操作失败，请重试',
            icon: 'none'
          })
        }
      },
      
      // 处理收藏/取消收藏
      async handleCollect() {
        try {
          const operationType = this.isCollected ? 4 : 3
          const response = await userApi.collectUser({
            targetUserId: this.userInfo.userId,
            operationType: operationType
          })
          if (response.code === 200) {
            // 切换收藏状态
            this.isCollected = !this.isCollected
            
            // 显示成功提示
            uni.showToast({
              title: this.isCollected ? '收藏成功' : '取消收藏成功',
              icon: 'success'
            })
          } else {
            throw new Error('请求失败')
          }
        } catch (error) {
          console.error('收藏操作失败:', error)
          uni.showToast({
            title: '操作失败，请重试',
            icon: 'none'
          })
        }
      },
      
      // 切换tab
      switchTab(tabName) {
        this.activeTab = tabName
      },

      // 处理喜欢/取消喜欢
      async handleLike() {
        try {
          // 获取当前用户信息
          const loginInfo = uni.getStorageSync('loginInfo')
          if (!loginInfo || !loginInfo.loginId) {
            uni.showToast({
              title: '请先登录',
              icon: 'none'
            })
            return
          }
          
          // 根据当前喜欢状态决定操作类型
          // 1: 喜欢, 2: 不喜欢
          const operationType = this.isLiked ? 2 : 1
          
          const response = await userApi.userInteraction({
            targetUserId: this.userInfo.userId,
            operationType: operationType
          })
          
          if (response.code === 200) {
            // 切换喜欢状态
            this.isLiked = !this.isLiked
            
            // 显示成功提示
            uni.showToast({
              title: this.isLiked ? '喜欢成功' : '取消喜欢成功',
              icon: 'success'
            })
          } else {
            throw new Error('请求失败')
          }
        } catch (error) {
          console.error('喜欢操作失败:', error)
          uni.showToast({
            title: '操作失败，请重试',
            icon: 'none'
          })
        }
      },

      // 处理发消息
      handleMessage() {
        // 跳转到聊天页面
        uni.navigateTo({
          url: `/pages/chat/index?channelID=${this.userInfo.userId}&channelType=1&nickName=${this.userInfo.nickname}`
        })
      },

      // 返回上一页
      goBack() {
        uni.navigateBack({
          delta: 1
        })
      },



      // 处理编辑资料
      handleEditProfile() {
        uni.navigateTo({
          url: `/pages/user/edit`
        })
      },

      // 处理发动态
      handlePostDynamic() {
        uni.navigateTo({
          url: `/pages/post/dynamic`
        })
      },

      // 显示分享弹框
      showShareDialog() {
        this.showShare = true;
      },

      // 隐藏分享弹框
      hideShareDialog() {
        this.showShare = false;
      },

      // 分享到微信朋友圈
      async shareToWeChatMoments() {
        try {
          // 使用uni.share API
          console.log(this.userInfo.nickname,this.avatarUrl)
          uni.share({
            provider: 'weixin',
            scene: 'WXSceneTimeline',
            type: 0,
            title: this.userInfo.nickname,
            href: this.avatarUrl,
			imageUrl: this.avatarUrl,
            success: () => {
              uni.showToast({
                title: '分享成功',
                icon: 'success'
              });
              this.hideShareDialog();
            },
            fail: (err) => {
              console.error('分享失败:', err);
              uni.showToast({
                title: '分享失败',
                icon: 'none'
              });
            }
          });
        } catch (error) {
          console.error('分享失败:', error);
          uni.showToast({
            title: '分享失败',
            icon: 'none'
          });
        }
      },

      // 分享到微信好友
      async shareToWeChatFriends() {
        try {
          // 使用uni.share API
          uni.share({
            provider: 'weixin',
            scene: 'WXSceneSession',
            type: 0,
            title: this.userInfo.nickname,
            imageUrl: this.avatarUrl,
            href: this.avatarUrl,
            success: () => {
              uni.showToast({
                title: '分享成功',
                icon: 'success'
              });
              this.hideShareDialog();
            },
            fail: (err) => {
              console.error('分享失败:', err);
              uni.showToast({
                title: '分享失败',
                icon: 'none'
              });
            }
          });
        } catch (error) {
          console.error('分享失败:', error);
          uni.showToast({
            title: '分享失败',
            icon: 'none'
          });
        }
      },

      // 举报用户
      reportUser() {
        this.hideShareDialog();
        this.showReportDialog();
      },

      // 显示举报弹框
      showReportDialog() {
        this.showReport = true;
      },

      // 隐藏举报弹框
      hideReportDialog() {
        this.showReport = false;
      }
  }
}
</script>

<style scoped lang="scss">
.expert-detail-container {
  background: #fff;
  min-height: 100vh;
  padding-bottom: 120rpx;
  // 页面通顶展示，确保从屏幕顶部开始
  padding-top: 0;
  margin-top: 0;
  overflow-x: hidden;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 28rpx;
  color: #999;
}

.carousel-section {
  // 轮播图通顶展示，去掉所有padding
  padding: 0;
  margin: 0;
  background: #fff;
  // 确保轮播图从顶部开始
  position: relative;
  top: 0;
  // 增加轮播图高度，确保banner足够高
  min-height: 600rpx;

  // 返回按钮样式
  .back-button {
    position: fixed;
    // top现在通过动态style设置，考虑状态栏高度
    left: 30rpx;
    width: 64rpx;
    height: 64rpx;
    // background: rgba(0, 0, 0, 0.4);
    // border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    // backdrop-filter: blur(8rpx);
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.95);
      background: rgba(0, 0, 0, 0.6);
    }
    
    .back-icon {
      width: 20rpx;
      height: 40rpx;
      // filter: brightness(0) invert(1); // 将图标变为白色
    }
  }

  // 分享按钮样式
  .share-button {
    position: fixed;
    right: 30rpx;
    width: 64rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      background: rgba(0, 0, 0, 0.6);
    }

    .three-dots {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 30rpx;
      height: 20rpx;

      .dot {
        width: 8rpx;
        height: 8rpx;
        background: #333;
        border-radius: 50%;
      }
    }
  }


}

.header {
  padding: 30rpx 30rpx 30rpx 30rpx;
  display: flex;
  align-items: center;
  margin-top: -124rpx;
  .avatar {
    width: 150rpx;
    height: 150rpx;
    border-radius: 50%;
    margin-right: 36rpx;
  }

  .base-info {
    flex: 1;
    .nickname-row {
      display: flex;
      align-items: flex-start;
      margin-top: 10px;
      
      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 10rpx;
        margin-right: 20rpx;
        border: 2rpx solid #f0f0f0;
      }
      
      .nickname {
        font-size: 44rpx;
        font-weight: bold;
        margin-right: 20rpx;
        color: white;
      }

      .gender-age-tag {
        display: flex;
        align-items: center;
        border-radius: 8rpx;
        padding: 6rpx 12rpx 6rpx 8rpx;
        margin-right: 16rpx;

        .gender-icon-img {
          width: 24rpx;
          height: 24rpx;
          margin-right: 6rpx;
        }

        .age-text {
          font-size: 24rpx;
          color: #1976d2;
          font-weight: 500;
        }

        &.female-bg {
          background: #ffe4ef;
        }

        &.male-bg {
          background: #e3f2fd;
        }
      }

      .verified {
        font-size: 21rpx;
        color: #8758FE;
        background: #EAE2FF;
        border-radius: 7rpx;
        padding: 5rpx 12rpx;
      }
    }

    .location-row {
      margin-top: 16rpx;
      font-size: 21rpx;
      color: #888;
      display: flex;
      flex-wrap: wrap;
      .location,
      .visitor-count,
      .online {
        margin-right: 18rpx;
      }

    }
  }
}

.tab-bar {
  display: flex;
  // border-bottom: 1rpx solid #f0f0f0;
  margin: 0 30rpx;

  .tab {
    flex: 1;
    text-align: center;
    font-size: 34rpx;
    color: #666;
    padding: 28rpx 0 18rpx 0;
    position: relative;

    &.active {
      color: #222;
      font-weight: bold;

      &::after {
        content: '';
        position: absolute;
        left: 57%;
        bottom: 18rpx;
        transform: translateX(-50%);
        width: 60rpx;
        height: 6rpx;
        background: linear-gradient( 45deg, #66D47E 0%, #91E02A 100%);
        box-shadow: 0px 0px 4px 1px rgba(145,224,42,0.5);
        border-radius: 3rpx;
      }

      &::before {
        content: '';
        display: inline-block;
        // width: 30rpx;
        // height: 30rpx;
        // background: url('/static/index/icon/active-tab.png') no-repeat center center;
        background-size: 100% 100%;
        margin-right: 8rpx;
        margin-bottom: -2rpx;
      }
    }
		&:nth-child(1).active::before {
        width: 32rpx;
        height: 32rpx;
        background: url('/static/index/icon/active-tab.png') no-repeat center center / 100% 100%;
		}
		&:nth-child(2).active::before {
        width: 32rpx;
        height: 32rpx;
        background: url('/static/index/icon/moments.png') no-repeat center center / 100% 100%;
		}
		&:nth-child(3).active::before {
        width: 28rpx;
        height: 32rpx;
        background: url('/static/index/icon/skill.png') no-repeat center center / 100% 100%;
		}
		&:nth-child(4).active::before {
        width: 34rpx;
        height: 32rpx;
        background: url('/static/index/icon/showcase.png') no-repeat center center / 100% 100%;
		}
  }
}

.tab-content {
  min-height: 600rpx;
  padding: 30rpx 30rpx 0 30rpx;
  background: #fff;
  min-height: calc(100vh - 500rpx);
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx 44rpx 30rpx;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.04);

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .action-btn {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .btn-icon {
          width: 42rpx;
          height: 42rpx;
          border-radius: 4rpx;
        }
        
        &.follow {
        }

        &.followed {
          border-color: #1890ff;
        }

        &.collect {
        }

        &.collected {
          border-color: #52c41a;
        }

        &.like {
        }

        &.liked {
          border-color: #ff6b35;
        }
      }
      
      .btn-text {
        font-size: 24rpx;
        color: #666;
        line-height: 1;
        
        &.followed {
          color: #1890ff;
        }
        
        &.collected {
          color: #52c41a;
        }
        
        &.liked {
          color: #ff6b35;
        }
      }
    }
  }

  .message-btn {
    background: #66D47E;
    color: #fff;
    padding: 20rpx 40rpx;
    border-radius: 60rpx;
    min-width: 160rpx;
    text-align: center;
    width: 86px;
    height: 24px;
    
    .message-text {
      font-size: 34rpx;
      color: #fff;
    }
  }

  .profile-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: 24rpx;

    .profile-action-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 40rpx;
      border-radius: 60rpx;
      min-width: 160rpx;
      text-align: center;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      .profile-btn-text {
        font-size: 26rpx;
        font-weight: 500;
      }

      &.edit-btn {
        background: #fff;
        color: #333;
        border: 2rpx solid #e0e0e0;

        &:active {
          background: #f5f5f5;
        }

        .profile-btn-text {
          color: #333;
        }
      }

      &.post-btn {
        background: #66D47E;
        color: #fff;

        &:active {
          background: #5bc46e;
        }

        .profile-btn-text {
          color: #fff;
        }
      }
    }
  }
}

.distance-icon {
  width: 20rpx;
  height: 24rpx;
  margin-right: 6rpx;
  display: inline-block;
  vertical-align: middle;
  filter: brightness(0) saturate(100%) invert(51%) sepia(0%) saturate(0%) hue-rotate(118deg) brightness(92%) contrast(87%);
}

.online-status {
  color: #4caf50;
  font-weight: 500;
  font-size: 21rpx;
  margin-right: 18rpx;

  &.offline {
    color: #ff4d4f;
  }
}
uni-page-body{
  overflow-y: auto;
}

.nickname-row {
    display: flex;
    align-items: flex-start;
    margin-top: 10px;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 10rpx;
      margin-right: 20rpx;
      border: 2rpx solid #f0f0f0;
    }
    
    .nickname {
      font-size: 44rpx;
      font-weight: bold;
      margin-right: 20rpx;
      color: white;
    }

    .gender-age-tag {
      display: flex;
      align-items: center;
      border-radius: 8rpx;
      padding: 6rpx 12rpx 6rpx 8rpx;
      margin-right: 16rpx;

      .gender-icon-img {
        width: 24rpx;
        height: 24rpx;
        margin-right: 6rpx;
      }

      .age-text {
        font-size: 24rpx;
        color: #1976d2;
        font-weight: 500;
      }

      &.female-bg {
        background: #ffe4ef;
      }

      &.male-bg {
        background: #e3f2fd;
      }
    }

    .verified {
      font-size: 21rpx;
      color: #8758FE;
      background: #EAE2FF;
      border-radius: 7rpx;
      padding: 5rpx 12rpx;
    }
  }

// 分享按钮样式
.share-button {
  position: fixed;
  right: 30rpx;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background: rgba(0, 0, 0, 0.6);
  }

  .three-dots {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 30rpx;
    height: 20rpx;

    .dot {
      width: 8rpx;
      height: 8rpx;
      background: #333;
      border-radius: 50%;
    }
  }
}

// 分享弹框样式
.share-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 1000;
}

.share-dialog {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.2);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .share-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  .share-close {
    font-size: 40rpx;
    color: #999;
    font-weight: bold;
  }
}

.share-content {
  padding: 30rpx;
}

.share-section {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 30rpx;
  padding: 0 30rpx;
}

.share-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 60rpx;

  .share-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10rpx;
    margin-bottom: 10rpx;
  }

  .wechat-moments {
    background: #fff;
  }

  .wechat-friends {
    background: #fff;
  }

  .camera-aperture,
  .chat-bubbles,
  .warning-triangle {
    width: 40rpx;
    height: 40rpx;
  }

  .share-text {
    font-size: 24rpx;
    color: #333;
    text-align: center;
  }
}

.share-divider {
  height: 1rpx;
  background: #f0f0f0;
  margin: 30rpx 0;
}
.share-icon-img{
  width: 88rpx;
  height: 88rpx;
}
</style>