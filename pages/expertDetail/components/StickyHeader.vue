<template>
  <view class="sticky-header-container">
    <!-- 原始位置的nickname-row -->
    <view 
      class="nickname-row original" 
      :class="{ 'hidden': isSticky }"
    >
      <image v-if="avatarUrl" class="avatar" :src="avatarUrl" mode="aspectFill" />
      <text class="nickname">{{ nickname }}</text>
    </view>
    
    <!-- 吸顶的nickname-row -->
    <view 
      class="nickname-row sticky" 
      :class="{ 'visible': isSticky }"
      :style="{ top: (statusBarHeight + 20 + 64) + 'px' }"
    >
      <image v-if="avatarUrl" class="avatar" :src="avatarUrl" mode="aspectFill" />
      <text class="nickname">{{ nickname }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StickyHeader',
  props: {
    avatarUrl: {
      type: String,
      default: ''
    },
    nickname: {
      type: String,
      default: ''
    },
    stickyTop: {
      type: Number,
      default: 120
    },
    scrollTop: {
      type: Number,
      default: 0
    },
    statusBarHeight: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      isSticky: false, // 确保初始状态为false
      originalElementTop: 0
    }
  },
  mounted() {
    // 确保初始状态为false
    this.isSticky = false
    this.calculateOriginalPosition()
  },
  watch: {
    scrollTop: {
      handler(newScrollTop) {
        this.handleScroll(newScrollTop)
      },
      immediate: false
    }
  },
  methods: {
    
    handleScroll(scrollTop) {
      // 确保原始位置已经计算完成
      if (this.originalElementTop === 0) {
        return
      }
      
      // 计算当前原始元素距离顶部的距离
      const currentElementTop = this.originalElementTop - scrollTop
      console.log('currentElementTop:', currentElementTop, 'scrollTop:', scrollTop, 'stickyTop:', this.stickyTop)
      
      // 当元素距离顶部小于等于stickyTop时，触发吸顶
      if (currentElementTop <= this.stickyTop) {
        this.isSticky = true
        console.log('设置吸顶状态为true')
      } else {
        this.isSticky = false
        console.log('设置吸顶状态为false')
      }
    },
    
    calculateOriginalPosition() {
      // 使用nextTick确保DOM已渲染
      this.$nextTick(() => {
        setTimeout(() => {
          const query = uni.createSelectorQuery().in(this)
          query.select('.original').boundingClientRect((rect) => {
            if (rect) {
              // 计算元素相对于页面的位置
              const systemInfo = uni.getSystemInfoSync()
              const statusBarHeight = systemInfo.statusBarHeight || 0
              this.originalElementTop = rect.top + statusBarHeight
              
              // 初始化时检查是否需要吸顶（页面加载时滚动位置为0）
              if (this.scrollTop > 0) {
                this.handleScroll(this.scrollTop)
              }
            }
          }).exec()
        }, 100) // 延迟100ms确保页面完全渲染
      })
    }
  }
}
</script>

<style scoped lang="scss">
.sticky-header-container {
  position: relative;
  z-index: 100;
}

.nickname-row {
  display: flex;
  align-items: flex-start;
  margin-top: 10px;
  transition: opacity 0.3s ease;
  
  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 10rpx;
    margin-right: 20rpx;
    border: 2rpx solid #f0f0f0;
  }
  
  .nickname {
    font-size: 44rpx;
    font-weight: bold;
    margin-right: 20rpx;
    color: white;
  }
  
  &.original {
    position: relative;
    top: -100rpx;
    left: 30rpx;
    z-index: 1;
    
    &.hidden {
      opacity: 0;
      pointer-events: none;
    }
  }
  
  &.sticky {
    position: fixed;
    left: 30rpx;
    right: 30rpx;
    z-index: 999;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10rpx);
    border-radius: 15rpx;
    padding: 15rpx 20rpx;
    margin: 0;
    opacity: 0;
    pointer-events: none;
    transform: translateY(-100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
    
    &.visible {
      opacity: 1;
      pointer-events: auto;
      transform: translateY(0);
    }
    
    .avatar {
      width: 80rpx;
      height: 80rpx;
      margin-right: 15rpx;
    }
    
    .nickname {
      font-size: 36rpx;
      color: white;
    }
  }
}
</style> 