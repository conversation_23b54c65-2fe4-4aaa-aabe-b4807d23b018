<template>
  <view class="skills-tab">
    <view class="skills-section">
      <!-- <view class="skills-title">专业技能</view> -->
      <view class="skills-list" v-if="skillsList.length > 0">
        <view class="skill-item" v-for="skill in skillsList" :key="skill.dataId">
          <!-- <view class="skill-header">
            <text class="skill-name">{{ getSkillName(skill) }}</text>
            <text class="skill-level">{{ getSkillLevel(skill) }}</text>
          </view>
          <view class="skill-desc" v-if="skill.customDesc">
            <text class="desc-text">{{ skill.customDesc }}</text>
          </view>
          <view class="skill-price" v-if="skill.pricePerHour">
            <text class="price-text">¥{{ skill.pricePerHour }}/小时</text>
            <text class="min-hours" v-if="skill.minOrderHours">最少{{ skill.minOrderHours }}小时</text>
          </view> -->
					<image :src="getSkillImage(skill)" class="icon-skill"></image>
					<view class="skill-info">
						<text class="skill-name">{{ getSkillName(skill) }}</text>
						<text class="skill-price">{{ skill.pricePerHour }}贝壳币/小时</text>
						<text class="talk">下单前聊一聊</text>
					</view>
					<view class="call" @click="gotoExpertOrder">找他</view>
        </view>
      </view>
      <view class="empty-state" v-else>
				<image src="/static/index/icon/no-skill.png" class="no-more"></image>
        <text class="empty-text">暂无技能</text>
				<view class="apply-wrap">
					<text class="txt">如想兼职接单，请</text>
				  <text class="apply" @click="applySkill">申请技能</text>
				  <text class="txt">认证</text>
				</view>
      </view>
    </view>
  </view>
</template>

<script>
import { skillApi } from '../../../common/api'
import { SKILL_NAMES, getSkillLevelByPrice } from '../../../common/constants'

export default {
  name: 'SkillsTab',
  props: {
    userInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      skillsList: [],
      loading: false
    }
  },
  mounted() {
    this.fetchSkillsList()
  },
  methods: {
    async fetchSkillsList() {
      try {
        this.loading = true
        const params = {
          pageNum: 1,
          pageSize: 20
        }
        
        // 如果有用户信息，添加达人用户ID参数
        if (this.userInfo && this.userInfo.userId) {
          params.darenUserId = this.userInfo.userId
        }
        
        const result = await skillApi.getSkillsList(params)
        
        if (result && result.code === 200) {
          this.skillsList = result.rows || []
        } else {
          console.error('获取技能列表失败:', result.msg)
        }
      } catch (error) {
        console.error('获取技能列表异常:', error)
      } finally {
        this.loading = false
      }
    },
    
    getSkillName(skill) {
      // 优先使用接口返回的技能名称，如果没有则使用常量文件中的映射
      return skill.skillName || SKILL_NAMES[skill.skillTemplateId] || '未知技能'
    },
    
    getSkillLevel(skill) {
      // 使用常量文件中的价格等级映射函数
      return getSkillLevelByPrice(skill.pricePerHour || 0)
    },
    
    getSkillImage(skill) {
      // 优先使用接口返回的技能图片，如果没有则使用默认图片
      return '/static/images/expertOrder/foodGuide.png'//skill.skillImg || 
    },
		gotoExpertOrder() {
			uni.navigateTo({
				url: '/pages/expertOrder/expertOrder'
			})
		},
		applySkill() {
			uni.navigateTo({
				url: '/pages/influencer/success'
			})
		}
  }
}
</script>

<style scoped lang="scss">
.skills-section {
  // margin-top: 36rpx;
  min-height: 600rpx; /* 固定高度，适应Tab内容区域 */
  
  .skills-title {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
  }
  
  .skills-list {
    .skill-item {
      background: #f6f7fb;
			height: 140rpx;
			box-sizing: border-box;
			display: flex;
      border-radius: 32rpx;
			align-items: center;
      padding: 18rpx 32rpx;
      margin-bottom: 24rpx;
      // box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
      .icon-skill {
				height: 76rpx;
				width: 76rpx;
				margin-right: 32rpx;
			}
			.skill-info {
				flex-grow: 1;
				.skill-name {
					height: 40rpx;
					line-height: 40rpx;
					font-size: 28rpx;
					color: #000;
				}
				.skill-price, .talk {
					height: 28rpx;
					line-height: 28rpx;
					font-size: 20rpx;
					color: #999;
				}
				.talk {
					margin-top: 8rpx
				}
			}
			.call {
				height: 48rpx;
				line-height: 46rpx;
				width: 88rpx;
				background: linear-gradient(270deg, #f8b401 0%, #fe9836 100%);
				box-shadow: inset 0 -2rpx 4rpx 2rpx rgba(255, 255, 255, .5);
				border-radius: 24rpx;
				color: #fff;
				text-align: center;
				font-size: 24rpx;
			}
      .skill-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;
        
        .skill-name {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
        }
        
        .skill-level {
          font-size: 24rpx;
          color: #1890ff;
          background: #e6f7ff;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
        }
      }
      
      .skill-desc {
        margin-bottom: 16rpx;
        
        .desc-text {
          font-size: 24rpx;
          color: #666;
          line-height: 1.5;
        }
      }
      
      .skill-price {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .price-text {
          font-size: 26rpx;
          font-weight: bold;
          color: #ff6b35;
        }
        
        .min-hours {
          font-size: 22rpx;
          color: #999;
        }
      }
    }
  }
  
  .empty-state {
		padding-top: 150rpx;
    display: flex;
  	flex-direction: column;
    justify-content: center;
    align-items: center;
    // padding: 100rpx 0;
    // background: #f5f5f5;
    .no-more {
  		height: 240rpx;
  		width: 240rpx;
  	}
    .empty-text {
      font-size: 32rpx;
      color: #000;
			height: 44rpx;
			line-height: 44rpx;
			margin: 20rpx 0 24rpx;
    }
		.apply-wrap {
			color: #666;
			font-size: 28rpx;
			height: 40rpx;
			line-height: 40rpx;
			.apply {
				color: #66d47e;
				padding: 0 8rpx;
			}
		}
  }
}
</style>