<template>
  <view class="about-tab">
    <!-- 精选相册 -->
    <view class="album-section">
      <view class="section-title">
        <text>精选相册</text>
        <text class="arrow" @click="goAlbum">›</text>
      </view>
      <view class="album-grid">
        <view 
          v-for="(photo, index) in albumPhotos" 
          :key="index"
          class="album-item"
          @click="previewImage(index)"
        >
          <image :src="photo" mode="aspectFill" class="album-image" />
        </view>
      </view>
    </view>

    <!-- 个人信息 -->
    <view class="info-section">
      <view class="section-title">个人信息</view>
      <view class="info-content">
        <view class="signature" v-if="userInfo.signature">
          <text class="signature-text">{{ userInfo.signature || '编辑个签，展示我的独特态度' }}</text>
        </view>
        <view class="line-delive"></view>
        <view class="info-tags">
          <view v-if="userInfo.sex || age" class="gender-age-tag" :class="userInfo.sex === 2 ? 'female-bg' : 'male-bg'">
            <image v-if="userInfo.sex" class="gender-icon-img" :src="userInfo.sex === 2 ? '/static/index/icon/female.png' : '/static/index/icon/male.png'" />
            <text class="age-text">22</text>
          </view>
          <text class="tag" v-if="userInfo.constellation">{{ userInfo.constellation }}</text>
          <text class="tag" v-if="userInfo.occupation">{{ userInfo.occupation }}</text>
          <text class="tag" v-if="userInfo.location">{{ userInfo.location }}</text>
        </view>
      </view>
    </view>

    <!-- 等级成就 -->
    <view class="level-section">
      <view class="section-title">等级成就</view>
      <view class="level-list">
        <view class="level-item">
          <text class="level-label">财富等级</text>
          <view class="level-value">
            <text class="level-text">LV6</text>
          </view>
        </view>
        <view class="level-item">
          <text class="level-label">达人等级</text>
          <view class="level-value">
            <text class="level-text">LV11</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { HTTP_IMG } from '../../../common/constant.js';

export default {
  name: 'AboutTab',
  props: {
    userInfo: {
      type: Object,
      required: true
    }
  },
  computed: {
    // 计算年龄
    age() {
      if (!this.userInfo.birthday) return 0
      const birthDate = new Date(this.userInfo.birthday)
      const today = new Date()
      let age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }
      return age
    },
    // 相册图片
    albumPhotos() {
      if (this.userInfo.albumPhotos && this.userInfo.albumPhotos.length > 0) {
        return this.userInfo.albumPhotos.map(photo => {
          if (photo.startsWith('http://') || photo.startsWith('https://')) {
            return photo
          }
          return HTTP_IMG + photo
        }).slice(0, 3) // 最多显示3张图片
      }
      return []
    }
  },
  methods: {
    previewImage(index) {
      uni.previewImage({
        current: index,
        urls: this.albumPhotos
      })
    },
    goAlbum() {
      console.log('goAlbum userInfo:', this.userInfo) // 调试日志
      console.log('goAlbum userId:', this.userInfo.userId) // 调试日志
      console.log('goAlbum id:', this.userInfo.id) // 调试日志 - 检查是否有id字段
      
      // 尝试多种可能的用户ID字段
      const userId = this.userInfo.userId || this.userInfo.id || this.userInfo.uid
      console.log('最终使用的userId:', userId) // 调试日志
      
      if (!userId) {
        uni.showToast({
          title: '用户ID不存在',
          icon: 'none'
        })
        return
      }
      
      uni.navigateTo({
        url: `/pages/expertDetail/FeaturedAlbum?userId=${userId}`
      })
    }
  }
}
</script>

<style scoped lang="scss">
.about-tab {
  background: #fff;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .arrow {
    font-size: 36rpx;
    color: #999;
  }
}

// 精选相册样式
.album-section {
  margin-bottom: 48rpx;
  
  .album-grid {
    display: flex;
    gap: 16rpx;
    
    .album-item {
      flex: 1;
      
      .album-image {
        width: 100%;
        height: 200rpx;
        border-radius: 16rpx;
      }
    }
  }
}

// 个人信息样式
.info-section {
  margin-bottom: 48rpx;
  
  .info-content {
    .signature {
      margin-bottom: 24rpx;
      
      .signature-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.4;
      }
    }
    
    .info-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      
      .gender-age-tag {
        display: flex;
        align-items: center;
        border-radius: 8rpx;
        padding: 6rpx 12rpx 6rpx 8rpx;
        margin-right: 8rpx;
        
        .gender-icon-img {
          width: 20rpx;
          height: 20rpx;
          margin-right: 6rpx;
        }
        
        .age-text {
          font-size: 24rpx;
          color: #1976d2;
          font-weight: 500;
        }
        
        &.female-bg {
          background: #ffe4ef;
        }
        
        &.male-bg {
          background: #e3f2fd;
        }
      }
      
      .tag {
        font-size: 26rpx;
        background: #f5f5f5;
        color: #666;
        border-radius: 6rpx;
        padding: 8rpx 20rpx;
      }
    }
  }
}

// 等级成就样式
.level-section {
  .level-list {
    display: flex;
    gap: 60rpx;
    
    .level-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10rpx 20rpx;
      background: linear-gradient( 180deg, #F0FEF1 0%, #FFFFFF 100%);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #EBF8EE;
      .level-label {
        font-size: 24rpx;
        color: #000000;
        margin-bottom: 8rpx;
      }
      
      .level-value {
        display: flex;
        align-items: center;
        gap: 8rpx;
        background: url('/static/index/level/lv1.png') no-repeat center center;
        background-size: 100% 100%;
        width: 100%;
        .level-icon {
          font-size: 24rpx;
          color: #ffb300;
        }
        
        .level-text {
          line-height: 36rpx;
          font-size: 21rpx;
          color: #000000;
          font-weight: bold;
          text-align: right;
          width: 100%;
          padding-right: 4px;
        }
      }
    }
  }
}
.line-delive{
  width: 100%;
  height: 1rpx;
  background: #F5F5F5;
  margin: 20rpx 0;
}
</style>