<template>
  <view class="image-carousel">
    <!-- 顶部banner轮播 -->
    <view class="banner-container">
      <swiper 
        class="banner-swiper" 
        :current="currentIndex" 
        @change="onSwiperChange"
        :indicator-dots="false"
        :autoplay="autoplay"
        :interval="interval"
        :duration="duration"
        :circular="circular"
      >
        <swiper-item v-for="(item, index) in imageList" :key="index">
          <image 
            class="banner-image" 
            :src="item" 
            mode="aspectFill"
            @click="onImageClick(item, index)"
          />
        </swiper-item>
      </swiper>
      
      <!-- 图片计数器 -->
      <view class="image-counter">
        {{ currentIndex + 1 }}/{{ imageList.length }}
      </view>
    </view>
    
    <!-- 底部缩略图列表 -->
    <!-- <view class="thumbnail-container">
      <scroll-view 
        class="thumbnail-scroll" 
        scroll-x="true" 
        :scroll-left="scrollLeft"
        show-scrollbar="false"
      >
        <view class="thumbnail-list">
          <view 
            v-for="(item, index) in imageList" 
            :key="index"
            :class="['thumbnail-item', { active: currentIndex === index }]"
            @click="switchToImage(index)"
          >
            <image 
              class="thumbnail-image" 
              :src="item" 
              mode="aspectFill"
            />
          </view>
        </view>
      </scroll-view>
    </view> -->
  </view>
</template>

<script>
export default {
  name: 'ImageCarousel',
  props: {
    // 图片列表
    imageList: {
      type: Array,
      default: () => []
    },
    // 是否自动播放
    autoplay: {
      type: Boolean,
      default: true
    },
    // 自动播放间隔时间(ms)
    interval: {
      type: Number,
      default: 3000
    },
    // 滑动动画时长(ms)
    duration: {
      type: Number,
      default: 500
    },
    // 是否循环播放
    circular: {
      type: Boolean,
      default: true
    },
    // 初始显示的图片索引
    initialIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      currentIndex: this.initialIndex,
      scrollLeft: 0
    }
  },
  watch: {
    currentIndex(newVal) {
      this.updateScrollPosition(newVal)
    }
  },
  mounted() {
    this.updateScrollPosition(this.currentIndex)
  },
  methods: {
    // 轮播图切换事件
    onSwiperChange(e) {
      this.currentIndex = e.detail.current
      this.$emit('change', {
        current: this.currentIndex,
        image: this.imageList[this.currentIndex]
      })
    },
    
    // 点击缩略图切换图片
    switchToImage(index) {
      this.currentIndex = index
      this.$emit('change', {
        current: this.currentIndex,
        image: this.imageList[this.currentIndex]
      })
    },
    
    // 点击banner图片
    onImageClick(image, index) {
      this.$emit('imageClick', {
        image,
        index
      })
    },
    
    // 更新缩略图滚动位置
    updateScrollPosition(index) {
      const thumbnailWidth = 120 // 缩略图宽度
      const containerWidth = uni.getSystemInfoSync().windowWidth - 60 // 容器宽度(减去左右padding)
      const maxScrollLeft = Math.max(0, (this.imageList.length * thumbnailWidth) - containerWidth)
      
      // 计算目标滚动位置，让当前选中的缩略图居中显示
      const targetScrollLeft = Math.max(0, (index * thumbnailWidth) - (containerWidth / 2) + (thumbnailWidth / 2))
      this.scrollLeft = Math.min(targetScrollLeft, maxScrollLeft)
    }
  }
}
</script>

<style lang="scss" scoped>
.image-carousel {
  width: 100%;
}

.banner-container {
  position: relative;
  width: 100%;
  height: 600rpx;
  background: #f5f5f5;
  border-radius: 0; // 去掉圆角，实现通顶效果
  overflow: hidden;
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-counter {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  // background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  z-index: 10;
}

.thumbnail-container {
  margin-top: 20rpx;
  padding: 0 30rpx;
}

.thumbnail-scroll {
  width: 100%;
  white-space: nowrap;
}

.thumbnail-list {
  display: flex;
  gap: 16rpx;
  padding: 10rpx 0;
  margin: 0 5px;
}

.thumbnail-item {
  flex-shrink: 0;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 3rpx solid transparent;
  transition: all 0.3s ease;
  
  &.active {
    border-color: #007aff;
    transform: scale(1.05);
  }
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.uni-scroll-view-content{
  margin: 0 5px;
}
.thumbnail-container{
  position: absolute;
  bottom: 0;
}
.banner-container{
  border-radius: 0;
}
.image-carousel{
  position: relative;
}
</style> 