<template>
  <view class="moments-tab">
    <!-- 使用scroll-view实现滚动到底部自动加载更多 -->
    <scroll-view
      class="scroll-container"
      scroll-y 
      @scrolltolower="loadMoreMoments"
      :lower-threshold="50"
    >
      <!-- 使用DynamicMoments组件来显示动态列表 -->
      <DynamicMoments 
        :list="momentsList" 
        :status="loadingStatus"
        :showHiChatButton="false"
				no-more-text=""
      />
      
      <!-- 空状态 -->
      <view v-if="!loading && momentsList.length === 0" class="empty-state">
				<image src="/static/index/icon/no-more.png" class="no-more"></image>
        <text class="empty-text">暂无数据</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import DynamicMoments from '../../index/components/DynamicMoments.vue'
import { momentsApi } from '../../../common/api.js'

export default {
  name: 'MomentsTab',
  components: {
    DynamicMoments
  },
  props: {
    userInfo: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      momentsList: [], // 动态列表
      loading: false, // 加载状态
      loadingStatus: 'more', // 加载状态：more-可以加载更多，loading-加载中，noMore-没有更多数据
      nextCursor: null, // 下一页游标
      pageSize: 10 // 每页数据量
    }
  },
  mounted() {
    this.loadMoments()
  },
  methods: {
    // 加载动态列表
    async loadMoments(isLoadMore = false) {
      if (this.loading) return
      
      try {
        this.loading = true
        this.loadingStatus = 'loading'
        
        const params = {
          size: this.pageSize
        }
        
        // 如果是加载更多，传递cursor参数
        if (isLoadMore && this.nextCursor) {
          params.cursor = this.nextCursor
        }
        
        const result = await momentsApi.getUserMomentsList(this.userInfo.userId, params)
        
        if (result.code === 200 && result.data) {
          const newData = result.data.posts || []
          
          if (isLoadMore) {
            // 追加数据
            this.momentsList = [...this.momentsList, ...newData]
          } else {
            // 重置数据
            this.momentsList = newData
          }
          
          // 更新游标
          this.nextCursor = result.data.nextCursor
          
          // 更新加载状态
          if (result.data.nextCursor) {
            this.loadingStatus = 'more'
          } else {
            this.loadingStatus = 'noMore'
          }
        } else {
          throw new Error(result.msg || '获取动态失败')
        }
      } catch (error) {
        console.error('获取用户动态失败:', error)
        this.loadingStatus = 'more'
        
        if (!isLoadMore) {
          uni.showToast({
            title: '获取动态失败',
            icon: 'none'
          })
        }
      } finally {
        this.loading = false
      }
    },
    
    // 加载更多动态
    loadMoreMoments() {
      if (this.loadingStatus === 'more' && !this.loading) {
        this.loadMoments(true)
      }
    },
    
    // 刷新动态列表
    refreshMoments() {
      this.nextCursor = null
      this.loadMoments()
    }
  }
}
</script>

<style scoped lang="scss">
.moments-tab {
  height: 100%;
  margin: -30rpx -30rpx 0 -30rpx; /* 抵消tab-content的padding */
  
  .scroll-container {
    height: 600rpx; /* 固定高度，适应Tab内容区域 */
  }
}

.empty-state {
  display: flex;
	flex-direction: column;
  justify-content: center;
  align-items: center;
  // padding: 100rpx 0;
  // background: #f5f5f5;
  .no-more {
		height: 256rpx;
		width: 256rpx;
	}
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>