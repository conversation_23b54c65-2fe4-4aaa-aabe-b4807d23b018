# StickyHeader 吸顶组件

## 功能描述

StickyHeader 是一个吸顶组件，当页面向下滚动时，当 nickname-row 与顶部屏幕距离小于指定像素时，会自动固定在屏幕顶部指定位置，不再移动。当页面重新向上滚动到触发置顶位置时，会恢复 nickname-row 的原始样式。

## 使用方法

### 1. 导入组件

```javascript
import StickyHeader from './components/StickyHeader.vue'

export default {
  components: {
    StickyHeader
  }
}
```

### 2. 在模板中使用

```vue
<template>
  <!-- 吸顶组件 -->
  <StickyHeader 
    :avatarUrl="avatarUrl" 
    :nickname="userInfo.nickname"
    :stickyTop="120"
    :scrollTop="pageScrollTop"
    :statusBarHeight="statusBarHeight"
  />
</template>
```

### 3. 在页面中添加滚动监听

```javascript
export default {
  data() {
    return {
      pageScrollTop: 0, // 页面滚动距离
      statusBarHeight: 0 // 状态栏高度
    }
  },
  
  onLoad(options) {
    // 获取系统信息，包括状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 0
  },
  
  // 页面滚动监听
  onPageScroll(e) {
    this.pageScrollTop = e.scrollTop
  }
}
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| avatarUrl | String | '' | 头像图片URL |
| nickname | String | '' | 用户昵称 |
| stickyTop | Number | 120 | 触发吸顶的距离阈值 |
| scrollTop | Number | 0 | 页面滚动距离，需要从父页面传递 |
| statusBarHeight | Number | 0 | 状态栏高度，用于计算吸顶位置 |

## 工作原理

1. **原始位置显示**：组件在原始位置显示 nickname-row，包含头像和昵称
2. **滚动监听**：父页面使用 `onPageScroll` 监听滚动，通过 props 传递给子组件
3. **触发吸顶**：当原始元素距离顶部小于等于 `stickyTop` 时，触发吸顶效果
4. **吸顶显示**：显示固定在顶部的 nickname-row，带有半透明背景和模糊效果
5. **恢复原状**：当页面向上滚动，元素距离顶部大于 `stickyTop` 时，恢复原始显示

## 样式特点

- **原始位置**：保持原有的样式和位置
- **吸顶样式**：半透明背景（rgba(0,0,0,0.6)），毛玻璃效果，圆角设计
- **吸顶位置**：固定在返回按钮下方，避免遮挡
- **平滑过渡**：使用 CSS transition 实现平滑的显示/隐藏效果
- **响应式**：适配不同屏幕尺寸和状态栏高度

## 注意事项

1. 组件通过 props 接收滚动距离，父页面需要实现 `onPageScroll` 监听
2. 组件会在 `mounted` 时自动计算原始位置
3. 吸顶效果使用 `position: fixed` 实现，确保始终显示在顶部
4. 组件使用 `watch` 监听 `scrollTop` 变化，自动触发吸顶逻辑

## 兼容性

- 支持 uniapp 所有平台
- 自动适配状态栏高度
- 响应式设计，适配不同屏幕尺寸 