<template>
  <view v-if="visible" class="report-overlay" @click="handleOverlayClick">
    <view class="report-dialog" @click.stop>
      <!-- 头部 -->
      <view class="report-header">
        <text class="report-title">举报</text>
        <view class="report-close" @click="handleClose">×</view>
      </view>
      
      <!-- 举报分类 -->
      <view class="report-section">
        <view class="section-title">
          <text>请选择举报分类</text>
          <text class="required">*</text>
        </view>
        <view class="report-types">
          <view 
            v-for="(type, index) in reportTypes" 
            :key="index"
            class="report-type-item"
            :class="{ active: selectedType === type.value }"
            @click="selectType(type.value)"
          >
            <view class="radio-button">
              <view class="radio-inner" v-if="selectedType === type.value"></view>
            </view>
            <text class="type-text">{{ type.label }}</text>
          </view>
        </view>
      </view>
      
      <!-- 原因描述 -->
      <view class="report-section">
        <view class="gray-divider"></view>
        <view class="section-title">原因描述</view>
        <textarea 
          class="reason-input"
          v-model="reasonContent"
          placeholder="请输入详细举报理由帮助我们进行判断"
          maxlength="500"
          show-confirm-bar="false"
        />
        <!-- <view class="char-count">{{ reasonContent.length }}/500</view> -->
      </view>
      
      <!-- 图片上传 -->
      <view class="report-section">
        <view class="upload-area">
          <view 
            v-for="(image, index) in uploadedImages" 
            :key="index"
            class="image-item"
          >
            <image class="uploaded-image" :src="image" mode="aspectFill" />
            <view class="delete-btn" @click="deleteImage(index)">×</view>
          </view>
          <view 
            v-if="uploadedImages.length < 3"
            class="upload-btn"
            @click="chooseImage"
          >
            <view class="camera-icon">+</view>
            <text class="upload-text">图片或视频</text>
          </view>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-section">
        <button 
          class="submit-btn"
          :class="{ disabled: !canSubmit }"
          :disabled="!canSubmit"
          @click="handleSubmit"
        >
          提交
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi, fileApi } from '../../../common/api'

export default {
  name: 'ReportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    targetUserId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      selectedType: '',
      reasonContent: '',
      uploadedImages: [],
      reportTypes: [
        { label: '淫秽色情', value: '淫秽色情' },
        { label: '冒充他人', value: '冒充他人' },
        { label: '言语低俗', value: '言语低俗' },
        { label: '其他', value: '其他' }
      ]
    }
  },
  computed: {
    canSubmit() {
      return this.selectedType && this.reasonContent.trim().length > 0
    }
  },
  methods: {
    handleOverlayClick() {
      this.handleClose()
    },
    
    handleClose() {
      this.$emit('close')
      this.resetForm()
    },
    
    selectType(type) {
      this.selectedType = type
    },
    
    async chooseImage() {
      try {
        const res = await uni.chooseImage({
          count: 3 - this.uploadedImages.length,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera']
        })
        
        // 使用系统已有的上传功能
        const uploadResults = await fileApi.uploadMultiple(res.tempFilePaths, 'report')
        
        // 添加图片URL到列表
        for (const result of uploadResults) {
          if (result.code === 200) {
            const imageUrl = 'http://************:9000/' + result.data
            this.uploadedImages.push(imageUrl)
          }
        }
      } catch (error) {
        console.error('选择图片失败:', error)
        uni.showToast({
          title: '图片上传失败',
          icon: 'none'
        })
      }
    },
    
    deleteImage(index) {
      this.uploadedImages.splice(index, 1)
    },
    
    async handleSubmit() {
      if (!this.canSubmit) return
      
      try {
        uni.showLoading({ title: '提交中...' })
        
        const reportData = {
          targetUserId: this.targetUserId,
          reportType: this.selectedType,
          content: this.reasonContent.trim(),
          imageUrls: this.uploadedImages
        }
        
        const result = await userApi.reportUser(reportData)
        
        if (result.code === 200) {
          uni.hideLoading()
          uni.showToast({
            title: '举报成功',
            icon: 'success'
          })
          this.handleClose()
        } else {
          throw new Error(result.msg || '举报失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('举报失败:', error)
        uni.showToast({
          title: error.message || '举报失败，请重试',
          icon: 'none'
        })
      }
    },
    
    resetForm() {
      this.selectedType = ''
      this.reasonContent = ''
      this.uploadedImages = []
    }
  }
}
</script>

<style scoped lang="scss">
.report-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 100;
}

.report-dialog {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
  position: relative;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.report-header {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 30rpx 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .report-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }

  .report-close {
    position: absolute;
    right: 30rpx;
    font-size: 40rpx;
    color: #999;
    font-weight: bold;
    padding: 10rpx;
  }
}

.report-section {
  padding: 30rpx;

  .section-title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;

    .required {
      color: #ff4d4f;
      margin-left: 4rpx;
    }
  }
}

.report-types {
  .report-type-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .radio-button {
      width: 32rpx;
      height: 32rpx;
      border: 2rpx solid #ddd;
      border-radius: 50%;
      margin-right: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .radio-inner {
        width: 20rpx;
        height: 20rpx;
        background: #66D47E;
        border-radius: 50%;
      }
    }

    .type-text {
      font-size: 28rpx;
      color: #333;
    }

    &.active {
      .radio-button {
        border-color: #66D47E;
      }
    }
  }
}

.gray-divider {
  height: 12rpx;
  background: #f0f0f0;
  margin: 0 -30rpx 20rpx -30rpx;
}

.reason-input {
  width: 100%;
  min-height: 30rpx;
  height: 50px;
  padding: 20rpx;
  border: none;
  border-radius: 0;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  box-sizing: border-box;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;

  .image-item {
    position: relative;
    width: 160rpx;
    height: 160rpx;

    .uploaded-image {
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
    }

    .delete-btn {
      position: absolute;
      top: -10rpx;
      right: -10rpx;
      width: 40rpx;
      height: 40rpx;
      background: rgba(0, 0, 0, 0.6);
      color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
    }
  }

  .upload-btn {
    width: 160rpx;
    height: 160rpx;
    border: 2rpx dashed #ddd;
    border-radius: 8rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafafa;

    .camera-icon {
      width: 48rpx;
      height: 48rpx;
      margin-bottom: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      color: #999;
      font-weight: bold;
    }

    .upload-text {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.submit-section {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(45deg, #66D47E 0%, #91E02A 100%);
    color: #fff;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
    box-shadow: 0px 0px 4px 1px rgba(145,224,42,0.5);

    &.disabled {
      background: #66D47E;
      color: #fff;
      box-shadow: none;
    }
  }
}
</style> 