<template>
  <view class="featured-album">
    <NavigationCustorm>
      <template #title>
        精选相册
      </template>
    </NavigationCustorm>
    
    <!-- 相册展示区域 -->
    <view class="album-container">
      <view v-if="loading" class="loading-state">
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="albumImages.length === 0" class="empty-state">
        <image class="empty-icon" src="/static/icon/empty-album.png" />
        <text class="empty-text">暂无精选相册</text>
      </view>
      
      <view v-else class="album-grid">
        <view 
          v-for="(image, index) in albumImages" 
          :key="index" 
          class="album-item"
          @click="previewImage(index)"
        >
          <image 
            class="album-image" 
            :src="image" 
            mode="aspectFill"
            @load="onImageLoad"
            @error="onImageError"
          />
          <view class="image-overlay">
            <view class="image-index">{{ index + 1 }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { userApi } from '@/common/api'
import { HTTP_IMG } from '@/common/constant'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

export default {
  name: 'FeaturedAlbum',
  components: {
    NavigationCustorm
  },
  data() {
    return {
      albumImages: [],
      loading: false,
      userInfo: {},
      targetUserId: ''
    }
  },
  computed: {
    // 计算相册图片列表
    computedAlbumImages() {
      if (this.userInfo?.albumPhotos && this.userInfo.albumPhotos.length > 0) {
        // 为相册图片添加域名
        return this.userInfo.albumPhotos.map(photo => {
          if (photo.startsWith('http://') || photo.startsWith('https://')) {
            return photo
          }
          return HTTP_IMG + photo
        })
      }
      // 如果没有相册图片，使用头像作为默认图片
      return this.userInfo?.avatar ? [HTTP_IMG + this.userInfo.avatar] : []
    }
  },
  onLoad(options) {
    console.log('onLoad options:', options) // 调试日志
    // 获取路由参数中的用户ID
    if (options.userId) {
      this.targetUserId = options.userId
      console.log('设置targetUserId:', this.targetUserId) // 调试日志
      // 立即获取数据
      this.fetchUserDetail()
    } else {
      console.log('未找到userId参数') // 调试日志
      uni.showToast({
        title: '缺少用户ID参数',
        icon: 'none'
      })
    }
  },
  methods: {
    // 图片预览功能
    previewImage(index) {
      uni.previewImage({
        current: index,
        urls: this.albumImages
      })
    },

    // 图片加载成功
    onImageLoad(e) {
      console.log('图片加载成功:', e)
    },

    // 图片加载失败
    onImageError(e) {
      console.log('图片加载失败:', e)
    },

    // 获取用户详情数据
    async fetchUserDetail() {
      try {
        this.loading = true
        console.log('fetchUserDetail开始，targetUserId:', this.targetUserId) // 调试日志
        
        if (this.targetUserId) {
          console.log('调用API获取用户详情，userId:', this.targetUserId) // 调试日志
          const res = await userApi.getUserProfile(this.targetUserId)
          console.log('API返回结果:', res) // 调试日志
          
          if (res.data) {
            this.userInfo = res.data
            console.log('设置userInfo:', this.userInfo) // 调试日志
            // 设置相册图片
            this.albumImages = this.computedAlbumImages
            console.log('设置albumImages:', this.albumImages) // 调试日志
          }
        } else {
          console.log('targetUserId为空，显示错误提示') // 调试日志
          uni.showToast({
            title: '缺少用户ID参数',
            icon: 'none'
          })
        }
        
      } catch (error) {
        console.error('获取用户详情失败:', error)
        uni.showToast({
          title: '获取用户详情失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.featured-album {
  height: 100vh;
  background: #f4f8fb;
  padding: 192rpx 0 120rpx 0;
  box-sizing: border-box;
}

.album-container {
  padding: 40rpx 30rpx;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 200rpx 0;
  
  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
  
  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 40rpx;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.album-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  padding: 20rpx 0;
}

.album-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f0f0f0;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
  }
  
  .album-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.3) 100%);
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    padding: 16rpx;
    
    .image-index {
      background: rgba(0, 0, 0, 0.6);
      color: white;
      font-size: 20rpx;
      padding: 8rpx 12rpx;
      border-radius: 12rpx;
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .album-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }
}

@media (min-width: 1200rpx) {
  .album-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 24rpx;
  }
}
</style> 