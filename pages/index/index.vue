<template>
  <view class="container">
    <view class="tab-header" :style="{ opacity: tabHeaderOpacity, transition: 'opacity 0.3s' }">
      <view class="head-images">
        <image class="head-img left" src="/static/index/head1.png" mode="heightFix" />
        <image class="head-img right" src="/static/index/head2.png" mode="heightFix" />
      </view>
      <view v-for="(tab, index) in tabs" :key="index" class="tab-item" @tap="switchTab(index)">
        <image
          :src="currentTab === index ? tab.active_icon : tab.inactive_icon"
          class="tab-image"
          mode="heightFix"
        />
      </view>
      <view class="sub-header" v-if="currentTab < 3">
        <scroll-view
          scroll-x
          class="tab-scroll my-tab-scroll"
          :scroll-into-view="'subtab-' + currentDynamicTab"
        >
          <view class="tab-container">
            <view
              v-for="(item, index) in dynamicTabList"
              :key="index"
              :class="['tab-item', currentDynamicTab === index ? 'active' : '']"
              :id="'subtab-' + index"
              @tap="switchDynamicTab(index)"
            >
              {{ item }}
            </view>
          </view>
        </scroll-view>
        <view class="layout-switch" @tap="toggleLayout">
          <image
            :src="isGrid ? '/static/index/grid.png' : '/static/index/list.png'"
            class="layout-image"
            mode="heightFix"
          />
        </view>
        <view class="filter-switch" @click="openFilterPopup">
          <image src="/static/index/filter.png" class="filter-image" mode="heightFix" />
        </view>
      </view>
    </view>

    <scroll-view
      class="tab-content"
      scroll-y
      @scroll="onScroll"
      :scroll-top="scrollTop"
      :scroll-with-animation="false"
    >
      <!-- 推荐标签页 -->
      <Dynamic
        v-show="currentTab === 0"
        :list="recommendList"
        :status="recommendStatus"
        :currentLayout="currentLayout"
      />
      
      <!-- 新人标签页 -->
      <Dynamic
        v-show="currentTab === 1"
        :list="newUserList"
        :status="newUserStatus"
        :currentLayout="currentLayout"
      />
      
      <!-- 关注标签页 -->
      <Dynamic
        v-show="currentTab === 2"
        :list="followList"
        :status="followStatus"
        :currentLayout="currentLayout"
      />
      
      <!-- 动态标签页 -->
      <DynamicMoments 
        v-show="currentTab === 3" 
        :list="momentsList" 
        :status="momentsStatus" 
      />
      
      <!-- 活动标签页 -->
      <Recruit 
        v-show="currentTab === 4" 
        :list="recruitList" 
        :status="recruitStatus" 
      />

      <!-- 加载状态指示器 -->
      <!-- <view v-if="loadingStatus === 'loading' && listData.length > 0" class="loading-indicator">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view> -->

      <!-- 已经到底了提示 -->
      <!-- <view v-if="loadingStatus === 'noMore' && listData.length > 0" class="no-more-indicator">
				<text class="no-more-text">已经到底了</text>
			</view> -->
    </scroll-view>
    <FilterPopup v-model:visible="isFilterPopupVisible" @confirm="applyFilters" />
    <!-- 定位权限弹窗 -->
    <LocationPermission
      v-model:visible="isLocationPermissionVisible"
      @close="isLocationPermissionVisible = false"
      @success="
        isLocationPermissionVisible = false;
        checkLocationPermission()
      "
    />
  </view>
</template>

<script>
  import { useUserStore } from '@/stores/user'
  import Dynamic from './components/Dynamic.vue'
  import Shop from '../shopping/shopping.vue'
  import FilterPopup from './components/FilterPopup.vue'
  import LocationPermission from '@/components/LocationPermission.vue'
  import { getLocation, checkLocationPermission } from '@/utils/location.js'
  import DynamicMoments from './components/DynamicMoments.vue'
  import Recruit from './components/recruit.vue'
  export default {
    components: {
      Dynamic,
      Shop,
      FilterPopup,
      LocationPermission,
      DynamicMoments,
      Recruit
    },
    // 页面配置
    onLoad() {
      // 设置页面标题为空，隐藏导航栏
      uni.setNavigationBarTitle({
        title: ''
      })

      // 禁用下拉刷新
      uni.stopPullDownRefresh()

      if (!this.isLoggedIn) {
        // 可以选择跳转到登录页或其他处理
      }
      
      // 同时加载所有标签页的数据
      this.fetchAllTabData()
      this.checkLocationPermission()
    },
    // 页面配置 - 禁用下拉刷新
    onPullDownRefresh() {
      // 立即停止下拉刷新
      uni.stopPullDownRefresh()
    },
    setup() {
      const userStore = useUserStore()

      return {
        userStore
      }
    },
    data() {
      return {
        currentTab: 0,
        tabHeaderOpacity: 1,
        tabs: [
          {
            name: '推荐',
            sub: '精选',
            active_icon: '/static/index/tj-active.png',
            inactive_icon: '/static/index/tj.png'
          },
          {
            name: '新人',
            sub: '新面孔',
            active_icon: '/static/index/xr-active.png',
            inactive_icon: '/static/index/xr.png'
          },
          {
            name: '关注',
            sub: '你在意',
            active_icon: '/static/index/gz-active.png',
            inactive_icon: '/static/index/gz.png'
          },
          {
            name: '动态',
            sub: '有趣事',
            active_icon: '/static/index/dt-active.png',
            inactive_icon: '/static/index/dt.png'
          },
          {
            name: '活动',
            sub: '一起玩',
            active_icon: '/static/index/zm-active.png',
            inactive_icon: '/static/index/zm.png'
          }
        ],
        dynamicTabList: ['综合', '运动类', '娱乐类', '功能类'],
        currentDynamicTab: 0,
        isGrid: true,
        currentLayout: 'ListGrid',
        isFilterPopupVisible: false,
        // 推荐标签页数据
        recommendList: [],
        recommendStatus: 'loading',
        recommendFilters: {},
        
        // 新人标签页数据
        newUserList: [],
        newUserStatus: 'loading',
        newUserFilters: {},
        
        // 关注标签页数据
        followList: [],
        followStatus: 'loading',
        followFilters: {},
        
        // 通用数据
        page: 1,
        size: 10,
        filters: {},
        isLocationPermissionVisible: false,
        userLocation: null,
        scrollTop: 0,
        scrollTimer: null,
        isLoadingMore: false,
        momentsList: [],
        momentsStatus: 'more',
        nextCursor: null,
        recruitList: [],
        recruitStatus: 'more'
      }
    },
    computed: {
      currentUser() {
        return this.userStore.userInfo
      },
      isLoggedIn() {
        return this.userStore.checkIsLoggedIn
      },
      userToken() {
        return this.userStore.token
      }
    },
    methods: {
      // 同时获取所有标签页的数据
      async fetchAllTabData() {
        // 并行获取推荐、新人、关注三个标签页的数据
        await Promise.all([
          this.fetchRecommendData(),
          this.fetchNewUserData(),
          this.fetchFollowData()
        ])
      },
      
      // 获取推荐数据
      async fetchRecommendData() {
        this.recommendStatus = 'loading'
        const apiParams = {
          pageNum: 1,
          pageSize: 1000
        }
        
        // 应用推荐标签页的筛选条件
        this.applyFiltersToParams(apiParams, this.recommendFilters)
        
        try {
          const res = await this.userStore.getRecommendList(apiParams)
          if (res.code === 200 && res.rows) {
            await this.processUserList(res.rows)
            this.recommendList = res.rows
            this.recommendStatus = 'noMore'
          } else {
            this.recommendStatus = 'noMore'
          }
        } catch (error) {
          console.error('获取推荐数据失败:', error)
          this.recommendStatus = 'noMore'
        }
      },
      
      // 获取新人数据
      async fetchNewUserData() {
        this.newUserStatus = 'loading'
        const apiParams = {
          pageNum: 1,
          pageSize: 1000
        }
        
        // 应用新人标签页的筛选条件
        this.applyFiltersToParams(apiParams, this.newUserFilters)
        
        try {
          const res = await this.userStore.getNewUserList(apiParams)
          if (res.code === 200 && res.rows) {
            await this.processUserList(res.rows)
            this.newUserList = res.rows
            this.newUserStatus = 'noMore'
          } else {
            this.newUserStatus = 'noMore'
          }
        } catch (error) {
          console.error('获取新人数据失败:', error)
          this.newUserStatus = 'noMore'
        }
      },
      
      // 获取关注数据
      async fetchFollowData() {
        this.followStatus = 'loading'
        const apiParams = {
          pageNum: 1,
          pageSize: 1000
        }
        
        // 应用关注标签页的筛选条件
        this.applyFiltersToParams(apiParams, this.followFilters)
        
        try {
          const res = await this.userStore.getFollowList(apiParams)
          if (res.code === 200 && res.rows) {
            await this.processUserList(res.rows)
            this.followList = res.rows
            this.followStatus = 'noMore'
          } else {
            this.followStatus = 'noMore'
          }
        } catch (error) {
          console.error('获取关注数据失败:', error)
          this.followStatus = 'noMore'
        }
      },
      
      // 处理用户列表（获取在线状态并排序）
      async processUserList(userList) {
        if (!userList || userList.length === 0) return
        
        // 获取用户在线状态
        const userIds = userList.map(item => item.userId).filter(id => id)
        if (userIds.length > 0) {
          try {
            const onlineStatusRes = await this.userStore.getOnlineStatus(userIds)
            if (onlineStatusRes.code === 200 && onlineStatusRes.data) {
              // 将在线状态数据合并到用户列表中
              const onlineStatusMap = {}
              onlineStatusRes.data.forEach(item => {
                if (item.uid && typeof item.online === 'number') {
                  // online: 1为在线，0为离线
                  onlineStatusMap[item.uid] = item.online === 1
                }
              })

              // 更新用户列表中的在线状态
              userList.forEach(item => {
                // 如果在线状态映射中存在该用户ID，则为在线，否则为离线
                item.isOnline = onlineStatusMap[item.userId] === true
              })

              // 对用户列表进行排序，在线用户排在前面
              userList.sort((a, b) => {
                // 在线状态相同时，保持原有顺序
                if (a.isOnline === b.isOnline) {
                  return 0
                }
                // 在线用户排在前面
                return a.isOnline ? -1 : 1
              })
            }
          } catch (error) {
            console.error('获取在线状态失败:', error)
            // 如果获取在线状态失败，使用默认值
            userList.forEach(item => {
              item.isOnline = false
            })
          }
        } else {
          // 如果没有有效的userId，设置默认在线状态
          userList.forEach(item => {
            item.isOnline = false
          })
        }
      },
      
      // 应用筛选条件到API参数
      applyFiltersToParams(apiParams, filters) {
        // 性别筛选：转换为整数格式
        if (filters.gender && filters.gender !== 'all') {
          apiParams.gender = filters.gender === 'male' ? 1 : 2
        }

        // 年龄筛选：只在用户真正选择了年龄范围时才添加参数
        if (filters.minAge !== undefined && filters.maxAge !== undefined) {
          // 如果不是默认的18-60岁范围，则添加筛选条件
          if (filters.minAge !== 18 || filters.maxAge !== 60) {
            apiParams.minAge = filters.minAge
            apiParams.maxAge = filters.maxAge
          }
        } else if (filters.minAge !== undefined) {
          // 如果只设置了最小年龄且不是默认值
          if (filters.minAge !== 18) {
            apiParams.minAge = filters.minAge
          }
        } else if (filters.maxAge !== undefined) {
          // 如果只设置了最大年龄且不是默认值
          if (filters.maxAge !== 60) {
            apiParams.maxAge = filters.maxAge
          }
        }

        // 距离筛选：只在用户真正选择了距离时才添加参数
        if (filters.distance !== undefined && filters.distance !== 50) {
          apiParams.distance = filters.distance
        }

        // 在线状态筛选：转换为整数格式
        if (filters.onlineStatus && filters.onlineStatus !== 'any') {
          apiParams.status = filters.onlineStatus === 'online' ? 1 : 0
        }

        // 标签筛选：转换为 tags 参数
        if (filters.tag && filters.tag !== '' && filters.tag !== null) {
          apiParams.tags = filters.tag
        }
      },
      
      // 保留原有的fetchListData方法用于兼容性
      async fetchListData(isRefresh = false) {
        // 根据当前标签页调用对应的获取方法
        switch (this.currentTab) {
          case 0:
            await this.fetchRecommendData()
            break
          case 1:
            await this.fetchNewUserData()
            break
          case 2:
            await this.fetchFollowData()
            break
        }
      },
      async fetchMomentsList(isRefresh = false) {
        if (isRefresh) {
          this.momentsList = []
          this.momentsStatus = 'more'
          this.nextCursor = null
        }
        if (this.momentsStatus === 'noMore' || this.momentsStatus === 'loading') return
        this.momentsStatus = 'loading'
        try {
          // 构造请求参数
          const params = {
            size: 10
          }

          // 如果不是刷新且有 nextCursor，则添加 cursor 参数
          if (!isRefresh && this.nextCursor) {
            params.cursor = this.nextCursor
          }

          const res = await this.userStore.getMomentsList(params)
          if (res.code === 200 && res.data && res.data.posts) {
            // 处理返回的动态列表数据
            const newMoments = res.data.posts
            this.momentsList = isRefresh ? newMoments : [...this.momentsList, ...newMoments]

            // 更新 nextCursor 用于下一页请求
            this.nextCursor = res.data.nextCursor

            // 如果 nextCursor 为 null，表示没有更多数据
            this.momentsStatus = res.data.nextCursor ? 'more' : 'noMore'
          } else {
            this.momentsStatus = 'noMore'
          }
        } catch (e) {
          console.error('获取动态列表失败:', e)
          this.momentsStatus = 'noMore'
        }
      },
      async fetchRecruitList(isRefresh = false) {
        if (isRefresh) {
          this.recruitList = []
          this.recruitStatus = 'more'
        }
        if (this.recruitStatus === 'noMore' || this.recruitStatus === 'loading') return
        this.recruitStatus = 'loading'
        try {
          // 构造请求参数
          const params = {
            pageSize: 20
          }

          // 如果不是刷新且有 nextCursor，则添加 cursor 参数
          if (!isRefresh && this.nextCursor) {
            params.cursor = this.nextCursor
          }

          const res = await this.userStore.getRecruitList(params)
          if (res.code === 200 && res.data && res.data.list) {
            // 处理返回的动态列表数据
            const newMoments = res.data.list
            this.recruitList = isRefresh ? newMoments : [...this.recruitList, ...newMoments]

            // 更新 nextCursor 用于下一页请求
            this.nextCursor = res.data.nextCursor

            // 如果 nextCursor 为 null，表示没有更多数据
            this.recruitStatus = res.data.nextCursor ? 'more' : 'noMore'
          } else {
            this.recruitStatus = 'noMore'
          }
        } catch (e) {
          console.error('获取动态列表失败:', e)
          this.recruitStatus = 'noMore'
        }
      },
      switchTab(index) {
        this.currentTab = index
        // 重置滚动相关状态
        this.scrollTop = 0
        this.isLoadingMore = false
        if (this.scrollTimer) {
          clearTimeout(this.scrollTimer)
          this.scrollTimer = null
        }
        if (index === 3) {
          this.fetchMomentsList(true)
        } else if (index === 4) {
          this.fetchRecruitList(true)
        }
        // 推荐、新人、关注标签页的数据已经在页面加载时获取完成，无需重新获取
      },
      onPageScroll(e) {
        let opacity = 1
        if (e.scrollTop > 0) {
          opacity = Math.max(0, 1 - e.scrollTop / 80)
        }
        this.tabHeaderOpacity = opacity
      },
      switchDynamicTab(index) {
        this.currentDynamicTab = index
        // 重置滚动相关状态
        this.scrollTop = 0
        this.isLoadingMore = false
        if (this.scrollTimer) {
          clearTimeout(this.scrollTimer)
          this.scrollTimer = null
        }
        // 根据dynamic tab的内容设置筛选条件
        const tagMapping = {
          0: null, // 综合 - 不设置标签筛选
          1: '运动类',
          2: '娱乐类',
          3: '功能类'
        }

        // 更新当前标签页的筛选条件
        const currentFilters = this.getCurrentTabFilters()
        currentFilters.tag = tagMapping[index]

        // 重新获取当前标签页的数据
        this.refreshCurrentTabData()
      },
      toggleLayout() {
        this.isGrid = !this.isGrid
        this.currentLayout = this.isGrid ? 'ListGrid' : 'ListTable'
      },
      openFilterPopup() {
        this.isFilterPopupVisible = true
      },
      // 获取当前标签页的筛选条件
      getCurrentTabFilters() {
        switch (this.currentTab) {
          case 0:
            return this.recommendFilters
          case 1:
            return this.newUserFilters
          case 2:
            return this.followFilters
          default:
            return this.filters
        }
      },
      
      // 刷新当前标签页的数据
      async refreshCurrentTabData() {
        switch (this.currentTab) {
          case 0:
            await this.fetchRecommendData()
            break
          case 1:
            await this.fetchNewUserData()
            break
          case 2:
            await this.fetchFollowData()
            break
        }
      },
      
      applyFilters(filters) {
        // 更新当前标签页的筛选条件
        const currentFilters = this.getCurrentTabFilters()
        Object.assign(currentFilters, filters)
        
        // 重新获取当前标签页的数据
        this.refreshCurrentTabData()
        console.log('Applied filters:', filters)
      },
      checkLocationPermission() {
        checkLocationPermission()
          .then(result => {
            if (result.authorized) {
              console.log('定位权限已授权:', result)
              // 可以在这里处理已授权的情况，比如保存位置信息
              this.userLocation = result.data
              uni.getLocation({
                // gcj02 返回国测局坐标（App 和 H5 需配置定位 SDK 信息才可支持 gcj02。）
                type: 'gcj02', 
                // 是否解析地址信息（仅App平台支持（安卓需指定 type 为 gcj02 并配置三方定位SDK））
                geocode: true, 
                success: function(res) {
                  console.log('返回国测局坐标',res);
                }
              });
              // 调用更新位置接口
              this.updateUserLocation(result.data)
            } else {
              console.log('定位权限未授权:', result.error)
              // 显示定位权限弹窗
              this.isLocationPermissionVisible = true
            }
          })
          .catch(err => {
            console.log('检查定位权限失败:', err)
            // 显示定位权限弹窗
            this.isLocationPermissionVisible = true
          })
      },

      // 更新用户位置信息
      async updateUserLocation(locationData) {
        try {
          // 构造接口需要的参数格式
          const locationParams = {
            longitude: locationData.longitude,
            latitude: locationData.latitude
          }

          const result = await this.userStore.updateLocation(locationParams)
          if (result.success) {
            console.log('位置信息更新成功')
          } else {
            console.error('位置信息更新失败:', result.message)
          }
        } catch (error) {
          console.error('更新位置信息时发生错误:', error)
        }
      },
      onScroll(e) {
        // 清除之前的定时器
        if (this.scrollTimer) {
          clearTimeout(this.scrollTimer)
        }

        // 设置防抖定时器
        this.scrollTimer = setTimeout(() => {
          const { scrollTop, scrollHeight } = e.detail
          this.scrollTop = scrollTop

          // 获取scroll-view的高度信息
          const query = uni.createSelectorQuery().in(this)
          query
            .select('.tab-content')
            .boundingClientRect(data => {
              if (data) {
                const clientHeight = data.height
                // 计算距离底部的距离
                const distanceToBottom = scrollHeight - scrollTop - clientHeight

                // 如果距离底部小于等于100像素
                if (distanceToBottom <= 100) {
                  // 根据当前 tab 触发加载更多数据
                  if (this.currentTab === 3) {
                    // 动态 tab
                    if (this.momentsStatus === 'more' && !this.isLoadingMore) {
                      this.isLoadingMore = true
                      this.fetchMomentsList().finally(() => {
                        this.isLoadingMore = false
                      })
                    }
                  } else if (this.currentTab === 4) {
                    // 活动 tab
                    if (this.recruitStatus === 'more' && !this.isLoadingMore) {
                      this.isLoadingMore = true
                      this.fetchRecruitList().finally(() => {
                        this.isLoadingMore = false
                      })
                    }
                  }
                  // 推荐、新人、关注标签页不再需要分页加载，因为已经一次性加载全部数据
                }
              }
            })
            .exec()
        }, 100) // 100ms防抖
      }
    },
    watch: {
      isFilterPopupVisible(visible) {
        if (visible) {
          uni.hideTabBar({
            animation: true
          })
        } else {
          uni.showTabBar({
            animation: true
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  /* 去除页面默认边距和状态栏空间 */
  page {
    padding-top: 0 !important;
    margin-top: 0 !important;
    background-color: #fff;
  }

  /* 强制去除uni-page-head */
  .uni-page-head {
    display: none !important;
    height: 0 !important;
    overflow: hidden !important;
  }

  .head-images {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 16rpx 24rpx 0 24rpx;
    box-sizing: border-box;
    margin-bottom: 6px;
  }
  .head-img {
  }
  .left {
    height: 34px;
    width: 104px;
  }
  .right {
    height: 26px;
    width: 115px;
  }

  .container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding-top: 0 !important;
    margin-top: 0 !important;
    position: relative;
    top: 0;
    background:
      url('@/static/index/background.png') no-repeat top center,
      #fff;
    background-size: 100%;
    position: fixed;
  }

  .tab-header {
    display: flex;
    // background: linear-gradient( 90deg, #A3EAB6 0%, rgba(158,233,178,0) 100%);
    // border-bottom: 1rpx solid #eee;
    margin-top: 0 !important;
    padding-top: 40px !important;
    position: relative;
    z-index: 999;
    flex-wrap: wrap;
    transition: opacity 0.3s;
    .tab-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 10rpx 0;

      .tab-image {
        height: 80rpx;
      }
    }
  }

  .tab-content {
    flex: 1;
    height: 0; /* 确保flex子元素能正确计算高度 */
    overflow: hidden;
    background:
      url('@/static/index/bg.png') no-repeat top center,
      transparent;
    background-size: cover;
    background-position-y: -3px;
  }

  .sub-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 0 20rpx;

    .tab-scroll {
      flex: 1;
      white-space: nowrap;
      // overflow: auto;
      .tab-container {
        display: inline-flex;
        padding: 0 20rpx;

        .tab-item {
          padding: 20rpx 30rpx;
          font-size: 28rpx;
          color: #888;
          position: relative;

          &.active {
            color: #222;
            font-weight: bold;

            &::after {
              content: '';
              position: absolute;
              bottom: -25rpx;
              left: 50%;
              transform: translateX(-50%);
              width: 60rpx;
              height: 30rpx;
              background: url('@/static/index/bg_1.png') no-repeat center/cover;
              border-radius: 2rpx;
            }
          }
        }
      }
    }

    .layout-switch {
      padding: 20rpx;
      margin-left: 20rpx;

      .layout-image {
        width: 30rpx;
        height: 30rpx;
        display: block;
      }
    }

    .filter-switch {
      padding: 20rpx 10rpx 20rpx 0;
      .filter-image {
        width: 30rpx;
        height: 30rpx;
        display: block;
      }
    }
  }

  /* 加载状态指示器样式 */
  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    background-color: #f8f9fa;
    margin: 20rpx;
    border-radius: 12rpx;
  }

  .loading-spinner {
    border: 4rpx solid rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
    border-radius: 50%;
    width: 60rpx;
    height: 60rpx;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
  }

  /* 没有更多数据提示样式 */
  .no-more-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    // background-color: #f8f9fa;
    margin: 20rpx;
    border-radius: 12rpx;
  }

  .no-more-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60px;
  }
</style>
