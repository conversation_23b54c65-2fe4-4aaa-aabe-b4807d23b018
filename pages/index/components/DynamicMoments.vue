<template>
    <view class="moments-list">
      <view v-for="item in initializedList" :key="item.postCode || item.userId" class="moment-item">
        <view class="moment-header">
          <view class="avatar-container" @click="handleFindTalent(item)">
            <image :src="getFullUrl(item.profileImg) || '/static/index/level/head.png'" class="avatar"/>
            <image :src="'/static/index/level/l1.png'" class="level-icon"/>
          </view>
          <view class="user-info" @click="goToDetail(item)">
            <text class="nickname">{{ item.nickname || '匿名用户' }}</text>
            <view class="location-time">
              <text class="location">{{ formatTime(item.postDate) }}</text>
              <text class="divider">|</text>
              <text class="location">{{ item.lastLocationName }}</text>
              <text class="divider">|</text>
              <text class="distance">距你{{ formatDistance(item.distance) }}</text>
            </view>
          </view>
          <view class="find-ta-btn" @click.stop="handleFindTa(item)" v-if="showHiChatButton && !isOwnPost(item)">
            <text>Hi聊</text>
            <image class="btn-star" src="/static/index/expert/right.png" />
          </view>
        </view>
        <view class="moment-content" @click="goToDetail(item)">{{ item.postText || '' }}</view>
        <view v-if="item.images && item.images.length > 0" class="moment-images" :class="getImageLayoutClass(item.imgLayout)">
          <image 
            v-for="(img, imgIndex) in item.images" 
            :key="img.imageId || imgIndex" 
            :src="getFullUrl(img.thumbnailUrl || img.originalUrl || img.url)" 
            class="moment-image"
            mode="aspectFill"
            @click="previewImage(img, item.images)"
          />
        </view>
        <view v-if="item.audios && item.audios.length > 0" class="moment-audios">
          <view 
            v-for="(audio, audioIndex) in item.audios" 
            :key="audio.audioId || audio.audioId || audioIndex" 
            class="audio-item"
            @click="playAudio(audio)"
          >
            <text class="audio-icon">🎵</text>
            <text class="audio-duration">{{ formatDuration(audio.duration || audio.length) }}</text>
          </view>
        </view>
        <view class="moment-footer">
          <view class="action-buttons">
            <view class="action-item" v-if="item.forbidShare !== 1">
              <image src="/static/index/expert/fx.png" class="action-icon-img"/>
              <text class="action-text">分享</text>
            </view>
            <view class="action-item" @click="goToDetail(item)">
              <image src="/static/index/expert/pl.png" class="action-icon-img"/>
              <text class="action-text">评论</text>
            </view>
            <view class="action-item" @click="handleLike(item)">
              <image 
                :src="item.isLiked ? '/static/index/expert/zan-active.png' : '/static/index/expert/zan.png'" 
                class="action-icon-img"
                :class="{ 'liked': item.isLiked }"
              />
              <text class="action-text" :class="{ 'liked-text': item.isLiked }">{{ item.likeCount }}</text>
            </view>
          </view>
          <view class="more-dots">
            <text class="dot"></text>
            <text class="dot"></text>
            <text class="dot"></text>
          </view>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view v-if="status === 'loading'" class="loading-indicator">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view v-if="status === 'noMore'" class="no-more-indicator">
        <text class="no-more-text">{{ noMoreText }}</text>
      </view>
    </view>
  </template>
  
  <script>
  import { userApi, postApi } from '@/common/api.js'
  import { useUserStore } from '@/stores/user'
  
  export default {
    name: 'DynamicMoments',
    props: {
      list: {
        type: Array,
        default: () => []
      },
      status: {
        type: String,
        default: 'more'
      },
      showHiChatButton: {
        type: Boolean,
        default: true
      },
			noMoreText: {
				type: String,
				default: '已经到底了'
			}
    },
    data() {
      return {
        userStore: useUserStore()
      }
    },
    computed: {
      // 初始化后的列表数据
      // 处理点赞数据的初始化逻辑：
      // 1. likeCount: 点赞数，确保有默认值0
      // 2. isLiked: 当前用户是否已点赞，确保有默认值false
      // 3. 数据一致性检查：如果用户已点赞但点赞数为0，则修正为1
      initializedList() {
        return this.list.map(item => {
          // 使用 Vue.set 或直接返回对象，确保响应式更新
          const initializedItem = {
            ...item,
            likeCount: item.likeCount !== undefined && item.likeCount !== null ? item.likeCount : 0,
            isLiked: item.isLiked !== undefined && item.isLiked !== null ? item.isLiked : false
          }
          
          // 数据一致性检查：如果用户已点赞但点赞数为0，说明数据不一致
          // 这种情况下，我们假设用户已经点赞，所以点赞数至少应该是1
          if (initializedItem.isLiked && initializedItem.likeCount === 0) {
            initializedItem.likeCount = 1
          }
          
          return initializedItem
        })
      }
    },
    methods: {
      // 获取完整URL
      getFullUrl(url) {
        if (!url) return ''
        if (url.startsWith('http')) return url
        // 移除开头的斜杠，避免双斜杠
        const cleanUrl = url.startsWith('/') ? url.substring(1) : url
        return `http://47.123.3.183:9000/${cleanUrl}`
      },
      
      // 格式化时间显示
      formatTime(dateTime) {
        if (!dateTime) return '刚刚'
        
        try {
          const now = new Date()
          const postTime = new Date(dateTime)
          
          // 检查日期是否有效
          if (isNaN(postTime.getTime())) {
            return '刚刚'
          }
          
          const diff = now - postTime
          
          const minutes = Math.floor(diff / (1000 * 60))
          const hours = Math.floor(diff / (1000 * 60 * 60))
          const days = Math.floor(diff / (1000 * 60 * 60 * 24))
          
          if (minutes < 1) return '刚刚'
          if (minutes < 60) return `${minutes}分钟前`
          if (hours < 24) return `${hours}小时前`
          if (days < 7) return `${days}天前`
          
          return postTime.toLocaleDateString('zh-CN')
        } catch (error) {
          console.error('时间格式化错误:', error)
          return '刚刚'
        }
      },
      
      // 格式化距离显示
      formatDistance(distance) {
        if (!distance && distance !== 0) return '未知距离'
        
        // 距离为0时显示"附近"
        if (distance === 0) return '附近'
        
        if (distance < 1000) {
          return `${Math.round(distance)}m`
        } else {
          return `${(distance / 1000).toFixed(1)}km`
        }
      },
      
      // 根据图片布局获取样式类
      getImageLayoutClass(layout) {
        const layoutMap = {
          1: 'layout-single',
          2: 'layout-double',
          3: 'layout-triple',
          4: 'layout-four',
          5: 'layout-nine'
        }
        return layoutMap[layout] || 'layout-default'
      },
      
      // 格式化音频时长
      formatDuration(duration) {
        if (!duration) return '0:00'
        
        const minutes = Math.floor(duration / 60)
        const seconds = duration % 60
        return `${minutes}:${seconds.toString().padStart(2, '0')}`
      },
      
      // 预览图片
      previewImage(currentImg, allImages) {
        const urls = allImages.map(img => this.getFullUrl(img.originalUrl || img.thumbnailUrl || img.url))
        const current = this.getFullUrl(currentImg.originalUrl || currentImg.thumbnailUrl || currentImg.url)
        
        uni.previewImage({
          urls: urls,
          current: current
        })
      },
      
      // 播放音频
      playAudio(audio) {
        const audioUrl = audio.originalUrl || audio.url || audio.audioUrl
        if (!audioUrl) {
          uni.showToast({
            title: '音频文件不存在',
            icon: 'none'
          })
          return
        }
        
        // 创建音频实例
        const audioContext = uni.createInnerAudioContext()
        audioContext.src = this.getFullUrl(audioUrl)
        audioContext.play()
        
        audioContext.onError((res) => {
          console.error('音频播放失败:', res)
          uni.showToast({
            title: '音频播放失败',
            icon: 'none'
          })
        })
      },
      
      // 处理点赞功能
      // 逻辑说明：
      // 1. 根据 isLiked 状态判断当前操作是点赞还是取消点赞
      // 2. 点赞时：isLiked = false -> true，likeCount + 1
      // 3. 取消点赞时：isLiked = true -> false，likeCount - 1
      async handleLike(item) {
        if (!item.postCode) {
          uni.showToast({
            title: '动态信息不完整',
            icon: 'none'
          })
          return
        }
        
        try {
          // 获取当前点赞状态
          const currentLiked = item.isLiked
          
          // 调用新的动态点赞接口
          const operationType = currentLiked ? 2 : 1 // 1:点赞, 2:取消点赞
          const result = await postApi.likePostNew(item.postCode, operationType)
          
          if (result.code === 200) {
            // 立即更新UI状态，提供即时反馈
            item.isLiked = !currentLiked
            
            // 更新点赞数量
            if (currentLiked) {
              // 取消点赞：点赞数减1
              item.likeCount = Math.max(0, item.likeCount - 1)
            } else {
              // 点赞：点赞数加1
              item.likeCount = item.likeCount + 1
            }
            
            // 强制更新视图
            this.$forceUpdate()
            
            // 调试信息
            console.log('点赞状态更新:', {
              postCode: item.postCode,
              isLiked: item.isLiked,
              likeCount: item.likeCount,
              operation: currentLiked ? '取消点赞' : '点赞'
            })
            
            // 显示成功提示
            uni.showToast({
              title: currentLiked ? '取消点赞' : '点赞成功',
              icon: 'success',
              duration: 1500
            })
          } else {
            uni.showToast({
              title: result.msg || '操作失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('点赞操作失败:', error)
          uni.showToast({
            title: '操作失败，请重试',
            icon: 'none'
          })
        }
      },
      
      // 跳转到动态详情页面
      goToDetail(item) {
        if (!item.postCode) {
          uni.showToast({
            title: '动态信息不完整',
            icon: 'none'
          })
          return
        }
        
        // 跳转到动态详情页面，传递动态ID
        uni.navigateTo({
          url: `/pages/moment/detail?postCode=${item.postCode}&userId=${item.userId || ''}`
        })
      },
      
          // 判断是否是自己的动态
    isOwnPost(item) {
      const currentUserId = this.userStore.userInfo?.userId
      return currentUserId && item.userId && currentUserId.toString() === item.userId.toString()
    },
    
    // 处理找TA功能
    handleFindTa(item) {
      // 获取当前用户ID
      const currentUserId = this.userStore.userInfo?.userId
      
      // 检查是否是自己的动态
      if (this.isOwnPost(item)) {
        uni.showToast({
          title: '不能和自己聊天哦',
          icon: 'none',
          duration: 2000
        })
        return
      }
      
      // 检查用户是否已登录
      if (!currentUserId) {
        uni.showToast({
          title: '请先登录',
          icon: 'none',
          duration: 2000
        })
        return
      }
      
      // 直接跳转到聊天页面
      uni.navigateTo({
        url: `/pages/chat/index?channelID=${item.userId}&channelType=1&nickName=${item.nickname || '用户'}`
      })
    },
    // 去达人详情
    handleFindTalent(item) {
      // 获取当前用户ID
      const currentUserId = this.userStore.userInfo?.userId
      
      // 检查是否是自己的动态
      if (this.isOwnPost(item)) {
        uni.navigateTo({
          url: `/pages/expertDetail/expert-detail?id=${item.userId}`
        })
        return
      }else{
        uni.navigateTo({
          url: `/pages/expertDetail/expert-detail?id=${item.userId}&fromIndex=1`
        })
      }
    }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .moments-list {
    padding: 20rpx;
  }
  
  .moment-item {
    margin-bottom: 24rpx;
    padding: 24rpx;
    border-bottom: 1px solid #f1eded;
  }
  
  .moment-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    position: relative;
  }
  
  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin-right: 16rpx;
  }
  
  .user-info {
    flex: 1;
  }
  
  .find-ta-btn {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8rpx 16rpx;
    transition: all 0.3s ease;
    width: 52px;
    height: 23px;
    background: linear-gradient( 270deg, #F8B401 0%, #FE9836 100%);
    box-shadow: inset 0px -1px 2px 0px rgba(255,255,255,0.5);
    border-radius: 18px;
    justify-content: space-evenly;
    &:active {
      transform: scale(0.95);
      box-shadow: 0 2rpx 8rpx rgba(255, 167, 38, 0.2);
    }
    
    .btn-star {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }
    
    text {
      font-size: 24rpx;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 1;
    }
  }
  
  .nickname {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 4rpx;
  }
  
  .location-time {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }
  
  .location {
    font-size: 24rpx;
    color: #999;
  }
  
  .distance {
    font-size: 24rpx;
    color: #999;
  }
  
  .divider {
    font-size: 24rpx;
    color: #ccc;
  }
  
  .moment-content {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
    margin-bottom: 16rpx;
    padding-left: 46px;
  }
  
  .moment-images {
    display: flex;
    gap: 12rpx;
    margin-bottom: 16rpx;
    flex-wrap: wrap;
    padding-left: 46px;
    
    &.layout-single {
      .moment-image {
        width: 100px;
        height: 100px;
        max-width: 100%;
      }
    }
    
    &.layout-double {
      .moment-image {
        width: 100px;
        height: 100px;
      }
    }
    
    &.layout-triple {
      .moment-image {
        width: 100px;
        height: 100px;
      }
    }
    
    &.layout-four {
      .moment-image {
        width: 100px;
        height: 100px;
      }
    }
    
    &.layout-nine {
      .moment-image {
        width: 100px;
        height: 100px;
      }
    }
  }
  
  .moment-image {
    width: 100px;
    height: 100px;
    border-radius: 8px;
  }
  
  .moment-audios {
    margin-bottom: 16rpx;
  }
  
  .audio-item {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 16rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
    margin-bottom: 8rpx;
  }
  
  .audio-icon {
    font-size: 24rpx;
  }
  
  .audio-duration {
    font-size: 24rpx;
    color: #666;
  }
  
  .moment-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16rpx;
    padding-left: 45px;
    padding-right: 15px;
    position: relative;
  }
  
  .action-buttons {
    display: flex;
    flex: 1;
    justify-content: flex-start;
    gap: 40rpx;
  }
  
  .action-item {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 8rpx 0;
    justify-content: center;
    cursor: pointer;
    transition: opacity 0.2s ease;
    
    &:active {
      opacity: 0.7;
    }
  }
  
  .action-icon {
    font-size: 28rpx;
    color: #999;
    transition: color 0.3s ease;
    
    &.liked {
      color: #ff6b6b;
    }
  }
  
  .action-icon-img {
    width: 32rpx;
    height: 32rpx;
    transition: all 0.3s ease;
    
    &.liked {
      transform: scale(1.1);
    }
  }
  
  .action-text {
    font-size: 28rpx;
    color: #666;
    transition: color 0.3s ease;
    
    &.liked-text {
      color: #ff6b6b;
      font-weight: 500;
    }
  }
  
  .more-dots {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8rpx 12rpx;
    gap: 4rpx;
    min-width: 40rpx;
  }
  
  .dot {
    width: 6rpx;
    height: 6rpx;
    background-color: #999;
    border-radius: 50%;
    display: block;
  }
  
  /* 加载状态指示器样式 */
  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    background-color: #f8f9fa;
    margin: 20rpx;
    border-radius: 12rpx;
  }
  
  .loading-spinner {
    border: 4rpx solid rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
    border-radius: 50%;
    width: 60rpx;
    height: 60rpx;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  .loading-text {
    font-size: 28rpx;
    color: #666;
  }
  
  /* 没有更多数据提示样式 */
  .no-more-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    margin: 20rpx;
    border-radius: 12rpx;
  }
  
  .no-more-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60rpx;
  }
  .level-icon{
    width: 20px;
    height: 20px;
    position: absolute;
    bottom: 2px;
    left: 26px;
  }
  .avatar-container{
    position: relative;
  }
  </style>