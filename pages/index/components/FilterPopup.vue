<template>
	<uni-popup ref="popup" type="bottom" :safe-area="false" @change="onPopupChange" style="z-index: 999;">
		<view class="filter-popup-container">
			<view class="header">
				<text class="title">筛选</text>
				<uni-icons type="close" size="24" class="close-icon" @click="close"></uni-icons>
			</view>
			
			<scroll-view scroll-y class="content">
				<!-- 性别 -->
				<view class="filter-section">
					<view class="title">性别</view>
					<view class="options">
						<button 
							v-for="(item, index) in genderOptions" 
							:key="index"
							:class="['option-btn', filters.gender === item.value ? 'active' : '']"
							@click="selectFilter('gender', item.value)"
						>
							{{ item.text }}
						</button>
					</view>
				</view>

				<!-- 年龄 -->
				<view class="filter-section">
					<view class="title-with-value">
						<text>年龄范围</text>
						<view class="age-header">
							<text>{{ getAgeDisplayText() }}</text>
							<button 
								v-if="(filters.minAge && filters.minAge !== 18) || (filters.maxAge && filters.maxAge !== 60)"
								class="reset-age-btn" 
								@click="resetAgeFilter"
							>
								重置
							</button>
						</view>
					</view>
					<view class="age-slider-container">
						<view class="dual-slider-wrapper">
							<view class="slider-track" ref="sliderTrack" @tap="onTrackTap">
								<view 
									class="slider-fill" 
									:style="{ 
										left: getSliderFillLeft() + '%', 
										width: getSliderFillWidth() + '%' 
									}"
								></view>
								<view 
									class="slider-thumb min-thumb"
									:style="{ left: getMinThumbPosition() + '%' }"
									@touchstart="onMinThumbTouchStart"
									@touchmove="onMinThumbTouchMove"
									@touchend="onThumbTouchEnd"
								></view>
								<view 
									class="slider-thumb max-thumb"
									:style="{ left: getMaxThumbPosition() + '%' }"
									@touchstart="onMaxThumbTouchStart"
									@touchmove="onMaxThumbTouchMove"
									@touchend="onThumbTouchEnd"
								></view>
							</view>
						</view>
						<view class="age-labels">
							<text class="age-label">18</text>
							<text class="age-label">60</text>
						</view>
					</view>
				</view>

				<!-- 距离 -->
				<view class="filter-section">
					<view class="title-with-value">
						<text>距离</text>
						<text>{{ getDistanceDisplayText() }}</text>
					</view>
					<slider 
						:value="filters.distance" 
						@changing="onSliderChange('distance', $event)" 
						min="0" 
						max="50" 
						block-size="20"
						active-color="#69C689"
					/>
				</view>

				<!-- 在线状态 -->
				<view class="filter-section">
					<view class="title">在线状态</view>
					<view class="options">
						<button 
							v-for="(item, index) in onlineStatusOptions" 
							:key="index"
							:class="['option-btn', filters.onlineStatus === item.value ? 'active' : '']"
							@click="selectFilter('onlineStatus', item.value)"
						>
							{{ item.text }}
						</button>
					</view>
				</view>

				<!-- 标签分类 -->
				<view class="filter-section">
					<view class="title">标签分类</view>
					<view v-if="filters.tag && filters.tag !== null" class="selected-tags">
						<text class="selected-tags-label">已选：</text>
						<view class="selected-tags-list">
							<view 
								v-for="(tag, index) in getSelectedTags()" 
								:key="index"
								class="selected-tag"
							>
								{{ tag }}
							</view>
						</view>
					</view>
					<view class="options-grid">
						<button 
							v-for="(item, index) in tagOptions" 
							:key="index"
							:class="['option-btn', isTagSelected(item.text) ? 'active' : '']"
							@click="toggleTag(item.text)"
						>
							{{ item.text }}
						</button>
					</view>
				</view>
			</scroll-view>

			<view class="footer">
				<button class="reset-btn" @click="resetFilters">重置</button>
				<button class="confirm-btn" @click="confirmFilters">确定</button>
			</view>
		</view>
	</uni-popup>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
	name: 'FilterPopup',
	emits: ['update:visible', 'confirm'],
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	setup(props, { emit }) {
		const popup = ref(null)

		const initialFilters = {
			gender: 'all',
			minAge: 18,
			maxAge: 60,
			distance: 50,
			onlineStatus: 'any',
			tag: null
		}

		const filters = reactive({ ...initialFilters })

		const genderOptions = [
			{ text: '全部', value: 'all' },
			{ text: '♂ 男', value: 'male' },
			{ text: '♀ 女', value: 'female' }
		]

		const onlineStatusOptions = [
			{ text: '不限', value: 'any' },
			{ text: '在线', value: 'online' },
			{ text: '离线', value: 'offline' }
		]

		const tagOptions = [
			{ text: '不限', value: null },
			{ text: '陪诊服务' },
			{ text: '求职咨询' },
			{ text: '法律咨询' },
			{ text: '心理咨询' },
			{ text: '向导服务' }
		]

		const onPopupChange = (e) => {
			if (!e.show) {
				emit('update:visible', false)
			}
		}

		const open = () => {
			popup.value.open()
		}

		const close = () => {
			popup.value.close()
		}

		const selectFilter = (key, value) => {
			filters[key] = value
		}

		const onSliderChange = (key, event) => {
			filters[key] = event.detail.value
		}

		// 双滑块相关状态
		const activeThumb = ref(null)
		const sliderTrack = ref(null)

		const onMinThumbTouchStart = (event) => {
			activeThumb.value = 'min'
		}

		const onMaxThumbTouchStart = (event) => {
			activeThumb.value = 'max'
		}

		const onMinThumbTouchMove = (event) => {
			if (activeThumb.value === 'min') {
				const touch = event.touches[0]
				// 使用 uni-app 兼容的方式获取元素位置
				const query = uni.createSelectorQuery()
				query.select('.slider-track').boundingClientRect((rect) => {
					if (rect) {
						const percentage = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100))
						const age = Math.round(18 + (percentage / 100) * 42) // 18-60岁范围
						
						// 确保最小年龄不大于最大年龄
						if (filters.maxAge && age > filters.maxAge) {
							filters.minAge = filters.maxAge
						} else {
							filters.minAge = age
						}
					}
				}).exec()
			}
		}

		const onMaxThumbTouchMove = (event) => {
			if (activeThumb.value === 'max') {
				const touch = event.touches[0]
				// 使用 uni-app 兼容的方式获取元素位置
				const query = uni.createSelectorQuery()
				query.select('.slider-track').boundingClientRect((rect) => {
					if (rect) {
						const percentage = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100))
						const age = Math.round(18 + (percentage / 100) * 42) // 18-60岁范围
						
						// 确保最大年龄不小于最小年龄
						if (filters.minAge && age < filters.minAge) {
							filters.maxAge = filters.minAge
						} else {
							filters.maxAge = age
						}
					}
				}).exec()
			}
		}

		const onThumbTouchEnd = () => {
			activeThumb.value = null
		}

		const getMinThumbPosition = () => {
			// 考虑轨道边距，调整位置计算
			const percentage = ((filters.minAge - 18) / 42) * 100
			return Math.max(0, Math.min(100, percentage))
		}

		const getMaxThumbPosition = () => {
			// 考虑轨道边距，调整位置计算
			const percentage = ((filters.maxAge - 18) / 42) * 100
			return Math.max(0, Math.min(100, percentage))
		}

		const getSliderFillLeft = () => {
			return getMinThumbPosition()
		}

		const getSliderFillWidth = () => {
			const minPos = getMinThumbPosition()
			const maxPos = getMaxThumbPosition()
			return maxPos - minPos
		}

		const toggleAgeFilter = (key) => {
			if (filters[key]) {
				// 如果已经设置了值，则取消设置
				filters[key] = null
			} else {
				// 如果没有设置值，则设置默认值
				filters[key] = key === 'minAge' ? 18 : 60
			}
		}
		
		const resetFilters = () => {
			Object.assign(filters, initialFilters)
		}

		const confirmFilters = () => {
			// 创建要传递的筛选参数副本
			const filtersToSend = { ...filters }
			
			// 如果年龄是默认值（18-60岁），则不传递年龄参数，表示用户没有选择年龄限制
			if (filtersToSend.minAge === 18 && filtersToSend.maxAge === 60) {
				delete filtersToSend.minAge
				delete filtersToSend.maxAge
			}
			
			// 如果距离是默认值（50km），则不传递距离参数，表示用户没有选择距离限制
			if (filtersToSend.distance === 50) {
				delete filtersToSend.distance
			}
			
			emit('confirm', filtersToSend)
			close()
		}
		
		const getAgeDisplayText = () => {
			if (filters.minAge && filters.maxAge) {
				if (filters.minAge === 18 && filters.maxAge === 60) {
					return '不限'
				} else if (filters.minAge === filters.maxAge) {
					return `${filters.minAge}岁`
				} else {
					return `${filters.minAge}-${filters.maxAge}岁`
				}
			} else if (filters.minAge) {
				return `≥${filters.minAge}岁`
			} else if (filters.maxAge) {
				return `≤${filters.maxAge}岁`
			} else {
				return '不限'
			}
		}
		
		const resetAgeFilter = () => {
			filters.minAge = 18
			filters.maxAge = 60
		}
		
		const onTrackTap = (event) => {
			const query = uni.createSelectorQuery()
			query.select('.slider-track').boundingClientRect((rect) => {
				if (rect) {
					const touch = event.touches ? event.touches[0] : event
					const percentage = Math.max(0, Math.min(100, ((touch.clientX - rect.left) / rect.width) * 100))
					const age = Math.round(18 + (percentage / 100) * 42) // 18-60岁范围
					
					// 判断点击位置更接近哪个滑块
					const minDistance = Math.abs(percentage - getMinThumbPosition())
					const maxDistance = Math.abs(percentage - getMaxThumbPosition())
					
					if (minDistance < maxDistance) {
						// 更接近最小年龄滑块
						if (age > filters.maxAge) {
							filters.minAge = filters.maxAge
						} else {
							filters.minAge = age
						}
					} else {
						// 更接近最大年龄滑块
						if (age < filters.minAge) {
							filters.maxAge = filters.minAge
						} else {
							filters.maxAge = age
						}
					}
				}
			}).exec()
		}
		
		const isTagSelected = (tag) => {
			// 处理"不限"选项
			if (tag === '不限') {
				return filters.tag === null
			}
			
			// 如果当前是null，其他标签都不选中
			if (filters.tag === null) {
				return false
			}
			
			// 如果当前是空字符串，其他标签都不选中
			if (!filters.tag) {
				return false
			}
			
			// 检查标签是否在已选列表中
			const selectedTags = filters.tag.split(',').map(t => t.trim())
			return selectedTags.includes(tag)
		}
		
		const toggleTag = (tag) => {
			// 如果选择的是"不限"，直接设置为null
			if (tag === '不限') {
				filters.tag = null
				return
			}
			
			// 如果当前是null，直接设置为选中的标签
			if (filters.tag === null) {
				filters.tag = tag
				return
			}
			
			// 如果当前是空字符串，直接设置为选中的标签
			if (!filters.tag) {
				filters.tag = tag
				return
			}
			
			// 处理多选逻辑
			const selectedTags = filters.tag.split(',').map(t => t.trim())
			const index = selectedTags.indexOf(tag)
			
			if (index > -1) {
				// 移除标签
				selectedTags.splice(index, 1)
				filters.tag = selectedTags.length > 0 ? selectedTags.join(',') : ''
			} else {
				// 添加标签
				selectedTags.push(tag)
				filters.tag = selectedTags.join(',')
			}
		}
		
		const getSelectedTags = () => {
			if (filters.tag === null) return []
			if (!filters.tag) return []
			return filters.tag.split(',').map(t => t.trim())
		}
		
		const getDistanceDisplayText = () => {
			if (filters.distance === 50) {
				return '不限'
			} else {
				return `${filters.distance}km`
			}
		}
		
		return {
			popup,
			filters,
			genderOptions,
			onlineStatusOptions,
			tagOptions,
			onPopupChange,
			open,
			close,
			selectFilter,
			onSliderChange,
			resetFilters,
			confirmFilters,
			toggleAgeFilter,
			getAgeDisplayText,
			resetAgeFilter,
			activeThumb,
			sliderTrack,
			onMinThumbTouchStart,
			onMaxThumbTouchStart,
			onMinThumbTouchMove,
			onMaxThumbTouchMove,
			onThumbTouchEnd,
			getMinThumbPosition,
			getMaxThumbPosition,
			getSliderFillLeft,
			getSliderFillWidth,
			onTrackTap,
			isTagSelected,
			toggleTag,
			getSelectedTags,
			getDistanceDisplayText
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.open()
			} else {
				this.close()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
/* 提高筛选弹框的层级 */
:deep(.uni-popup) {
	z-index: 9999 !important;
}

.filter-popup-container {
	background-color: #fff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
	padding: 20rpx 30rpx;
	display: flex;
	flex-direction: column;
	max-height: 80vh;

	.header {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;

		.title {
			font-size: 32rpx;
			font-weight: bold;
		}

		.close-icon {
			position: absolute;
			right: 0;
			top: 50%;
			transform: translateY(-50%);
			color: #999;
		}
	}

	.content {
		flex: 1;
		overflow-y: auto;
		padding: 20rpx 0;
	}

	.filter-section {
		margin-bottom: 40rpx;

		.title {
			font-size: 28rpx;
			color: #333;
			margin-bottom: 20rpx;
			font-weight: 500;
		}
		
		.title-with-value {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 28rpx;
			color: #333;
			margin-bottom: 20rpx;
			font-weight: 500;

			.age-header {
				display: flex;
				align-items: center;
				min-width: 120rpx;
				justify-content: flex-end;

				.reset-age-btn {
					background-color: #f5f5f5;
					border: 1rpx solid #ddd;
					color: #666;
					border-radius: 15rpx;
					padding: 4rpx 12rpx;
					font-size: 22rpx;
					margin: 0;
					margin-left: 15rpx;
					z-index: 20;
					position: relative;

					&::after {
						border: none;
					}
				}
			}
		}

		.options {
			display: flex;
			
			.option-btn {
				margin-right: 20rpx;
				
				&:last-child {
					margin-right: 0;
				}
			}
		}
		
		.options-grid {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			
			.option-btn {
				margin-right: 20rpx;
				margin-bottom: 20rpx;
				
				&:nth-child(3n) {
					margin-right: 0;
				}
			}
		}

		.selected-tags {
			margin-bottom: 20rpx;
			
			.selected-tags-label {
				font-size: 26rpx;
				color: #666;
				margin-bottom: 10rpx;
				display: block;
			}
			
			.selected-tags-list {
				display: flex;
				flex-wrap: wrap;
				
				.selected-tag {
					background-color: #e8f8ee;
					border: 1rpx solid #69C689;
					color: #69C689;
					border-radius: 20rpx;
					padding: 6rpx 12rpx;
					font-size: 24rpx;
					margin-right: 10rpx;
					margin-bottom: 10rpx;
				}
			}
		}

		.age-slider-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;

			.dual-slider-wrapper {
				width: 100%;
				position: relative;
				margin-bottom: 10rpx;
			}

			.slider-track {
				position: relative;
				height: 20rpx;
				background-color: #f5f5f5;
				border-radius: 10rpx;
				margin: 0 14rpx;
			}

			.slider-fill {
				position: absolute;
				top: 0;
				left: 0;
				height: 100%;
				background-color: #69C689;
				border-radius: 10rpx;
			}

			.slider-thumb {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				width: 28rpx;
				height: 28rpx;
				background-color: #fff;
				border: 3rpx solid #69C689;
				border-radius: 50%;
				cursor: pointer;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
				z-index: 10;
			}

			.min-thumb {
				left: 0%;
				border-color: #ff6b6b;
			}

			.max-thumb {
				left: 100%;
				border-color: #4ecdc4;
				transform: translateY(-50%) translateX(-100%);
			}

			.age-labels {
				display: flex;
				justify-content: space-between;
				width: 100%;
				padding: 0 30rpx;
				box-sizing: border-box;
				margin-top: 10rpx;

				.age-label {
					font-size: 24rpx;
					color: #666;
				}
			}
		}

		.option-btn {
			background-color: #f5f5f5;
			border: 1rpx solid #f5f5f5;
			color: #333;
			border-radius: 30rpx;
			padding: 12rpx 0;
			font-size: 26rpx;
			margin: 0;
			flex: 1;

			&::after {
				border: none;
			}
			
			&.active {
				background-color: #e8f8ee;
				border-color: #69C689;
				color: #69C689;
			}
		}
	}

	.footer {
		display: flex;
		padding-top: 20rpx;
		border-top: 1rpx solid #f5f5f5;
		
		.reset-btn, .confirm-btn {
			flex: 1;
			border-radius: 40rpx;
			font-size: 30rpx;
			padding: 18rpx 0;
			margin: 0;
			&::after {
				border: none;
			}
		}

		.reset-btn {
			background-color: #fff;
			border: 1rpx solid #ccc;
			color: #333;
			margin-right: 10rpx;
		}

		.confirm-btn {
			background-color: #69C689;
			color: #fff;
			margin-left: 10rpx;
		}
	}
}
</style> 