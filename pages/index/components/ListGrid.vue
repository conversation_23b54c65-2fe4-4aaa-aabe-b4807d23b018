<template>
  <view class="waterfall-container">
    <view class="waterfall-column">
      <view
        class="waterfall-item"
        v-for="(item, index) in leftColumn"
        :key="item.userId"
        :style="{ marginTop: index === 0 ? '0' : '16rpx' }"
        @click="goToDetail(item)"
      >
        <image 
          class="item-image" 
          :src="getImageUrl(item.img)" 
          mode="aspectFill"
          :style="{ height: getRandomHeight(item.userId) + 'rpx' }"
        ></image>
        <view class="find-ta-btn" @click.stop="handleFindTa(item)">
          <image class="btn-star" src="/static/index/icon/<EMAIL>" />
          <text>找TA</text>
        </view>
        <view class="item-header" :style="{ top: (getRandomHeight(item.userId) - 50) + 'rpx' }">
          <view class="online-status" :class="{ offline: !item.isOnline }">{{ item.isOnline ? '在线' : '离线' }}</view>
          <view v-if="item.dis" class="distance">
            <image class="distance-icon" src="/static/index/icon/<EMAIL>" />
            {{ (item.dis / 1000).toFixed(2) }}km
          </view>
        </view>
        <view class="item-info">
          <view class="info-line-1">
            <text class="nickname">{{ item.nickName || '未知用户' }}</text>
            <image class="s-mark" :src="'/static/index/level/vmark.png'" />
            <view class="user-level">
              <image class="level-icon" :src="getUserLevelIcon(getOriginalIndex(item))" />
              <text class="level-text">LV{{ index+1 }}</text>
            </view>
          </view>
          <view class="info-line-2">
            <text class="brief">{{ item.brief || '这个人很懒，什么都没写...' }}</text>
          </view>
          <view class="info-line-3">
            <view v-if="item.sex || item.age" class="gender-age-tag" :class="item.sex === 2 ? 'female-bg' : 'male-bg'">
              <image v-if="item.sex" class="gender-icon-img" :src="item.sex === 2 ? '/static/index/icon/female.png' : '/static/index/icon/male.png'" />
              <text v-if="item.age" class="age-text">{{ item.age }}</text>
            </view>
            <view v-if="item.constellation" class="constellation-tag">
              <image class="constellation-icon" :src="getConstellationIcon(item.constellation)" />
            </view>
            <text class="tag" v-for="tag in getTags(item)" :key="tag">{{ tag }}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="waterfall-column">
      <view
        class="waterfall-item"
        v-for="(item, index) in rightColumn"
        :key="item.userId"
        :style="{ marginTop: index === 0 ? '0' : '16rpx' }"
        @click="goToDetail(item)"
      >
        <image 
          class="item-image" 
          :src="getImageUrl(item.img)" 
          mode="aspectFill"
          :style="{ height: getRandomHeight(item.userId) + 'rpx' }"
        ></image>
        <view class="find-ta-btn" @click.stop="handleFindTa(item)">
          <image class="btn-star" src="/static/index/icon/<EMAIL>" />
          <text>找TA</text>
        </view>
        <view class="item-header" :style="{ top: (getRandomHeight(item.userId) - 50) + 'rpx' }">
          <view class="online-status" :class="{ offline: !item.isOnline }">{{ item.isOnline ? '在线' : '离线' }}</view>
          <view v-if="item.dis" class="distance">
            <image class="distance-icon" src="/static/index/icon/<EMAIL>" />
            {{ (item.dis / 1000).toFixed(2) }}km
          </view>
        </view>
        <view class="item-info">
          <view class="info-line-1">
            <text class="nickname">{{ item.nickName || '未知用户' }}</text>
            <image class="s-mark" :src="'/static/index/level/vmark.png'" />
            <view class="user-level">
              <image class="level-icon" :src="getUserLevelIcon(getOriginalIndex(item))" />
              <text class="level-text">LV{{ index+1 }}</text>
            </view>
          </view>
          <view class="info-line-2">
            <text class="brief">{{ item.brief || '这个人很懒，什么都没写...' }}</text>
          </view>
          <view class="info-line-3">
            <view v-if="item.sex || item.age" class="gender-age-tag" :class="item.sex === 2 ? 'female-bg' : 'male-bg'">
              <image v-if="item.sex" class="gender-icon-img" :src="item.sex === 2 ? '/static/index/icon/female.png' : '/static/index/icon/male.png'" />
              <text v-if="item.age" class="age-text">{{ item.age }}</text>
            </view>
            <view v-if="item.constellation" class="constellation-tag">
              <image class="constellation-icon" :src="getConstellationIcon(item.constellation)" />
            </view>
            <text class="tag" v-for="tag in getTags(item)" :key="tag">{{ tag }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'ListGrid',
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        // 存储每个item的高度，避免重复计算
        heightCache: {}
      }
    },
    computed: {
      leftColumn() {
        return this.list.filter((_, index) => index % 2 === 0)
      },
      rightColumn() {
        return this.list.filter((_, index) => index % 2 !== 0)
      }
    },
    methods: {
      // 获取随机高度，基于userId确保每次渲染都是相同的高度
      getRandomHeight(userId) {
        if (!this.heightCache[userId]) {
          // 使用userId作为种子生成伪随机数，确保每次都是相同的高度
          const seed = userId.toString().split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
          const random = Math.sin(seed) * 10000;
          const normalizedRandom = random - Math.floor(random);
          // 高度范围在450-530rpx之间，再次调高图片高度
          this.heightCache[userId] = Math.floor(450 + normalizedRandom * 80);
        }
        return this.heightCache[userId];
      },
      
      // 处理图片URL
      getImageUrl(img) {
        if (!img) {
          return '/static/index/default.png'
        } else {
          return `http://47.123.3.183:9000/${img}`
        }
      },
      // 处理标签数据
      getTags(item) {
        // 从接口数据中提取标签信息
        // 这里可以根据实际需求从不同字段组合标签
        const tags = []

        // 如果有星座信息，可以作为标签
        if (item.constellation) {
          tags.push(item.constellation)
        }

        // 如果有简介信息，可以提取关键词作为标签
        if (item.brief) {
          // 简单的关键词提取逻辑
          const keywords = ['运动', '健身', '音乐', '电影', '旅行', '美食', '摄影', '游戏']
          const matchedKeywords = keywords.filter(keyword => item.brief.includes(keyword))
          tags.push(...matchedKeywords.slice(0, 2))
        }

        // 如果没有标签，提供默认标签
        // if (tags.length === 0) {
        //   tags.push('新人', '活跃')
        // }

        return tags.slice(0, 2) // 最多显示2个标签
      },
      handleFindTa(item) {
        // 跳转到达人详情页面，带上showSkills标识
        uni.navigateTo({
          url: `/pages/expertDetail/expert-detail?id=${item.userId}&fromIndex=1&showSkills=1`
        })
      },
      getConstellationIcon(constellation) {
        const map = {
          '白羊座': 'aries',
          '金牛座': 'taurus',
          '双子座': 'gemini',
          '巨蟹座': 'cancer',
          '狮子座': 'leo',
          '处女座': 'virgo',
          '天秤座': 'libra',
          '天蝎座': 'scorpio',
          '射手座': 'sagittarius',
          '摩羯座': 'capricornus',
          '水瓶座': 'aquarius',
          '双鱼座': 'pisces'
        }
        return map[constellation] ? `/static/index/constellation/${map[constellation]}@2x.png` : ''
      },
      goToDetail(item) {
        uni.navigateTo({
          url: `/pages/expertDetail/expert-detail?id=${item.userId}&fromIndex=1`
        })
      },
      getUserLevel(index) {
        // 根据index计算用户等级，返回1-5的等级
        return (index % 5) + 1
      },
      getUserLevelIcon(index) {
        // 根据等级获取等级图标
        const level = this.getUserLevel(index)
        return `/static/index/level/lv${level}.png`
      },
      // 获取原始数据中的index
      getOriginalIndex(item) {
        return this.list.findIndex(listItem => listItem.userId === item.userId)
      }
    },
    setup() {
      return {}
    }
  }
</script>

<style lang="scss">
  .waterfall-container {
    padding: 16rpx;
    display: flex;
    // background-color: #f5f5f5;

    .waterfall-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      &:first-child {
        margin-right: 8rpx;
      }
      
      &:last-child {
        margin-left: 8rpx;
      }

      .waterfall-item {
        position: relative;
        // background: #fff;
        border-radius: 20rpx;
        // border: 1rpx solid #eee;
        // box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.10);
        overflow: hidden;
        transition: all 0.2s ease;
        margin-bottom: 16rpx;

        &:active {
          transform: scale(0.98);
          box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.12);
        }

        .item-image {
          width: 100%;
          // height: 360rpx; // 移除固定高度
          border-radius: 20rpx;
          object-fit: cover;
          background-color: #f0f0f0;
        }

        .find-ta-btn {
          position: absolute;
          top: 16rpx;
          right: 16rpx;
          width: 134rpx;
          height: 56rpx;
          background: url('/static/index/icon/<EMAIL>') no-repeat center center;
          background-size: cover;
          border-radius: 24rpx;
          font-size: 26rpx;
          color: white;
          font-weight: 700;
          box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
          z-index: 10;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0;
        }

        .btn-star {
          width: 22rpx;
          height: 20rpx;
          margin-right: 8rpx;
          display: inline-block;
        }

        .item-header {
          position: absolute;
          // top: 310rpx; // 移除固定top值，改为动态计算
          left: 16rpx;
          right: 16rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #fff;
          z-index: 5;

          .online-status {
            display: flex;
            align-items: center;
            background-color: rgba(0,0,0,.5);
            padding: 1px 6px;
            border-radius: 3px;
            color: white;
            font-size: 20rpx;
            font-weight: 500;
          }

          .online-status::before {
            content: '';
            display: inline-block;
            width: 12rpx;
            height: 12rpx;
            background: #4caf50;
            border-radius: 50%;
            margin-right: 6rpx;
          }

          .online-status.offline {
            color: #9e9e9e;
          }

          .online-status.offline::before {
            background: #9e9e9e;
          }

          .distance {
            font-size: 20rpx;
            color: white;
          }
        }

        .item-info {
          // padding: 16rpx;

          .info-line-1 {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-bottom: 6rpx;

            .nickname {
              font-size: 28rpx;
              font-weight: 600;
              color: #333;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .user-level {
              margin-left: 8rpx;
              display: flex;
              align-items: center;
              position: relative;
            }

            .level-icon {
              width: 41px;
              height: 14px;
              display: block;
            }

            .level-tag {
              display: flex;
              align-items: center;
              margin-left: 8rpx;
            }

            .level-tag .level-v {
              background: #f44336;
              color: #fff;
              border-radius: 50%;
              width: 28rpx;
              height: 28rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
              font-size: 20rpx;
            }
          }

          .info-line-2 {
            margin-bottom: 8rpx;
            
            .brief {
              font-size: 22rpx;
              color: #666;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .info-line-3 {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            font-size: 20rpx;

            .gender-age-tag {
              display: flex;
              align-items: center;
              border-radius: 2px;
              line-height: 14px;
              text-align: center;
              padding: 3rpx 12rpx 3rpx 6rpx;
              margin-right: 8rpx;
              margin-bottom: 6rpx;
            }

            .female-bg {
              background: #ffe4ef;
            }

            .male-bg {
              background: #e3f2fd;
            }

            .gender-icon-img {
              width: 20rpx;
              height: 20rpx;
              margin-right: 6rpx;
              display: inline-block;
            }

            .age-text {
              color: #1976d2;
              font-weight: 500;
              font-size: 18rpx;
            }

            .constellation-tag {
              display: flex;
              align-items: center;
              margin-right: 8rpx;
              margin-bottom: 6rpx;
              background: none;
              padding: 0;
              border-radius: 0;
            }

            .constellation-icon {
              width: 40rpx;
              height: 40rpx;
              display: inline-block;
              margin: 0;
            }

            .tag {
              background: #f5f5f5;
              color: #333;
              font-size: 18rpx;
              border-radius: 16rpx;
              display: flex;
              align-items: center;
              margin-right: 6rpx;
              margin-bottom: 6rpx;
              padding: 3rpx 10rpx;
            }

            .tag::before {
              content: '🏷️';
              margin-right: 4rpx;
              font-size: 18rpx;
            }
          }
        }
      }
    }
  }

  /* 适配不同屏幕尺寸 */
  @media screen and (max-width: 750rpx) {
    .waterfall-container {
      padding: 12rpx;
      
      .waterfall-column {
        &:first-child {
          margin-right: 6rpx;
        }
        
        &:last-child {
          margin-left: 6rpx;
        }
      }
      
      .waterfall-column .waterfall-item {
        .item-image {
          // height: 320rpx; // 移除固定高度
        }
        
        .item-header {
          // top: 270rpx; // 移除固定top值
        }
        
        .item-info {
          padding: 12rpx;
          
          .info-line-1 .nickname {
            font-size: 26rpx;
          }
          
          .info-line-2 .brief {
            font-size: 20rpx;
          }
        }
      }
    }
  }

  /* 适配大屏幕 */
  @media screen and (min-width: 1200rpx) {
    .waterfall-container {
      padding: 20rpx;
      
      .waterfall-column {
        &:first-child {
          margin-right: 10rpx;
        }
        
        &:last-child {
          margin-left: 10rpx;
        }
      }
      
      .waterfall-column .waterfall-item {
        .item-image {
          // height: 400rpx; // 移除固定高度
        }
        
        .item-header {
          // top: 350rpx; // 移除固定top值
        }
      }
    }
  }

  .distance-icon {
    width: 20rpx;
    height: 24rpx;
    margin-right: 6rpx;
    display: inline-block;
    vertical-align: middle;
  }
  .level-text {
    color: #888;
    font-size: 18rpx;
    font-weight: 500;
    position: absolute;
    left: 17px;
  }
  .s-mark{
    margin-left: 3px;
    width: 14px;
    height: 14px;
  }
</style>
