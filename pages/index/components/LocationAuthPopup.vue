<template>
  <view v-if="visible" class="custom-popup-overlay" @click="handleOverlayClick">
    <view class="custom-popup-content" @click.stop>
      <view class="location-popup-content">
        <image src="/static/location.png" style="width:80px;height:80px;margin:auto;" />
        <view class="title">开启位置权限<br/>遇见附近的有趣灵魂！</view>
        <view class="desc">请在手机设置中开启位置授权，以便我们为你推荐附近的人和活动</view>
        <button class="open-btn" @click="openLocationAuth">一键开启</button>
        <button class="cancel-btn" @click="handleClose">暂不启用</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LocationAuthPopup',
  emits: ['update:visible', 'close', 'success'],
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleOverlayClick() {
      this.$emit('update:visible', false)
    },
    openLocationAuth() {
      // 申请定位权限
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          this.$emit('success')
        },
        fail: () => {
          // 引导用户去设置
          uni.showModal({
            title: '提示',
            content: '请在设置中开启位置权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                uni.openSetting()
              }
            }
          })
        }
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.custom-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.custom-popup-content {
  animation: slideIn 0.3s ease-out;
  margin: 0 20px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.location-popup-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx 30rpx 30rpx;
  background: #fff;
  border-radius: 24rpx;
  min-width: 500rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  margin: 30rpx 0 10rpx 0;
  text-align: center;
}
.desc {
  font-size: 26rpx;
  color: #888;
  margin-bottom: 40rpx;
  text-align: center;
}
.open-btn {
  width: 100%;
  background: linear-gradient(90deg, #A3EAB6 0%, #69C689 100%);
  color: #fff;
  font-size: 30rpx;
  border: none;
  border-radius: 30rpx;
  padding: 18rpx 0;
  margin-bottom: 20rpx;
}
.cancel-btn {
  width: 100%;
  background: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  border: none;
  border-radius: 30rpx;
  padding: 16rpx 0;
}
</style> 