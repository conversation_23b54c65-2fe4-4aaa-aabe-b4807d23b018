<template>
  <view class="list-table">
    <view class="table-container">
      <view class="table-item" v-for="(item, index) in list" :key="item.userId" @click="goToDetail(item)">
        <view class="item-image-container">
          <image class="item-image" :src="getImageUrl(item.img)" mode="aspectFill"></image>
          <view class="online-indicator" :class="{ offline: !item.isOnline }"></view>
        </view>
        <view class="item-info">
          <view class="item-header">
            <view class="header-left">
              <text class="item-title">{{ item.nickName || '未知用户' }}</text>
              <image class="s-mark" :src="'/static/index/level/vmark.png'" />
              <view class="user-level">
                <image class="level-icon" :src="getUserLevelIcon(getOriginalIndex(item))" />
                <text class="level-text">LV{{ index+1 }}</text>
              </view>
            </view>
            <view class="header-right">
            </view>
          </view>
          <view class="item-desc-container">
            <view v-if="item.dis" class="distance">
              <image class="distance-icon" src="/static/index/icon/<EMAIL>" />
              {{ (item.dis / 1000).toFixed(2) }}km
            </view>
            <text v-if="item.dis" class="separator">|</text>
            <text class="item-desc">{{ item.brief || '这个人很懒，什么都没写...' }}</text>
          </view>
          <view class="item-tags">
            <view v-if="item.sex || item.age" class="gender-age-tag" :class="item.sex === 2 ? 'female-bg' : 'male-bg'">
              <image v-if="item.sex" class="gender-icon-img" :src="item.sex === 2 ? '/static/index/icon/female.png' : '/static/index/icon/male.png'" />
              <text v-if="item.age" class="age-text">{{ item.age }}</text>
            </view>
            <view v-if="item.constellation" class="constellation-tag">
              <image class="constellation-icon" :src="getConstellationIcon(item.constellation)" />
            </view>
            <text class="tag" v-for="tag in getTags(item)" :key="tag">{{ tag }}</text>
          </view>
        </view>
        
        <view class="item-actions">
            <view class="find-ta-btn" @click.stop="handleFindTa(item)">
              <image class="btn-star" src="/static/index/icon/<EMAIL>" />
              <text>找TA</text>
            </view>
          </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'ListTable',
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    methods: {
      // 处理图片URL
      getImageUrl(img) {
        if (!img) {
          return '/static/index/default.png'
        } else {
          return `http://************:9000/${img}`
        }
      },
      // 处理标签数据 - 以ListGrid.vue为准
      getTags(item) {
        // 从接口数据中提取标签信息
        // 这里可以根据实际需求从不同字段组合标签
        const tags = []

        // 如果有星座信息，可以作为标签
        if (item.constellation) {
          tags.push(item.constellation)
        }

        // 如果有简介信息，可以提取关键词作为标签
        if (item.brief) {
          // 简单的关键词提取逻辑
          const keywords = ['运动', '健身', '音乐', '电影', '旅行', '美食', '摄影', '游戏']
          const matchedKeywords = keywords.filter(keyword => item.brief.includes(keyword))
          tags.push(...matchedKeywords.slice(0, 2))
        }

        return tags.slice(0, 2) // 最多显示2个标签
      },
      // 处理找TA按钮点击
      handleFindTa(item) {
        // 跳转到达人详情页面，带上showSkills标识
        uni.navigateTo({
          url: `/pages/expertDetail/expert-detail?id=${item.userId}&fromIndex=1&showSkills=1`
        })
      },
      getConstellationIcon(constellation) {
        const map = {
          '白羊座': 'aries',
          '金牛座': 'taurus',
          '双子座': 'gemini',
          '巨蟹座': 'cancer',
          '狮子座': 'leo',
          '处女座': 'virgo',
          '天秤座': 'libra',
          '天蝎座': 'scorpio',
          '射手座': 'sagittarius',
          '摩羯座': 'capricornus',
          '水瓶座': 'aquarius',
          '双鱼座': 'pisces'
        }
        return map[constellation] ? `/static/index/constellation/${map[constellation]}@2x.png` : ''
      },
      goToDetail(item) {
        uni.navigateTo({
          url: `/pages/expertDetail/expert-detail?id=${item.userId}&fromIndex=1`
        })
      },
      // 根据index计算用户等级，返回1-5的等级
      getUserLevel(index) {
        return (index % 5) + 1
      },
      // 根据等级获取等级图标
      getUserLevelIcon(index) {
        const level = this.getUserLevel(index)
        return `/static/index/level/lv${level}.png`
      },
      // 获取原始数据中的index
      getOriginalIndex(item) {
        return this.list.findIndex(listItem => listItem.userId === item.userId)
      }
    },
    setup() {
      return {}
    }
  }
</script>

<style lang="scss" scoped>
  .list-table {
    padding: 20rpx;

    .table-container {
      .table-item {
        display: flex;
        padding: 20rpx;
        margin-bottom: 20rpx;
        border-radius: 12rpx;

        .item-image-container {
          position: relative;

          .item-image {
            width: 130rpx;
            height: 130rpx;
            border-radius: 50%;
            margin-right: 20rpx;
          }

          .online-indicator {
            position: absolute;
            top: 0;
            right: 22rpx;
            width: 20rpx;
            height: 20rpx;
            border-radius: 50%;
            background-color: #4caf50;
            border: 4rpx solid #fff;
            box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
          }

          .online-indicator.offline {
            background-color: #ccc;
          }
        }

        .item-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          // min-height: 160rpx;

          .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8rpx;

            .header-left {
              display: flex;
              align-items: center;
              flex: 1;

              .item-title {
                font-size: 28rpx;
                font-weight: bold;
                color: #333;
                margin-right: 8rpx;
              }

              .user-level {
                margin-right: 8rpx;
                display: flex;
                align-items: center;
                position: relative;
              }

              .level-icon {
              width: 41px;
              height: 14px;
                display: block;
              }

              .level-tag {
                display: flex;
                align-items: center;
              }

              .level-tag .level-v {
                background: #f44336;
                color: #fff;
                border-radius: 50%;
                width: 28rpx;
                height: 28rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 20rpx;
              }

              .level-tag .level-text {
                background: #f5f5f5;
                color: #888;
                border-radius: 8rpx;
                font-size: 18rpx;
                margin-left: 6rpx;
                padding: 0 8rpx;
                font-weight: 500;
              }
            }

            .header-right {
            }
          }

          .item-desc-container {
            display: flex;
            align-items: center;
            margin: 8rpx 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
            .distance {
              font-size: 20rpx;
              color: #666;
              display: flex;
              align-items: center;

              .distance-icon {
                width: 20rpx;
                height: 24rpx;
                margin-right: 6rpx;
                filter: grayscale(100%) brightness(0.6);
              }
            }

            .separator {
              margin: 0 6rpx;
              color: #ccc;
              font-size: 20rpx;
            }

            .item-desc {
              font-size: 24rpx;
              color: #666;
              line-height: 1.4;
              flex: 1;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 107px;
            }
          }

          .item-tags {
            display: flex;
            flex-wrap: wrap;
            margin: 8rpx 0;
            align-items: center;

            .gender-age-tag {
              display: flex;
              align-items: center;
              border-radius: 2px;
              line-height: 14px;
              text-align: center;
              padding: 3rpx 12rpx 3rpx 6rpx;
              margin-right: 8rpx;
              margin-bottom: 6rpx;
            }

            .female-bg {
              background: #ffe4ef;
            }

            .male-bg {
              background: #e3f2fd;
            }

            .gender-icon-img {
              width: 20rpx;
              height: 20rpx;
              margin-right: 6rpx;
              display: inline-block;
            }

            .age-text {
              color: #1976d2;
              font-weight: 500;
              font-size: 18rpx;
            }

            .constellation-tag {
              display: flex;
              align-items: center;
              margin-right: 8rpx;
              margin-bottom: 6rpx;
              background: none;
              padding: 0;
              border-radius: 0;
            }

            .constellation-icon {
              width: 40rpx;
              height: 40rpx;
              display: inline-block;
              margin: 0;
            }

            .tag {
              background: #f5f5f5;
              color: #333;
              font-size: 18rpx;
              border-radius: 16rpx;
              display: flex;
              align-items: center;
              margin-right: 6rpx;
              margin-bottom: 6rpx;
              padding: 3rpx 10rpx;
            }

            .tag::before {
              content: '🏷️';
              margin-right: 4rpx;
              font-size: 18rpx;
            }
          }

        }
      }
    }
  }
  
  .item-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 8rpx;

    .find-ta-btn {
      width: 134rpx;
      height: 56rpx;
      background: url('/static/index/icon/<EMAIL>') no-repeat center center;
      background-size: cover;
      border-radius: 24rpx;
      font-size: 26rpx;
      color: white;
      font-weight: 700;
      box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;

      .btn-star {
        width: 22rpx;
        height: 20rpx;
        margin-right: 8rpx;
        display: inline-block;
      }
    }
  }
  .level-text {
    color: #888;
    font-size: 18rpx;
    font-weight: 500;
    position: absolute;
    left: 17px;
  }
  .s-mark{
    margin-left: 3px;
    width: 14px;
    height: 14px;
  }
</style>
