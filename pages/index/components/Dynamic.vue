<template>
  <view class="dynamic-container">
    <component :is="currentLayout" :list="list"></component>
    <!-- 加载状态 -->
    <view v-if="status === 'loading'" class="loading-indicator">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view v-if="status === 'noMore'" class="no-more-indicator">
      <text class="no-more-text">已经到底了</text>
    </view>
  </view>
</template>

<script>
  import ListGrid from './ListGrid.vue'
  import ListTable from './ListTable.vue'

  export default {
    name: 'Dynamic',
    components: {
      ListGrid,
      ListTable
    },
    props: {
      list: {
        type: Array,
        default: () => []
      },
      currentLayout: {
        type: String,
        default: 'ListGrid'
      }
    },
    setup() {
      return {}
    }
  }
</script>

<style lang="scss">
  .dynamic-container {
    padding: 0;
  }
  .loading-text {
    font-size: 28rpx;
    color: #666;
  }

  /* 没有更多数据提示样式 */
  .no-more-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    margin: 20rpx;
    border-radius: 12rpx;
  }

  .no-more-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60rpx;
  }
</style>
