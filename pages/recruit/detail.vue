<template>
  <view class="detail-container">
    <view v-for="(item, index) in list" :key="index" class="activity-item">
      <!-- 顶部标签和优惠标签 -->
      <view class="item-header">
        <view class="item-header-left">
          <image class="tag-icon" src="/static/zhaomu/headerLeft.png" mode="heightFix" />
          <text class="tag-text">{{ item.title }}</text>
        </view>
        <view class="item-header-right">
          <image class="tag-icon" :src="getStatusImage(item.activityStatus)" mode="heightFix" />
        </view>
      </view>
      <!-- 活动描述 -->
      <view class="activity-content">
        <view class="activity-content-top">
          <!-- <view class="category-tag">
              <text class="tag-text">{{ getType(item.activityStatus) }}</text>
            </view> -->
          <text class="activity-title">{{ item.description }}</text>
        </view>
      </view>
      <!-- 日期时间 -->
      <view class="time-info">
        <image class="time-icon" src="/static/zhaomu/time.png" mode="heightFix" />
        <text class="time-text">{{ item.formattedServiceTime }}</text>
      </view>

      <!-- 地点 -->
      <view class="location-info">
        <image class="location-icon" src="/static/zhaomu/location.png" mode="heightFix" />
        <text class="location-text">{{
          getLocationFromSnapshot(item.serviceAddressSnapshot)
        }}</text>
      </view>
      <!-- 地点 -->
      <view class="publish-info">
        <image class="location-icon" src="/static/zhaomu/money.png" mode="heightFix" />
        <text class="time-text">{{ item.pricePerPerson }}贝壳币</text>
      </view>
      <!-- 底部信息 -->
      <view class="item-footer">
        <view class="participants-info">
          <view class="image-box">
            <image
              v-for="(avatar, avatarIndex) in item.recentApplicants.slice(0, 8)"
              :key="avatarIndex"
              :src="getFullUrl(avatar.avatar)"
              class="participant-avatar"
              mode="aspectFill"
            />
          </view>

          <text class="participant-count">{{ item.applicationCount }}人已上车</text>
        </view>
      </view>
      <view class="tip">
        <image
          v-if="item.isCaptain === 1"
          src="/static/zhaomu/my-group.png"
          style="
            position: absolute;
            right: 0rpx;
            bottom: 0rpx;
            width: 174rpx;
            height: 174rpx;
            z-index: 999;
          "
          mode="aspectFit"
        />
        <image
          v-else-if="item.isCaptain === 2"
          src="/static/zhaomu/my-baom.png"
          style="
            position: absolute;
            right: 0rpx;
            bottom: 0rpx;
            width: 174rpx;
            height: 174rpx;
            z-index: 999;
          "
          mode="aspectFit"
        />
        <text class="tip-text" v-if="item.isCaptain === 1">我的报名</text>
        <text class="tip-text" v-else-if="item.isCaptain === 2">我的招募</text>
      </view>
    </view>
    <view class="table-title">
      <text>报名({{ list.length > 0 ? list[0].recentApplicants.length : 0 }})</text>
    </view>
    <view
      class="error-container"
      v-if="list.length > 0 && (list[0].activityStatus == 5 || list[0].activityStatus == 6)"
    >
      <image class="fail-png" :src="'/static/zhaomu/group-fail.png'" />
      <text class="fail-text">招募失败</text>
    </view>
    <view class="table-container" v-else>
      <uni-swipe-action v-if="list[0]">
        <uni-swipe-action-item
          v-for="(item, index) in list[0].recentApplicants"
          :key="item.userId"
          :right-options="getSwipeOptions(index, item)"
          @click="e => handleSwipeClick(e, item, index)"
          auto-close
        >
          <view class="table-item">
            <view class="item-image-container">
              <image class="item-image" :src="getImageUrl(item.avatar)" mode="aspectFill"></image>
              <!-- <view class="online-indicator" :class="{ offline: !item.isOnline }"></view> -->
            </view>
            <view class="item-info">
              <view class="item-header">
                <view class="header-left">
                  <text class="item-title">{{ item.nickname || '未知用户' }}</text>
                  <image class="s-mark" :src="'/static/index/level/vmark.png'" />
                  <view class="user-level">
                    <image class="level-icon" :src="getUserLevelIcon(getOriginalIndex(item))" />
                    <text class="level-text">LV{{ index + 1 }}</text>
                  </view>
                </view>
                <view class="header-right"> </view>
              </view>
              <view class="item-desc-container">
                <text class="item-desc">{{ item.brief || '这个人很懒，什么都没写...' }}</text>
              </view>
              <view class="item-tags">
                <view
                  v-if="item.sex || item.age"
                  class="gender-age-tag"
                  :class="item.sex === 2 ? 'female-bg' : 'male-bg'"
                >
                  <image
                    v-if="item.sex"
                    class="gender-icon-img"
                    :src="
                      item.sex === 2
                        ? '/static/index/icon/female.png'
                        : '/static/index/icon/male.png'
                    "
                  />
                  <text v-if="item.age" class="age-text">{{ item.age }}</text>
                </view>
                <view v-if="item.constellation" class="constellation-tag">
                  <image
                    class="constellation-icon"
                    :src="getConstellationIcon(item.constellation)"
                  />
                </view>
                <text class="tag" v-for="tag in getTags(item)" :key="tag">{{ tag }}</text>
              </view>
            </view>

            <view class="item-actions">
              <!-- <view class="find-status" @click.stop="handleFindTa(item)">
                <text>{{ item.applicationStatus === 1 ? '' : '招募中' }}</text>
              </view> -->
              <!-- 接受按钮 - 当是队长且申请状态为1时显示 -->
              <view 
                class="accept-btn" 
                @click.stop="handleAccept(item)"
                v-if="list.length > 0 && list[0].activityStatus === 1 && list[0].isCaptain === 1 && item.applicationStatus === 1"
              >
                <text>接受</text>
              </view>
              <view class="find-ta-btn" @click.stop="handleFindTa(item)" v-if="!item.isCurrentUser">
                <text>发消息</text>
              </view>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>
    </view>
    <view
      class="detai-action"
      v-if="list.length > 0 && list[0].activityStatus !== 6 && list[0].activityStatus !== 5"
    >
      <!-- 队长的操作按钮 -->
      <view
        v-if="list.length > 0 && list[0].isCaptain === 1 && list[0].activityStatus === 1"
        class="action-buttons"
      >
        <view class="cancel-btn" @tap="handleCancelRecruit">
          <text>取消招募</text>
        </view>
        <view class="complete-btn" @tap="handleCompleteTeam">
          <text>招募完成</text>
        </view>
      </view>

      <!-- 队员的操作按钮 -->
      <view
        v-if="list.length > 0 && list[0].isCaptain === 2 && list[0].activityStatus === 1"
        class="action-buttons"
      >
        <view class="cancel-btn" @tap="cancelJoinActivity">
          <text>退出招募</text>
        </view>
      </view>

      <!-- 既不属于队长也不是队员可以加入活动的操作按钮 -->
      <view v-if="list[0].isCaptain === 0 && list[0].activityStatus === 1" class="action-buttons">
        <view class="join-btn" @tap="handleJoinActivity">
          <text>加入招募</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { recruitApi } from '@/common/api'
  import listItem from './listItem.vue'
  export default {
    // 启用下拉刷新
    enablePullDownRefresh: true,
    // 设置下拉刷新的样式
    backgroundColor: '#f8f8f8',
    backgroundTextStyle: 'dark',
    components: {
      listItem
    },
    data() {
      return {
        // index为0时的选项：只有拒绝
        optionsForIndex0: [
          {
            text: '拒绝',
            style: {
              backgroundColor: 'rgba(255, 95, 84, 1)'
            }
          }
        ],
        // index为1时的选项：踢出
        optionsForIndex1: [
          {
            text: '踢出',
            style: {
              backgroundColor: 'rgba(153, 153, 153, 1)'
            }
          }
        ],
        list: [],
        groupList: [
          {
            userId: '1',
            img: '',
            nickName: '张三',
            dis: 1000,
            brief: '这个人很懒，什么都没写...',
            sex: 1,
            age: 20,
            constellation: '白羊座',
            tags: ['旅游', '摄影', '美食'],
            isOnline: true,
            isCaptain: true
          },
          {
            userId: '2',
            img: '',
            nickName: '李四',
            dis: 800,
            brief: '喜欢旅游和摄影，期待与你同行',
            sex: 2,
            age: 25,
            constellation: '金牛座',
            tags: ['摄影', '旅游'],
            isOnline: false,
            isCaptain: false
          },
          {
            userId: '3',
            img: '',
            nickName: '王五',
            dis: 1200,
            brief: '热爱美食，乐于分享',
            sex: 1,
            age: 28,
            constellation: '双子座',
            tags: ['美食', '运动'],
            isOnline: true,
            isCaptain: false
          },
          {
            userId: '4',
            img: '',
            nickName: '赵六',
            dis: 1200,
            brief: '热爱美食，乐于分享',
            sex: 1,
            age: 28,
            constellation: '双子座',
            tags: ['美食', '运动'],
            isOnline: true,
            isCaptain: false
          }
        ],
        activityCode: ''
      }
    },
    onLoad(options) {
      this.activityCode = options.activityCode
      this.getDetail(options)
    },
    methods: {
      // 获取完整URL
      getFullUrl(url) {
        if (!url) return ''
        if (url.startsWith('http')) return url
        return `http://************:9000/${url}`
      },
      // 获取地址信息
      getLocationFromSnapshot(serviceAddressSnapshot) {
        try {
          if (!serviceAddressSnapshot) return '暂无地点'

          // 如果已经是对象，直接取address字段
          if (typeof serviceAddressSnapshot === 'object') {
            return serviceAddressSnapshot.address || '暂无地点'
          }

          // 如果是字符串，尝试解析为JSON
          if (typeof serviceAddressSnapshot === 'string') {
            const addressData = JSON.parse(serviceAddressSnapshot)
            return addressData.address || '暂无地点'
          }

          return '暂无地点'
        } catch (error) {
          console.error('解析地址信息失败:', error)
          return '暂无地点'
        }
      },
      // 根据条件获取滑动选项
      getSwipeOptions(index, item) {
        // 检查条件：activityStatus为1，isCaptain为1，applicationStatus为1
        if (
          this.list.length > 0 &&
          this.list[0].activityStatus === 1 &&
          this.list[0].isCaptain === 1 &&
          item.applicationStatus === 1
        ) {
          return this.optionsForIndex0 // 显示接受和拒绝按钮
        }

        // 其他情况可以根据需要添加更多条件
        // 例如：如果是队长且需要踢出已接受的成员
        if (
          this.list.length > 0 &&
          this.list[0].activityStatus === 1 &&
          this.list[0].isCaptain === 1 &&
          item.applicationStatus === 2 // 假设2表示已接受的成员
        ) {
          return this.optionsForIndex1 // 显示踢出按钮
        }

        return [] // 不显示任何滑动选项
      },

      getTags(item) {
        // 从接口数据中提取标签信息
        // 这里可以根据实际需求从不同字段组合标签
        const tags = []

        // 如果有星座信息，可以作为标签
        if (item.constellation) {
          tags.push(item.constellation)
        }

        // 如果有简介信息，可以提取关键词作为标签
        if (item.brief) {
          // 简单的关键词提取逻辑
          const keywords = ['运动', '健身', '音乐', '电影', '旅行', '美食', '摄影', '游戏']
          const matchedKeywords = keywords.filter(keyword => item.brief.includes(keyword))
          tags.push(...matchedKeywords.slice(0, 2))
        }

        return tags.slice(0, 2) // 最多显示2个标签
      },
      // 处理图片URL
      getImageUrl(img) {
        if (!img) {
          return 'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
        } else {
          return `http://************:9000/${img}`
        }
      },
      // 根据index计算用户等级，返回1-5的等级
      getUserLevel(index) {
        return (index % 5) + 1
      },
      // 根据等级获取等级图标
      getUserLevelIcon(index) {
        const level = this.getUserLevel(index)
        return `/static/index/level/lv${level}.png`
      },
      // 获取原始数据中的index
      getOriginalIndex(item) {
        return this.list[0].recentApplicants.findIndex(listItem => listItem.userId === item.userId)
      },
      getConstellationIcon(constellation) {
        const map = {
          白羊座: 'aries',
          金牛座: 'taurus',
          双子座: 'gemini',
          巨蟹座: 'cancer',
          狮子座: 'leo',
          处女座: 'virgo',
          天秤座: 'libra',
          天蝎座: 'scorpio',
          射手座: 'sagittarius',
          摩羯座: 'capricornus',
          水瓶座: 'aquarius',
          双鱼座: 'pisces'
        }
        return map[constellation] ? `/static/index/constellation/${map[constellation]}@2x.png` : ''
      },
      getStatusImage(activityStatus) {
        if (activityStatus === 6 || activityStatus === 5) {
          return '/static/zhaomu/group-error.png'
        } else if (activityStatus === 2 || activityStatus === 3 || activityStatus === 4) {
          return '/static/zhaomu/group-success.png'
        } else {
          return '/static/zhaomu/group-ing.png'
        }
      },
      getDetail(options) {
        // 获取路由参数中的用户ID
        console.log(options)
        // this.isLoading = true
        recruitApi.getRecruitDetail(this.activityCode, {}).then(res => {
          this.isLoading = false
          console.log(res.data)
          this.list = [res.data]
        })
      },

      // 处理滑动操作点击
      handleSwipeClick(e, item, itemIndex) {
        console.log(e, item, itemIndex)
        const { position, content, index } = e
        if (position !== 'right') return
        if (item.applicationStatus === 2) {
          this.handleKickOut(item)
        } else {
          switch (index) {
            case 0: // 拒绝
              this.handleReject(item)
              break
          }
        }
      },

      // 处理接受操作
      handleAccept(item) {
        console.log('接受报名:', item.nickname)
        recruitApi
          .agreeOrReject(item.applicationId, {
            action: 'ACCEPT'
          })
          .then(res => {
            console.log(res)
            if (res.code == 200) {
              uni.showToast({
                title: `已接受 ${item.nickname} 的报名`,
                icon: 'success'
              })
            }
            this.getDetail()
          })
          .catch(err => {
            console.log(err)
            uni.showToast({
              title: err.msg,
              icon: 'none'
            })
          })

        // 这里可以添加实际的接受逻辑
      },

      // 处理拒绝操作
      handleReject(item) {
        console.log('拒绝报名:', item.nickname)
        recruitApi
          .agreeOrReject(item.applicationId, {
            action: 'REJECT'
          })
          .then(res => {
            console.log(res)
            uni.showToast({
              title: `已拒绝 ${item.nickname} 的报名`,
              icon: 'none'
            })
            this.getDetail()
          })

        // 这里可以添加实际的拒绝逻辑
      },

      // 处理踢出操作
      handleKickOut(item) {
        console.log('踢出成员:', item)
        uni.showModal({
          title: '确认踢出',
          content: `确定要踢出 ${item.nickname} 吗？`,
          success: res => {
            if (res.confirm) {
              recruitApi.kickOut(item.applicationId, {}).then(res => {
                console.log(res)
                if (res.code == 200) {
                  uni.showToast({
                    title: `已踢出 ${item.nickname}`,
                    icon: 'success'
                  })
                  this.getDetail()
                } else {
                  uni.showToast({
                    title: res.msg,
                    icon: 'none'
                  })
                }
              })

              // 这里可以添加实际的踢出逻辑
            }
          }
        })
      },
      handleFindTa(item) {
        console.log('发消息:', item.nickName)
        // 跳转到聊天页面
        uni.navigateTo({
          url: `/pages/chat/index?channelID=${item.userId}&channelType=1&nickName=${item.nickname}`
        })
        // 这里可以添加实际的发消息逻辑
      },

      // 处理取消招募
      handleCancelRecruit() {
        uni.showModal({
          title: '确认取消',
          content: '确定要取消这次招募吗？',
          success: res => {
            if (res.confirm) {
              // 更新活动状
              recruitApi.cancelRecruit(this.activityCode, {}).then(res => {
                if (res.code == 200) {
                  uni.showToast({
                    title: '招募已取消',
                    icon: 'success'
                  })
                } else {
                  uni.showToast({
                    title: res.msg,
                    icon: 'none'
                  })
                }
                // 返回到我的招募列表
                uni.redirectTo({
                  url: '/pages/recruit/my'
                })
              })
            }
          }
        })
      },

      // 处理招募完成
      handleCompleteTeam() {
        uni.showModal({
          title: '确认完成',
          content: '确定要完成招募吗？',
          success: res => {
            if (res.confirm) {
              // 这里可以添加实际的完成组队API调用
              recruitApi.completeRecruit(this.activityCode, {}).then(res => {
                if (res.code == 200) {
                  uni.showToast({
                    title: '招募已完成',
                    icon: 'success'
                  })
                  uni.navigateTo({
                    url: '/pages/recruit/my'
                  })
                } else {
                  // 处理二次确认强制完成组队
                  uni.showModal({
                    title: '提示',
                    content: res.msg,
                    success: res => {
                      if (res.confirm) {
                        recruitApi
                          .completeRecruit(this.activityCode, {
                            forceComplete: true
                          })
                          .then(res => {
                            if (res.code == 200) {
                              uni.showToast({
                                title: '招募已完成',
                                icon: 'success'
                              })
                              uni.navigateTo({
                                url: '/pages/recruit/my'
                              })
                            }
                          })
                      }
                    }
                  })
                }
              })
            }
          }
        })
      },

      // 处理加入活动
      handleJoinActivity() {
        uni.showModal({
          title: '确认加入',
          content: '确定要加入这个招募吗？',
          success: res => {
            if (res.confirm) {
              recruitApi
                .joinActivity({
                  activityId: this.list[0].dataId
                })
                .then(res => {
                  if (res.code === 200) {
                    uni.showToast({
                      title: res.msg,
                      icon: 'success'
                    })
                    console.log(res)
                    this.getDetail()
                  } else {
                    uni.showToast({
                      title: res.msg,
                      icon: 'error'
                    })
                  }
                })
              // 这里可以添加实际的加入活动API调用
              // 例如: this.joinActivityApi()
            }
          }
        })
      },
      cancelJoinActivity() {
        uni.showModal({
          title: '确认退出',
          content: '确定要退出这个招募吗？',
          success: res => {
            if (res.confirm) {
              recruitApi.leaveActivity(this.activityCode, {}).then(res => {
                if (res.code == 200) {
                  uni.showToast({
                    title: '已退出',
                    icon: 'success'
                  })
                  this.getDetail()
                } else {
                  uni.showToast({
                    title: res.msg,
                    icon: 'error'
                  })
                }
              })
            }
          }
        })
      }
    }
  }
</script>

<style scoped lang="scss">
  .detail-container {
    background: #f4f8fb;
    // min-height: 100vh;
    height: calc(100% - 200rpx);
    padding: 20rpx 20rpx 140rpx 20rpx; // 增加底部padding为固定按钮留出空间
    overflow-y: auto;
  }
  .activity-item {
    border-radius: 30rpx;
    margin-bottom: 30rpx;
    background-color: #fff;
    position: relative;
    overflow: hidden;
    > div {
      padding: 0 30rpx;
    }
    .item-header {
      padding: 0;
    }
    .item-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      position: relative;
      margin-bottom: 10rpx;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 200rpx;
        height: 80rpx;
        background: transparent;
        border-radius: 0 0 20rpx 0;
        z-index: 1;
      }
      .item-header-left {
        display: flex;
        align-items: center;
        background: transparent;
        padding: 5rpx 10rpx 10rpx 0;
        .tag-icon {
          height: 24.81rpx;
          width: 24.81rpx;
          margin-right: 8rpx;
        }
        .tag-text {
          font-size: 32rpx;
          color: #000;
          font-weight: 600;
        }
      }
      .item-header-right {
        position: absolute;
        top: 0;
        right: 0;
        .tag-icon {
          height: 90.87rpx;
          width: 124.05rpx;
        }
      }
    }

    .category-tag {
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(102, 212, 126, 1);
      color: white;
      padding: 8rpx 16rpx;
      border-radius: 6rpx;
      font-size: 24rpx;
      font-weight: 500;

      .tag-icon {
        width: 24rpx;
        height: 24rpx;
        margin-right: 8rpx;
      }

      .tag-text {
        color: white;
      }
    }

    .discount-tag {
      background: linear-gradient(135deg, #ff5722, #f44336);
      color: white;
      padding: 8rpx 12rpx;
      border-radius: 12rpx;
      font-size: 20rpx;
      font-weight: bold;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        right: -10rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 10rpx 0 10rpx 10rpx;
        border-color: transparent transparent transparent #f44336;
      }
    }

    .activity-content {
      padding: 0 30rpx;
      margin-bottom: 20rpx;
      &-top {
        position: relative;
        .activity-title {
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          line-height: 1.6;
          width: 100%;
          // 多行文本省略 - 最多显示2行
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          position: relative;
        }
        .category-tag {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
          // width: 84rpx;
          // height: 38rpx;
          font-size: 22rpx;
        }
      }
    }

    .activity-desc {
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .time-info,
    .location-info,
    .publish-info {
      padding: 0 30rpx;
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      color: rgba(51, 51, 51, 1);
    }
    .publish-info {
      margin-bottom: 30rpx;
    }
    .time-icon,
    .location-icon {
      width: 28rpx;
      height: 28rpx;
      margin-right: 12rpx;
      opacity: 0.7;
    }

    .time-text,
    .location-text {
      font-size: 23rpx;
      // color: #888;
      line-height: 1.4;
    }

    .item-footer {
      padding: 20rpx 0;
      margin: 30rpx 30rpx 0 30rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 20rpx;
      // padding-top: 20rpx;
      border-top: 2rpx solid rgba(209, 209, 209, 1);
      .participant-count {
        font-size: 24rpx;
        color: #666;
        margin-left: 8rpx;
      }
      .price-info {
        display: flex;
        align-items: center;
      }

      .price {
        font-size: 32rpx;
        font-weight: 600;
        color: #4caf50;

        &::after {
          content: '';
          margin-left: 4rpx;
        }
      }
    }
  }
  .table-title {
    font-weight: 600;
    font-size: 27rpx;
    color: rgba(51, 51, 51, 1);
    margin-bottom: 35rpx;
  }
  .table-container {
    .table-item {
      display: flex;
      align-items: center;
      padding: 20rpx;
      margin-bottom: 20rpx;
      border-bottom: 2rpx solid rgba(209, 209, 209, 1);

      .item-image-container {
        position: relative;

        .item-image {
          width: 130rpx;
          height: 130rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .online-indicator {
          position: absolute;
          top: 0;
          right: 22rpx;
          width: 20rpx;
          height: 20rpx;
          border-radius: 50%;
          background-color: #4caf50;
          border: 4rpx solid #fff;
          box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }

        .online-indicator.offline {
          background-color: #ccc;
        }
      }

      .item-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        // min-height: 160rpx;

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          // margin-bottom: 8rpx;

          .header-left {
            display: flex;
            align-items: center;
            flex: 1;

            .item-title {
              font-size: 28rpx;
              font-weight: bold;
              color: #333;
              margin-right: 8rpx;
            }

            .user-level {
              margin-right: 8rpx;
              display: flex;
              align-items: center;
              position: relative;
            }

            .level-icon {
              width: 41px;
              height: 14px;
              display: block;
            }

            .level-tag {
              display: flex;
              align-items: center;
            }

            .level-tag .level-v {
              background: #f44336;
              color: #fff;
              border-radius: 50%;
              width: 28rpx;
              height: 28rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
              font-size: 20rpx;
            }

            .level-tag .level-text {
              background: #f5f5f5;
              color: #888;
              border-radius: 8rpx;
              font-size: 18rpx;
              margin-left: 6rpx;
              padding: 0 8rpx;
              font-weight: 500;
            }
          }

          .header-right {
          }
        }

        .item-desc-container {
          display: flex;
          align-items: center;
          margin: 8rpx 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 150px;
          .distance {
            font-size: 20rpx;
            color: #666;
            display: flex;
            align-items: center;

            .distance-icon {
              width: 20rpx;
              height: 24rpx;
              margin-right: 6rpx;
              filter: grayscale(100%) brightness(0.6);
            }
          }

          .separator {
            margin: 0 6rpx;
            color: #ccc;
            font-size: 20rpx;
          }

          .item-desc {
            font-size: 24rpx;
            color: #666;
            line-height: 1.4;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 107px;
          }
        }

        .item-tags {
          display: flex;
          flex-wrap: wrap;
          align-items: center;

          .gender-age-tag {
            display: flex;
            align-items: center;
            border-radius: 2px;
            line-height: 14px;
            text-align: center;
            padding: 3rpx 12rpx 3rpx 6rpx;
            margin-right: 8rpx;
            margin-bottom: 6rpx;
          }

          .female-bg {
            background: #ffe4ef;
          }

          .male-bg {
            background: #e3f2fd;
          }

          .gender-icon-img {
            width: 20rpx;
            height: 20rpx;
            margin-right: 6rpx;
            display: inline-block;
          }

          .age-text {
            color: #1976d2;
            font-weight: 500;
            font-size: 18rpx;
          }

          .constellation-tag {
            display: flex;
            align-items: center;
            margin-right: 8rpx;
            margin-bottom: 6rpx;
            background: none;
            padding: 0;
            border-radius: 0;
          }

          .constellation-icon {
            width: 40rpx;
            height: 40rpx;
            display: inline-block;
            margin: 0;
          }

          .tag {
            background: #f5f5f5;
            color: #333;
            font-size: 18rpx;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            margin-right: 6rpx;
            margin-bottom: 6rpx;
            padding: 3rpx 10rpx;
          }

          .tag::before {
            content: '🏷️';
            margin-right: 4rpx;
            font-size: 18rpx;
          }
        }
      }
    }
  }
  .level-text {
    color: #888;
    font-size: 18rpx;
    font-weight: 500;
    position: absolute;
    left: 17px;
  }
  .s-mark {
    margin-left: 3px;
    width: 14px;
    height: 14px;
  }
  :deep(.uni-swipe-action__container) {
    position: relative;
    width: 100%;
  }

  :deep(.uni-swipe-action--btn) {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100% !important;
    padding: 0 30rpx;
  }
  :deep(.button-group--right) {
    bottom: 20rpx;
  }

  .custom-swipe-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 30rpx;
    height: 100%;

    .swipe-icon {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 10rpx;
    }

    .swipe-text {
      font-size: 24rpx;
      color: #fff;
    }
  }
  .find-ta-btn {
    width: 130rpx;
    height: 57rpx;
    background: #ffffff;
    border-radius: 29rpx;
    border: 2rpx solid #d1d1d1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000000;
  }

  .accept-btn {
    width: 130rpx;
    height: 57rpx;
    background: rgba(102, 212, 126, 1);
    border-radius: 29rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
  }

  .detai-action {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0 20rpx 20rpx;
  }

  .action-buttons {
    display: flex;
    gap: 20rpx;
    justify-content: space-between;
  }

  .cancel-btn,
  .complete-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 31rpx;
    font-weight: 500;
  }

  .cancel-btn {
    background: #ffffff;
    border: 2rpx solid rgba(209, 209, 209, 1);
    color: rgba(51, 51, 51, 1);
  }

  .complete-btn {
    background: #4caf50;
    color: #ffffff;
  }

  .join-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: 600;
    background: rgba(102, 212, 126, 1);
    color: #ffffff;
  }
  .error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    // height: 100%;
    .fail-png {
      height: 130rpx;
      width: 230rpx;
    }
    .fail-text {
      font-size: 30rpx;
      color: rgba(51, 51, 51, 1);
      margin-top: 20rpx;
      font-weight: 600;
    }
  }
  .item-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20rpx;
    .find-status {
      color: rgba(102, 212, 126, 1);
      margin-right: 10rpx;
    }
  }
  .participants-info {
    display: flex;
    align-items: center;
    .participant-count {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 12rpx;
    }
  }

  .avatar-group {
    display: flex;
    align-items: center;
    margin-right: 12rpx;
  }

  .participant-avatar {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid #fff;
    margin-right: -8rpx;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

    &:first-child {
      margin-right: -8rpx;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  .participant-count {
    font-size: 24rpx;
    color: #666;
    margin-left: 8rpx;
  }
  .tip-text {
    font-weight: 500;
    font-size: 27rpx;
    color: #ffffff;
    display: inline-block;
    transform: rotate(-45deg);
    transform-origin: left;
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 9999;
  }
</style>
