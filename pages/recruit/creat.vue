<template>
  <view class="container">
    <!-- <page-header title="发布活动" @back="goBack" /> -->
    <scroll-view class="content" scroll-y>
      <!-- 描述输入区域 -->
      <view class="description-section">
        <textarea
          class="description-textarea"
          placeholder="描述下你的具体要求"
          v-model="form.description"
          maxlength="100"
          auto-height
        ></textarea>
        <view class="char-count">{{ form.description.length }}/100</view>
      </view>

      <!-- 选择技能 -->
      <view class="select-item" @click="showSkillPicker">
        <uni-icons type="plus" size="16" color="#4CAF50"></uni-icons>
        <text class="select-text">选择技能</text>
        <view class="selected-skills" v-if="form.selectedSkill">
          <text class="skill-tag">{{ form.selectedSkill }}</text>
        </view>
      </view>

      <!-- 表单项 -->
      <view class="form-section">
        <!-- 性别 -->
        <view class="form-item" @click="showGenderPicker">
          <text class="form-label">性别</text>
          <view class="form-value">
            <text :class="genderText ? 'selected' : 'placeholder'">
              {{ genderText || '请选择性别要求' }}
            </text>
            <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
          </view>
        </view>

        <!-- 人数 -->
        <view class="form-item">
          <text class="form-label">人数</text>
          <view class="number-selector">
            <view class="number-btn" @click="changeNumber(-1)"> - </view>
            <text class="number-value">{{ form.neededCount }}</text>
            <view class="number-btn" @click="changeNumber(1)"> + </view>
          </view>
        </view>

        <!-- 服务时间 -->
        <view class="form-item" @click="selectServiceTime">
          <text class="form-label">服务时间</text>
          <view class="form-value">
            <text :class="form.serviceStartTime ? 'selected' : 'placeholder'">
              {{ form.serviceStartTime || '请选择开始服务时间' }}
            </text>
            <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
          </view>
        </view>

        <!-- 招募截至时间 -->
        <view class="form-item" @click="selectExpireTime">
          <text class="form-label">招募截至时间</text>
          <view class="form-value">
            <text :class="form.expireTime ? 'selected' : 'placeholder'">
              {{ form.expireTime || '请选择招募截至时间' }}
            </text>
            <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
          </view>
        </view>

        <!-- 时长 -->
        <view class="form-item" @click="showDurationPicker">
          <text class="form-label">时长</text>
          <view class="form-value">
            <text :class="durationText ? 'selected' : 'placeholder'">
              {{ durationText || '请选择时长' }}
            </text>
            <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
          </view>
        </view>

        <!-- 距离 -->
        <!-- <view class="form-item" @click="showDistancePicker">
          <text class="form-label">距离</text>
          <view class="form-value">
            <text :class="form.distance ? 'selected' : 'placeholder'">
              {{form.distance || '请选择距离范围'}}
            </text>
            <uni-icons type="arrowright" size="16" color="#999"></uni-icons>
          </view>
        </view> -->

        <!-- 服务地址 -->
        <view class="form-item" @click="showAddressPicker">
          <text class="form-label">服务地址</text>
          <view class="form-value">
            <text :class="form.address ? 'selected' : 'placeholder'">
              {{ form.address || '请选择您的地址' }}
            </text>
            <uni-icons type="location" size="16" color="#4CAF50"></uni-icons>
          </view>
        </view>

        <!-- 贝壳币输入 -->
        <view class="city-info">
          <text class="city-name">贝壳币</text>
          <input
            class="city-input"
            type="number"
            v-model="form.cityInput"
            placeholder="请输入"
            @click.stop
            style="text-align: right"
          />
          /人
        </view>
      </view>

      <!-- 订单金额 -->
      <view class="amount-section">
        <view class="section-title">
          <text>订单金额</text>
          <uni-icons type="minus" size="16" color="#333"></uni-icons>
        </view>
        <view class="form-value">
          <text>
            {{ form.totalAmount }}
          </text>
        </view>
      </view>

      <!-- 交通费细则 -->
      <!-- <view class="amount-section">
        <view class="section-title">
          <text>交通费细则</text>
          <uni-icons type="help" size="16" color="#999"></uni-icons>
          <uni-icons type="minus" size="16" color="#333"></uni-icons>
        </view>
      </view> -->

      <!-- 保险细则 -->
      <!-- <view class="amount-section">
        <view class="section-title">
          <text>保险细则</text>
          <uni-icons type="help" size="16" color="#999"></uni-icons>
          <text class="insurance-info">{{form.cityCount}} 贝壳市</text>
          <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
        </view>
      </view> -->

      <!-- 支付方式 -->
      <view class="payment-section">
        <!-- 账户余额 -->
        <view class="payment-item" @click="handlePaymentSelect('balance')">
          <view class="payment-left">
            <image :src="'/static/images/shoping/ye.png'" class="method-icon"></image>
            <view class="payment-info">
              <text class="payment-name">账户余额</text>
            </view>
          </view>
          <uni-icons
            :type="selectPayment === 'balance' ? 'checkmarkempty' : 'circle'"
            size="20"
            :color="selectPayment === 'balance' ? '#4CAF50' : '#ddd'"
          ></uni-icons>
        </view>

        <!-- 支付宝 -->
        <view class="payment-item" @click="handlePaymentSelect('alipay')">
          <view class="payment-left">
            <image :src="'/static/images/shoping/zfb.png'" class="method-icon"></image>
            <text class="payment-name">支付宝</text>
          </view>
          <uni-icons
            :type="selectPayment === 'alipay' ? 'checkmarkempty' : 'circle'"
            size="20"
            :color="selectPayment === 'alipay' ? '#4CAF50' : '#ddd'"
          ></uni-icons>
        </view>

        <!-- 微信支付 -->
        <view class="payment-item" @click="handlePaymentSelect('wechat')">
          <view class="payment-left">
            <image :src="'/static/images/shoping/wx.png'" class="method-icon"></image>
            <text class="payment-name">微信支付</text>
          </view>
          <uni-icons
            :type="selectPayment === 'wechat' ? 'checkmarkempty' : 'circle'"
            size="20"
            :color="selectPayment === 'wechat' ? '#4CAF50' : '#ddd'"
          ></uni-icons>
        </view>
      </view>
    </scroll-view>

    <!-- 底部支付栏 -->
    <view class="bottom-bar">
      <view class="total-info">
        <text class="total-label">总计：</text>
        <text class="total-amount">{{ calculatedTotalAmount || 0 }}</text>
        <text class="currency">{{ selectPayment === 'balance' ? '贝壳币' : '元' }}</text>
      </view>
      <button class="pay-button" szie="mini" @click="handlePay">立即支付</button>
    </view>

    <!-- 技能选择弹窗 -->
    <!-- <uni-popup ref="skillPopup" type="bottom" background-color="#fff">
      <view class="popup-content" @touchmove.stop>
        <view class="popup-header">
          <text class="popup-title">选择技能</text>

        </view>
        <view class="skill-selector">
          <view class="skill-picker-container" @touchmove.stop>
            <view class="category-picker">
              <scroll-view
                class="picker-scroll"
                scroll-y
                :scroll-top="categoryScrollTop"
                @scroll="onCategoryScroll"
                :scroll-with-animation="true"
              >
                <view class="picker-item-wrapper">
                  <view
                    v-for="(category, index) in skillCategories"
                    :key="index"
                    :class="['picker-item', selectedCategoryIndex === index ? 'selected' : '']"
                    @click="selectCategory(index)"
                  >
                    <text class="category-name">{{ category.name }}</text>
                  </view>
                </view>
              </scroll-view>
              <view class="picker-indicator"></view>
            </view>
            <view class="skill-picker">
              <scroll-view
                class="picker-scroll"
                scroll-y
                :scroll-top="skillScrollTop"
                @scroll="onSkillScroll"
                :scroll-with-animation="true"
              >
                <view class="picker-item-wrapper">
                  <view
                    v-for="(skill, index) in currentSkills"
                    :key="index"
                    :class="[
                      'picker-item',
                      tempSelectedSkills && tempSelectedSkills.id === skill.id ? 'selected' : ''
                    ]"
                    @click="toggleSkill(skill, index)"
                  >
                    <text class="skill-name">{{ skill.name }}</text>
                    <view
                      v-if="tempSelectedSkills && tempSelectedSkills.id === skill.id"
                      class="selected-icon"
                    >
                      <uni-icons type="checkmarkempty" size="16" color="#4CAF50"></uni-icons>
                    </view>
                  </view>
                </view>
              </scroll-view>
              <view class="picker-indicator"></view>
            </view>
          </view>
        </view>

        <view class="popup-actions">
          <button class="cancel-btn" size="default" @click="closeSkillPicker">取消</button>
          <button class="confirm-btn" size="default" @click="confirmSkills">确定</button>
        </view>
      </view>
    </uni-popup> -->
    <!-- 技能 -->
    <uni-popup
      ref="skillPopup"
      type="bottom"
      borderRadius="30rpx 30rpx 0 0"
      class="type-popup"
      background-color="#fff"
    >
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择技能</text>
        </view>
        <!-- {{ skillList }} -->
        <view class="picker-row">
          <picker-view
            indicator-style="height: 50px;"
            :value="skillIndex"
            @change="bindChange"
            class="picker-view"
          >
            <picker-view-column>
              <view
                class="item"
                :class="{ active: skillIndex[0] === index }"
                v-for="(item, index) in skillList"
                :key="index"
              >
                {{ item.name }}
              </view>
            </picker-view-column>
            <picker-view-column>
              <view
                class="item"
                :class="{ active: skillIndex[1] === index }"
                v-for="(item, index) in skillList[skillIndex[0] || 0]?.children || []"
                :key="index"
                >{{ item.skillName }}
              </view>
            </picker-view-column>
          </picker-view>
        </view>
        <view class="popup-btn-row">
          <button class="btn cancel-btn" @click="handleCancel">取消</button>
          <button class="btn confirm-btn" @click="handleConfirm">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 性别选择弹窗 -->
    <uni-popup ref="genderPopup" type="bottom" background-color="#fff">
      <view class="popup-content" @touchmove.stop>
        <view class="popup-header">
          <text class="popup-title">选择性别</text>
          <!-- <uni-icons
            type="closeempty"
            size="20"
            color="#333"
            @click="closeGenderPicker"
          ></uni-icons> -->
        </view>

        <view class="gender-options">
          <view
            v-for="option in genderOptions"
            :key="option.value"
            :class="['gender-option', tempGender === option.value ? 'selected' : '']"
            @click="selectGender(option.value)"
          >
            <view class="gender-info">
              <image :src="`/static/zhaomu/${option.icon}.png`" class="gender-icon"></image>
              <uni-icons :type="option.icon" size="20" :color="option.color"></uni-icons>
              <text class="gender-text">{{ option.label }}</text>
            </view>
            <uni-icons
              :type="tempGender === option.value ? 'checkmarkempty' : 'circle'"
              size="20"
              :color="tempGender === option.value ? '#4CAF50' : '#ddd'"
            ></uni-icons>
          </view>
        </view>

        <view class="popup-actions">
          <button class="cancel-btn" size="default" @click="closeGenderPicker">取消</button>
          <button class="confirm-btn" size="default" @click="confirmGender">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 服务时间选择弹窗 -->
    <!-- 服务时间选择器 -->
    <view class="picker-popup" v-if="timePicker.show">
      <view class="picker-overlay" @click="closeTimePicker"></view>
      <view class="time-picker-content">
        <view class="time-picker-header">
          <text class="time-picker-title">选择服务时间</text>
          <text class="close-btn" @click="closeTimePicker">×</text>
        </view>
        <picker-view
          class="time-picker-view"
          :indicator-style="`height: 50px;`"
          :value="timePicker.selectedIndex"
          @change="onTimePickerChange"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(day, index) in timePicker.dates" :key="index">
              {{ day.label }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view 
              class="picker-item" 
              :class="{ 'disabled': !isHourAvailable(hour, index) }"
              v-for="(hour, index) in timePicker.hours" 
              :key="index"
            >
              {{ hour.label }}
            </view>
          </picker-view-column>
        </picker-view>
        <view class="time-picker-footer">
          <button class="confirm-btn" @click="confirmTimeSelection">确定</button>
        </view>
      </view>
    </view>

    <!-- 招募截至时间选择弹窗 -->
    <!-- 招募截至时间选择器 -->
    <view class="picker-popup" v-if="expireTimePicker.show">
      <view class="picker-overlay" @click="closeExpireTimePicker"></view>
      <view class="time-picker-content">
        <view class="time-picker-header">
          <text class="time-picker-title">选择招募截至时间</text>
          <text class="close-btn" @click="closeExpireTimePicker">×</text>
        </view>
        <picker-view
          class="time-picker-view"
          :indicator-style="`height: 50px;`"
          :value="expireTimePicker.selectedIndex"
          @change="onExpireTimePickerChange"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(day, index) in expireTimePicker.dates" :key="index">
              {{ day.label }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(hour, index) in expireTimePicker.hours" :key="index">
              {{ hour.label }}
            </view>
          </picker-view-column>
        </picker-view>
        <view class="time-picker-footer">
          <button class="confirm-btn" @click="confirmExpireTimeSelection">确定</button>
        </view>
      </view>
    </view>
    <!-- 选择时长 -->
    <uni-popup ref="durationPopup" type="bottom" background-color="#fff">
      <view class="popup-content" @touchmove.stop>
        <view class="popup-header">
          <text class="popup-title">选择时长</text>
          <!-- <uni-icons
            type="closeempty"
            size="20"
            color="#333"
            @click="closeDurationPicker"
          ></uni-icons> -->
        </view>

        <view class="duration-selector">
          <view class="duration-picker">
            <scroll-view
              class="picker-scroll"
              scroll-y
              :scroll-top="scrollTop"
              @scroll="onScroll"
              :scroll-with-animation="true"
            >
              <view class="picker-item-wrapper">
                <view
                  v-for="(item, index) in durationOptions"
                  :key="index"
                  :class="['picker-item', tempDuration === item.value ? 'selected' : '']"
                  @click="selectDuration(item.value, index)"
                >
                  <text class="duration-number">{{ item.value }}</text>
                </view>
              </view>
            </scroll-view>
            <view class="picker-indicator"></view>
          </view>
          <view class="duration-unit">
            <text class="unit-text">小时</text>
          </view>
        </view>

        <view class="popup-actions">
          <button class="cancel-btn" size="default" @click="closeDurationPicker">取消</button>
          <button class="confirm-btn" size="default" @click="confirmDuration">确定</button>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="customModal" mode="center" :mask-click="false">
      <view class="confirm-popup-content">
        <view class="title">{{ modalTitle }}</view>
        <view class="content">{{ modalMessage }}</view>
        <view class="btn-box">
          <view class="cancel" @click="closeModal">取消</view>
          <view class="confirm" @click="confirmModal">确定</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import PageHeader from '@/components/PageHeader.vue'
  import { recruitApi, addressApi, payApi } from '@/common/api'
  export default {
    components: {
      PageHeader
    },
    data() {
      return {
        form: {
          title: '',
          description: '',
          skillTemplateId: null,
          genderRequirement: 0, // 0=不限，1=男，2=女
          neededCount: 1,
          serviceStartTime: '',
          serviceDurationHours: null,
          address: '',
          longitude: null,
          latitude: null,
          pricePerPerson: null,
          expireTime: '',
          commonOrderId: '',
          // UI相关字段
          selectedSkill: null,
          balance: 3200,
          paymentMethod: 'balance',
          totalAmount: '',
          cityInput: '' // 添加贝壳币输入字段
        },
        selectPayment: 'wechat',
        // 临时选择状态
        tempSelectedSkills: '',
        tempGender: 1,
        tempDuration: 6,

        // 技能选择器相关
        selectedCategoryIndex: 0,
        categoryScrollTop: 0,
        skillScrollTop: 0,

        // 时长选择相关
        durationOptions: [
          { value: 1, label: '1小时' },
          { value: 2, label: '2小时' },
          { value: 3, label: '3小时' },
          { value: 4, label: '4小时' },
          { value: 5, label: '5小时' },
          { value: 6, label: '6小时' },
          { value: 7, label: '7小时' },
          { value: 8, label: '8小时' },
          { value: 9, label: '9小时' },
          { value: 10, label: '10小时' },
          { value: 11, label: '11小时' },
          { value: 12, label: '12小时' }
        ],
        scrollTop: 0,

        // 技能分类
        skillCategories: [],

        // 性别选项
        genderOptions: [
          { label: '男生', value: 1, icon: 'man', color: '#1296db' },
          { label: '女生', value: 2, icon: 'woman', color: '#ff69b4' },
          { label: '不限制', value: 0, icon: 'all', color: '#999' }
        ],

        // 时间选择器
        timePicker: {
          show: false,
          selectedIndex: [0, 0],
          tempIndex: [0, 0],
          dates: [],
          hours: []
        },

        // 招募截至时间选择器
        expireTimePicker: {
          show: false,
          selectedIndex: [0, 0],
          tempIndex: [0, 0],
          dates: [],
          hours: []
        },
        yue: null,
        confirmCallback: null,
        modalTitle: '',
        modalMessage: '',
        skillList: [],
        skillIndex: [0, 0]
      }
    },

    computed: {
      // 获取当前选中分类的技能列表
      currentSkills() {
        if (
          this.selectedCategoryIndex >= 0 &&
          this.selectedCategoryIndex < this.skillCategories.length
        ) {
          return this.skillCategories[this.selectedCategoryIndex].items
        }
        return []
      },

      // 性别文本显示
      genderText() {
        const genderMap = {
          0: '不限制',
          1: '男生',
          2: '女生'
        }
        return genderMap[this.form.genderRequirement] || ''
      },

      // 时长文本显示
      durationText() {
        return this.form.serviceDurationHours ? `${this.form.serviceDurationHours}小时` : ''
      },

      // 计算总金额
      calculatedTotalAmount() {
        const price = parseFloat(this.form.cityInput) || 0
        const count = this.form.neededCount || 1
        const total = price * count
        return total
        // 返回整数，避免小数点
        // return Math.round(total)
      }
    },

    watch: {
      // 监听贝壳币输入变化，自动计算总金额
      'form.cityInput': {
        handler(newVal) {
          // 更新总金额
          this.form.totalAmount = this.calculatedTotalAmount
        },
        immediate: true
      },
      // 监听人数变化，自动计算总金额
      'form.neededCount': {
        handler(newVal) {
          // 更新总金额
          this.form.totalAmount = this.calculatedTotalAmount
        },
        immediate: true
      }
    },
    onLoad() {
      // 初始化默认选择
      this.tempGender = 1
      this.form.genderRequirement = 1 // 1=男生
      this.tempDuration = 6
      this.form.serviceDurationHours = 6
      // 初始化时间选择器数据
      this.generateTimePickerData()
      this.generateExpireTimePickerData()
      this.getSkillTemplates()
      // 检查是否有本地存储的地址信息
      const selectedAddress = uni.getStorageSync('selectedAddress')
      if (selectedAddress) {
        this.address = selectedAddress
      } else {
        this.getAddressList()
      }
      this.getBalance()
      // 监听地址选择事件
      uni.$on('addressSelected', this.updateAddress)
    },
    onUnload() {
      // 页面卸载时移除事件监听
      uni.$off('addressSelected', this.updateAddress)
      // 离开页面时删除selectedAddress缓存
      uni.removeStorageSync('selectedAddress')
    },
    onReady() {
      // 页面渲染完成后，确保弹窗组件已挂载
      this.$nextTick(() => {
        if (!this.$refs.customModal) {
          console.warn('弹窗组件未正确挂载')
        }
      })
    },
    methods: {
      handleCancel() {
        this.$refs.skillPopup.close()
      },
      handleConfirm() {
        this.$refs.skillPopup.close()
        const skill = this.skillList[this.skillIndex[0]].children[this.skillIndex[1]]
        console.log(skill)
        this.form.title = skill.skillName
        this.form.selectedSkill = skill.skillName
        this.form.skillTemplateId = skill.skillCode
      },
      bindChange(e) {
        this.skillIndex = e.detail.value
        console.log(this.skillIndex)
      },
      async getAddressList() {
        const res = await addressApi.getAddressList()
        this.address = res.data.length > 0 ? res.data?.[0] : ''
        this.form.address = this.address ? this.address.detailAddress : ''
      },
      updateAddress(address) {
        this.address = address
        this.form.address = this.address.detailAddress || ''
      },
      getSkillTemplates() {
        recruitApi.getSkillTemplates().then(res => {
          console.log('技能模板列表：', res)
          // 将接口返回的数据结构转换成需要的格式
          this.skillList = Object.keys(res.data).map(key => {
            const skills = res.data[key] || []
            return {
              name: key,
              children: skills
            }
          })
        })
      },

      goBack() {
        uni.navigateBack()
      },

      // 改变人数
      changeNumber(delta) {
        const newCount = this.form.neededCount + delta
        if (newCount >= 1 && newCount <= 99) {
          this.form.neededCount = newCount
        }
      },

      // 显示技能选择器
      showSkillPicker() {
        this.tempSelectedSkills = this.form.selectedSkill
        this.$refs.skillPopup.open()
      },

      closeSkillPicker() {
        this.$refs.skillPopup.close()
      },

      // 确认技能选择
      confirmSkills() {
        this.form.selectedSkill = this.tempSelectedSkills
        // 更新表单标题为选中的技能名称
        if (this.tempSelectedSkills) {
          this.form.title = this.tempSelectedSkills.name
          this.form.skillTemplateId = this.tempSelectedSkills.id
        }
        this.closeSkillPicker()
      },

      // 选择分类
      selectCategory(index) {
        this.selectedCategoryIndex = index
        this.categoryScrollTop = index * 50
        this.skillScrollTop = 0 // 重置技能列表滚动位置
      },

      // 切换技能选择状态
      toggleSkill(skill, index) {
        console.log(skill, index)
        // 单选模式：直接设置选中的技能
        this.tempSelectedSkills = skill
        // 滚动到点击的位置
        this.skillScrollTop = index * 50
      },

      // 移除已选技能
      removeSkill(skill) {
        this.tempSelectedSkills = ''
      },

      // 分类滚动处理
      onCategoryScroll(e) {
        const scrollTop = e.detail.scrollTop
        const index = Math.round(scrollTop / 50)

        if (
          index >= 0 &&
          index < this.skillCategories.length &&
          index !== this.selectedCategoryIndex
        ) {
          this.selectedCategoryIndex = index
        }
      },

      // 技能滚动处理
      onSkillScroll(e) {
        // 可以根据需要添加滚动逻辑
      },

      // 显示性别选择器
      showGenderPicker() {
        this.$refs.genderPopup.open()
      },

      closeGenderPicker() {
        this.$refs.genderPopup.close()
      },

      // 选择性别
      selectGender(value) {
        this.tempGender = value
      },

      // 确认性别选择
      confirmGender() {
        this.form.genderRequirement = this.tempGender
        this.closeGenderPicker()
      },

      // 生成时间选择器数据
      generateTimePickerData() {
        const dates = []
        const hours = []
        const week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        const now = new Date()
        const currentHour = now.getHours()

        // 生成未来14天的日期
        for (let i = 0; i < 14; i++) {
          const date = new Date()
          date.setDate(date.getDate() + i)
          const month = (date.getMonth() + 1).toString().padStart(2, '0')
          const day = date.getDate().toString().padStart(2, '0')
          const weekDay = week[date.getDay()]

          let label = `${month}-${day}/${weekDay}`
          if (i === 0) {
            label = `今天/${weekDay}`
          }
          if (i === 1) {
            label = `明天/${weekDay}`
          }
          dates.push({
            label,
            value: `${date.getFullYear()}-${month}-${day}`,
            isToday: i === 0
          })
        }

        // 生成小时（只生成8点到22点）
        for (let i = 8; i <= 22; i++) {
          const hour = i.toString().padStart(2, '0')
          hours.push({
            label: `${hour}:00`,
            value: hour
          })
        }

        this.timePicker.dates = dates
        this.timePicker.hours = hours
      },

      // 生成招募截至时间选择器数据
      generateExpireTimePickerData() {
        const dates = []
        const hours = []
        const week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

        // 生成未来30天的日期（招募截至时间可以设置更长时间）
        for (let i = 0; i < 30; i++) {
          const date = new Date()
          date.setDate(date.getDate() + i)
          const month = (date.getMonth() + 1).toString().padStart(2, '0')
          const day = date.getDate().toString().padStart(2, '0')
          const weekDay = week[date.getDay()]

          let label = `${month}-${day}/${weekDay}`
          if (i === 0) {
            label = `今天/${weekDay}`
          }
          if (i === 1) {
            label = `明天/${weekDay}`
          }
          dates.push({
            label,
            value: `${date.getFullYear()}-${month}-${day}`
          })
        }

        // 生成小时
        for (let i = 0; i < 24; i++) {
          const hour = i.toString().padStart(2, '0')
          hours.push({
            label: `${hour}:00`,
            value: hour
          })
        }

        this.expireTimePicker.dates = dates
        this.expireTimePicker.hours = hours
      },

      // 选择服务时间
      selectServiceTime() {
        // 确保默认选择的是有效时间
        let defaultIndex = this.timePicker.selectedIndex
        const now = new Date()
        const currentHour = now.getHours()
        
        // 如果是今天，确保选择的时间不早于当前时间且在8-22范围内
        if (defaultIndex[0] === 0) {
          const actualHour = defaultIndex[1] + 8
          if (actualHour <= currentHour || actualHour < 8 || actualHour > 22) {
            // 找到下一个可用的小时（在8-22范围内）
            const nextAvailableHour = Math.max(8, Math.min(currentHour + 1, 22))
            const nextHourIndex = nextAvailableHour - 8
            defaultIndex = [0, nextHourIndex]
          }
        }
        
        this.timePicker.tempIndex = defaultIndex
        this.timePicker.show = true
      },

      // 关闭时间选择器
      closeTimePicker() {
        this.timePicker.show = false
      },

      // 检查小时是否可用
      isHourAvailable(hour, hourIndex) {
        const now = new Date()
        const currentHour = now.getHours()
        
        // 获取当前选择的日期索引
        const selectedDateIndex = this.timePicker.tempIndex ? this.timePicker.tempIndex[0] : 0
        
        // 计算实际的小时值（因为索引从8开始）
        const actualHour = hourIndex + 8
        
        // 只允许选择8点到22点的小时
        if (actualHour < 8 || actualHour > 22) {
          return false
        }
        
        // 如果是今天，只允许选择当前时间之后的小时
        if (selectedDateIndex === 0) {
          return actualHour > currentHour
        }
        
        // 其他日期都可以选择（在8-22点范围内）
        return true
      },

      // 时间选择器变化
      onTimePickerChange(e) {
        const newIndex = e.detail.value
        const dateIndex = newIndex[0]
        const hourIndex = newIndex[1]
        
        // 如果日期改变了，需要调整小时选择
        if (this.timePicker.tempIndex && this.timePicker.tempIndex[0] !== dateIndex) {
          const now = new Date()
          const currentHour = now.getHours()
          
          // 如果切换到今天，确保选择的时间不早于当前时间
          if (dateIndex === 0) {
            const actualHour = hourIndex + 8
            if (actualHour <= currentHour) {
              // 找到下一个可用的小时（在8-22范围内）
              const nextAvailableHour = Math.max(8, Math.min(currentHour + 1, 22))
              const nextHourIndex = nextAvailableHour - 8
              this.timePicker.tempIndex = [dateIndex, nextHourIndex]
              return
            }
          }
        }
        
        // 检查选择的时间是否可用
        if (this.isHourAvailable(this.timePicker.hours[hourIndex], hourIndex)) {
          this.timePicker.tempIndex = newIndex
        } else {
          // 如果选择的时间不可用，保持原来的选择
          console.log('选择的时间不可用')
        }
      },

      // 确认时间选择
      confirmTimeSelection() {
        const dateIndex = this.timePicker.tempIndex[0]
        const hourIndex = this.timePicker.tempIndex[1]

        const selectedDate = this.timePicker.dates[dateIndex]
        const selectedHour = this.timePicker.hours[hourIndex]

        // 检查选择的时间是否有效
        if (!selectedDate || !selectedHour || !this.isHourAvailable(selectedHour, hourIndex)) {
          uni.showToast({ title: '请选择有效的时间', icon: 'none' })
          return
        }

        // 格式化为 yyyy-MM-dd HH:mm:ss
        this.form.serviceStartTime = `${selectedDate.value} ${selectedHour.value}:00:00`
        this.timePicker.selectedIndex = this.timePicker.tempIndex
        this.closeTimePicker()
      },

      // 选择招募截至时间
      selectExpireTime() {
        this.expireTimePicker.tempIndex = this.expireTimePicker.selectedIndex
        this.expireTimePicker.show = true
      },

      // 关闭招募截至时间选择器
      closeExpireTimePicker() {
        this.expireTimePicker.show = false
      },

      // 招募截至时间选择器变化
      onExpireTimePickerChange(e) {
        this.expireTimePicker.tempIndex = e.detail.value
      },

      // 确认招募截至时间选择
      confirmExpireTimeSelection() {
        const dateIndex = this.expireTimePicker.tempIndex[0]
        const hourIndex = this.expireTimePicker.tempIndex[1]

        const selectedDate = this.expireTimePicker.dates[dateIndex]
        const selectedHour = this.expireTimePicker.hours[hourIndex]

        // 格式化为 yyyy-MM-dd HH:mm:ss
        this.form.expireTime = `${selectedDate.value} ${selectedHour.value}:00:00`
        this.expireTimePicker.selectedIndex = this.expireTimePicker.tempIndex
        this.closeExpireTimePicker()
      },

      // 显示时长选择器
      showDurationPicker() {
        this.$refs.durationPopup.open()
        // 滚动到当前选中的位置
        this.$nextTick(() => {
          const selectedIndex = this.durationOptions.findIndex(
            item => item.value === this.tempDuration
          )
          if (selectedIndex > -1) {
            this.scrollTop = selectedIndex * 50
          }
        })
      },

      closeDurationPicker() {
        this.$refs.durationPopup.close()
      },

      // 选择时长
      selectDuration(value, index) {
        this.tempDuration = value
        // 滚动到选中位置
        this.scrollTop = index * 50
      },

      // 处理滚动事件
      onScroll(e) {
        const scrollTop = e.detail.scrollTop
        const index = Math.round(scrollTop / 50)

        if (index >= 0 && index < this.durationOptions.length) {
          this.tempDuration = this.durationOptions[index].value
        }
      },

      // 确认时长选择
      confirmDuration() {
        this.form.serviceDurationHours = this.tempDuration
        this.closeDurationPicker()
      },

      showDistancePicker() {
        uni.showToast({ title: '距离选择功能开发中', icon: 'none' })
      },

      showAddressPicker() {
        // uni.showToast({ title: '地址选择功能开发中', icon: 'none' })
        uni.navigateTo({
          // url: '/pages/shopping/commonAddress'
          url: '/pages/profile/address'
        })
      },
      getBalance() {
        payApi.getWallet().then(res => {
          this.yue = res.data.walletAccount
        })
      },
      // 选择支付方式
      handlePaymentSelect(method) {
        console.log('method', method)
        this.selectPayment = method
      },

      // 处理支付
      handlePay() {
        if (!this.form.description.trim()) {
          uni.showToast({ title: '请填写具体要求', icon: 'none' })
          return
        }

        if (!this.form.selectedSkill) {
          uni.showToast({ title: '请选择技能', icon: 'none' })
          return
        }

        if (!this.form.cityInput || parseFloat(this.form.cityInput) <= 0) {
          uni.showToast({ title: '请输入贝壳币金额', icon: 'none' })
          return
        }

        if (!this.form.serviceStartTime) {
          uni.showToast({ title: '请选择服务时间', icon: 'none' })
          return
        }

        if (!this.form.expireTime) {
          uni.showToast({ title: '请选择招募截至时间', icon: 'none' })
          return
        }

        if (!this.form.serviceDurationHours) {
          uni.showToast({ title: '请选择服务时长', icon: 'none' })
          return
        }

        if (!this.form.address.trim()) {
          uni.showToast({ title: '请选择服务地址', icon: 'none' })
          return
        }

        // 构建API请求数据
        const requestData = {
          title: this.form.title,
          description: this.form.description,
          skillTemplateId: this.form.skillTemplateId,
          genderRequirement: this.form.genderRequirement,
          neededCount: this.form.neededCount,
          serviceStartTime: this.form.serviceStartTime,
          serviceDurationHours: this.form.serviceDurationHours,
          address: this.form.address,
          longitude: this.address.longitude,
          latitude: this.address.latitude,
          pricePerPerson: parseFloat(this.form.cityInput) || 0,
          expireTime: this.form.expireTime,
          commonOrderId: this.form.commonOrderId
        }
        if (this.selectPayment === 'wechat') {
          // 微信支付
          this.addOrder()
        } else if (this.selectPayment === 'balance') {
          // 账户余额支付
          this.handleBalancePay()
        } else if (this.selectPayment === 'alipay') {
          // 支付宝支付
          this.handleAlipayPay()
        }
        // uni.showToast({ title: '支付功能开发中', icon: 'none' })
      },
      /**
       * 账户余额支付
       */
      async handleBalancePay() {
        // 账户余额支付
        const balance = parseFloat(this.yue) || 0
        const amount = this.calculatedTotalAmount

        if (balance < amount) {
          uni.showToast({
            title: `余额不足，当前余额 ${balance}贝壳币，需要 ${amount}贝壳币`,
            icon: 'none',
            duration: 3000
          })
          return
        }

        const result = await this.showConfirmDialog(
          `是否使用账户余额支付 ${amount}贝壳币？`,
          '确认支付'
        )
        if (!result) return
        this.addOrder()
      },
      /**
       * 显示确认对话框
       */
      showConfirmDialog(content, title = '提示') {
        this.modalTitle = title
        this.modalMessage = content
        // 使用 nextTick 确保 DOM 更新后再打开弹窗
        this.$nextTick(() => {
          if (this.$refs.customModal) {
            this.$refs.customModal.open()
          } else {
            console.warn('弹窗组件未找到，使用备用方案')
            // 备用方案：使用 uni.showModal
            uni.showModal({
              title: title,
              content: content,
              success: res => {
                if (this.confirmCallback) {
                  this.confirmCallback(res.confirm)
                }
              }
            })
          }
        })
        this.confirmCallback = null // 清空之前的回调
        return new Promise(resolve => {
          this.confirmCallback = resolve
        })
      },
      /**
       * 关闭自定义弹窗
       */
      closeModal() {
        if (this.$refs.customModal) {
          this.$refs.customModal.close()
        }
        this.modalTitle = ''
        this.modalMessage = ''
        this.confirmCallback = null
      },

      /**
       * 确认自定义弹窗
       */
      confirmModal() {
        if (this.confirmCallback) {
          this.confirmCallback(true)
        }
        this.closeModal()
      },
      /**
       * 支付宝支付
       */
      handleAlipayPay() {
        uni.showToast({ title: '暂不支持支付宝支付，请使用微信或余额支付', icon: 'none' })
        return
        // #ifdef APP-PLUS
        uni.requestPayment({
          provider: 'alipay',
          orderInfo: 'your-alipay-order-info', // 支付宝订单信息
          success: res => {
            console.log('支付宝支付成功:', res)
            uni.showToast({
              title: '支付成功',
              icon: 'success'
            })
            this.handlePaySuccess()
          },
          fail: err => {
            console.log('支付宝支付失败:', err)
            if (err.errMsg.includes('cancel')) {
              uni.showToast({
                title: '支付已取消',
                icon: 'none'
              })
            } else {
              uni.showToast({
                title: '支付失败',
                icon: 'none'
              })
            }
          }
        })
        // #endif

        // #ifdef H5
        uni.showToast({
          title: 'H5环境暂不支持支付宝支付',
          icon: 'none'
        })
        // #endif
      },
      async addOrder() {
        let params = {
          payMethod: this.selectPayment === 'wechat' ? '1' : '5',
          title: this.form.title,
          description: this.form.description,
          skillTemplateId: this.form.skillTemplateId,
          genderRequirement: this.form.genderRequirement,
          neededCount: this.form.neededCount,
          serviceStartTime: this.form.serviceStartTime,
          expireTime: this.form.expireTime,
          serviceDurationHours: this.form.serviceDurationHours,
          address: this.form.address,
          longitude: this.address.longitude,
          latitude: this.address.latitude,
          pricePerPerson: this.form.cityInput
        }
        console.log(params)

        const payParams = await recruitApi.createActivity({
          ...params
        })
        if (payParams.code === 200) {
          const wxStr = payParams.data.paymentResp
          console.log('wxStr', wxStr)
          if (this.selectPayment === 'wechat') {
            uni.requestPayment({
              provider: 'wxpay',
              orderInfo: {
                appid: wxStr.appId, // 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
                noncestr: wxStr.nonceStr, // 随机字符串
                package: 'Sign=WXPay', // 固定值
                partnerid: wxStr.mchId, // 微信支付商户号
                prepayid: wxStr.prepayId, // 统一下单订单号
                timestamp: wxStr.timeStamp, // 时间戳（单位：秒）
                sign: wxStr.sign // 签名，这里用的 MD5/RSA 签名
              }, // 微信支付订单信息
              success: () => {
                uni.showToast({ title: '支付成功', icon: 'success' })
                this.handlePaySuccess(wxStr)
              },
              fail: err => {
                console.log('err', err)
                uni.showToast({ title: '支付失败', icon: 'none' })
                this.handlePayError(wxStr)
              }
            })
          } else if (this.selectPayment === 'balance') {
            if (wxStr.orderNo || wxStr.orderStatus === '1') {
              this.handlePaySuccess(wxStr)
              return
            } else {
              this.handlePayError(wxStr)
              return
            }
          }
        } else {
          uni.showToast({ title: payParams.msg, icon: 'none' })
        }
      },
      handlePaySuccess(payParams) {
        // 支付成功后的处理逻辑
        // 例如：跳转到支付成功页面、更新订单状态等
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/recruit/index'
          })
        }, 1500)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .container {
    height: 100vh;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .content {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden; // 确保scroll-view能正常工作
      position: relative; // 为scroll-view提供定位上下文
      padding-bottom: 147rpx; // 107rpx(高度) + 40rpx(上下padding) = 147rpx
    }
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: #fff;
    border-bottom: 1px solid #eee;

    .title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }
  }

  .content {
    flex: 1;
    padding: 15px;
    height: 100%; // 确保scroll-view占满可用空间
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch; // iOS滚动优化
    overflow-y: auto; // 确保能够滚动
  }

  .description-section {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;

    .description-textarea {
      width: 100%;
      min-height: 100px;
      font-size: 14px;
      color: #333;
      line-height: 1.5;
    }

    .char-count {
      position: absolute;
      right: 15px;
      bottom: 15px;
      font-size: 12px;
      color: #999;
    }
  }

  .select-item {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;

    .select-text {
      margin-left: 8px;
      font-size: 14px;
      color: rgba(153, 153, 153, 1);
    }

    .selected-skills {
      margin-left: auto;
      display: flex;
      gap: 5px;

      .skill-tag {
        background: #e8f5e8;
        color: #4caf50;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }

  .form-section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 15px;
  }

  .form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .form-label {
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }

    .form-value {
      display: flex;
      align-items: center;
      gap: 8px;

      .selected {
        color: #333;
      }

      .placeholder {
        color: #999;
      }
    }

    .number-selector {
      display: flex;
      align-items: center;
      background-color: rgba(245, 247, 249, 1);
      border-radius: 25px;
      gap: 15px;

      .number-btn {
        width: 32px;
        height: 32px;
        // border: 1px solid #e0e0e0;

        display: flex;
        align-items: center;
        justify-content: center;

        transition: all 0.3s ease;

        // &:active {
        //   background-color: #f0f0f0;
        //   border-color: #ccc;
        // }
      }

      .number-value {
        font-size: 16px;
        color: #333;
        min-width: 30px;
        text-align: center;
        font-weight: 500;
      }
    }
  }

  .city-info {
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;

    .city-name {
      color: #333;
    }

    .city-count {
      color: #4caf50;
    }

    .city-input {
      color: #999;
      margin-left: auto;
    }
  }

  .amount-section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 15px;

    .section-title {
      padding: 15px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #333;

      .insurance-info {
        margin-left: auto;
        color: #4caf50;
      }
    }
  }

  .payment-section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 15px;
  }

  .payment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .payment-left {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .payment-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.balance-icon {
        background: #e8f5e8;
      }

      &.alipay-icon {
        background: #e8f4fd;
      }

      &.wechat-icon {
        background: #e8f5e8;
      }
    }

    .payment-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .payment-name {
      font-size: 14px;
      color: #333;
    }

    .balance-amount {
      font-size: 12px;
      color: #ff6b35;
    }
  }

  .bottom-bar {
    background: #fff;
    border-top: 1px solid #eee;
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 107rpx; // 固定高度
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;

    .total-info {
      display: flex;
      align-items: center;
      gap: 4px;

      .total-label {
        font-size: 14px;
        color: #333;
      }

      .total-amount {
        font-size: 18px;
        font-weight: 500;
        color: #ff6b35;
      }

      .currency {
        font-size: 14px;
        color: #ff6b35;
      }
    }

    .pay-button {
      background: linear-gradient(135deg, #4caf50, #81c784);
      color: #fff;
      border: none;
      border-radius: 25px;
      // padding: 12px 30px;
      font-size: 16px;
      font-weight: 500;
      margin: 0;
    }
  }

  .popup-content {
    max-height: 80vh;
    display: flex;
    flex-direction: column;
  }

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    // border-bottom: 1px solid #eee;

    .popup-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .skill-selector {
    padding: 20px;
    max-height: 60vh;
    display: flex;
    flex-direction: column;
  }

  .skill-picker-container {
    display: flex;
    gap: 20px;
    height: 300px;
  }

  .category-picker,
  .skill-picker {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .picker-title {
    text-align: center;
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  .picker-scroll {
    height: 100%;
    width: 100%;
  }

  .picker-item-wrapper {
    padding: 125px 0; // 上下padding确保第一个和最后一个item能滚动到中心
  }

  .picker-item {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
  }

  .category-name,
  .skill-name {
    font-size: 16px;
    color: #ccc;
    font-weight: 300;
    transition: all 0.3s ease;
    text-align: center;
  }

  .picker-item.selected .category-name,
  .picker-item.selected .skill-name {
    color: #4caf50;
    font-size: 18px;
    font-weight: 500;
  }

  .selected-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  // 选中指示器
  // .picker-indicator {
  //   position: absolute;
  //   top: 50%;
  //   left: 0;
  //   right: 0;
  //   height: 50px;
  //   margin-top: -25px;
  //   border-top: 1px solid #eee;
  //   border-bottom: 1px solid #eee;
  //   background-color: rgba(76, 175, 80, 0.05);
  //   pointer-events: none;
  // }

  // 已选技能显示区域
  .selected-skills-display {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
  }

  .selected-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
    font-weight: 500;
  }

  .selected-skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .selected-skill-tag {
    background: #e8f5e8;
    color: #4caf50;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
      background: #d4edda;
    }

    .skill-text {
      font-size: 12px;
    }
  }

  .gender-options {
    padding: 0 20px;
  }

  .gender-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    // border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .gender-info {
      display: flex;
      align-items: center;
      gap: 12px;
      .gender-icon {
        width: 30.53rpx;
        height: 30.53rpx;
      }
    }

    .gender-text {
      font-size: 14px;
      color: #333;
    }

    &.selected {
      .gender-text {
        color: #4caf50;
      }
    }
  }

  .time-slots {
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
  }

  .time-slot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    &.disabled {
      opacity: 0.5;
    }

    .time-info {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .time-date {
      font-size: 14px;
      color: #333;
      min-width: 80px;
    }

    .time-period {
      font-size: 14px;
      color: #4caf50;
    }

    &.selected {
      .time-date,
      .time-period {
        color: #4caf50;
      }
    }
  }

  .popup-actions {
    display: flex;
    gap: 15px;
    padding: 10px 20px;
    // border-top: 1px solid #eee;

    .cancel-btn {
      // width: 330.15rpx;
      // height: 91.6rpx;
      // line-height: 91.6rpx;
      font-size: 31rpx;
      background: #f5f5f5;
      color: #666;
      // border: none;
      border-radius: 25px;
      // padding: 12px;
      // font-size: 14px;
      width: 330rpx;
      height: 92rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      &:after {
        border: none;
      }
    }

    .confirm-btn {
      width: 330rpx;
      height: 92rpx;
      // width: 330.15rpx;
      // height: 91.6rpx;
      // line-height: 91.6rpx;
      font-size: 31rpx;
      background: linear-gradient(135deg, #4caf50, #81c784);
      color: #fff;
      // border: none;
      border-radius: 25px;
      // padding: 12px;
      // font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.full-width {
        flex: none;
        width: 100%;
      }
    }
  }

  // 时长选择器样式
  .duration-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    min-height: 200px;
  }

  .duration-picker {
    position: relative;
    width: 100px;
    height: 150px;
    margin-right: 20px;
  }

  .picker-scroll {
    height: 100%;
    width: 100%;
  }

  .picker-item-wrapper {
    padding: 50px 0; // 上下padding确保第一个和最后一个item能滚动到中心
  }

  .picker-item {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .duration-number {
    font-size: 28px;
    color: #ccc;
    font-weight: 300;
    transition: all 0.3s ease;
  }

  .picker-item.selected .duration-number {
    color: #333;
    font-size: 32px;
    font-weight: 500;
  }

  // 选中指示器
  // .picker-indicator {
  //   position: absolute;
  //   top: 50%;
  //   left: 0;
  //   right: 0;
  //   height: 50px;
  //   margin-top: -25px;
  //   border-top: 1px solid #eee;
  //   border-bottom: 1px solid #eee;
  //   background-color: rgba(76, 175, 80, 0.05);
  //   pointer-events: none;
  // }

  .duration-unit {
    display: flex;
    align-items: center;
    height: 150px;
  }

  .unit-text {
    font-size: 18px;
    color: #666;
    font-weight: 400;
  }

  // 时间选择器样式
  .picker-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;

    .picker-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
    }
  }

  .time-picker-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;

    .time-picker-header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 30px;
      position: relative;
      border-bottom: 1px solid #eee;

      .time-picker-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .close-btn {
        position: absolute;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #999;
        cursor: pointer;
      }
    }

    .time-picker-view {
      width: 100%;
      height: 200px;

      .picker-item {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 17px;
        color: #333;
        
        &.disabled {
          color: #ccc;
          opacity: 0.5;
        }
      }
    }

    .time-picker-footer {
      padding: 20px 30px;

      .confirm-btn {
        background: linear-gradient(135deg, #4caf50, #81c784);
        color: #fff;
        border: none;
        border-radius: 25px;
        height: 40px;
        width: 100%;
        font-size: 16px;
        margin: 0;
      }
    }
  }
  .method-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 20rpx;
  }
  .picker-view {
    height: 400rpx;
    text-align: center;
    margin-right: 16rpx;
    background: transparent;
    display: block;

    .item {
      height: 50px;
      line-height: 50px;
      text-align: center;
      color: #666;
      font-size: 30rpx;

      &.active {
        color: #000;
        font-size: 34rpx;
      }
    }
  }
  .popup-btn-row {
    display: flex;
    justify-content: space-between;
    margin-top: 16rpx;
    margin-bottom: 54rpx;

    .btn {
      width: 328rpx;
      height: 92rpx;
      line-height: 92rpx;
      border-radius: 46rpx 46rpx 46rpx 46rpx;

      &::after {
        border: none !important;
      }

      &::before {
        border: none !important;
      }

      &.cancel-btn {
        color: #666666;
        background: #f5f5f5;
      }

      &.confirm-btn {
        color: #fff;
        background: #66d47e;
      }
    }
  }
  .confirm-popup-content {
    width: 540rpx;
    background-color: #fff;
    border-radius: 30rpx;
    padding: 40rpx 60rpx;
    box-sizing: border-box;

    .title {
      font-family: 苹方-简, 苹方-简;
      font-weight: normal;
      font-size: 30rpx;
      color: #000000;
      line-height: 42rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-bottom: 30rpx;
    }

    .content {
      font-family: 苹方-简, 苹方-简;
      font-weight: normal;
      font-size: 24rpx;
      color: #666666;
      line-height: 36rpx;
      text-align: center;
    }

    .btn-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 30rpx;

      .cancel,
      .confirm {
        width: 200rpx;
        height: 80rpx;
        border-radius: 40rpx;
      }
    }

    .cancel {
      width: 200rpx;
      height: 80rpx;
      background-color: #f5f5f5;
      border-radius: 40rpx;
      color: #666666;
      text-align: center;
      line-height: 80rpx;
    }

    .confirm {
      width: 200rpx;
      height: 80rpx;
      background: #34bc4d;
      border-radius: 40rpx;
      color: #fff;
      text-align: center;
      line-height: 80rpx;
    }
  }
</style>
