<template>
  <view class="container">
    <!-- 搜索 -->
    <view class="search-container">
      <view class="search-input">
        <uni-icons type="search" size="20" color="#000"></uni-icons>
        <input
          type="text"
          placeholder="输入技能名称查找"
          v-model="searchValue"
          @confirm="search"
          @input="onSearchInput"
          size="mini"
        />
        <uni-icons
          v-if="searchValue"
          type="clear"
          size="20"
          color="#999"
          @click="clearSearch"
        ></uni-icons>
      </view>
    </view>

    <!-- 状态标签栏 -->
    <view class="tab-container">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-item', currentTab === index ? 'active' : '']"
        @click="switchTab(index)"
      >
        <text class="tab-text">{{ tab.name }}</text>
        <view v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</view>
      </view>
    </view>
    <scroll-view
      class="tab-content"
      scroll-y
      :scroll-with-animation="false"
      @scroll="onScroll"
      :scroll-top="scrollTop"
    >
      <listItem :list="recruitList" :status="recruitStatus"></listItem>
      <!-- 加载状态 -->
      <view v-if="recruitStatus === 'loading'" class="loading-indicator">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <!-- 空状态 -->
      <view v-if="!recruitList.length && recruitStatus === 'noMore'" class="no-more-indicator">
        <text class="empty-text">暂无活动</text>
      </view>
      <!-- 没有更多数据 -->
      <view v-if="recruitList.length && recruitStatus === 'noMore'" class="no-more-indicator">
        <text class="no-more-text">已经到底了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
  // import { shopApi } from '@/common/api'
  import { useUserStore } from '@/stores/user'
  import listItem from './listItem.vue'
  export default {
    // 启用下拉刷新
    enablePullDownRefresh: true,
    // 设置下拉刷新的样式
    backgroundColor: '#f8f8f8',
    backgroundTextStyle: 'dark',
    components: {
      listItem
    },
    data() {
      return {
        scrollTimer: null,
        scrollTop: 0,
        tabs: [
          { name: '全部', count: 0, status: 0 },
          { name: '招募中', count: 0, status: 1 },
          { name: '招募成功', count: 0, status: 2 },
          { name: '招募失败', count: 0, status: 3 }
        ],
        searchValue: '', // 搜索关键词
        currentTab: 0, // 当前选中的标签
        list: [], // 列表数据
        nextCursor: null,
        recruitList: [],
        recruitStatus: 'more',
        hasMore: true, // 是否还有更多数据
        isLoading: false, // 是否正在加载
        isRefreshing: false // 是否正在刷新
      }
    },
    setup() {
      const userStore = useUserStore()

      return {
        userStore
      }
    },
    onLoad() {
      this.fetchRecruitList()
    },
    // 下拉刷新
    onPullDownRefresh() {
      // 立即停止下拉刷新
      uni.stopPullDownRefresh()
    },
    methods: {
      async fetchRecruitList(isRefresh = false) {
        if (isRefresh) {
          this.recruitList = []
          this.recruitStatus = 'more'
        }
        console.log(this.recruitStatus)
        if (this.recruitStatus === 'noMore' || this.recruitStatus === 'loading') return
        this.recruitStatus = 'loading'

        try {
          // 构造请求参数
          const params = {
            name: this.searchValue,
            pageSize: 20,
            status: this.currentTab === 0 ? '' : this.currentTab
          }

          // 如果不是刷新且有 nextCursor，则添加 cursor 参数
          if (!isRefresh && this.nextCursor) {
            params.cursor = this.nextCursor
          }
          console.log(params)
          const res = await this.userStore.getRecruitListMy(params)
          if (res.code === 200 && res.data && res.data.list) {
            // 处理返回的动态列表数据
            const newMoments = res.data.list
            this.recruitList = isRefresh ? newMoments : [...this.recruitList, ...newMoments]

            // 更新 nextCursor 用于下一页请求
            this.nextCursor = res.data.nextCursor

            // 如果 nextCursor 为 null，表示没有更多数据
            this.recruitStatus = res.data.nextCursor ? 'more' : 'noMore'
          } else {
            this.recruitStatus = 'noMore'
          }
        } catch (e) {
          console.error('获取动态列表失败:', e)
          this.recruitStatus = 'noMore'
        }
      },
      /**
       * 搜索方法
       */
      search() {
        // 重置列表和分页状态
        this.recruitList = []
        this.nextCursor = null
        this.recruitStatus = 'more'
        // 重新获取数据
        this.fetchRecruitList(true)
      },
      /**
       * 搜索输入事件处理
       */
      onSearchInput() {
        // 可以在这里添加实时搜索逻辑，比如防抖处理
        // 目前简单处理，用户按回车时触发搜索
      },
      /**
       * 清除搜索
       */
      clearSearch() {
        this.searchValue = ''
        // 清除搜索后重新获取数据
        this.search()
      },
      /**
       * 切换标签
       */
      switchTab(index) {
        console.log(index, this.currentTab)
        if (this.currentTab === index) return
        this.scrollTop = 0
        this.currentTab = index
        this.pageNum = 1
        this.recruitList = []
        this.hasMore = true
        this.fetchRecruitList(true)
      },
      onScroll(e) {
        // 清除之前的定时器
        if (this.scrollTimer) {
          clearTimeout(this.scrollTimer)
        }

        // 设置防抖定时器
        this.scrollTimer = setTimeout(() => {
          const { scrollTop, scrollHeight } = e.detail
          this.scrollTop = scrollTop

          // 获取scroll-view的高度信息
          const query = uni.createSelectorQuery().in(this)
          query
            .select('.tab-content')
            .boundingClientRect(data => {
              if (data) {
                const clientHeight = data.height
                // 计算距离底部的距离
                const distanceToBottom = scrollHeight - scrollTop - clientHeight

                // 如果距离底部小于等于100像素
                if (distanceToBottom <= 100) {
                  // 根据当前 tab 触发加载更多数据

                  // 活动 tab
                  if (this.recruitStatus === 'more' && !this.isLoadingMore) {
                    this.isLoadingMore = true
                    this.fetchRecruitList().finally(() => {
                      this.isLoadingMore = false
                    })
                  }

                  // 推荐、新人、关注标签页不再需要分页加载，因为已经一次性加载全部数据
                }
              }
            })
            .exec()
        }, 100) // 100ms防抖
      }
    }
  }
</script>

<style scoped lang="scss">
  .container {
    background: #f4f8fb;
    height: 100vh;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
  }
  .nav-title {
    font-weight: bold;
  }

  .search-container {
    padding: 20rpx;
  }

  .search-input {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-radius: 50rpx;
    padding: 0 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  .search-input input {
    flex: 1;
    height: 60rpx;
    margin-left: 15rpx;
    font-size: 28rpx;
    color: #333;
  }

  .search-input uni-icons {
    margin-right: 0;
  }
  /* 标签栏样式 */
  .tab-container {
    display: flex;
    // background: #fff;
    // border-bottom: 1rpx solid #eee;
    width: 100%;
    box-sizing: border-box;
    padding: 0 30rpx;
  }
  .tab-content {
    flex: 1;
    height: 0; /* 确保flex子元素能正确计算高度 */
    overflow: hidden;
    // background:
    //   url('@/static/index/bg.png') no-repeat top center,
    //   transparent;
    // background-size: cover;
    // background-position-y: -3px;
  }
  .tab-item {
    position: relative;
    // flex: 1;
    // width:20%;
    display: flex;
    margin-right: 60rpx;
    align-items: center;
    justify-content: center;
    height: 60rpx;
    // border-bottom: 4rpx solid transparent;
    color: rgba(0, 0, 0, 0.5);

    &.active {
      border-bottom: 4rpx solid rgba(102, 212, 126, 1);

      .tab-text {
        color: rgba(0, 0, 0, 1);
        font-weight: bold;
      }
    }
  }

  .tab-text {
    font-size: 26rpx;
    color: #666;
  }

  .tab-badge {
    position: absolute;
    top: 10rpx;
    right: 20rpx;
    background: #ff4757;
    color: #fff;
    font-size: 20rpx;
    border-radius: 20rpx;
    padding: 4rpx 12rpx;
    min-width: 32rpx;
    text-align: center;
    line-height: 1;
  }
  .loading-text {
    font-size: 28rpx;
    color: #666;
  }

  /* 没有更多数据提示样式 */
  .no-more-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    margin: 20rpx;
    border-radius: 12rpx;
  }

  .no-more-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60rpx;
  }
  /* 加载状态指示器样式 */
  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    background-color: #f8f9fa;
    margin: 20rpx;
    border-radius: 12rpx;
  }

  .loading-spinner {
    border: 4rpx solid rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
    border-radius: 50%;
    width: 60rpx;
    height: 60rpx;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
  }
</style>
