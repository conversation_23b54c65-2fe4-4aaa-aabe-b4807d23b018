<template>
  <view class="activity-list">
    <view
      v-for="(item, index) in list"
      :key="index"
      class="activity-item"
      @tap="handleItemClick(item)"
    >
      <!-- 顶部标签和优惠标签 -->
      <view class="item-header">
        <view class="item-header-left">
          <image class="tag-icon" src="/static/zhaomu/headerLeft.png" mode="heightFix" />
          <text class="tag-text">{{ item.title }}</text>
        </view>
        <view class="item-header-right">
          <image class="tag-icon" :src="getStatusImage(item.activityStatus)" mode="heightFix" />
        </view>
      </view>
      <!-- 活动描述 -->
      <view class="activity-content">
        <view class="activity-content-top">
          <!-- <view class="category-tag">
              <text class="tag-text">{{ getType(item.activityStatus) }}</text>
            </view> -->
          <text class="activity-title">{{ item.description }}</text>
        </view>
      </view>
      <!-- 日期时间 -->
      <view class="time-info">
        <image class="time-icon" src="/static/zhaomu/time.png" mode="heightFix" />
        <text class="time-text">{{ item.expireTime }}</text>
      </view>

      <!-- 地点 -->
      <view class="location-info">
        <image class="location-icon" src="/static/zhaomu/location.png" mode="heightFix" />
        <text class="location-text">{{
          getLocationFromSnapshot(item.serviceAddressSnapshot)
        }}</text>
      </view>

      <!-- 底部信息 -->
      <view class="item-footer">
        <view class="participants-info">
          <view class="image-box">
            <image
              v-for="(avatar, avatarIndex) in item.recentApplicants.slice(0, 8)"
              :key="avatarIndex"
              :src="getFullUrl(avatar.avatar)"
              class="participant-avatar"
              mode="aspectFill"
            />
          </view>

          <text class="participant-count">{{ item.applicationCount }}人已上车</text>
        </view>
        <view class="price-info">
          <text class="price">{{ item.pricePerPerson }}贝壳币</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
  export default {
    name: 'ActivityList',
    props: {
      list: {
        type: Array,
        default: () => []
      },
      status: {
        type: String,
        default: 'more'
      }
    },
    data() {
      return {
        activityList: []
      }
    },
    methods: {
      // 获取完整URL
      getFullUrl(url) {
        if (!url) return ''
        if (url.startsWith('http')) return url
        return `http://************:9000/${url}`
      },
      // 获取地址信息
      getLocationFromSnapshot(serviceAddressSnapshot) {
        try {
          if (!serviceAddressSnapshot) return '暂无地点'

          // 如果已经是对象，直接取address字段
          if (typeof serviceAddressSnapshot === 'object') {
            return serviceAddressSnapshot.address || '暂无地点'
          }

          // 如果是字符串，尝试解析为JSON
          if (typeof serviceAddressSnapshot === 'string') {
            const addressData = JSON.parse(serviceAddressSnapshot)
            return addressData.address || '暂无地点'
          }

          return '暂无地点'
        } catch (error) {
          console.error('解析地址信息失败:', error)
          return '暂无地点'
        }
      },
      handleItemClick(item) {
        console.log('点击活动:', item)
        // 这里可以跳转到活动详情页
        uni.navigateTo({
          url: `/pages/recruit/detail?activityCode=${item.activityCode}`
        })
      },
      getStatusImage(activityStatus) {
        if (activityStatus === 5 || activityStatus === 6) {
          return '/static/zhaomu/group-error.png'
        } else if (activityStatus === 2 || activityStatus === 3 || activityStatus === 4) {
          return '/static/zhaomu/group-success.png'
        } else {
          return '/static/zhaomu/group-ing.png'
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  .activity-container {
    padding-top: 20rpx;
    height: 100%;
  }

  .activity-list {
    padding: 20rpx;
  }

  .activity-item {
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    background-color: #fff;
    position: relative;
    overflow: hidden;
    // clip-path: polygon(0 100rpx, 300rpx 0, 100% 0, 100% 100%, 0 100%, 0 100rpx);
    // height:390rpx;
    > div {
      padding: 0 30rpx;
    }
    .item-header {
      padding: 0;
    }
  }

  .item-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    position: relative;
    margin-bottom: 10rpx;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 200rpx;
      height: 80rpx;
      background: transparent;
      border-radius: 0 0 20rpx 0;
      z-index: 1;
    }
    .item-header-left {
      display: flex;
      align-items: center;
      background: transparent;
      padding: 5rpx 10rpx 10rpx 0;
      .tag-icon {
        height: 24.81rpx;
        width: 24.81rpx;
        margin-right: 8rpx;
      }
      .tag-text {
        font-size: 32rpx;
        color: #000;
        font-weight: 600;
      }
    }
    .item-header-right {
      position: absolute;
      top: 0;
      right: 0;
      .tag-icon {
        height: 90.87rpx;
        width: 124.05rpx;
      }
    }
  }

  .category-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(102, 212, 126, 1);
    color: white;
    padding: 8rpx 16rpx;
    border-radius: 6rpx;
    font-size: 24rpx;
    font-weight: 500;

    .tag-icon {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }

    .tag-text {
      color: white;
    }
  }

  .discount-tag {
    background: linear-gradient(135deg, #ff5722, #f44336);
    color: white;
    padding: 8rpx 12rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
    font-weight: bold;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      right: -10rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 10rpx 0 10rpx 10rpx;
      border-color: transparent transparent transparent #f44336;
    }
  }

  .activity-content {
    padding: 0 30rpx;
    margin-bottom: 20rpx;
    &-top {
      position: relative;
      .activity-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        line-height: 1.6;
        width: 100%;
        // 多行文本省略 - 最多显示2行
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
      }
      .category-tag {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        // width: 84rpx;
        // height: 38rpx;
        font-size: 22rpx;
      }
    }
  }

  .activity-desc {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .time-info,
  .location-info {
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
    color: rgba(51, 51, 51, 1);
  }
  .location-info {
    margin-bottom: 30rpx;
  }
  .time-icon,
  .location-icon {
    width: 28rpx;
    height: 28rpx;
    margin-right: 12rpx;
    opacity: 0.7;
  }

  .time-text,
  .location-text {
    font-size: 23rpx;
    // color: #888;
    line-height: 1.4;
  }

  .item-footer {
    padding: 20rpx 0;
    margin: 30rpx 30rpx 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20rpx;
    // padding-top: 20rpx;
    border-top: 2rpx solid rgba(209, 209, 209, 1);
  }

  .participants-info {
    display: flex;
    align-items: center;
    .participant-count {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 12rpx;
    }
  }

  .avatar-group {
    display: flex;
    align-items: center;
    margin-right: 12rpx;
  }

  .participant-avatar {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid #fff;
    margin-right: -8rpx;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

    &:first-child {
      margin-right: -8rpx;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  .participant-count {
    font-size: 24rpx;
    color: #666;
    margin-left: 8rpx;
  }

  .price-info {
    display: flex;
    align-items: center;
  }

  .price {
    font-size: 32rpx;
    font-weight: 600;
    color: #4caf50;
    white-space: nowrap;

    &::after {
      content: '';
      margin-left: 4rpx;
    }
  }

  .loading-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60rpx;

    .loading-spinner {
      width: 40rpx;
      height: 40rpx;
      border: 3rpx solid #f3f3f3;
      border-top: 3rpx solid #4caf50;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 20rpx;
    }

    .loading-text {
      font-size: 26rpx;
      color: #666;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 60rpx;

    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 30rpx;
      opacity: 0.5;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .loading-text {
    font-size: 28rpx;
    color: #666;
  }

  /* 没有更多数据提示样式 */
  .no-more-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;
    // margin: 20rpx;
    border-radius: 12rpx;
  }

  .no-more-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60rpx;
  }
</style>
