<template>
	<view class="container">
		<!-- 帖子内容 -->
		<view class="post-content">
			<!-- 用户信息 -->
			<view class="user-info">
				<image class="avatar" :src="post.user.avatar" mode="aspectFill" @tap="navigateToUser"></image>
				<view class="user-detail">
					<text class="username">{{post.user.nickname}}</text>
					<text class="time">{{formatTime(post.createTime)}}</text>
				</view>
				<button class="follow-btn" v-if="!post.user.isFollowing" @tap="followUser">关注</button>
			</view>
			
			<!-- 文本内容 -->
			<text class="content">{{post.content}}</text>
			
			<!-- 图片列表 -->
			<view class="image-list" v-if="post.images && post.images.length > 0">
				<image 
					v-for="(image, index) in post.images" 
					:key="index"
					:src="image"
					mode="aspectFill"
					@tap="previewImage(index)"
					:class="['post-image', `count-${post.images.length}`]"
				></image>
			</view>
			
			<!-- 位置信息 -->
			<view class="location" v-if="post.location" @tap="openLocation">
				<text class="iconfont icon-location"></text>
				<text class="location-name">{{post.location.name}}</text>
			</view>
			
			<!-- 话题标签 -->
			<view class="topics" v-if="post.topics && post.topics.length > 0">
				<text 
					class="topic-tag"
					v-for="(topic, index) in post.topics"
					:key="index"
					@tap="navigateToTopic(topic)"
				>#{{topic}}</text>
			</view>
			
			<!-- 互动数据 -->
			<view class="interaction-bar">
				<view class="interaction-item" @tap="toggleLike">
					<text class="iconfont" :class="post.isLiked ? 'icon-like-filled' : 'icon-like'"></text>
					<text class="count">{{post.likeCount}}</text>
				</view>
				<view class="interaction-item" @tap="focusComment">
					<text class="iconfont icon-comment"></text>
					<text class="count">{{post.commentCount}}</text>
				</view>
				<view class="interaction-item" @tap="sharePost">
					<text class="iconfont icon-share"></text>
					<text class="count">{{post.shareCount}}</text>
				</view>
			</view>
		</view>
		
		<!-- 评论列表 -->
		<view class="comments-section">
			<view class="section-title">
				<text>评论 {{post.commentCount}}</text>
				<text class="sort-btn" @tap="toggleSort">{{sortType === 'time' ? '按时间' : '按热度'}}</text>
			</view>
			
			<view class="comment-list">
				<view 
					class="comment-item"
					v-for="(comment, index) in comments"
					:key="comment.id"
				>
					<image class="avatar" :src="comment.user.avatar" mode="aspectFill" @tap="navigateToUser(comment.user.id)"></image>
					<view class="comment-content">
						<view class="comment-header">
							<text class="username">{{comment.user.nickname}}</text>
							<text class="time">{{formatTime(comment.createTime)}}</text>
						</view>
						<text class="text">{{comment.content}}</text>
						<view class="comment-footer">
							<text class="reply-btn" @tap="replyComment(comment)">回复</text>
							<view class="like-btn" @tap="likeComment(comment)">
								<text class="iconfont" :class="comment.isLiked ? 'icon-like-filled' : 'icon-like'"></text>
								<text class="count">{{comment.likeCount}}</text>
							</view>
						</view>
						
						<!-- 回复列表 -->
						<view class="replies" v-if="comment.replies && comment.replies.length > 0">
							<view 
								class="reply-item"
								v-for="(reply, replyIndex) in comment.replies"
								:key="reply.id"
							>
								<text class="username">{{reply.user.nickname}}</text>
								<text v-if="reply.replyTo" class="reply-to">回复</text>
								<text v-if="reply.replyTo" class="username">{{reply.replyTo.nickname}}</text>
								<text class="text">：{{reply.content}}</text>
							</view>
							<text 
								class="view-more"
								v-if="comment.replyCount > comment.replies.length"
								@tap="loadMoreReplies(comment)"
							>查看更多回复</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 评论输入框 -->
		<view class="comment-input-wrapper">
			<input
				class="comment-input"
				v-model="commentContent"
				:placeholder="replyTo ? `回复 ${replyTo.nickname}` : '说点什么...'"
				:focus="showCommentInput"
				@blur="onCommentBlur"
				@confirm="submitComment"
				confirm-type="send"
			/>
			<text class="send-btn" :class="{active: commentContent}" @tap="submitComment">发送</text>
		</view>
	</view>
</template>

<script>
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { formatTime } from '@/utils/index.js';

export default {
	data() {
		return {
			postId: '',
			post: {
				user: {},
				content: '',
				images: [],
				location: null,
				topics: [],
				likeCount: 0,
				commentCount: 0,
				shareCount: 0,
				isLiked: false,
				createTime: Date.now()
			},
			comments: [],
			commentContent: '',
			showCommentInput: false,
			replyTo: null,
			sortType: 'time',
			page: 1,
			loading: true,
			hasMore: true,
			userStore: null
		}
	},
	
	onLoad(options) {
		this.userStore = useUserStore()
		this.postId = options.id
		this.loadPost()
	},
	
	computed: {
		userInfo() {
			return this.userStore?.userInfo
		}
	},
	
	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMoreComments();
		}
	},
	
	methods: {
		formatTime,
		
		// 加载帖子详情
		async loadPost() {
			try {
				const post = await this.$api.getPostDetail(this.postId);
				this.post = post;
			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			}
		},
		
		// 加载评论列表
		async loadComments() {
			if (this.loading) return;
			
			this.loading = true;
			try {
				const { list, total } = await this.$api.getComments({
					postId: this.postId,
					page: this.page,
					sortType: this.sortType
				});
				
				this.comments = this.page === 1 ? list : [...this.comments, ...list];
				this.hasMore = this.comments.length < total;
				this.page++;
			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 加载更多评论
		loadMoreComments() {
			this.loadComments();
		},
		
		// 切换评论排序
		toggleSort() {
			this.sortType = this.sortType === 'time' ? 'hot' : 'time';
			this.page = 1;
			this.loadComments();
		},
		
		// 预览图片
		previewImage(index) {
			uni.previewImage({
				urls: this.post.images,
				current: index
			});
		},
		
		// 打开位置
		openLocation() {
			if (!this.post.location) return;
			
			uni.openLocation({
				latitude: this.post.location.latitude,
				longitude: this.post.location.longitude,
				name: this.post.location.name,
				address: this.post.location.address
			});
		},
		
		// 跳转到话题页
		navigateToTopic(topic) {
			uni.navigateTo({
				url: `/pages/topic/detail?name=${encodeURIComponent(topic)}`
			});
		},
		
		// 跳转到用户页面
		navigateToUser(userId) {
			uni.navigateTo({
				url: `/pages/user/profile?id=${userId}`
			});
		},
		
		// 关注用户
		async followUser() {
			try {
				await this.$api.followUser(this.post.user.id);
				this.post.user.isFollowing = true;
				uni.showToast({
					title: '关注成功',
					icon: 'success'
				});
			} catch (error) {
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				});
			}
		},
		
		// 点赞帖子
		async toggleLike() {
			try {
				if (this.post.isLiked) {
					await this.$api.unlikePost(this.postId);
					this.post.likeCount--;
				} else {
					await this.$api.likePost(this.postId);
					this.post.likeCount++;
				}
				this.post.isLiked = !this.post.isLiked;
			} catch (error) {
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				});
			}
		},
		
		// 分享帖子
		sharePost() {
			uni.showShareMenu({
				withShareTicket: true,
				success() {
					console.log('打开分享菜单成功');
				}
			});
		},
		
		// 聚焦评论输入框
		focusComment() {
			this.showCommentInput = true;
			this.replyTo = null;
		},
		
		// 回复评论
		replyComment(comment) {
			this.showCommentInput = true;
			this.replyTo = comment.user;
		},
		
		// 评论输入框失焦
		onCommentBlur() {
			this.showCommentInput = false;
		},
		
		// 提交评论
		async submitComment() {
			if (!this.commentContent.trim()) return;
			
			try {
				const comment = await this.$api.createComment({
					postId: this.postId,
					content: this.commentContent,
					replyTo: this.replyTo ? this.replyTo.id : null
				});
				
				// 更新评论列表
				if (this.replyTo) {
					const parentComment = this.comments.find(c => c.id === comment.parentId);
					if (parentComment) {
						if (!parentComment.replies) {
							parentComment.replies = [];
						}
						parentComment.replies.unshift(comment);
						parentComment.replyCount++;
					}
				} else {
					this.comments.unshift(comment);
				}
				
				this.post.commentCount++;
				this.commentContent = '';
				this.replyTo = null;
				
				uni.showToast({
					title: '评论成功',
					icon: 'success'
				});
			} catch (error) {
				uni.showToast({
					title: '评论失败',
					icon: 'none'
				});
			}
		},
		
		// 点赞评论
		async likeComment(comment) {
			try {
				if (comment.isLiked) {
					await this.$api.unlikeComment(comment.id);
					comment.likeCount--;
				} else {
					await this.$api.likeComment(comment.id);
					comment.likeCount++;
				}
				comment.isLiked = !comment.isLiked;
			} catch (error) {
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				});
			}
		},
		
		// 加载更多回复
		async loadMoreReplies(comment) {
			try {
				const { list } = await this.$api.getCommentReplies({
					commentId: comment.id,
					page: Math.ceil(comment.replies.length / 10) + 1
				});
				
				comment.replies = [...comment.replies, ...list];
			} catch (error) {
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f8f8f8;
	padding-bottom: 100rpx;
}

.post-content {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
	
	.user-info {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		
		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			margin-right: 20rpx;
		}
		
		.user-detail {
			flex: 1;
			
			.username {
				font-size: 32rpx;
				color: #333;
				font-weight: 500;
			}
			
			.time {
				font-size: 24rpx;
				color: #999;
				margin-top: 4rpx;
			}
		}
		
		.follow-btn {
			height: 56rpx;
			line-height: 56rpx;
			padding: 0 30rpx;
			font-size: 26rpx;
			color: $color-primary;
			background: rgba($color-primary, 0.1);
			border-radius: 28rpx;
		}
	}
	
	.content {
		font-size: 30rpx;
		color: #333;
		line-height: 1.6;
		margin-bottom: 20rpx;
	}
	
	.image-list {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -5rpx;
		
		.post-image {
			margin: 5rpx;
			background-color: #f5f5f5;
			
			&.count-1 {
				width: 500rpx;
				height: 500rpx;
				max-width: 100%;
			}
			
			&.count-2, &.count-4 {
				width: calc(50% - 10rpx);
				height: 340rpx;
			}
			
			&.count-3, &.count-5, &.count-6, &.count-7, &.count-8, &.count-9 {
				width: calc(33.33% - 10rpx);
				height: 220rpx;
			}
		}
	}
	
	.location {
		display: flex;
		align-items: center;
		margin-top: 20rpx;
		
		.iconfont {
			font-size: 28rpx;
			color: #666;
			margin-right: 10rpx;
		}
		
		.location-name {
			font-size: 26rpx;
			color: #666;
		}
	}
	
	.topics {
		display: flex;
		flex-wrap: wrap;
		margin-top: 20rpx;
		
		.topic-tag {
			height: 48rpx;
			line-height: 48rpx;
			padding: 0 20rpx;
			background: #f5f5f5;
			border-radius: 24rpx;
			font-size: 26rpx;
			color: $color-primary;
			margin-right: 20rpx;
			margin-bottom: 10rpx;
		}
	}
	
	.interaction-bar {
		display: flex;
		justify-content: space-around;
		margin-top: 30rpx;
		padding-top: 20rpx;
		border-top: 1rpx solid #eee;
		
		.interaction-item {
			display: flex;
			align-items: center;
			
			.iconfont {
				font-size: 40rpx;
				color: #666;
				margin-right: 10rpx;
				
				&.icon-like-filled {
					color: $color-primary;
				}
			}
			
			.count {
				font-size: 26rpx;
				color: #666;
			}
		}
	}
}

.comments-section {
	background-color: #fff;
	padding: 30rpx;
	
	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		
		text {
			font-size: 30rpx;
			color: #333;
			font-weight: 500;
		}
		
		.sort-btn {
			font-size: 26rpx;
			color: #666;
		}
	}
	
	.comment-list {
		.comment-item {
			display: flex;
			margin-bottom: 30rpx;
			
			.avatar {
				width: 64rpx;
				height: 64rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}
			
			.comment-content {
				flex: 1;
				
				.comment-header {
					display: flex;
					align-items: center;
					margin-bottom: 10rpx;
					
					.username {
						font-size: 28rpx;
						color: #333;
						font-weight: 500;
					}
					
					.time {
						font-size: 24rpx;
						color: #999;
						margin-left: 20rpx;
					}
				}
				
				.text {
					font-size: 28rpx;
					color: #333;
					line-height: 1.5;
				}
				
				.comment-footer {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 10rpx;
					
					.reply-btn {
						font-size: 26rpx;
						color: #666;
					}
					
					.like-btn {
						display: flex;
						align-items: center;
						
						.iconfont {
							font-size: 32rpx;
							color: #666;
							margin-right: 6rpx;
							
							&.icon-like-filled {
								color: $color-primary;
							}
						}
						
						.count {
							font-size: 26rpx;
							color: #666;
						}
					}
				}
				
				.replies {
					margin-top: 20rpx;
					padding: 20rpx;
					background: #f8f8f8;
					border-radius: 8rpx;
					
					.reply-item {
						margin-bottom: 10rpx;
						
						.username {
							font-size: 26rpx;
							color: #333;
							font-weight: 500;
						}
						
						.reply-to {
							font-size: 26rpx;
							color: #999;
							margin: 0 10rpx;
						}
						
						.text {
							font-size: 26rpx;
							color: #333;
						}
					}
					
					.view-more {
						font-size: 26rpx;
						color: #666;
						margin-top: 10rpx;
					}
				}
			}
		}
	}
}

.comment-input-wrapper {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	padding: 20rpx 30rpx;
	background: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	display: flex;
	align-items: center;
	
	.comment-input {
		flex: 1;
		height: 72rpx;
		background: #f5f5f5;
		border-radius: 36rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		margin-right: 20rpx;
	}
	
	.send-btn {
		font-size: 30rpx;
		color: #999;
		
		&.active {
			color: $color-primary;
		}
	}
}
</style> 