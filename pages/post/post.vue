<template>
	<view class="container">
		<!-- 顶部操作栏 -->
		<view class="header">
			<text class="cancel" @tap="cancel">取消</text>
			<button class="publish-btn" :disabled="!isValid" @tap="publishPost">发布</button>
		</view>
		
		<!-- 内容编辑区 -->
		<view class="content-box">
			<textarea
				class="content-input"
				v-model="content"
				placeholder="分享新鲜事..."
				:maxlength="2000"
				:show-confirm-bar="false"
				:auto-height="true"
			></textarea>
			
			<!-- 图片列表 -->
			<view class="image-list" v-if="images.length > 0">
				<view 
					class="image-item" 
					v-for="(image, index) in images" 
					:key="index"
				>
					<image 
						:src="image" 
						mode="aspectFill" 
						@tap="previewImage(index)"
					></image>
					<text 
						class="delete-btn"
						@tap="deleteImage(index)"
					>×</text>
				</view>
			</view>
			
			<!-- 添加图片按钮 -->
			<view class="add-image" @tap="chooseImage" v-if="images.length < 9">
				<text class="iconfont icon-image"></text>
				<text class="tip">{{images.length}}/9</text>
			</view>
		</view>
		
		<!-- 底部工具栏 -->
		<view class="toolbar">
			<view class="tool-item" @tap="chooseLocation">
				<text class="iconfont icon-location"></text>
				<text>所在位置</text>
			</view>
			<view class="tool-item" @tap="chooseTopic">
				<text class="iconfont icon-topic"></text>
				<text>话题</text>
			</view>
			<view class="tool-item" @tap="toggleVisibility">
				<text class="iconfont" :class="isPublic ? 'icon-public' : 'icon-private'"></text>
				<text>{{isPublic ? '公开' : '私密'}}</text>
			</view>
		</view>
		
		<!-- 位置信息 -->
		<view class="location-info" v-if="location.name" @tap="chooseLocation">
			<text class="iconfont icon-location"></text>
			<text class="location-name">{{location.name}}</text>
			<text class="delete-btn" @tap.stop="clearLocation">×</text>
		</view>
		
		<!-- 话题标签 -->
		<view class="topic-tags" v-if="topics.length > 0">
			<view 
				class="topic-tag" 
				v-for="(topic, index) in topics" 
				:key="index"
			>
				<text>#{{topic}}</text>
				<text 
					class="delete-btn"
					@tap="deleteTopic(index)"
				>×</text>
			</view>
		</view>
	</view>
</template>

<script>
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'

export default {
	data() {
		return {
			content: '',
			images: [],
			location: {
				name: '',
				address: '',
				latitude: 0,
				longitude: 0
			},
			topics: [],
			isPublic: true,
			userStore: null
		}
	},
	
	onLoad() {
		this.userStore = useUserStore()
	},
	
	computed: {
		userInfo() {
			return this.userStore?.userInfo
		},
		
		isValid() {
			return this.content.trim() || this.images.length > 0;
		}
	},
	
	methods: {
		// 取消发布
		cancel() {
			if (this.content || this.images.length > 0) {
				uni.showModal({
					title: '提示',
					content: '是否放弃编辑？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack();
						}
					}
				});
			} else {
				uni.navigateBack();
			}
		},
		
		// 发布帖子
		async publishPost() {
			if (!this.isValid) return;
			
			uni.showLoading({
				title: '发布中...'
			});
			
			try {
				// 上传图片
				const imageUrls = [];
				for (const image of this.images) {
					const url = await this.$api.uploadImage(image);
					imageUrls.push(url);
				}
				
				// 发布帖子
				await this.$api.createPost({
					content: this.content,
					images: imageUrls,
					location: this.location.name ? this.location : null,
					topics: this.topics,
					isPublic: this.isPublic
				});
				
				uni.hideLoading();
				uni.showToast({
					title: '发布成功',
					icon: 'success'
				});
				
				// 返回并刷新列表
				const pages = getCurrentPages();
				const prevPage = pages[pages.length - 2];
				if (prevPage && prevPage.$vm.refreshList) {
					prevPage.$vm.refreshList();
				}
				
				uni.navigateBack();
			} catch (error) {
				uni.hideLoading();
				uni.showToast({
					title: '发布失败',
					icon: 'none'
				});
			}
		},
		
		// 选择图片
		chooseImage() {
			const count = 9 - this.images.length;
			uni.chooseImage({
				count,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.images = [...this.images, ...res.tempFilePaths];
				}
			});
		},
		
		// 预览图片
		previewImage(index) {
			uni.previewImage({
				urls: this.images,
				current: index
			});
		},
		
		// 删除图片
		deleteImage(index) {
			this.images.splice(index, 1);
		},
		
		// 选择位置
		chooseLocation() {
			uni.chooseLocation({
				success: (res) => {
					this.location = {
						name: res.name,
						address: res.address,
						latitude: res.latitude,
						longitude: res.longitude
					};
				}
			});
		},
		
		// 清除位置
		clearLocation() {
			this.location = {
				name: '',
				address: '',
				latitude: 0,
				longitude: 0
			};
		},
		
		// 选择话题
		chooseTopic() {
			uni.navigateTo({
				url: '/pages/topic/choose'
			});
		},
		
		// 删除话题
		deleteTopic(index) {
			this.topics.splice(index, 1);
		},
		
		// 切换可见性
		toggleVisibility() {
			this.isPublic = !this.isPublic;
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #fff;
	padding-bottom: 100rpx;
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #eee;
		
		.cancel {
			font-size: 32rpx;
			color: #666;
		}
		
		.publish-btn {
			height: 64rpx;
			line-height: 64rpx;
			padding: 0 40rpx;
			background: $color-primary;
			color: #fff;
			font-size: 28rpx;
			border-radius: 32rpx;
			
			&:disabled {
				opacity: 0.6;
			}
		}
	}
	
	.content-box {
		padding: 30rpx;
		
		.content-input {
			width: 100%;
			min-height: 200rpx;
			font-size: 28rpx;
			line-height: 1.5;
		}
		
		.image-list {
			display: flex;
			flex-wrap: wrap;
			margin: 20rpx -10rpx;
			
			.image-item {
				position: relative;
				width: calc(33.33% - 20rpx);
				margin: 10rpx;
				
				image {
					width: 100%;
					height: 200rpx;
					border-radius: 8rpx;
				}
				
				.delete-btn {
					position: absolute;
					top: -20rpx;
					right: -20rpx;
					width: 40rpx;
					height: 40rpx;
					line-height: 40rpx;
					text-align: center;
					background: rgba(0, 0, 0, 0.5);
					color: #fff;
					border-radius: 50%;
					font-size: 32rpx;
				}
			}
		}
		
		.add-image {
			width: calc(33.33% - 20rpx);
			height: 200rpx;
			margin: 10rpx;
			background: #f5f5f5;
			border-radius: 8rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			
			.iconfont {
				font-size: 48rpx;
				color: #999;
				margin-bottom: 10rpx;
			}
			
			.tip {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
	
	.toolbar {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 100rpx;
		background: #fff;
		border-top: 1rpx solid #eee;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		
		.tool-item {
			display: flex;
			align-items: center;
			margin-right: 60rpx;
			
			.iconfont {
				font-size: 40rpx;
				color: #666;
				margin-right: 10rpx;
			}
			
			text {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
	
	.location-info {
		margin: 20rpx 30rpx;
		padding: 20rpx;
		background: #f5f5f5;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		
		.iconfont {
			font-size: 32rpx;
			color: #666;
			margin-right: 10rpx;
		}
		
		.location-name {
			flex: 1;
			font-size: 28rpx;
			color: #333;
		}
		
		.delete-btn {
			width: 40rpx;
			height: 40rpx;
			line-height: 40rpx;
			text-align: center;
			font-size: 32rpx;
			color: #999;
		}
	}
	
	.topic-tags {
		display: flex;
		flex-wrap: wrap;
		margin: 20rpx 20rpx;
		
		.topic-tag {
			height: 56rpx;
			line-height: 56rpx;
			padding: 0 20rpx;
			background: #f5f5f5;
			border-radius: 28rpx;
			margin: 10rpx;
			display: flex;
			align-items: center;
			
			text {
				font-size: 28rpx;
				color: $color-primary;
			}
			
			.delete-btn {
				width: 40rpx;
				height: 40rpx;
				line-height: 40rpx;
				text-align: center;
				font-size: 32rpx;
				color: #999;
				margin-left: 10rpx;
			}
		}
	}
}
</style> 