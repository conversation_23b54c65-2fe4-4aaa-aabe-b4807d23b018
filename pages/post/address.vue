<template>
	<view class="address-page-container">
		<!-- Custom Header -->
		<view class="custom-header">
			<view class="back-button" @click="goBack">
				<text class="iconfont icon-arrow-left"></text>
			</view>
			<text class="header-title">常用地址</text>
		</view>

		<!-- Address List -->
		<scroll-view scroll-y class="address-list-scroll">
			<view class="address-item" v-for="item in addressList" :key="item.id" @click="selectAddress(item)">
				<view class="info-section">
					<view class="user-info">
						<text class="name">{{ item.name }}</text>
						<text class="phone">{{ item.phone }}</text>
					</view>
					<view class="address-text">{{ item.fullAddress }}</view>
				</view>
				<view class="divider"></view>
				<view class="actions-section">
					<view class="default-selector" @click.stop="setDefault(item)">
						<view class="checkbox" :class="{ 'checked': item.isDefault }">
							<text v-if="item.isDefault">✓</text>
						</view>
						<text>{{ item.isDefault ? '已默认' : '设为默认' }}</text>
					</view>
					<view class="icon-buttons">
						<text class="iconfont icon-edit" @click.stop="editAddress(item)"></text>
						<text class="iconfont icon-delete" @click.stop="deleteAddress(item)"></text>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- Footer -->
		<view class="footer">
			<button class="add-btn" @click="addAddress">+ 新增地址</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				addressList: [{
						id: 1,
						name: '张三',
						phone: '17300000000',
						fullAddress: '江苏省/南京市/江宁区/双龙大道1698号景枫中心写字楼B4收发室',
						isDefault: true,
					},
					{
						id: 2,
						name: '张三',
						phone: '17300000000',
						fullAddress: '江苏省/南京市/江宁区/双龙大道1698号景枫中心写字楼23层2301',
						isDefault: false,
					},
					{
						id: 3,
						name: '张三',
						phone: '17300000000',
						fullAddress: '江苏省/南京市/江宁区/双龙大道1698号景枫中心写字楼23层2301',
						isDefault: false,
					}
				],
			};
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			selectAddress(address) {
				uni.$emit('addressSelected', address);
				uni.navigateBack();
			},
			setDefault(selectedAddress) {
				this.addressList.forEach(address => {
					address.isDefault = address.id === selectedAddress.id;
				});
				// TODO: Add API call to update default address on the server
			},
			editAddress(address) {
				uni.showToast({
					title: '编辑功能待实现',
					icon: 'none'
				});
				// Example navigation:
				// uni.navigateTo({ url: `/pages/address/edit?id=${address.id}` });
			},
			deleteAddress(addressToDelete) {
				uni.showModal({
					title: '确认删除',
					content: '您确定要删除这个地址吗？',
					success: (res) => {
						if (res.confirm) {
							this.addressList = this.addressList.filter(
								address => address.id !== addressToDelete.id
							);
							// TODO: Add API call to delete address from the server
							uni.showToast({
								title: '删除成功',
								icon: 'none'
							});
						}
					}
				});
			},
			addAddress() {
				uni.showToast({
					title: '新增功能待实现',
					icon: 'none'
				});
				// Example navigation:
				// uni.navigateTo({ url: '/pages/address/add' });
			}
		}
	}
</script>

<style lang="scss">
	@import url("https://at.alicdn.com/t/c/font_3958315_82e66gve9za.css");

	.address-page-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f7f7f7;
	}

	.custom-header {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx;
		background-color: #fff;
		position: relative;
		border-bottom: 1rpx solid #eee;

		.back-button {
			position: absolute;
			left: 20rpx;
			padding: 10rpx;
			.iconfont {
				font-size: 36rpx;
			}
		}

		.header-title {
			font-size: 34rpx;
			font-weight: 500;
		}
	}

	.address-list-scroll {
		flex: 1;
		padding: 24rpx;
		box-sizing: border-box;
	}

	.address-item {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 24rpx;

		.info-section {
			.user-info {
				display: flex;
				align-items: baseline;
				margin-bottom: 16rpx;

				.name {
					font-size: 32rpx;
					font-weight: 500;
					margin-right: 20rpx;
				}

				.phone {
					font-size: 28rpx;
					color: #666;
				}
			}

			.address-text {
				font-size: 28rpx;
				color: #333;
				line-height: 1.5;
			}
		}

		.divider {
			height: 1rpx;
			background-color: #f5f5f5;
			margin: 24rpx 0;
		}

		.actions-section {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.default-selector {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: #333;

				.checkbox {
					width: 36rpx;
					height: 36rpx;
					border: 1rpx solid #ccc;
					border-radius: 50%;
					margin-right: 16rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					&.checked {
						background-color: $color-primary;
						border-color: $color-primary;
						color: #fff;
					}
				}
			}

			.icon-buttons {
				.iconfont {
					font-size: 40rpx;
					color: #888;
					margin-left: 40rpx;
				}
			}
		}
	}

	.footer {
		padding: 20rpx 30rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		.add-btn {
			background: $color-primary;
			color: #fff;
			border: none;
			border-radius: 40rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 30rpx;
		}
	}
</style> 