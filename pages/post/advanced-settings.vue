<template>
	<view class="container">
		<!-- 顶部导航 -->
		<view class="header">
			<text class="back-btn" @tap="goBack">返回</text>
			<text class="title">高级设置</text>
			<text class="save-btn" @tap="saveSettings">保存</text>
		</view>
		
		<!-- 设置列表 -->
		<view class="settings-list">
			<!-- 评论设置 -->
			<view class="setting-group">
				<view class="group-title">评论设置</view>
				<view class="setting-item" @tap="selectCommentScope">
					<view>
						<text class="setting-title">谁可以评论</text>
						<text class="setting-desc">{{ getCommentScopeText() }}</text>
					</view>
					<uni-icons type="right" size="18" color="#999"></uni-icons>
				</view>
			</view>
			
			<!-- 可见性设置 -->
			<view class="setting-group">
				<view class="group-title">可见性设置</view>
				<view class="setting-item" @tap="selectVisibleGender">
					<view>
						<text class="setting-title">性别限制</text>
						<text class="setting-desc">{{ getVisibleGenderText() }}</text>
					</view>
					<uni-icons type="right" size="18" color="#999"></uni-icons>
				</view>
			</view>
			
			<!-- 权限设置 -->
			<view class="setting-group">
				<view class="group-title">权限设置</view>
				<view class="setting-item">
					<view>
						<text class="setting-title">禁止保存图片和视频</text>
						<text class="setting-desc">其他人无法保存你的媒体内容</text>
					</view>
					<switch :checked="settings.forbidSaveMedia" @change="toggleForbidSaveMedia" color="#3c9cff" style="transform:scale(0.8)" />
				</view>
				<view class="setting-item">
					<view>
						<text class="setting-title">禁止分享</text>
						<text class="setting-desc">其他人无法分享你的动态</text>
					</view>
					<switch :checked="settings.forbidShare" @change="toggleForbidShare" color="#3c9cff" style="transform:scale(0.8)" />
				</view>
				<view class="setting-item">
					<view>
						<text class="setting-title">允许报名</text>
						<text class="setting-desc">允许其他用户报名参与</text>
					</view>
					<switch :checked="settings.allowSignup" @change="toggleAllowSignup" color="#3c9cff" style="transform:scale(0.8)" />
				</view>
			</view>
		</view>
		
		<!-- 评论范围选择弹窗 -->
		<uni-popup ref="commentScopePopup" type="bottom">
			<view class="popup-container">
				<view class="popup-header">
					<text class="popup-title">选择评论范围</text>
					<uni-icons type="closeempty" size="20" color="#999" @tap="closeCommentScopePopup"></uni-icons>
				</view>
				<view class="popup-options">
					<view class="popup-option" @tap="setCommentScope(1)">
						<view>
							<text class="option-title">所有人</text>
							<text class="option-desc">任何人都可以评论</text>
						</view>
						<uni-icons v-if="settings.allowCommentScope === 1" type="checkmarkempty" size="20" color="#3c9cff"></uni-icons>
					</view>
					<view class="popup-option" @tap="setCommentScope(2)">
						<view>
							<text class="option-title">仅好友</text>
							<text class="option-desc">只有好友可以评论</text>
						</view>
						<uni-icons v-if="settings.allowCommentScope === 2" type="checkmarkempty" size="20" color="#3c9cff"></uni-icons>
					</view>
					<view class="popup-option" @tap="setCommentScope(3)">
						<view>
							<text class="option-title">禁止评论</text>
							<text class="option-desc">不允许任何人评论</text>
						</view>
						<uni-icons v-if="settings.allowCommentScope === 3" type="checkmarkempty" size="20" color="#3c9cff"></uni-icons>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 性别限制选择弹窗 -->
		<uni-popup ref="visibleGenderPopup" type="bottom">
			<view class="popup-container">
				<view class="popup-header">
					<text class="popup-title">选择性别限制</text>
					<uni-icons type="closeempty" size="20" color="#999" @tap="closeVisibleGenderPopup"></uni-icons>
				</view>
				<view class="popup-options">
					<view class="popup-option" @tap="setVisibleGender(0)">
						<view>
							<text class="option-title">不限制</text>
							<text class="option-desc">所有性别都可以看到</text>
						</view>
						<uni-icons v-if="settings.visibleGender === 0" type="checkmarkempty" size="20" color="#3c9cff"></uni-icons>
					</view>
					<view class="popup-option" @tap="setVisibleGender(1)">
						<view>
							<text class="option-title">仅男性</text>
							<text class="option-desc">只有男性可以看到</text>
						</view>
						<uni-icons v-if="settings.visibleGender === 1" type="checkmarkempty" size="20" color="#3c9cff"></uni-icons>
					</view>
					<view class="popup-option" @tap="setVisibleGender(2)">
						<view>
							<text class="option-title">仅女性</text>
							<text class="option-desc">只有女性可以看到</text>
						</view>
						<uni-icons v-if="settings.visibleGender === 2" type="checkmarkempty" size="20" color="#3c9cff"></uni-icons>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			settings: {
				forbidSaveMedia: false,
				forbidShare: false,
				allowCommentScope: 1,
				visibleGender: 0,
				allowSignup: false
			}
		}
	},
	
	onLoad(options) {
		// 接收传递过来的设置
		if (options.settings) {
			try {
				this.settings = JSON.parse(decodeURIComponent(options.settings));
			} catch (error) {
				console.error('解析设置失败:', error);
			}
		}
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		// 保存设置
		saveSettings() {
			// 将设置传递回上一页
			const pages = getCurrentPages();
			const prevPage = pages[pages.length - 2];
			if (prevPage) {
				prevPage.options.settings = encodeURIComponent(JSON.stringify(this.settings));
			}
			
			uni.showToast({
				title: '设置已保存',
				icon: 'success'
			});
			
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		},
		
		// 获取评论范围文本
		getCommentScopeText() {
			switch (this.settings.allowCommentScope) {
				case 1: return '所有人';
				case 2: return '仅好友';
				case 3: return '禁止评论';
				default: return '所有人';
			}
		},
		
		// 获取性别限制文本
		getVisibleGenderText() {
			switch (this.settings.visibleGender) {
				case 0: return '不限制';
				case 1: return '仅男性';
				case 2: return '仅女性';
				default: return '不限制';
			}
		},
		
		// 选择评论范围
		selectCommentScope() {
			this.$refs.commentScopePopup.open();
		},
		
		closeCommentScopePopup() {
			this.$refs.commentScopePopup.close();
		},
		
		setCommentScope(value) {
			this.settings.allowCommentScope = value;
			this.closeCommentScopePopup();
		},
		
		// 选择性别限制
		selectVisibleGender() {
			this.$refs.visibleGenderPopup.open();
		},
		
		closeVisibleGenderPopup() {
			this.$refs.visibleGenderPopup.close();
		},
		
		setVisibleGender(value) {
			this.settings.visibleGender = value;
			this.closeVisibleGenderPopup();
		},
		
		// 切换开关
		toggleForbidSaveMedia(e) {
			this.settings.forbidSaveMedia = e.detail.value;
		},
		
		toggleForbidShare(e) {
			this.settings.forbidShare = e.detail.value;
		},
		
		toggleAllowSignup(e) {
			this.settings.allowSignup = e.detail.value;
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f8f8f8;
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
		
		.back-btn {
			font-size: 32rpx;
			color: #666;
		}
		
		.title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
		
		.save-btn {
			font-size: 32rpx;
			color: #3c9cff;
		}
	}
	
	.settings-list {
		.setting-group {
			margin-top: 20rpx;
			background-color: #fff;
			
			.group-title {
				padding: 20rpx 30rpx;
				font-size: 28rpx;
				color: #999;
				background-color: #f8f8f8;
			}
			
			.setting-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx;
				font-size: 30rpx;
				color: #333;
				
				&:not(:last-child) {
					border-bottom: 1rpx solid #f5f5f5;
				}
				
				.setting-title {
					font-size: 30rpx;
					color: #333;
				}
				
				.setting-desc {
					font-size: 24rpx;
					color: #999;
					margin-top: 4rpx;
				}
			}
		}
	}
}

.popup-container {
	background-color: #fff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.popup-options {
		.popup-option {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			
			&:not(:last-child) {
				border-bottom: 1rpx solid #f5f5f5;
			}
			
			.option-title {
				font-size: 30rpx;
				color: #333;
			}
			
			.option-desc {
				font-size: 24rpx;
				color: #999;
				margin-top: 4rpx;
			}
		}
	}
}
</style> 