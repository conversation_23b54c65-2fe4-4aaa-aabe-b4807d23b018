<template>
	<view class="choose-bg">
		<view class="top-section">
			<text class="slogan-title"> &nbsp; </text>
			<text class="slogan-desc">
				<text class="island-bold"> &nbsp; 	</text> 
			</text>
			<image class="island-img" src="/static/island.png" mode="widthFix" />
		</view>
		<view class="bottom-section">
			<view class="action-btn activity-btn" @tap="navigateToActivity">
				<image class="btn-icon" src="/static/index/activity.png" mode="aspectFit" />
				<view class="btn-text-container">
					<view class="btn-text">发起招募</view>
					<view class="desc-item">寻找共同兴趣的人</view>
				</view>
			</view>
			<view class="action-btn post-btn" @tap="navigateToPost">
				<image class="btn-icon" src="/static/index/post.png" mode="aspectFit" />
				<view class="btn-text-container">
					<view class="btn-text">发布动态</view>
					<view class="desc-item">分享你的故事</view>
				</view>
			</view>
		</view>
		<view class="bottom-desc" @tap="goBack">
			<image class="close-img" src="/static/index/close.png" mode="aspectFit" />
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			
		}
	},
	
	onLoad() {
		// 页面加载时自动显示弹框
	},
	
	methods: {
		// 跳转到活动发布页面
		navigateToActivity() {
			uni.navigateTo({
				url: '/pages/recruit/creat'
			});
		},
		
		// 跳转到动态发布页面
		navigateToPost() {
			uni.navigateTo({
				url: '/pages/post/dynamic'
			});
		},
		
		// 返回首页
		goBack() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.choose-bg {
	min-height: 100vh;
	width: 100vw;
	background: url('@/static/post/bg.png') no-repeat center center;
	background-size: cover;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	padding: 0;
	z-index: 1000;
    position: relative;
}

.top-section {
	width: 100vw;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 120rpx;
	margin-bottom: 60rpx;
}
.slogan-title {
	font-size: 32rpx;
	color: #444;
	margin-bottom: 18rpx;
	letter-spacing: 2rpx;
}
.slogan-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 30rpx;
	.island-bold {
		font-weight: bold;
		color: #222;
	}
}
.island-img {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 0;
}

.bottom-section {
	width: 100vw;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: flex-end;
	margin-bottom: 40rpx;
	gap: 80rpx;
}
.action-btn {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin: 0 20rpx;
	position: relative;
}
.btn-icon {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 10rpx;
}
.btn-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

.bottom-desc {
	width: 100vw;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	margin-bottom: 60rpx;
	cursor: pointer;
}
.desc-item {
	font-size: 22rpx;
	color: #aaa;
	white-space: nowrap;
}
.btn-text-container{
	position: absolute;
    bottom: -50px;
    width: 100px;
    text-align: center;
    line-height: 21px;
}

.close-img {
	width: 40rpx;
	height: 40rpx;
}
</style> 