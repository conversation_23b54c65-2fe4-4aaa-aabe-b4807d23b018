<template>
	<view class="container">
		<!-- 顶部操作栏 -->
		<view class="header">
			<uni-icons type="closeempty" size="24" color="#666" @tap="cancel"></uni-icons>
			<text class="page-title">发布动态</text>
			<button class="publish-btn" :disabled="!isValid" @tap="publishDynamic">发布</button>
		</view>
		
		<!-- 内容编辑区 -->
		<view class="content-box">
			<textarea
				class="content-input"
				v-model="content"
				placeholder="那些埋在心底的歌, 写出来便是你的故事..."
				:maxlength="2000"
				:show-confirm-bar="false"
				:auto-height="true"
			></textarea>
			
			<!-- 图片列表 -->
			<view class="image-list" v-if="images.length > 0">
				<view 
					class="image-item" 
					v-for="(image, index) in images" 
					:key="index"
				>
					<image 
						:src="image" 
						mode="aspectFill" 
						@tap="previewImage(index)"
					></image>
					<text 
						class="delete-btn"
						@tap="deleteImage(index)"
					>×</text>
				</view>
			</view>
			
			<!-- 录音文件显示 -->
			<view class="audio-container" v-if="audioFilePath">
				<view class="audio-item">
					<view class="audio-info">
						<uni-icons type="sound" size="20" color="#00D08C"></uni-icons>
						<text class="audio-duration">{{ formatTimeDisplay() }}</text>
					</view>
					<view class="audio-controls">
						<view class="play-btn" @tap="togglePlay">
							<uni-icons :type="isPlaying ? 'pause' : 'play'" size="24" color="#00D08C"></uni-icons>
						</view>
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: playProgress + '%' }"></view>
						</view>
					</view>
					<text class="delete-btn" @tap="deleteAudio">×</text>
				</view>
			</view>
			
			<!-- 录音指示器 -->
			<view class="recording-indicator" v-if="recordingState === 'recording'">
				<view class="recording-content">
					<view class="recording-icon">
						<view class="pulse-dot"></view>
					</view>
					<text class="recording-text">录音中...</text>
					<text class="recording-tip">点击停止按钮结束录音</text>
				</view>
			</view>
		</view>
		
		<!-- 选项列表 -->
		<view class="options-list">
			<view class="option-item" @tap="selectVisibility">
				<text>{{ visibility }}</text>
				<uni-icons type="right" size="18" color="#999"></uni-icons>
			</view>
			<view class="option-item" @tap="goToAdvancedSettings">
				<text>高级设置</text>
				<uni-icons type="right" size="18" color="#999"></uni-icons>
			</view>
			<view class="option-item">
				<view>
					<text>限时模式</text>
					<view class="option-desc">24小时后变为仅你自己可见</view>
				</view>
				<switch :checked="isLimitedTime" @change="toggleLimitedTime" color="#00D08C" style="transform:scale(0.8)" />
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="footer">
			<view class="media-buttons">
				<view class="media-btn" @tap="chooseImage">
					<uni-icons type="camera" size="24" color="#666"></uni-icons>
					<!-- <text>照片/视频</text> -->
				</view>
				<view class="media-btn" @tap="handleRecord">
					<uni-icons 
						:type="recordingState === 'recording' ? 'stop' : 'mic'" 
						size="24" 
						:color="recordingState === 'recording' ? '#ff4757' : '#666'"
					></uni-icons>
					<!-- <text>录音</text> -->
				</view>
			</view>
		</view>
		
		<!-- 可见性选择弹窗 -->
		<uni-popup ref="visibilityPopup" type="bottom">
			<view class="visibility-popup-container">
				<view class="popup-header">
					<text class="popup-title">选择可见范围</text>
					<uni-icons type="closeempty" size="20" color="#999" @tap="closeVisibilityPopup"></uni-icons>
				</view>
				<view class="visibility-options">
					<view class="visibility-option" @tap="setVisibility('广场可见')">
						<view>
							<text class="option-title">广场可见</text>
							<text class="option-desc">所有人可见</text>
						</view>
						<uni-icons v-if="visibility === '广场可见'" type="checkmarkempty" size="20" color="#3c9cff"></uni-icons>
					</view>
					<view class="visibility-option" @tap="setVisibility('仅自己可见')">
						<view>
							<text class="option-title">仅自己可见</text>
							<text class="option-desc">内容仅自己可见</text>
						</view>
						<uni-icons v-if="visibility === '仅自己可见'" type="checkmarkempty" size="20" color="#3c9cff"></uni-icons>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'

export default {
	data() {
		return {
			content: '',
			images: [],
			visibility: '广场可见',
			isLimitedTime: false,
			userStore: null,
			// 录音相关状态
			recordingState: 'idle', // idle, recording, recorded
			audioFilePath: '', // 录音文件路径
			audioDuration: 0, // 录音时长（秒）
			audioContext: null, // 音频上下文
			recorderManager: null, // 录音管理器
			isPlaying: false, // 是否正在播放
			playProgress: 0, // 播放进度
			currentPlayTime: 0, // 当前播放时间（秒）
			// 高级设置相关
			seniorSettings: {
				forbidSaveMedia: false, // 禁止保存媒体
				forbidShare: false, // 禁止分享
				allowCommentScope: 1, // 评论范围：1-所有人，2-好友，3-禁止评论
				visibleGender: 0, // 性别限制：0-不限制，1-仅男性，2-仅女性
				allowSignup: false // 允许报名
			}
		}
	},
	
	onLoad() {
		this.userStore = useUserStore()
		
		// 初始化录音管理器
		this.initRecorderManager()
		
		// 获取状态栏高度并设置为CSS变量
		this.setStatusBarHeight()
	},
	
	onShow() {
		// 检查是否有从高级设置页面返回的数据
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		if (currentPage.options.settings) {
			try {
				this.seniorSettings = JSON.parse(decodeURIComponent(currentPage.options.settings));
			} catch (error) {
				console.error('解析高级设置失败:', error);
			}
		}
	},
	
	onUnload() {
		// 页面销毁时清理音频资源
		if (this.audioContext) {
			this.audioContext.stop();
			this.audioContext.destroy();
			this.audioContext = null;
		}
		
		// 停止录音（如果正在录音）
		if (this.recordingState === 'recording' && this.recorderManager) {
			this.recorderManager.stop();
		}
	},
	
	computed: {
		userInfo() {
			return this.userStore?.userInfo
		},
		
		isValid() {
			return this.content.trim() || this.images.length > 0 || this.audioFilePath;
		}
	},
	
	methods: {
		// 取消发布
		cancel() {
			if (this.content || this.images.length > 0 || this.audioFilePath) {
				uni.showModal({
					title: '提示',
					content: '确定要退出编辑吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack();
						}
					}
				});
			} else {
				uni.navigateBack();
			}
		},
		
		// 发布动态
		async publishDynamic() {
			if (!this.isValid) return;
			
			uni.showLoading({
				title: '发布中...'
			});
			
			try {
				console.log('开始发布动态，图片数量:', this.images.length);
				
				// 上传图片并获取图片ID列表
				const imageIds = [];
				for (let i = 0; i < this.images.length; i++) {
					const image = this.images[i];
					console.log(`开始上传第${i + 1}张图片:`, image);
					
					try {
						const uploadResult = await this.uploadImage(image);
						console.log(`第${i + 1}张图片上传成功:`, uploadResult);
						
						// 从上传结果中获取图片ID
						const imageId = uploadResult.data.imageId;
						if (imageId) {
							imageIds.push(imageId);
						} else {
							throw new Error('图片上传成功但未获取到图片ID');
						}
					} catch (uploadError) {
						console.error(`第${i + 1}张图片上传失败:`, uploadError);
						throw new Error(`图片上传失败: ${uploadError.message}`);
					}
				}
				
				console.log('所有图片上传完成，图片ID列表:', imageIds);
				
				// 上传录音文件
				let audioId = null;
				if (this.audioFilePath) {
					console.log('开始上传录音文件:', this.audioFilePath);
					try {
						const audioUploadResult = await this.uploadAudio(this.audioFilePath);
						console.log('录音文件上传成功:', audioUploadResult);
						
						// 从上传结果中获取录音ID
						audioId = audioUploadResult.data.audioId;
						if (!audioId) {
							throw new Error('录音上传成功但未获取到录音ID');
						}
					} catch (uploadError) {
						console.error('录音文件上传失败:', uploadError);
						throw new Error(`录音上传失败: ${uploadError.message}`);
					}
				}
				
				// 根据可见性设置确定visibleScope值
				let visibleScope = 1; // 默认公开
				if (this.visibility === '仅自己可见') {
					visibleScope = 3; // 私密
				} else if (this.visibility === '广场可见') {
					visibleScope = 1; // 公开
				}
				
				// 构建发布参数 - 按照接口说明的格式
				const postData = {
					userId: this.userInfo?.userId || this.userInfo?.loginId || '',
					postText: this.content,
					imageIds: imageIds,
					audioId: audioId,
					visibleScope: visibleScope
				};
				
				console.log('发布数据构建完成:', postData);
				
				// 发布动态
				const { post } = await import('@/common/request');
				console.log('开始调用 /moments/post 接口...');
				console.log('请求参数:', JSON.stringify(postData, null, 2));
				
				const result = await post('/moments/post', postData);
				console.log('发布成功，返回结果:', result);
				
				// 检查返回结果
				if (result && result.code === 200) {
					console.log('接口调用成功，返回数据:', result.data);
					
					uni.hideLoading();
					uni.showToast({
						title: '发布成功',
						icon: 'success'
					});
					
					// 发布成功后跳转到首页
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/index/index'
						});
					}, 1500); // 延迟1.5秒，让用户看到成功提示
				} else {
					console.warn('接口返回异常:', result);
					throw new Error(result.msg || '发布失败');
				}
			} catch (error) {
				console.error('发布失败:', error);
				uni.hideLoading();
				
				// 根据错误类型显示不同的提示
				let errorMessage = '发布失败';
				if (error.message && error.message.includes('图片上传失败')) {
					errorMessage = '图片上传失败，请重试';
				} else if (error.message && error.message.includes('录音上传失败')) {
					errorMessage = '录音上传失败，请重试';
				} else if (error.message && error.message.includes('网络')) {
					errorMessage = '网络连接失败，请检查网络';
				} else if (error.message && error.message.includes('token')) {
					errorMessage = '登录已过期，请重新登录';
				} else if (error.message) {
					errorMessage = error.message;
				}
				
				uni.showToast({
					title: errorMessage,
					icon: 'none'
				});
			}
		},
		
		// 上传图片
		async uploadImage(filePath) {
			const { uploads } = await import('@/common/request');
			
			// #ifdef APP-PLUS
			// 原生包环境下的路径处理
			try {
				// 检查文件是否存在
				const fileExists = await new Promise(resolve => {
					plus.io.resolveLocalFileSystemURL(
						filePath,
						() => resolve(true),
						() => resolve(false)
					)
				});

				if (!fileExists) {
					throw new Error('文件不存在: ' + filePath);
				}

				// 统一转换文件路径
				if (filePath.startsWith('file:///')) {
					// 已经是完整路径，直接使用
					console.log('使用完整路径:', filePath);
				} else {
					// 转换路径为完整路径
					const convertedPath = plus.io.convertLocalFileSystemURL(filePath);
					if (convertedPath) {
						filePath = convertedPath;
						console.log('转换后的路径:', filePath);
					} else {
						console.warn('路径转换失败，使用原始路径');
					}
				}
			} catch (error) {
				console.error('原生包路径处理失败:', error);
				// 如果路径处理失败，继续使用原始路径
			}
			// #endif
			
			try {
				const result = await uploads('/moments/upload/images', filePath);
				console.log('图片上传结果:', result);
				
				// 根据新的接口返回格式处理数据
				if (result && result.code === 200 && result.data && result.data.images && result.data.images.length > 0) {
					// 获取第一张图片的信息
					const imageInfo = result.data.images[0];
					
					// 返回标准格式，包含图片ID和URL
					return {
						data: {
							imageId: imageInfo.imageId,
							url: imageInfo.compressedUrl || imageInfo.originalUrl,
							originalUrl: imageInfo.originalUrl,
							thumbnailUrl: imageInfo.thumbnailUrl,
							size: imageInfo.fileSize || 0,
							width: imageInfo.width || 0,
							height: imageInfo.height || 0,
							sortOrder: imageInfo.sortOrder || 0
						}
					};
				}
				
				// 如果返回格式不符合预期，返回默认格式
				return {
					data: {
						imageId: `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
						url: result.data?.images?.[0]?.compressedUrl || result.data?.images?.[0]?.originalUrl || '',
						originalUrl: result.data?.images?.[0]?.originalUrl || '',
						thumbnailUrl: result.data?.images?.[0]?.thumbnailUrl || '',
						size: result.data?.images?.[0]?.fileSize || 0,
						width: result.data?.images?.[0]?.width || 0,
						height: result.data?.images?.[0]?.height || 0,
						sortOrder: result.data?.images?.[0]?.sortOrder || 0
					}
				};
			} catch (error) {
				console.error('上传图片失败:', error);
				throw error;
			}
		},
		
		// 上传录音文件
		async uploadAudio(filePath) {
			const { uploads } = await import('@/common/request');
			
			// #ifdef APP-PLUS
			// 原生包环境下的路径处理
			try {
				// 检查文件是否存在
				const fileExists = await new Promise(resolve => {
					plus.io.resolveLocalFileSystemURL(
						filePath,
						() => resolve(true),
						() => resolve(false)
					)
				});

				if (!fileExists) {
					throw new Error('录音文件不存在: ' + filePath);
				}

				// 统一转换文件路径
				if (filePath.startsWith('file:///')) {
					// 已经是完整路径，直接使用
					console.log('使用完整录音路径:', filePath);
				} else {
					// 转换路径为完整路径
					const convertedPath = plus.io.convertLocalFileSystemURL(filePath);
					if (convertedPath) {
						filePath = convertedPath;
						console.log('转换后的录音路径:', filePath);
					} else {
						console.warn('录音路径转换失败，使用原始路径');
					}
				}
			} catch (error) {
				console.error('原生包录音路径处理失败:', error);
				// 如果路径处理失败，继续使用原始路径
			}
			// #endif
			
			try {
				// 使用通用文件上传接口，指定bucket为audio
				const result = await uploads('/moments/upload/audio', filePath);
				console.log('录音上传结果:', result);
				
				// 根据接口返回格式处理数据
				if (result && result.code === 200 && result.data) {
					const fileInfo = result.data;
					
					// 返回标准格式，包含录音ID和URL
					return {
						data: {
							audioId: fileInfo.audioId || fileInfo.id || `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
							url: fileInfo.url || fileInfo.fileUrl || '',
							duration: this.audioDuration,
							size: fileInfo.size || 0,
							format: 'mp3'
						}
					};
				}
				
				// 如果返回格式不符合预期，返回默认格式
				return {
					data: {
						audioId: `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
						url: result.data?.url || result.data?.fileUrl || '',
						duration: this.audioDuration,
						size: result.data?.size || 0,
						format: 'mp3'
					}
				};
			} catch (error) {
				console.error('上传录音失败:', error);
				throw error;
			}
		},
		
		// 选择图片
		chooseImage() {
			const count = 9 - this.images.length;
			
			// #ifdef MP-WEIXIN
			// 微信小程序使用chooseMedia接口
			uni.chooseMedia({
				count,
				mediaType: ['image'],
				sourceType: ['album', 'camera'],
				sizeType: ['compressed'],
				success: (res) => {
					const tempFilePaths = res.tempFiles.map(item => item.tempFilePath);
					this.images = [...this.images, ...tempFilePaths];
				}
			});
			// #endif
			
			// #ifndef MP-WEIXIN
			// 其他平台使用chooseImage接口
			uni.chooseImage({
				count,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.images = [...this.images, ...res.tempFilePaths];
				}
			});
			// #endif
		},
		
		// 预览图片
		previewImage(index) {
			let urls;
			
			// #ifdef APP-PLUS
			// 原生包环境下处理图片路径
			urls = this.images.map(path => {
				if (path.startsWith('file:///')) {
					return path;
				} else {
					// 尝试转换路径
					const convertedPath = plus.io.convertLocalFileSystemURL(path);
					return convertedPath || path;
				}
			});
			// #endif
			
			// #ifndef APP-PLUS
			urls = this.images;
			// #endif
			
			uni.previewImage({
				urls: urls,
				current: index
			});
		},
		
		// 删除图片
		deleteImage(index) {
			this.images.splice(index, 1);
		},
		
		// 播放/暂停录音
		togglePlay() {
			console.log('togglePlay 被调用，当前播放状态:', this.isPlaying);
			console.log('录音文件路径:', this.audioFilePath);
			
			if (this.isPlaying) {
				this.pauseAudio();
			} else {
				this.playAudio();
			}
		},
		
		// 播放录音
		playAudio() {
			console.log('playAudio 被调用');
			if (!this.audioFilePath) {
				console.error('录音文件路径为空');
				uni.showToast({
					title: '没有录音文件',
					icon: 'none'
				});
				return;
			}
			
			console.log('开始播放录音文件:', this.audioFilePath);
			
			// 处理文件路径
			let audioSrc = this.audioFilePath;
			
			// #ifdef APP-PLUS
			// 原生包环境下处理音频文件路径
			if (!audioSrc.startsWith('file:///')) {
				// 转换路径为完整路径
				const convertedPath = plus.io.convertLocalFileSystemURL(audioSrc);
				if (convertedPath) {
					audioSrc = convertedPath;
					console.log('转换后的音频路径:', audioSrc);
				} else {
					console.warn('音频路径转换失败，使用原始路径');
				}
			}
			
			// 检查文件是否存在
			plus.io.resolveLocalFileSystemURL(
				audioSrc,
				() => {
					console.log('音频文件存在，开始播放');
					this.doPlayAudio(audioSrc);
				},
				() => {
					console.error('音频文件不存在:', audioSrc);
					uni.showToast({
						title: '录音文件不存在',
						icon: 'none'
					});
				}
			);
			// #endif
			
			// #ifndef APP-PLUS
			// 非APP环境直接播放
			this.doPlayAudio(audioSrc);
			// #endif
		},
		
		// 执行播放音频
		doPlayAudio(audioSrc) {
			// 创建音频上下文
			if (!this.audioContext) {
				console.log('创建新的音频上下文');
				this.audioContext = uni.createInnerAudioContext();
				
				// 监听播放开始
				this.audioContext.onPlay(() => {
					console.log('音频开始播放');
					this.isPlaying = true;
				});
				
				// 监听播放进度
				this.audioContext.onTimeUpdate(() => {
					if (this.audioContext.duration > 0) {
						this.playProgress = (this.audioContext.currentTime / this.audioContext.duration) * 100;
						this.currentPlayTime = Math.floor(this.audioContext.currentTime);
						console.log('播放进度:', this.playProgress.toFixed(2) + '%', '当前时间:', this.currentPlayTime + 's');
					}
				});
				
				// 监听播放结束
				this.audioContext.onEnded(() => {
					console.log('播放结束');
					this.isPlaying = false;
					this.playProgress = 0;
					this.currentPlayTime = 0;
				});
				
				// 监听播放错误
				this.audioContext.onError((res) => {
					console.error('播放错误:', res);
					this.isPlaying = false;
					this.currentPlayTime = 0;
					uni.showToast({
						title: '播放失败: ' + (res.errMsg || '未知错误'),
						icon: 'none',
						duration: 3000
					});
				});
				
				// 监听音频加载完成
				this.audioContext.onCanplay(() => {
					console.log('音频加载完成，可以播放');
				});
			}
			
			// 设置音频源
			this.audioContext.src = audioSrc;
			console.log('音频源已设置:', this.audioContext.src);
			
			// 开始播放
			try {
				this.audioContext.play();
				console.log('播放命令已发送');
			} catch (error) {
				console.error('播放命令执行失败:', error);
				uni.showToast({
					title: '播放失败',
					icon: 'none'
				});
			}
		},
		
		// 暂停播放
		pauseAudio() {
			console.log('pauseAudio 被调用');
			if (this.audioContext) {
				try {
					this.audioContext.pause();
					this.isPlaying = false;
					console.log('播放已暂停');
				} catch (error) {
					console.error('暂停播放失败:', error);
				}
			} else {
				console.warn('音频上下文不存在');
			}
		},
		
		// 删除录音
		deleteAudio() {
			uni.showModal({
				title: '提示',
				content: '确定要删除录音吗？',
				success: (res) => {
					if (res.confirm) {
						// 停止播放
						if (this.audioContext) {
							this.audioContext.stop();
							this.audioContext.destroy();
							this.audioContext = null;
						}
						
						// 重置状态
						this.recordingState = 'idle';
						this.audioFilePath = '';
						this.audioDuration = 0;
						this.isPlaying = false;
						this.playProgress = 0;
						this.currentPlayTime = 0;
					}
				}
			});
		},
		
		// 格式化时长显示
		formatDuration(seconds) {
			const minutes = Math.floor(seconds / 60);
			const remainingSeconds = seconds % 60;
			return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
		},
		
		// 格式化时间显示（播放时显示当前时间/总时间）
		formatTimeDisplay() {
			if (this.isPlaying) {
				// 播放时显示当前时间/总时间
				const currentTime = this.formatDuration(this.currentPlayTime);
				const totalTime = this.formatDuration(this.audioDuration);
				return `${currentTime} / ${totalTime}`;
			} else {
				// 非播放时显示总时长
				return this.formatDuration(this.audioDuration);
			}
		},
		
		// 选择可见性
		selectVisibility() {
			this.$refs.visibilityPopup.open();
		},
		
		closeVisibilityPopup() {
			this.$refs.visibilityPopup.close();
		},

		setVisibility(value) {
			this.visibility = value;
			this.closeVisibilityPopup();
		},
		
		// 高级设置
		goToAdvancedSettings() {
			// 将当前设置传递到高级设置页面
			uni.navigateTo({
				url: `/pages/post/advanced-settings?settings=${encodeURIComponent(JSON.stringify(this.seniorSettings))}`
			});
		},
		
		// 切换限时模式
		toggleLimitedTime(e) {
			this.isLimitedTime = e.detail.value;
		},
		
		// 初始化录音管理器
		initRecorderManager() {
			console.log('初始化录音管理器');
			// 创建录音管理器
			this.recorderManager = uni.getRecorderManager();
			console.log('录音管理器创建成功:', this.recorderManager);
			
			// 设置事件监听器
			this.recorderManager.onStart(() => {
				console.log('录音开始事件触发');
				uni.showToast({
					title: '录音中...',
					icon: 'none',
					duration: 1000
				});
			});
			
			this.recorderManager.onStop((res) => {
				console.log('录音结束事件触发，结果:', res);
				this.recordingState = 'recorded';
				this.audioFilePath = res.tempFilePath;
				
				// 处理录音时长
				if (res.duration) {
					// 支付宝小程序返回的是秒，其他平台返回的是毫秒
					// #ifdef MP-ALIPAY
					this.audioDuration = Math.floor(res.duration);
					// #endif
					// #ifndef MP-ALIPAY
					this.audioDuration = Math.floor(res.duration / 1000);
					// #endif
				} else {
					this.audioDuration = 0;
				}
				
				console.log('录音完成，文件路径:', this.audioFilePath);
				console.log('录音时长:', this.audioDuration, '秒');
				console.log('录音文件大小:', res.fileSize || '未知', '字节');
				
				// 检查文件是否存在
				// #ifdef APP-PLUS
				if (this.audioFilePath) {
					plus.io.resolveLocalFileSystemURL(
						this.audioFilePath,
						() => {
							console.log('录音文件存在，可以播放');
						},
						() => {
							console.error('录音文件不存在:', this.audioFilePath);
						}
					);
				}
				// #endif
				
				uni.showToast({
					title: '录音完成',
					icon: 'success'
				});
			});
			
			this.recorderManager.onError((res) => {
				console.error('录音错误事件触发:', res);
				this.recordingState = 'idle';
				uni.showToast({
					title: '录音失败: ' + (res.errMsg || '未知错误'),
					icon: 'none',
					duration: 3000
				});
			});
			
			// 监听录音中断事件（微信小程序）
			// #ifdef MP-WEIXIN
			this.recorderManager.onInterruptionBegin(() => {
				console.log('录音被中断');
				this.recordingState = 'idle';
				uni.showToast({
					title: '录音被中断',
					icon: 'none'
				});
			});
			
			this.recorderManager.onInterruptionEnd(() => {
				console.log('录音中断结束');
			});
			// #endif
			
			console.log('录音管理器事件监听器设置完成');
		},
		
		// 处理录音
		handleRecord() {
			console.log('录音按钮被点击，当前状态:', this.recordingState);
			console.log('录音管理器状态:', this.recorderManager ? '已初始化' : '未初始化');
			
			if (this.recordingState === 'idle') {
				console.log('开始录音...');
				this.startRecord();
			} else if (this.recordingState === 'recording') {
				console.log('停止录音...');
				this.stopRecord();
			}
		},
		
		// 开始录音
		startRecord() {
			console.log('startRecord 被调用');
			// 检查录音权限
			// #ifdef APP-PLUS
			plus.android.requestPermissions(
				['android.permission.RECORD_AUDIO'],
				(resultObj) => {
					console.log('权限请求结果:', resultObj);
					if (resultObj.granted.length === 1) {
						console.log('权限已授予，开始录音');
						this.doStartRecord();
					} else {
						console.log('权限被拒绝');
						uni.showToast({
							title: '需要录音权限',
							icon: 'none'
						});
					}
				}
			);
			// #endif
			
			// #ifndef APP-PLUS
			console.log('非APP-PLUS环境，直接开始录音');
			this.doStartRecord();
			// #endif
		},
		
		// 执行开始录音
		doStartRecord() {
			console.log('doStartRecord 被调用');
			if (!this.recorderManager) {
				console.error('录音管理器未初始化');
				uni.showToast({
					title: '录音初始化失败',
					icon: 'none'
				});
				return;
			}
			
			console.log('设置录音状态为 recording');
			this.recordingState = 'recording';
			
			// 录音参数配置
			const recordOptions = {
				format: 'mp3',
				sampleRate: 16000,
				numberOfChannels: 1,
				encodeBitRate: 96000
			};
			
			// #ifdef MP-WEIXIN
			// 微信小程序支持frameSize参数
			recordOptions.frameSize = 50;
			// #endif
			
			console.log('开始录音，参数:', recordOptions);
			
			// 开始录音
			this.recorderManager.start(recordOptions);
		},
		
		// 停止录音
		stopRecord() {
			console.log('stopRecord 被调用');
			if (this.recorderManager) {
				console.log('调用录音管理器停止方法');
				this.recorderManager.stop();
			} else {
				console.error('录音管理器未初始化');
			}
		},
		
		// 获取状态栏高度并设置为CSS变量
		setStatusBarHeight() {
			try {
				const systemInfo = uni.getSystemInfoSync();
				const statusBarHeight = systemInfo.statusBarHeight || 0;
				console.log('状态栏高度:', statusBarHeight);
				
				// 设置CSS变量
				document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight}px`);
			} catch (error) {
				console.error('获取状态栏高度失败:', error);
				// 设置默认值
				document.documentElement.style.setProperty('--status-bar-height', '44px');
			}
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background-color: #f8f8f8;
	padding-bottom: 140rpx;
	/* 调整顶部间距，包含状态栏高度 */
	padding-top: calc(100rpx + var(--status-bar-height, 44px));
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #eee;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		background: #fff;
		z-index: 1;
		/* 为状态栏预留空间 */
		padding-top: calc(20rpx + var(--status-bar-height));
		
		.page-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
		}
		
		.publish-btn {
			height: 56rpx;
			line-height: 56rpx;
			padding: 0 32rpx;
			background: #00D08C;
			color: #fff;
			font-size: 26rpx;
			border-radius: 32rpx;
			margin: 0;
			&:disabled {
				background: #e5e5e5;
				color: #fff;
			}
		}
	}
	
	.content-box {
		padding: 30rpx;
		background-color: #fff;
		
		.content-input {
			width: 100%;
			min-height: 200rpx;
			font-size: 28rpx;
			line-height: 1.6;
			padding-bottom: 30rpx;
			background: #fff;
			border: none;
			&::placeholder {
				color: #cccccc;
				font-size: 28rpx;
			}
		}
		
		.image-list {
			display: flex;
			flex-wrap: wrap;
			margin-top: 20rpx;
			
			.image-item {
				position: relative;
				width: calc((100% - 40rpx) / 3);
				height: calc((100vw - 60rpx - 40rpx) / 3);
				margin-right: 20rpx;
				margin-bottom: 20rpx;
				
				&:nth-child(3n) {
					margin-right: 0;
				}
				
				image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
				
				.delete-btn {
					position: absolute;
					top: -15rpx;
					right: -15rpx;
					width: 30rpx;
					height: 30rpx;
					line-height: 28rpx;
					text-align: center;
					background: rgba(0, 0, 0, 0.5);
					color: #fff;
					border-radius: 50%;
					font-size: 28rpx;
				}
			}
		}
		
		.audio-container {
			margin-top: 20rpx;
			padding: 20rpx;
			background-color: #f8f9fa;
			border-radius: 12rpx;
			border: 1rpx solid #e9ecef;
			
			.audio-item {
				display: flex;
				align-items: center;
				padding: 16rpx;
				background-color: #fff;
				border-radius: 8rpx;
				position: relative;
				
				.audio-info {
					display: flex;
					align-items: center;
					margin-right: 20rpx;
					
					.audio-duration {
						font-size: 24rpx;
						color: #666;
						margin-left: 8rpx;
						font-weight: 500;
						min-width: 80rpx;
						text-align: center;
					}
				}
				
				.audio-controls {
					flex: 1;
					display: flex;
					align-items: center;
					
					.play-btn {
						width: 60rpx;
						height: 60rpx;
						border-radius: 50%;
						background-color: #00D08C;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 20rpx;
						box-shadow: 0 2rpx 8rpx rgba(0, 208, 140, 0.3);
						
						uni-icons {
							color: #fff !important;
						}
					}
					
					.progress-bar {
						flex: 1;
						height: 6rpx;
						background-color: #e9ecef;
						border-radius: 3rpx;
						overflow: hidden;
						position: relative;
						
						.progress-fill {
							height: 100%;
							background: linear-gradient(90deg, #00D08C, #00b894);
							border-radius: 3rpx;
							transition: width 0.1s ease;
						}
					}
				}
				
				.delete-btn {
					position: absolute;
					top: -10rpx;
					right: -10rpx;
					width: 32rpx;
					height: 32rpx;
					line-height: 30rpx;
					text-align: center;
					background: rgba(255, 71, 87, 0.9);
					color: #fff;
					border-radius: 50%;
					font-size: 24rpx;
					font-weight: bold;
					box-shadow: 0 2rpx 6rpx rgba(255, 71, 87, 0.4);
				}
			}
		}
		
		.recording-indicator {
			margin-top: 20rpx;
			padding: 20rpx;
			background-color: #f8f9fa;
			border-radius: 12rpx;
			border: 1rpx solid #e9ecef;
			
			.recording-content {
				display: flex;
				flex-direction: column;
				align-items: center;
				
				.recording-icon {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
					background-color: #00D08C;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 10rpx;
					animation: pulse 1.5s ease-in-out infinite;
					
					.pulse-dot {
						width: 10rpx;
						height: 10rpx;
						border-radius: 50%;
						background-color: #fff;
						margin: 2rpx;
						animation: blink 1s ease-in-out infinite;
					}
				}
				
				.recording-text {
					font-size: 26rpx;
					color: #666;
					font-weight: 500;
					margin-bottom: 4rpx;
				}
				
				.recording-tip {
					font-size: 20rpx;
					color: #999;
				}
			}
		}
	}
	
	.options-list {
		margin-top: 20rpx;
		background-color: #fff;
		border-radius: 0;
		
		.option-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 100rpx;
			padding: 0 40rpx;
			font-size: 30rpx;
			color: #222;
			background: #fff;
			
			&:not(:last-child) {
				border-bottom: 1rpx solid #f3f3f3;
			}

			.option-desc {
				font-size: 20rpx;
				color: #bbb;
				margin-top: 4rpx;
			}

			// switch样式微调
			switch {
				transform: scale(0.7);
				margin-left: 20rpx;
			}
		}
	}
	
	.footer {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100vw;
		background: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.03);
		padding: 10rpx 30rpx 30rpx 30rpx;
		z-index: 1;

		.media-buttons {
			display: flex;
			flex-direction: row;
			align-items: center;
			margin-right: 20rpx;

			.media-btn {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 100rpx;
				height: 90rpx;
				border: none;
				background: transparent;
				margin-right: 10rpx;

				text {
					margin-top: 6rpx;
					font-size: 22rpx;
					color: #666;
				}
			}
		}

		.close-btn {
			margin-left: 10rpx;
		}

		.publish-btn {
			height: 56rpx;
			line-height: 56rpx;
			padding: 0 32rpx;
			background: #00D08C;
			color: #fff;
			font-size: 26rpx;
			border-radius: 32rpx;
			margin-left: 10rpx;
			&:disabled {
				background: #e5e5e5;
				color: #fff;
			}
		}
	}
}

.visibility-popup-container {
	background-color: #fff;
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.visibility-options {
		.visibility-option {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;

			&:not(:last-child) {
				border-bottom: 1rpx solid #f5f5f5;
			}

			.option-title {
				font-size: 30rpx;
				color: #333;
			}
			
			.option-desc {
				font-size: 24rpx;
				color: #999;
				margin-top: 4rpx;
			}
		}
	}
}

@keyframes pulse {
	0% {
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(0, 208, 140, 0.7);
	}
	70% {
		transform: scale(1.05);
		box-shadow: 0 0 0 10rpx rgba(0, 208, 140, 0);
	}
	100% {
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(0, 208, 140, 0);
	}
}

@keyframes blink {
	0%, 50% {
		opacity: 1;
	}
	51%, 100% {
		opacity: 0.3;
	}
}
</style> 