<template>
  <view class="post-activity-container">
    <!-- Custom Header -->
    <page-header title="发布活动" @back="goBack" />

    <view class="form-wrapper">
      <!-- 描述 -->
      <view class="desc-wrapper">
        <textarea
          class="desc-textarea"
          v-model="form.description"
          placeholder="描述一下你的具体要求"
          :maxlength="100"
        ></textarea>
        <view class="char-counter">{{ form.description.length }}/100</view>
      </view>

      <!-- 选择技能 -->
      <view class="skill-selector" @click="showSkillInput" v-if="!isAddingSkill">
        <text class="icon-plus">+</text>
        <text>选择技能</text>
      </view>

      <!-- 技能输入框 -->
      <view class="skill-input-wrapper" v-if="isAddingSkill">
        <input
          class="skill-input"
          v-model="currentSkill"
          placeholder="请输入技能后按 ✓"
          :maxlength="10"
        />
        <text class="confirm-skill-btn" @click="confirmSkill">✓</text>
      </view>

      <view class="selected-skills-display" v-if="form.skills.length > 0">
        <view class="skill-tag" v-for="(skill, index) in form.skills" :key="index">
          {{ skill }} <text class="remove-skill" @click.stop="removeSkill(index)">x</text>
        </view>
      </view>

      <!-- 表单项 -->
      <view class="form-section">
        <view class="form-item">
          <text class="label">性别</text>
          <view class="gender-selector">
            <text
              v-for="gender in genders"
              :key="gender"
              :class="{ active: form.gender === gender }"
              @click="form.gender = gender"
              >{{ gender }}</text
            >
          </view>
        </view>

        <view class="form-item" @click="selectPeople">
          <text class="label">人数</text>
          <view class="value-display">
            <text :class="{ placeholder: !form.peopleCount }">{{
              form.peopleCount ? `${form.peopleCount}人` : '请选择人数'
            }}</text>
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>

        <view class="form-item" @click="selectServiceTime">
          <text class="label">服务时间</text>
          <view class="value-display">
            <text :class="{ placeholder: !form.serviceTime }">{{
              form.serviceTime || '请选择开始服务时间'
            }}</text>
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>

        <view class="form-item" @click="selectDuration">
          <text class="label">时长</text>
          <view class="value-display">
            <text :class="{ placeholder: !form.duration }">{{
              form.duration ? `${form.duration}小时` : '请选择服务时长'
            }}</text>
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>

        <view class="form-item" @click="selectDistance">
          <text class="label">距离</text>
          <view class="value-display">
            <text :class="{ placeholder: !form.distance }">{{
              form.distance ? `${form.distance}km` : '请选择距离范围'
            }}</text>
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>

        <view class="form-item" @click="selectAddress">
          <text class="label">服务地址</text>
          <view class="value-display">
            <text :class="{ placeholder: !form.address }">{{
              form.address || '点击选择您的地址'
            }}</text>
            <text class="iconfont icon-location"></text>
          </view>
        </view>

        <view class="form-item">
          <text class="label">贝壳币</text>
          <view class="island-coin-input">
            <text class="coin-icon">💎</text>
            <input type="number" v-model="form.islandCoin" placeholder="请输入" />
            <text class="unit">/人</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 支付信息 -->
    <view class="payment-section">
      <view class="payment-details">
        <view class="detail-item">
          <text>订单金额</text>
          <text>{{ orderAmount }} 贝壳币</text>
        </view>
        <view class="detail-item">
          <text>意外险 <text class="info-icon">ⓘ</text></text>
          <view class="insurance-option">
            <text>2 贝壳币</text>
            <switch class="custom-switch" :checked="includeInsurance" @change="toggleInsurance" />
          </view>
        </view>
      </view>

      <!-- 支付方式 -->
      <view class="payment-methods">
        <view class="method-item" @click="selectPaymentMethod('balance')">
          <view>
            <text class="method-title">账户余额</text>
            <text class="balance">99997 贝壳币</text>
          </view>
          <view class="radio" :class="{ selected: paymentMethod === 'balance' }"></view>
        </view>
        <view class="method-item" @click="selectPaymentMethod('wechat')">
          <text class="method-title">微信</text>
          <view class="radio" :class="{ selected: paymentMethod === 'wechat' }"></view>
        </view>
        <view class="method-item" @click="selectPaymentMethod('alipay')">
          <text class="method-title">支付宝</text>
          <view class="radio" :class="{ selected: paymentMethod === 'alipay' }"></view>
        </view>
      </view>
    </view>

    <!-- 底部支付栏 -->
    <view class="footer">
      <view class="total-amount">
        <text>总计:</text>
        <text class="amount">{{ totalAmount }} 贝壳币</text>
      </view>
      <button class="pay-btn" :disabled="!isValid" @click="submit">立即支付</button>
    </view>

    <!-- 通用选择器 -->
    <view class="picker-popup" v-if="picker.show">
      <view class="picker-overlay" @click="closePicker"></view>
      <view class="picker-content">
        <view class="picker-header">
          <text class="picker-cancel" @click="closePicker">取消</text>
          <text class="picker-title">{{ picker.title }}</text>
          <text class="picker-confirm" @click="confirmPicker">确定</text>
        </view>
        <picker-view
          class="picker-view"
          :indicator-style="`height: 50px;`"
          :value="picker.selectedIndex"
          @change="onPickerChange"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in picker.data" :key="index">
              {{ item.label }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>

    <!-- 服务时间选择器 -->
    <view class="picker-popup" v-if="timePicker.show">
      <view class="picker-overlay" @click="closeTimePicker"></view>
      <view class="time-picker-content">
        <view class="time-picker-header">
          <text class="time-picker-title">选择服务时间</text>
          <text class="close-btn" @click="closeTimePicker">×</text>
        </view>
        <picker-view
          class="time-picker-view"
          :indicator-style="`height: 50px;`"
          :value="timePicker.selectedIndex"
          @change="onTimePickerChange"
        >
          <picker-view-column>
            <view class="picker-item" v-for="(day, index) in timePicker.dates" :key="index">
              {{ day.label }}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="picker-item" v-for="(hour, index) in timePicker.hours" :key="index">
              {{ hour.label }}
            </view>
          </picker-view-column>
        </picker-view>
        <view class="time-picker-footer">
          <button class="confirm-btn" @click="confirmTimeSelection">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import PageHeader from '@/components/PageHeader.vue'

  export default {
    components: {
      PageHeader
    },
    data() {
      return {
        form: {
          description: '',
          skills: [], // 初始化为空
          gender: '女',
          peopleCount: null,
          serviceTime: '',
          duration: null,
          distance: null,
          address: null,
          islandCoin: 800
        },
        isAddingSkill: false,
        currentSkill: '',
        genders: ['女', '男', '不限'],
        includeInsurance: true,
        paymentMethod: 'balance', // 'balance', 'wechat', 'alipay'
        picker: {
          show: false,
          title: '',
          data: [],
          type: '', // 'people', 'duration', 'distance'
          selectedIndex: [0],
          tempIndex: [0]
        },
        pickerOptions: {
          people: Array.from(
            {
              length: 10
            },
            (_, i) => ({
              label: `${i + 1}人`,
              value: i + 1
            })
          ),
          duration: Array.from(
            {
              length: 24
            },
            (_, i) => ({
              label: `${i + 1}小时`,
              value: i + 1
            })
          ),
          distance: [5, 10, 15, 20, 30, 50].map(d => ({
            label: `${d}km`,
            value: d
          }))
        },
        timePicker: {
          show: false,
          selectedIndex: [0, 0],
          tempIndex: [0, 0],
          dates: [],
          hours: []
        }
      }
    },
    created() {
      this.generateTimePickerData()
    },
    computed: {
      orderAmount() {
        const amount = this.form.islandCoin || 0
        const people = this.form.peopleCount || 1
        return amount * people
      },
      totalAmount() {
        let total = this.orderAmount
        if (this.includeInsurance) {
          total += 2
        }
        return total
      },
      isValid() {
        const {
          description,
          skills,
          peopleCount,
          serviceTime,
          duration,
          distance,
          address,
          islandCoin
        } = this.form
        return (
          description &&
          skills.length > 0 &&
          peopleCount &&
          serviceTime &&
          duration &&
          distance &&
          address &&
          islandCoin > 0
        )
      }
    },
    methods: {
      goBack() {
        uni.navigateBack()
      },
      showSkillInput() {
        this.isAddingSkill = true
      },
      confirmSkill() {
        const skill = this.currentSkill.trim()
        if (skill && !this.form.skills.includes(skill)) {
          this.form.skills.push(skill)
        }
        this.currentSkill = ''
        this.isAddingSkill = false
      },
      removeSkill(index) {
        this.form.skills.splice(index, 1)
      },
      selectPeople() {
        this.openPicker('people')
      },
      selectServiceTime() {
        this.openTimePicker()
      },
      selectDuration() {
        this.openPicker('duration')
      },
      selectDistance() {
        this.openPicker('distance')
      },
      selectAddress() {
        uni.navigateTo({
          url: '/pages/post/address'
        })
      },
      toggleInsurance(e) {
        this.includeInsurance = e.detail.value
      },
      selectPaymentMethod(method) {
        this.paymentMethod = method
      },
      submit() {
        if (!this.isValid) {
          uni.showToast({
            title: '请填写完整的活动信息',
            icon: 'none'
          })
          return
        }
        console.log('Submitting form:', this.form)
        uni.showLoading({
          title: '正在支付...'
        })
        // Mock submission
        setTimeout(() => {
          uni.hideLoading()
          uni.showToast({
            title: '支付成功',
            icon: 'success'
          })
          setTimeout(() => uni.navigateBack(), 1500)
        }, 1000)
      },

      // Picker methods
      openPicker(type) {
        let title = ''
        let data = []
        let currentValue = null
        let selectedIndex = [0]

        switch (type) {
          case 'people':
            title = '选择人数'
            data = this.pickerOptions.people
            currentValue = this.form.peopleCount
            break
          case 'duration':
            title = '选择时长'
            data = this.pickerOptions.duration
            currentValue = this.form.duration
            break
          case 'distance':
            title = '选择距离'
            data = this.pickerOptions.distance
            currentValue = this.form.distance
            break
        }

        const currentIndex = data.findIndex(item => item.value === currentValue)
        if (currentIndex !== -1) {
          selectedIndex = [currentIndex]
        }

        this.picker = {
          show: true,
          title,
          data,
          type,
          selectedIndex,
          tempIndex: selectedIndex
        }
      },
      closePicker() {
        this.picker.show = false
      },
      onPickerChange(e) {
        this.picker.tempIndex = e.detail.value
      },
      confirmPicker() {
        const selectedIndex = this.picker.tempIndex[0]
        const selectedValue = this.picker.data[selectedIndex].value

        switch (this.picker.type) {
          case 'people':
            this.form.peopleCount = selectedValue
            break
          case 'duration':
            this.form.duration = selectedValue
            break
          case 'distance':
            this.form.distance = selectedValue
            break
        }
        this.closePicker()
      },

      // Time Picker methods
      generateTimePickerData() {
        const dates = []
        const hours = []
        const week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

        // 生成未来14天的日期
        for (let i = 0; i < 14; i++) {
          const date = new Date()
          date.setDate(date.getDate() + i)
          const month = (date.getMonth() + 1).toString().padStart(2, '0')
          const day = date.getDate().toString().padStart(2, '0')
          const weekDay = week[date.getDay()]

          let label = `${month}-${day}/${weekDay}`
          if (i === 0) {
            label = `今天/${weekDay}`
          }
          if (i === 1) {
            label = `明天/${weekDay}`
          }
          dates.push({
            label,
            value: `${date.getFullYear()}-${month}-${day}`
          })
        }

        // 生成小时
        for (let i = 0; i < 24; i++) {
          const hour = i.toString().padStart(2, '0')
          hours.push({
            label: `${hour}:00`,
            value: hour
          })
        }

        this.timePicker.dates = dates
        this.timePicker.hours = hours
      },
      openTimePicker() {
        this.timePicker.tempIndex = this.timePicker.selectedIndex
        this.timePicker.show = true
      },
      closeTimePicker() {
        this.timePicker.show = false
      },
      onTimePickerChange(e) {
        this.timePicker.tempIndex = e.detail.value
      },
      confirmTimeSelection() {
        const dateIndex = this.timePicker.tempIndex[0]
        const hourIndex = this.timePicker.tempIndex[1]

        const selectedDate = this.timePicker.dates[dateIndex]
        const selectedHour = this.timePicker.hours[hourIndex]

        this.form.serviceTime = `${selectedDate.label} ${selectedHour.label}`
        this.timePicker.selectedIndex = this.timePicker.tempIndex
        this.closeTimePicker()
      }
    },
    onShow() {
      uni.$once('addressSelected', address => {
        this.form.address = address.fullAddress
      })
    }
  }
</script>

<style lang="scss">
  /* 使用 unocss, 这里仅作演示 */
  @import url('https://at.alicdn.com/t/c/font_3958315_82e66gve9za.css');

  .post-activity-container {
    background-color: #f7f7f7;
    padding-bottom: 180rpx;
  }

  .form-wrapper,
  .payment-section {
    background-color: #fff;
    margin: 24rpx;
    padding: 24rpx;
    border-radius: 16rpx;
  }

  .desc-wrapper {
    background-color: #f7f7f7;
    padding: 20rpx;
    border-radius: 8rpx;
    position: relative;

    .desc-textarea {
      width: 100%;
      height: 160rpx;
      font-size: 28rpx;
    }

    .char-counter {
      position: absolute;
      bottom: 20rpx;
      right: 20rpx;
      font-size: 24rpx;
      color: #999;
    }
  }

  .skill-selector {
    border: 1rpx dashed #ccc;
    border-radius: 8rpx;
    padding: 16rpx 24rpx;
    display: inline-flex;
    align-items: center;
    margin-top: 24rpx;
    color: #888;
    font-size: 28rpx;

    .icon-plus {
      margin-right: 8rpx;
    }
  }

  .skill-input-wrapper {
    background-color: #f7f7f7;
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
    position: relative;
    display: flex;
    align-items: center;
    margin-top: 24rpx;

    .skill-input {
      flex-grow: 1;
      height: 60rpx;
      font-size: 28rpx;
      background-color: transparent;
    }

    .confirm-skill-btn {
      font-size: 40rpx;
      color: $color-primary;
      padding-left: 20rpx;
      font-weight: bold;
    }
  }

  .selected-skills-display {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16rpx;

    .skill-tag {
      background-color: #eef7ff;
      color: $color-primary;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      margin-right: 16rpx;
      margin-bottom: 16rpx;
      display: flex;
      align-items: center;

      .remove-skill {
        margin-left: 8rpx;
        color: $color-primary;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }

  .form-section {
    margin-top: 30rpx;
  }

  .form-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 28rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 30rpx;
      color: #333;
    }

    .value-display {
      display: flex;
      align-items: center;
      color: #333;
      font-size: 28rpx;

      .placeholder {
        color: #999;
      }

      .iconfont {
        font-size: 24rpx;
        color: #999;
        margin-left: 10rpx;
      }
    }
  }

  .gender-selector {
    display: flex;
    background-color: #f5f5f5;
    border-radius: 8rpx;
    padding: 4rpx;

    text {
      padding: 10rpx 24rpx;
      font-size: 26rpx;
      border-radius: 6rpx;
      color: #666;

      &.active {
        background-color: #fff;
        color: #333;
        font-weight: 500;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }
    }
  }

  .island-coin-input {
    display: flex;
    align-items: center;

    .coin-icon {
      font-size: 32rpx;
    }

    input {
      text-align: right;
      width: 150rpx;
      font-size: 28rpx;
      margin: 0 10rpx;
    }

    .unit {
      font-size: 28rpx;
      color: #666;
    }
  }

  .payment-details,
  .payment-methods {
    .detail-item,
    .method-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 28rpx 0;
      font-size: 28rpx;
    }

    .detail-item {
      .info-icon {
        font-size: 24rpx;
        color: #999;
        margin-left: 8rpx;
        border: 1rpx solid #999;
        border-radius: 50%;
        width: 30rpx;
        height: 30rpx;
        display: inline-block;
        text-align: center;
        line-height: 30rpx;
      }

      .insurance-option {
        display: flex;
        align-items: center;

        .custom-switch {
          transform: scale(0.8);
        }

        text {
          margin-right: 16rpx;
        }
      }
    }

    .method-item {
      .method-title {
        color: #333;
      }

      .balance {
        font-size: 24rpx;
        color: #999;
        margin-left: 16rpx;
      }

      .radio {
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        border: 1rpx solid #ccc;

        &.selected {
          background-color: $color-primary;
          border-color: $color-primary;
          position: relative;

          &::after {
            content: '✓';
            color: white;
            font-size: 24rpx;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
  }

  .payment-methods {
    .method-item {
      border-top: 1rpx solid #f5f5f5;
    }
  }

  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .total-amount {
      font-size: 28rpx;
      color: #333;

      .amount {
        font-size: 36rpx;
        color: #ff4d4f;
        font-weight: bold;
        margin-left: 10rpx;
      }
    }

    .pay-btn {
      background: $color-primary;
      color: #fff;
      border: none;
      border-radius: 40rpx;
      padding: 0 60rpx;
      height: 80rpx;
      line-height: 80rpx;
      font-size: 30rpx;

      &:disabled {
        opacity: 0.6;
      }
    }
  }

  @keyframes slide-up {
    from {
      transform: translateY(100%);
    }

    to {
      transform: translateY(0);
    }
  }

  .picker-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;

    .picker-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
    }

    .picker-content {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background-color: #fff;
      border-top-left-radius: 20rpx;
      border-top-right-radius: 20rpx;
      animation: slide-up 0.3s ease;
    }

    .picker-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 30rpx;
      border-bottom: 1rpx solid #eee;

      .picker-cancel,
      .picker-confirm {
        font-size: 30rpx;
        color: #666;
      }

      .picker-confirm {
        color: $color-primary;
      }

      .picker-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }
    }

    .picker-view {
      width: 100%;
      height: 400rpx;
    }

    .picker-item {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 34rpx;
      color: #333;
    }
  }

  .time-picker-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    animation: slide-up 0.3s ease;
  }

  .time-picker-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx 30rpx;
    position: relative;
    border-bottom: 1rpx solid #eee;

    .time-picker-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }

    .close-btn {
      position: absolute;
      right: 30rpx;
      top: 50%;
      transform: translateY(-50%);
      font-size: 40rpx;
      color: #999;
    }
  }

  .time-picker-view {
    width: 100%;
    height: 400rpx;
  }

  .time-picker-footer {
    padding: 20rpx 30rpx;
    padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

    .confirm-btn {
      background: $color-primary;
      color: #fff;
      border: none;
      border-radius: 40rpx;
      height: 80rpx;
      line-height: 80rpx;
      font-size: 30rpx;
    }
  }
</style>
