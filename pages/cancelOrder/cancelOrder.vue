<template>
	<view class="cancel-order-page">
		<view class="header">
			<image class="status-icon" :src="`/static/images/expertOrder/cancel-order.svg`"></image>
			<view class="status-info">
				<view>取消订单</view>
				<text class="tip">确定取消么，再考虑考虑？</text>
			</view>
		</view>
		<view class="reason-wrap">
			<textarea class="text-area" maxlength="-1" placeholder="请简述取消原因"></textarea>
			<view class="txt">
				预计以下资产将原路退回
			</view>
			<view class="cost">
				<text class="txt1">贝壳币</text>
				<text class="txt2">900</text>
			</view>
		</view>
		<button class="submit">提交</button>
		<view class="rule" @click="showRules">退款规则</view>
		<uni-popup ref="popup">
			<view class="rules-wrap">
				<view class="title">退款规则</view>
				<uni-icons
				  type="closeempty"
					size="20"
					color="#333"
					class="close"
					@click="closePop"
				></uni-icons>
				<view class="rule-line">
					1. 当前时间距离订单开始时间 > 1h、免费取消。
				</view>
				<view class="rule-line">
					2. 当前时间距离订单开始时间 < 1h > 30m、可以取消、需求扣除40%的平台费。但不补贴搭子损失。
				</view>
				<view class="rule-line">
					3. 当前时间距离订单开始时间 < 30m 订单不可以取消。
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			showRules() {
				this.$refs.popup.open('bottom')
			},
			closePop() {
				this.$refs.popup.close()
			}
		}
	}
</script>

<style lang="scss" scoped>
.cancel-order-page {
	padding: 32rpx 32rpx 0;
	height: 100vh;
	background-color: #f4f8fb;
	.header {
		height: 96rpx;
		padding: 0 8rpx;
		display: flex;
		align-items: center;
		.status-icon {
			height: 72rpx;
			flex-shrink: 0;
			width:56rpx;
			margin-right: 24rpx;
		}
		.status-info {
			flex-grow: 1;
			.status-txt {
				height: 56rpx;
				line-height: 56rpx;
				font-size: 36rpx;
				color: #000;
			}
			.tip {
				color: #666;
				font-size: 24rpx;
				height: 40rpx;
				line-height: 40rpx;
			}
		}
	}
  .reason-wrap {
		padding: 32rpx 24rpx;
		background-color: #fff;
		border-radius: 16rpx;
		height: 580rpx;
		box-sizing: border-box;
		margin: 24rpx 0 60rpx;
		.text-area {
			height: 384rpx ;
			line-height: 44rpx;
			font-size: 24rpx;
			color: #333;
			width: 100%;
			padding-bottom: 12rpx;
			border-bottom: 2rpx solid rgba(209,209,209, .5);
		}
		.txt {
			margin: 32rpx 0 8rpx;
			color: #666;
			font-size: 24rpx;
			height: 44rpx;
			line-height: 44rpx;
			font-weight: 600;
		}
		.cost {
			display: flex;
			height: 44rpx;
			justify-content: space-between;
			.txt1, .txt2 {
			  height: 44rpx;
				line-height: 44rpx;
				font-weight: 600;
				font-size: 24rpx;
				color: #333
			}
			.txt2 {
				font-size: 28rpx;
				color: #000
			}
		}
	}
	.submit {
		width: 100%;
		border-radius: 48rpx;
		height: 96rpx;
		line-height: 96rpx;
		color: #fff;
		background-color: #66d47e;
		font-size: 32rpx;
	}
	.rule {
		height: 40rpx;
		line-height: 40rpx;
		color: #999;
		font-size: 24rpx;
		text-align: center;
		margin-top: 60rpx;
	}
	.rules-wrap {
		height: 600rpx;
		background-color: #fff;
		border-radius: 32rpx 32rpx 0 0;
		padding: 32rpx 32rpx 0;
		box-sizing: border-box;
		.title {
			height: 48rpx;
			line-height: 48rpx;
			color: #000;
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			margin-bottom: 60rpx;
		}
		.close {
			position: absolute;
			top: 36rpx;
			right: 32rpx;
		}
		.rule-line {
			line-height: 48rpx;
			font-size: 32rpx;
			color: #000;
		}
	}
}
</style>