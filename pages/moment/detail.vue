<template>
  <view class="detail-container">
    <!-- 顶部导航栏 -->
    <view class="navbar" :style="{ height: (navbarHeight || 88) + 'px', paddingTop: (statusBarHeight || 20) + 'px' }">
      <view class="nav-left" @click="goBack">
        <image src="/static/icon/back.png" mode="widthFix" class="nav-icon"></image>
      </view>
      <view class="nav-title">动态详情</view>
      <view class="nav-right">
        <text class="nav-icon">⋮</text>
      </view>
    </view>

    <!-- 动态内容 -->
    <view class="moment-detail" v-if="momentDetail" :style="{ marginTop: (navbarHeight+50 || 118) + 'px' }">
      <!-- 用户信息 -->
      <view class="user-section">
        <image :src="getUserAvatarUrl(momentDetail.user)" class="user-avatar"/>
        <view class="user-info">
          <text class="user-nickname">{{ momentDetail.user?.nickname || '未知用户' }}</text>
          <view class="post-meta">
            <text class="post-time">{{ formatTime(momentDetail.createTime) }}</text>
            <text class="post-location" v-if="momentDetail.location">{{ momentDetail.location }}</text>
            <text class="post-distance" v-if="momentDetail.distance">距离 {{ formatDistance(momentDetail.distance) }}</text>
          </view>
        </view>
      </view>

      <!-- 动态文本内容 -->
      <view class="moment-content" v-if="momentDetail.content">
        <text class="content-text">{{ momentDetail.content }}</text>
      </view>

      <!-- 图片展示 -->
      <view v-if="momentDetail.images && momentDetail.images.length > 0" class="moment-images">
        <image 
          v-for="(img, index) in momentDetail.images" 
          :key="index" 
          :src="getFullUrl(img.url || img.thumbnailUrl || img.originalUrl)" 
          class="moment-image"
          :class="getImageClass(momentDetail.images.length, index)"
          mode="aspectFill"
          @click="previewImage(img, momentDetail.images)"
        />
      </view>
    </view>

    <!-- 评论区域 -->
    <view class="comment-section">
      <view class="comment-header">
        <text class="comment-title">评论 {{ commentList.length }}</text>
      </view>

      <!-- 评论列表 -->
      <view class="comment-list" v-if="commentList.length > 0">
        <view v-for="comment in commentList" :key="comment.commentCode" class="comment-item">
          <image :src="getUserAvatarUrl(comment.user)" class="comment-avatar"/>
          <view class="comment-content">
            <view class="comment-header-info">
              <text class="comment-nickname">{{ comment.user?.nickname || '未知用户' }}</text>
              <text class="comment-time">{{ formatTime(comment.createTime) }}</text>
            </view>
            <text class="comment-text">{{ comment.content }}</text>
            
            <!-- 评论操作 -->
            <view class="comment-actions">
              <view class="comment-action" @click="likeComment(comment)">
                <text class="action-icon" :class="{ 'liked': comment.isLiked }">👍</text>
                <text class="action-count" v-if="comment.likeCount > 0">{{ comment.likeCount }}</text>
              </view>
              <view class="comment-action" @click="replyComment(comment)">
                <text class="action-text">回复</text>
              </view>
            </view>

            <!-- 二级回复 -->
            <view v-if="comment.replies && comment.replies.length > 0" class="reply-list">
              <view v-for="reply in comment.replies" :key="reply.commentCode" class="reply-item">
                <text class="reply-user">{{ reply.user?.nickname }}</text>
                <text class="reply-to" v-if="reply.replyToUser">回复 {{ reply.replyToUser.nickname }}</text>
                <text class="reply-content">{{ reply.content }}</text>
                <text class="reply-time">{{ formatTime(reply.createTime) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-comment">
        <text class="empty-text">暂无评论，快来抢沙发吧～</text>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="action-item" @click="handleLike">
          <text class="action-icon like-icon" :class="{ 'liked': momentDetail?.isLiked }">👍</text>
          <text class="action-count" v-if="momentDetail?.likeCount > 0">{{ momentDetail.likeCount }}</text>
        </view>
        <view class="action-item">
          <text class="action-icon">💬</text>
          <text class="action-count">{{ commentList.length }}</text>
        </view>
      </view>
      <view class="comment-input-area">
        <input 
          v-model="commentText" 
          placeholder="说点什么..."
          class="comment-input"
          @focus="onInputFocus"
          @confirm="sendComment"
        />
      </view>
    </view>

    <!-- 加载状态 -->
    <uni-load-more v-if="loading" :status="loadingStatus"></uni-load-more>
  </view>
</template>

<script>
import { postApi, commentApi, userApi } from '@/common/api.js'

export default {
  name: 'MomentDetail',
  
  // 页面配置
  onReady() {
    // 页面渲染完成后再次获取系统信息
    this.getSystemInfo()
  },
  data() {
    return {
      postCode: '',
      momentDetail: null,
      commentList: [],
      commentText: '',
      loading: false,
      loadingStatus: 'loading',
      replyingTo: null, // 当前回复的评论
      statusBarHeight: 0,
      navbarHeight: 0
    }
  },
  
  onLoad(options) {
    console.log('页面参数:', options)
    this.postCode = options.postCode || options.postcode || ''
    
    // 获取系统信息，设置状态栏高度
    this.getSystemInfo()
    
    if (this.postCode) {
      this.loadData()
    } else {
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      })
    }
  },

  methods: {
    // 获取系统信息
    getSystemInfo() {
      try {
        const systemInfo = uni.getSystemInfoSync()
        this.statusBarHeight = systemInfo.statusBarHeight || 0
        // 导航栏高度 = 状态栏高度 + 导航栏内容高度(88rpx)
        this.navbarHeight = this.statusBarHeight + 44 // 44px = 88rpx
        console.log('系统信息:', systemInfo)
        console.log('状态栏高度:', this.statusBarHeight, '导航栏高度:', this.navbarHeight)
      } catch (error) {
        console.error('获取系统信息失败:', error)
        // 设置默认值
        this.statusBarHeight = 20
        this.navbarHeight = 64
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 加载页面数据
    async loadData() {
      await Promise.all([
        this.loadMomentDetail(),
        this.loadCommentList()
      ])
    },

    // 获取完整URL
    getFullUrl(url) {
      if (!url) return ''
      if (url.startsWith('http')) return url
      return `http://47.123.3.183:9000/${url}`
    },

    // 获取用户头像URL
    getUserAvatarUrl(user) {
      if (!user || !user.img) return '/static/default-avatar.png'
      return this.getFullUrl(user.img)
    },

    // 格式化时间显示
    formatTime(dateTime) {
      if (!dateTime) return '刚刚'
      
      const now = new Date()
      const postTime = new Date(dateTime)
      const diff = now - postTime
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      
      return postTime.toLocaleDateString('zh-CN')
    },

    // 格式化距离显示
    formatDistance(distance) {
      if (!distance) return ''
      
      if (distance < 1000) {
        return `${Math.round(distance)}m`
      } else {
        return `${(distance / 1000).toFixed(2)}km`
      }
    },

    // 获取图片样式类
    getImageClass(totalCount, index) {
      if (totalCount === 1) {
        return 'single-image'
      } else if (totalCount === 2) {
        return 'double-image'
      } else if (totalCount === 3) {
        return 'triple-image'
      } else {
        return 'multi-image'
      }
    },

    // 预览图片
    previewImage(currentImg, allImages) {
      const urls = allImages.map(img => this.getFullUrl(img.url || img.originalUrl || img.thumbnailUrl))
      const current = this.getFullUrl(currentImg.url || currentImg.originalUrl || currentImg.thumbnailUrl)
      
      uni.previewImage({
        urls: urls,
        current: current
      })
    },

    // 处理点赞
    async handleLike() {
      if (!this.momentDetail?.userId) {
        uni.showToast({
          title: '用户信息不完整',
          icon: 'none'
        })
        return
      }

      try {
        const currentLiked = this.momentDetail.isLiked
        const operationType = currentLiked ? 2 : 1 // 1:喜欢, 2:不喜欢
        
        const result = await userApi.userInteraction({
          targetUserId: this.momentDetail.userId,
          operationType: operationType
        })
        
        if (result.code === 0 || result.code === 200) {
          this.momentDetail.isLiked = !currentLiked
          if (currentLiked) {
            this.momentDetail.likeCount = Math.max(0, (this.momentDetail.likeCount || 0) - 1)
          } else {
            this.momentDetail.likeCount = (this.momentDetail.likeCount || 0) + 1
          }
          
          uni.showToast({
            title: currentLiked ? '取消点赞' : '点赞成功',
            icon: 'success',
            duration: 1500
          })
        } else {
          uni.showToast({
            title: result.msg || '操作失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('点赞操作失败:', error)
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        })
      }
    },

    // 加载动态详情
    async loadMomentDetail() {
      try {
        this.loading = true
        this.loadingStatus = 'loading'
        
        const result = await postApi.getPostDetail(this.postCode)
        
        if (result.code === 0 || result.code === 200) {
          // 适配新的数据结构
          const data = result.data
          this.momentDetail = {
            ...data,
            content: data.postText,
            createTime: data.postDate,
            location: data.postCoordinate ? '定位信息' : null, // 可根据需要解析坐标
            user: {
              userId: data.userId,
              nickname: data.nickname,
              img: data.profileImg ? decodeURIComponent(data.profileImg) : null
            }
          }
          
          // 验证数据适配
          console.log('原始数据:', data)
          console.log('适配后数据:', this.momentDetail)
          
          this.loadingStatus = 'noMore'
        } else {
          this.loadingStatus = 'error'
          uni.showToast({
            title: result.msg || '加载失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载动态详情失败:', error)
        this.loadingStatus = 'error'
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载评论列表
    async loadCommentList() {
      try {
        const result = await commentApi.getCommentList({
          postCode: this.postCode
        })
        
        if (result.code === 0 || result.code === 200) {
          this.commentList = result.rows || []
        } else {
          console.error('加载评论失败:', result.msg)
        }
      } catch (error) {
        console.error('加载评论列表失败:', error)
      }
    },

    // 输入框获得焦点
    onInputFocus() {
      // 可以在这里处理输入框焦点逻辑
    },

    // 发送评论
    async sendComment() {
      const content = this.commentText.trim()
      if (!content) {
        uni.showToast({
          title: '请输入评论内容',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({
          title: '发送中...'
        })
        
        const requestData = {
          postCode: this.postCode,
          content: content
        }
        
        // 如果是回复评论，添加父评论编码
        if (this.replyingTo) {
          requestData.parentCommentCode = this.replyingTo.commentCode
        }
        
        const result = await commentApi.addComment(requestData)
        
        if (result.code === 0 || result.code === 200) {
          this.commentText = ''
          this.replyingTo = null
          
          // 重新加载评论列表
          await this.loadCommentList()
          
          uni.showToast({
            title: '评论成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: result.msg || '评论失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('发送评论失败:', error)
        uni.showToast({
          title: '发送失败，请重试',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 点赞评论
    async likeComment(comment) {
      try {
        const result = await commentApi.likeComment(comment.commentCode)
        
        if (result.code === 0 || result.code === 200) {
          comment.isLiked = !comment.isLiked
          if (comment.isLiked) {
            comment.likeCount = (comment.likeCount || 0) + 1
          } else {
            comment.likeCount = Math.max(0, (comment.likeCount || 0) - 1)
          }
          
          uni.showToast({
            title: comment.isLiked ? '点赞成功' : '取消点赞',
            icon: 'success',
            duration: 1000
          })
        } else {
          uni.showToast({
            title: result.msg || '操作失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('点赞评论失败:', error)
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        })
      }
    },

    // 回复评论
    replyComment(comment) {
      this.replyingTo = comment
      this.commentText = `@${comment.user?.nickname || '用户'} `
      
      // 聚焦到输入框
      this.$nextTick(() => {
        // 由于uni-app的限制，这里可能需要其他方式来聚焦
        console.log('回复评论:', comment.user?.nickname)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

// 导航栏
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  z-index: 999;
  /* 备用方案：固定高度 */
  height: 88px;
  padding-top: 20px;
  
  .nav-left, .nav-right {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .nav-icon {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
  }
  
  .nav-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

// 动态详情
.moment-detail {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
  /* 备用方案：固定上边距 */
  margin-top: 88px;
}

// 用户信息区域
.user-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.user-info {
  flex: 1;
}

.user-nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

.post-location {
  font-size: 24rpx;
  color: #007bff;
}

.post-distance {
  font-size: 24rpx;
  color: #666;
}

// 动态内容
.moment-content {
  margin-bottom: 24rpx;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}

// 图片展示
.moment-images {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.moment-image {
  border-radius: 12rpx;
  
  &.single-image {
    width: 100%;
    height: 400rpx;
    max-width: 500rpx;
  }
  
  &.double-image {
    width: calc(50% - 8rpx);
    height: 240rpx;
  }
  
  &.triple-image {
    width: calc(33.33% - 11rpx);
    height: 180rpx;
  }
  
  &.multi-image {
    width: calc(50% - 8rpx);
    height: 180rpx;
  }
}

// 评论区域
.comment-section {
  background: #fff;
  padding: 32rpx;
}

.comment-header {
  margin-bottom: 24rpx;
}

.comment-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

// 评论列表
.comment-list {
  
}

.comment-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.comment-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.comment-content {
  flex: 1;
}

.comment-header-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.comment-nickname {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.comment-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
}

.action-icon {
  font-size: 24rpx;
  color: #999;
  transition: color 0.3s ease;
  
  &.liked {
    color: #ff6b6b;
  }
}

.action-count {
  font-size: 24rpx;
  color: #666;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

// 二级回复
.reply-list {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.reply-item {
  margin-bottom: 12rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.reply-user {
  font-size: 26rpx;
  color: #007bff;
  font-weight: bold;
}

.reply-to {
  font-size: 26rpx;
  color: #666;
  margin-left: 8rpx;
}

.reply-content {
  font-size: 26rpx;
  color: #333;
  margin-left: 8rpx;
}

.reply-time {
  font-size: 22rpx;
  color: #999;
  margin-left: 8rpx;
}

// 空状态
.empty-comment {
  text-align: center;
  padding: 80rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

// 底部操作栏
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 24rpx;
  z-index: 999;
}

.action-left {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
  
  .action-icon {
    font-size: 32rpx;
    color: #666;
    transition: color 0.3s ease;
    
    &.like-icon.liked {
      color: #ff6b6b;
    }
  }
  
  .action-count {
    font-size: 28rpx;
    color: #666;
  }
}

.comment-input-area {
  flex: 1;
}

.comment-input {
  height: 72rpx;
  padding: 0 24rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 36rpx;
  font-size: 28rpx;
  background-color: #f8f9fa;
  color: #333;
}
.nav-icon {
    width: 20rpx;
    height: 40rpx;
}
uni-page-body{
  background-color: white!important;
}
</style> 