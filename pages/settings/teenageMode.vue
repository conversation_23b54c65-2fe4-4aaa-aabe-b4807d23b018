<template>
  <view class="teenage-mode-page">
    <NavigationCustorm>
      <template #title>
        青少年模式
      </template>
    </NavigationCustorm>
    <view class="teenage-mode-content">
      <view class="teenage-mode-item">
        <view class="teenage-mode-item-title">
          青少年模式
        </view>
      </view>
      <view class="teenage-mode-desc">
        <div class="left">
          <image src="@/static/images/setting/mode-icon.png" mode="widthFix" style="width: 28rpx;height: 28rpx;">
          </image>
        </div>
        <div class="right">
          <text>在青少年模式下，我们将严格审核内容，挑选适合青少年的内容，且限制部分功能使用，每日晚22时至次日早6时期间无法使用APP；在青少年模式下，不可被其他用户搜索到，不可使用核心充值付费功能，我们将限制匹配次数等;</text>
        </div>
      </view>
      <view class="teenage-mode-desc">
        <div class="left">
          <image src="@/static/images/setting/mode-icon.png" mode="widthFix" style="width: 28rpx;height: 28rpx;">
          </image>
        </div>
        <div class="right">
          <view>时间锁：40分钟
          </view>
          <text>时间锁：40分钟
            单日使用时长超过上述时间，需要输入密码才能继续使用</text>
        </div>
      </view>
      <view class="teenage-mode-desc">
        <div class="left">
          <image src="@/static/images/setting/mode-icon.png" mode="widthFix" style="width: 28rpx;height: 28rpx;">
          </image>
        </div>
        <div class="right">
          <view>禁用时间：22:00-06:00</view>
          <text>
            该时间段内青少年模式的用户无法使用 APP</text>
        </div>
      </view>
    </view>
  </view>
</template>
<script setup>
import NavigationCustorm from '@/components/navigation-custorm/index.vue'
</script>
<style lang="scss" scoped>
.teenage-mode-page {
  background: #fff;
  min-height: 100vh;
}

.teenage-mode-content {
  padding-top: 192rpx;
}

.teenage-mode-item {
  background-image: url('@/static/images/setting/mode-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 16rpx;
  height: 630rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: 44rpx;
}

.teenage-mode-desc {
  padding: 0 30rpx 0 40rpx;
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
  .left {
    width: 28rpx;
    height: 28rpx;
    margin: 10rpx 20rpx 0 0;
  }

  .right {
    flex: 1;
    font-weight: 400;
    font-size: 26rpx;
    color: #666666;
    line-height: 46rpx;
  }
}
</style>