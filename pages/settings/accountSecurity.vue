<template>
	<view class="container">
		<NavigationCustorm>
			<template #title>
				账号安全
			</template>
		</NavigationCustorm>
		<!-- 账号安全 -->
		<view class="section">
			<view class="menu-list ">
				<view class="menu-item nav-header" @tap="handleBindPhone">
					<text>绑定手机号</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>
		
		<!-- 消息通知 -->
		<view class="section">
			<view class="menu-list">
				<!-- <view class="menu-item nav-header" @tap="handleSetPassword">
					<text>设置密码</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view> -->
				<view class="menu-item nav-header">
          <view class="nav-header-item">
            <image src="/static/images/setting/wechat.png" mode="widthFix" style="width: 40rpx;height: 40rpx;"></image>
            <text>微信</text>
          </view>
					<button type="primary" size="mini" class="nav-header-item-button" @tap="handleBindWechat">绑定</button>
				</view>
				<view class="menu-item nav-header" @tap="handleLogout">
					<text>注销账号</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { useUserStore } from '@/stores/user'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

// 获取当前实例用于访问 $api
const { proxy } = getCurrentInstance()

// 响应式数据
const userInfo = reactive({
	phone: ''
})

const settings = reactive({
	messageNotify: true,
	sound: true,
	vibrate: true,
	allowMessage: true,
	publicProfile: true
})

const cacheSize = ref('0MB')
const version = ref('1.0.0')
const userStore = useUserStore()

// 页面加载时执行
onMounted(() => {
	loadSettings()
})



const handleBindPhone = () => {
	uni.showToast({
		title: '功能开发中',
		icon: 'none'
	})
}

const handleSetPassword = () => {
	uni.showToast({
		title: '功能开发中',
		icon: 'none'
	})
}

const handleBindWechat = () => {
	uni.showToast({
		title: '功能开发中',
		icon: 'none'
	})
}

const handleLogout = () => {
	uni.showToast({
		title: '功能开发中',
		icon: 'none'
	})
}

const loadSettings = () => {
	const storedSettings = uni.getStorageSync('settings')
	if (storedSettings) {
		Object.assign(settings, JSON.parse(storedSettings))
	}
}

const toggleSetting = (key) => {
	settings[key] = !settings[key]
	uni.setStorageSync('settings', JSON.stringify(settings))
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	padding:192rpx 0 30rpx 0;
	box-sizing: border-box;

	.tag-page-header{
		background: #FFF;
	}
}

.section {
	margin-top: 20rpx;
	background-color: #fff;
	
	.section-header {
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #eee;
		
		.title {
			font-size: 28rpx;
			color: #999;
		}
	}
}

.menu-list {
	.menu-item {
		display: flex;
		align-items: center;
		height: 100rpx;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #eee;
		
		&:last-child {
			border-bottom: none;
		}
		
		text {
			font-size: 30rpx;
			color: #333;
			
			&.value {
				flex: 1;
				text-align: right;
				margin-right: 20rpx;
				color: #999;
			}
			
			&.iconfont {
				font-size: 32rpx;
				color: #999;
			}
		}
		
		switch {
			transform: scale(0.8);
			margin-right: -10rpx;
		}
	}
}

.btn-section {
	margin-top: 60rpx;
	padding: 0 30rpx;
	
	.logout-btn {
		height: 88rpx;
		line-height: 88rpx;
		background: #fff;
		color: $color-danger;
		font-size: 32rpx;
		border-radius: 12rpx;
	}

}
.nav-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
    .nav-header-item {
      display: flex;
      align-items: center;
      
    }
    .nav-header-item-button {
        margin: 0 10rpx;
      }
	}
</style> 