<template>
	<view class="container">
		<NavigationCustorm>
			<template #title>
				设置
			</template>
		</NavigationCustorm>
		<div class="content">
		<!-- 账号安全 -->
		<view class="section">
			<view class="menu-list ">
				<view class="menu-item nav-header" @tap="handleAccountSecurity">
					<text>账号与安全</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>
		
		<!-- 消息通知 -->
		<view class="section">
			<view class="menu-list">
				<view class="menu-item nav-header">
					<text>消息</text>
					<switch 
					color="#66D47E"
						:checked="settings.messageNotify" 
						@change="toggleSetting('messageNotify')"
					></switch>
				</view>
				<view class="menu-item nav-header" @tap="handleYouthMode">
					<text>青少年模式</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
				<view class="menu-item nav-header"  @tap="handlePrivacySettings">
					<text>隐私设置</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>
		
		<!-- 通用 -->
		<view class="section">
			<view class="section-header">
				<text class="title">通用</text>
			</view>
			<view class="menu-list">
				<view class="menu-item" @tap="clearCache">
					<text>清除缓存</text>
					<text class="value">{{cacheSize}}</text>
					<text class="iconfont icon-arrow-right"></text>
				</view>
				<view class="menu-item" @tap="checkUpdate">
					<text>检查更新</text>
					<text class="value">当前版本 {{version}}</text>
					<text class="iconfont icon-arrow-right"></text>
				</view>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view class="btn-section">
			<button class="logout-btn" @tap="logout">退出登录</button>
		</view>
	</div>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { getVersion } from '@/common/utils'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'

// 使用 store
const userStore = useUserStore()

// 响应式数据
const userInfo = reactive({
	phone: ''
})

const settings = reactive({
	messageNotify: true,
	sound: true,
	vibrate: true,
	allowMessage: true,
	publicProfile: true
})

const cacheSize = ref('0MB')

const version = ref(getVersion())

// 生命周期
onMounted(() => {
	loadSettings()
	getCacheSize()
})

// 方法
const handleAccountSecurity = () => {
	uni.navigateTo({
		url: '/pages/settings/accountSecurity'
	})
}

// 加载设置
const loadSettings = () => {
	const savedSettings = uni.getStorageSync('settings')
	if (savedSettings) {
		Object.assign(settings, JSON.parse(savedSettings))
	}
}

// 切换设置
const toggleSetting = (key) => {
	settings[key] = !settings[key]
	uni.setStorageSync('settings', JSON.stringify(settings))
}

const handleYouthMode = () => {

	uni.navigateTo({
		url: '/pages/settings/teenageMode'
	})
}

const handlePrivacySettings = () => {
	uni.showToast({
		title: '功能开发中',
		icon: 'none'
	})
}

// 获取缓存大小
const getCacheSize = async () => {
	// #ifdef APP-PLUS
	try {
		uni.getStorageInfo({
			success: function (res) {
        cacheSize.value = res?.currentSize ? res.currentSize + 'MB' : '0MB'
			}
		})
	} catch (error) {
		cacheSize.value = '获取失败'
	}
	// #endif
}

// 清除缓存
const clearCache = () => {
	uni.showModal({
		title: '提示',
		content: '确定要清除缓存吗？',
		success: async (res) => {
			if (res.confirm) {
				try {
					// 清除缓存的具体实现，这里需要根据实际API调用方式修改
					// await $api.clearCache()
					uni.clearStorageSync()
					getCacheSize()
					uni.showToast({
						title: '清除成功',
						icon: 'success'
					})
				} catch (error) {
					uni.showToast({
						title: '清除失败',
						icon: 'none'
					})
				}
			}
		}
	})
}

// 获取版本号
// const getVersion = () => {
	// #ifdef APP-PLUS
	// version.value = plus.runtime.version
	// #endif
// }

// 检查更新
const checkUpdate = () => {
	// TODO: 实现检查更新功能
	uni.showToast({
		title: '已是最新版本',
		icon: 'none'
	})
}

// 退出登录
const logout = () => {
	uni.showModal({
		title: '提示',
		content: '确定要退出登录吗？',
		success: (res) => {
			if (res.confirm) {
				userStore.logout()
			}
		}
	})
}
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background: #F4F8FB;
	padding-bottom: 120rpx;
	box-sizing: border-box;
	.tag-page-header{
		background: #FFF;
	}
}
.content{
	padding-top: 192rpx;
}
.section {
	margin-top: 20rpx;
	background-color: #fff;
	
	.section-header {
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #eee;
		
		.title {
			font-size: 28rpx;
			color: #999;
		}
	}
}

.menu-list {
	.menu-item {
		display: flex;
		align-items: center;
		height: 100rpx;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #eee;
		
		&:last-child {
			border-bottom: none;
		}
		
		text {
			font-size: 30rpx;
			color: #333;
			
			&.value {
				flex: 1;
				text-align: right;
				margin-right: 20rpx;
				color: #999;
			}
			
			&.iconfont {
				font-size: 32rpx;
				color: #999;
			}
		}
		
		switch {
			transform: scale(0.8);
			margin-right: -10rpx;
		}
	}
}

.btn-section {
	margin-top: 60rpx;
	padding: 0 30rpx;
	
	.logout-btn {
		height: 92rpx;
		line-height: 92rpx;
		background: #66D47E;
		color: #fff;
		border: none;
		border-radius: 46rpx;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 32rpx;
		cursor: pointer;
		transition: background 0.2s;
	}

}
.nav-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
</style> 