<template>
  <view class="safety-container">
    <NavigationCustorm :isFixed="false" :color="'#fff'">
      <template #title>
        安全中心
      </template>
    </NavigationCustorm>
    <view class="safety-content">
      <!-- 头部区域 -->
      <view class="header">
        <view class="header-content">
          <view class="header-text">
            <text class="main-text">安全保障守护每一程</text>
            <text class="sub-text">为你提供多重保护,全方位防护随行</text>
          </view>
          <view class="shield-icon">
            <image src="@/static/images/setting/safety-protect.png" mode="widthFix" style="width: 100%;height: 100%;">
            </image>
          </view>
        </view>
      </view>

      <!-- 3重保障卡片 -->
      <view class="guarantee-card">
        <text class="card-title">3重保障</text>
        <view class="guarantee-items">
          <view class="guarantee-item">

            <text class="item-title">实时轨迹保护</text>
            <view class="item-desc">安全随行 <view class="item-icon purple">
                <image src="@/static/images/setting/icon-3.png" mode="widthFix" style="width: 54rpx;height: 54rpx">
                </image>
              </view>
            </view>
          </view>
          <view class="guarantee-item">

            <text class="item-title">24小时专线</text>
            <view class="item-desc">全程守护 <view class="item-icon green">
                <image src="@/static/images/setting/icon-2.png" mode="widthFix" style="width: 54rpx;height: 54rpx">
                </image>
              </view>
            </view>
          </view>
          <view class="guarantee-item">

            <text class="item-title">紧急联系人</text>
            <view class="item-desc">行程分享 <view class="item-icon blue">
                <image src="@/static/images/setting/icon-1.png" mode="widthFix" style="width: 54rpx;height: 54rpx">
                </image>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="setting-item baojing" @click="handleAlarm">
        <image src="@/static/images/setting/icon-4.png" mode="widthFix"
          style="width: 32rpx;height: 32rpx;margin-right: 10rpx;"></image> 一键报警
      </view>
      <view class="setting-item" @click="handleRecord">
        <image src="@/static/images/setting/icon-6.png" mode="widthFix"
          style="width: 32rpx;height: 32rpx;margin-right: 10rpx;"></image> 一键录音
      </view>
      <!-- <view class="setting-item">
        <image src="@/static/images/setting/icon-5.png" mode="widthFix"
          style="width: 32rpx;height: 32rpx;margin-right: 10rpx;"></image> 实时分享
      </view> -->
    </view>
  </view>
</template>

<script setup>
// 页面逻辑可以在这里添加
import { ref } from 'vue'
import NavigationCustorm from '@/components/navigation-custorm/index.vue'



const handleAlarm = () => {
  uni.makePhoneCall({
    phoneNumber: '110' //仅为示例
  });
}
const recorderManager = ref(null)
const recordingText =ref('')

const initRecord = () => {
  recorderManager.value = uni.getRecorderManager()

  recorderManager.value?.onStart(() => {
    console.log('开始录音')
    recordingText.value = '正在录音...'
  })

  recorderManager.value?.onStop(res => {
    console.log('录音结束', res)
    if (res.tempFilePath) {
      sendVoiceMessage(res.tempFilePath, res.duration)
    }
  })

  recorderManager.value?.onError(err => {
    console.error('录音错误', err)
    uni.showToast({
      title: '录音失败',
      icon: 'error'
    })
  })
}
initRecord()

const handleRecord = () => {
  recorderManager.value.start({
    duration: 1000*60*10,
    sampleRate: 16000,
    numberOfChannels: 1,
    encodeBitRate: 48000,
    format: 'mp3'
  })
}
</script>

<style scoped lang="scss">
.safety-container {
  min-height: 100vh;
  background-color: #44baff;
  display: flex;
  flex-direction: column;
}

.safety-content {
  background-color: #f4f8fb;
  flex: 1;
}

.header {
  background-image: url('@/static/images/setting/safety-bg.png');
  padding: 20rpx 30rpx 60rpx;
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

:deep(.tag-page-header-title) {
  color: #fff !important;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;

  .time {
    color: white;
    font-size: 28rpx;
    font-weight: 500;
  }

  .status-icons {
    display: flex;
    gap: 10rpx;

    .icon {
      color: white;
      font-size: 24rpx;
    }
  }
}

.header-content {
  margin-top: 26rpx;
  position: relative;

  .back-btn {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;

    .back-icon {
      color: white;
      font-size: 40rpx;
      font-weight: bold;
    }
  }

  .title {
    color: white;
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    display: block;
    margin-bottom: 40rpx;
  }

  .header-text {
    .main-text {
      color: white;
      font-size: 48rpx;
      font-weight: bold;
      display: block;
      margin-bottom: 20rpx;
      line-height: 1.2;
    }

    .sub-text {
      color: rgba(255, 255, 255, 0.9);
      font-size: 28rpx;
      display: block;
      line-height: 1.4;
    }
  }

  .shield-icon {
    position: absolute;
    right: 0;
    top: -60rpx;
    width: 200rpx;
    height: 210rpx;
    z-index: 10;

  }
}

.guarantee-card {
  background: white;
  margin: -30rpx 30rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 22rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  z-index: 9;
  position: relative;
  top: -18rpx;

  .card-title {
    color: #4A90E2;
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 40rpx;
    display: block;
  }

  .guarantee-items {
    display: flex;
    justify-content: space-between;
    gap: 20rpx;
  }

  .guarantee-item {
    flex: 1;
    text-align: center;
    background: rgba(68, 186, 255, 0.1);
    padding: 16rpx;
    border-radius: 16rpx;

    .item-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20rpx;

      .icon-text {
        color: white;
        font-size: 32rpx;
      }
    }

    .item-title {
      color: #333;
      font-size: 28rpx;
      font-weight: 500;
      display: block;
      margin-bottom: 10rpx;
    }

    .item-desc {
      color: #666;
      font-size: 22rpx;
      display: flex;
    }
  }
}

.setting-item {
  height: 92rpx;
  border-radius: 92rpx;
  border: 1rpx solid #D1D1D1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 30rpx;
  margin-bottom: 40rpx;
  background-color: #fff;
  color: #333333;
  font-size: 32rpx;
  font-weight: 500;
  box-sizing: border-box;

  &.baojing {
    background-color: #FF5F54;
    border: none;
    color: #fff;
  }
}
</style>