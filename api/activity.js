// 活动相关API接口
import request from '@/utils/request'

// 创建活动
export function createActivity(data) {
	return request({
		url: '/activity/create',
		method: 'POST',
		data
	})
}

// 获取活动列表
export function getActivityList(params) {
	return request({
		url: '/activity/list',
		method: 'GET',
		params
	})
}

// 获取活动详情
export function getActivityDetail(id) {
	return request({
		url: `/activity/detail/${id}`,
		method: 'GET'
	})
}

// 参与活动
export function joinActivity(id) {
	return request({
		url: `/activity/join/${id}`,
		method: 'POST'
	})
}

// 取消参与活动
export function cancelJoinActivity(id) {
	return request({
		url: `/activity/cancel/${id}`,
		method: 'POST'
	})
}

// 删除活动
export function deleteActivity(id) {
	return request({
		url: `/activity/delete/${id}`,
		method: 'DELETE'
	})
}

// 更新活动
export function updateActivity(id, data) {
	return request({
		url: `/activity/update/${id}`,
		method: 'PUT',
		data
	})
} 