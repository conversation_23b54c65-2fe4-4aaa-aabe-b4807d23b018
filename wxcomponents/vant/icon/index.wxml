<view
  class="custom-class {{ classPrefix }} {{ isImageName ? 'van-icon--image' : classPrefix + '-' + name }}"
  style="{{ color ? 'color: ' + color + ';' : '' }}{{ size ? 'font-size: ' + sizeWithUnit + ';' : '' }}{{ customStyle }}"
  bind:tap="onClick"
>
  <van-info
    wx:if="{{ info !== null || dot }}"
    dot="{{ dot }}"
    info="{{ info }}"
    custom-class="van-icon__info"
  />
  <image
    wx:if="{{ isImageName }}"
    src="{{ name }}"
    mode="aspectFit"
    class="van-icon__image"
  />
</view>
