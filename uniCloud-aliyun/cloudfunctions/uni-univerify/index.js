'use strict';
exports.main = async (event, context) => {
	// event里包含着客户端提交的参数
	const res = await uniCloud.getPhoneNumber({
		appid: '__UNI__F6E35A0', // 替换成自己开通一键登录的应用的DCloud appid
		provider: 'univerify',
		access_token: event.access_token,
		openid: event.openid
	})

	console.log('uni-univerfiy====res>',res);
	// 执行用户信息入库等操作，正常情况下不要把完整手机号返回给前端
	// 如果数据库在uniCloud上，可以直接入库
	// 如果数据库不在uniCloud上，可以通过 uniCloud.httpclient
	// API，将手机号通过http方式传递给其他服务器的接口，详见：https://doc.dcloud.net.cn/uniCloud/cf-functions?id=httpclient
	const res2 = await uniCloud.httpclient.request('http://47.123.3.183/api/auth/onekeyGetPhoneNumber', {
		method: 'POST',
		data: res,
		contentType: 'json', // 指定以application/json发送data内的数据
		dataType: 'json' // 指定返回值为json格式，自动进行parse
	})
	console.log('uni-univerfiy====res2>',res2);
	return {
		code: 200,
		message: '获取手机号成功',
		data: res2.data
	}
}