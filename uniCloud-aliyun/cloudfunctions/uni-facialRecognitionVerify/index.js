exports.main = async (event, context) => {
  const frvManager = uniCloud.getFacialRecognitionVerifyManager({
    requestId: context.requestId
  })
  
  const { realName, idCard, metaInfo, action,certifyId} = event
  
  if(action === 'getCertifyId'){
	  try {
	  	const result = await frvManager.getCertifyId({
	  	  realName: realName,
	  	  idCard: idCard,
	  	  metaInfo: metaInfo
	  	})
	  	console.log('getCertifyId-result',result)
      return result
	  } catch (error) {
		console.log('getCertifyId-error',error)
    return '认证id获取失败'
	  }
 
  }else if(action === 'getAuthResult'){
    try {
      const result = await frvManager.getAuthResult({
      certifyId: certifyId
      })
      console.log('getAuthResult-result',result)
      return result
    } catch (error) {
      console.log('getAuthResult-error',error)
      return '获取认证结果失败'
    }
  }
}
