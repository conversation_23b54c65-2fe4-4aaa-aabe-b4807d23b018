# Small Island 社区应用

一个基于 uni-app 开发的移动端社区应用，支持 iOS 和 Android 平台。

## 框架搭建者
- 姓名：diwanjian
- 邮箱：<EMAIL>

## 技术栈

- 框架：uni-app (Vue 3)
- 状态管理：Pinia
- UI 框架：uni-ui
- 图标：iconfont
- 样式：SCSS

## 主要功能

### 1. 用户系统
- 手机号注册/登录
- 第三方登录（微信、QQ）
- 用户资料编辑
- 关注/粉丝管理

### 2. 社区功能
- 动态发布（文字、图片、位置、话题）
- 点赞、评论、分享
- 话题系统
- 动态列表（关注、推荐、最新）

### 3. 消息系统
- 私信聊天
- 消息通知
- 点赞和评论提醒
- 系统通知

### 4. 商城功能
- 商品展示（图片、名称、描述、价格）
- 轮播图广告展示
- 商品搜索功能
- 商品分类浏览
- 价格和销量显示
- 商品详情页

### 5. 附近的人
- 基于地理位置的用户发现
- 距离筛选（1km、3km、5km、10km）
- 用户位置权限管理
- 实时距离计算
- 用户最后活跃时间显示
- 下拉刷新和加载更多
- 用户资料快速查看

### 6. 其他功能
- 实时聊天
- 图片上传
- 位置服务
- 表情包支持

## 项目结构

```
├── common          # 公共工具和配置
├── components      # 组件
├── pages          # 页面
│   ├── index      # 首页
│   ├── message    # 消息
│   ├── community  # 商城
│   ├── nearby     # 附近的人
│   ├── profile    # 个人中心
│   ├── post       # 发布相关
│   ├── chat       # 聊天相关
│   └── user       # 用户相关
├── static         # 静态资源
├── stores         # Pinia 状态管理
└── uni.scss       # 全局样式变量
```

## 开发环境搭建

1. 安装依赖：
```bash
npm install
# 或
yarn install
```

2. 运行项目：
```bash
# 运行到 H5
npm run dev:h5

# 运行到微信小程序
npm run dev:mp-weixin

# 运行到 App
npm run dev:app
```

3. 打包发布：
```bash
# 打包 H5
npm run build:h5

# 打包微信小程序
npm run build:mp-weixin

# 打包 App
npm run build:app
```

## 注意事项

### 1. 基础配置
- 需要配置相应平台的 AppID 和密钥
- 需要配置后端服务器地址
- 第三方登录需要配置对应平台的证书和密钥
- 地图功能需要配置对应平台的地图 SDK

### 2. iOS 打包注意事项
- 需要申请 Apple Developer 账号
- 需要配置 iOS 证书和描述文件
- 注意 iOS 隐私权限描述配置：
  - NSCameraUsageDescription (相机权限)
  - NSPhotoLibraryUsageDescription (相册权限)
  - NSLocationWhenInUseUsageDescription (位置权限)
  - NSMicrophoneUsageDescription (麦克风权限)
- 第三方 SDK 需要在 Info.plist 中配置相应的 Scheme
- 注意 iOS 14+ 需要配置 ATTrackingManager 权限

### 3. Android 打包注意事项
- 需要配置应用签名信息
- Android 11+ 需要配置 package visibility
- 注意 Android 动态权限申请：
  - CAMERA (相机权限)
  - WRITE_EXTERNAL_STORAGE (存储权限)
  - ACCESS_FINE_LOCATION (定位权限)
  - RECORD_AUDIO (录音权限)
- 需要在 AndroidManifest.xml 中配置第三方 SDK 的权限
- 注意 Android 应用大小优化

### 4. 跨平台兼容性建议
- 使用条件编译处理平台差异：
```js
// #ifdef APP-PLUS
// 仅在 App 平台生效的代码
// #endif

// #ifdef APP-PLUS-IOS
// 仅在 iOS 平台生效的代码
// #endif

// #ifdef APP-PLUS-ANDROID
// 仅在 Android 平台生效的代码
// #endif
```
- 样式兼容性处理：
  - iOS 底部安全区域适配
  - Android 软键盘弹出处理
  - 不同机型屏幕适配

### 5. 性能优化建议
- 图片资源压缩和优化
- 使用 lazyload 延迟加载
- 合理使用组件缓存
- 避免频繁 DOM 操作
- 减少不必要的数据更新
- 大列表使用虚拟列表

### 6. 云打包配置
- 配置 manifest.json：
  - 基础配置
  - App 图标配置
  - App 启动页配置
  - 权限配置
  - 模块配置
- 云打包时选择合适的 SDK 版本
- 注意控制 App 包体积大小
- 使用自定义基座调试

### 7. 发布相关
- iOS 需要遵守 App Store 审核规范
- Android 需要注意各应用市场的要求
- 准备隐私政策和用户协议
- 准备应用更新机制

### 8. 新功能开发注意事项

#### 8.1 位置服务开发
- 需要在 manifest.json 中配置位置权限
- iOS 需要配置 NSLocationWhenInUseUsageDescription
- Android 需要配置 ACCESS_FINE_LOCATION 权限
- 注意处理位置权限被拒绝的情况
- 实现位置刷新机制
- 注意位置服务的耗电优化

#### 8.2 商城功能开发
- 实现商品数据的缓存机制
- 图片使用懒加载优化
- 实现商品搜索的防抖处理

#### 8.3 悟空IM配置
- 需要在项目根目录创建 `.env` 文件并配置以下环境变量：
```bash
# WuKongIM V2 Configuration
VUE_APP_WUKONG_API_URL=http://localhost:5001  # 替换为您的悟空IM API服务器地址
VUE_APP_WUKONG_WS_URL=您的WebSocket服务器地址
```

- 确保已安装最新版本的悟空IM SDK：
```bash
npm install wukongimjssdk@latest
```

- 聊天功能开发注意事项：
  - 消息发送前要确保用户已登录
  - 处理消息发送失败的重试机制
  - 实现消息本地存储
  - 处理网络断开重连机制
  - 注意消息时间显示格式化
  - 处理不同类型消息的展示（文本、图片、语音等）
  - 实现消息已读状态同步
  - 处理群聊和私聊的不同逻辑

## 贡献指南

1. Fork 本仓库
2. 创建您的特性分支 (git checkout -b feature/AmazingFeature)
3. 提交您的更改 (git commit -m 'Add some AmazingFeature')
4. 推送到分支 (git push origin feature/AmazingFeature)
5. 打开一个 Pull Request

## 许可证

本项目基于 MIT 许可证开源，详见 [LICENSE](LICENSE) 文件。

## 项目介绍

这是一个基于uni-app框架开发的社交社区应用，具有动态分享、即时通讯、话题讨论等功能。项目采用Vue.js作为主要开发语言，使用Vuex进行状态管理，并集成了多个常用的第三方服务。

## 技术栈

- uni-app
- Vue.js
- Vuex
- SCSS
- ES6+

## 目录结构

```
├── common                 # 公共工具和配置
│   ├── api.js            # API接口管理
│   ├── request.js        # 网络请求封装
│   └── utils.js          # 工具函数
├── components            # 公共组件
├── pages                 # 页面文件
│   ├── index            # 首页-动态列表
│   ├── message          # 消息页面
│   ├── community        # 社区/发现页面
│   ├── profile          # 个人中心
│   └── login            # 登录页面
├── static               # 静态资源
│   ├── images          # 图片资源
│   ├── icons           # 图标资源
│   └── styles          # 样式资源
├── store                # Vuex状态管理
│   └── index.js        # Store配置
├── uni.scss            # 全局样式变量
└── pages.json          # 全局配置文件
```

## 功能特性

### 1. 用户系统
- 手机号注册/登录
- 第三方登录(微信、Apple)
- 个人信息管理
- 关注/粉丝系统

### 2. 动态系统
- 发布图文动态
- 点赞/评论功能
- 动态列表展示
- 图片上传预览

### 3. 社区系统
- 话题分类
- 话题讨论
- 内容搜索
- 热门推荐

### 4. 消息系统
- 私信聊天
- 消息通知
- 点赞/评论提醒
- 系统通知

### 5. 其他功能
- 主题切换
- 多语言支持
- 图片压缩
- 缓存管理

## 安装和运行

1. 克隆项目
```bash
git clone [项目地址]
```

2. 安装依赖
```bash
npm install
```

3. 运行项目
```bash
# 运行到H5
npm run dev:h5

# 运行到APP
npm run dev:app

# 运行到微信小程序
npm run dev:mp-weixin
```

4. 发布构建
```bash
# H5平台
npm run build:h5

# APP平台
npm run build:app

# 微信小程序
npm run build:mp-weixin
```

## 开发规范

### 命名规范
- 文件夹名：小写中划线
- 组件名：大驼峰
- 变量名：小驼峰
- 常量名：大写下划线

### 代码规范
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 遵循Vue.js官方风格指南

### Git提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式
- refactor: 重构
- test: 测试
- chore: 构建过程或辅助工具的变动

## API接口

项目支持以下登录相关接口：

- `POST /auth/login` - 短信验证码登录
- `POST /auth/qq-login` - QQ登录
- `POST /auth/wechat-login` - 微信登录
- `POST /auth/univerify-login` - 一键登录
- `POST /common/sms/send` - 发送短信验证码

### 微信登录功能实现

### 功能特点
✅ **完整的微信登录流程**
- 支持微信授权登录
- 获取用户基本信息（昵称、头像等）
- 支持UnionID机制，实现多应用间用户身份统一
- 安全的code换取模式，AppSecret保存在服务器端

✅ **完善的错误处理**
- 详细的错误码处理
- 用户友好的错误提示
- 网络异常处理
- 登录状态管理

✅ **用户体验优化**
- 登录中状态显示
- 自动跳转到相应页面
- 登录成功提示
- 协议同意检查

### 配置说明

#### 1. 微信开放平台配置
1. 访问 [微信开放平台](https://open.weixin.qq.com/)
2. 添加移动应用并提交审核，获取AppID和AppSecret
3. 申请开通微信登录功能并等待审核通过
4. 配置iOS UniversalLinks（iOS平台必需）

#### 2. 项目配置
在 `manifest.json` 中已配置微信登录：
```json
{
  "app-plus": {
    "distribute": {
      "sdkConfigs": {
        "oauth": {
          "weixin": {
            "appid": "wxd55a943553bb707a",
            "UniversalLinks": "https://static-mp-15a7410c-ae89-44ab-904a-137eac7f7fb6.next.bspapp.com/uni-universallinks/__UNI__218117B/"
          }
        }
      }
    }
  }
}
```

### 使用方法

#### 1. 前端调用
```javascript
// 在登录页面中调用微信登录
uni.login({
  provider: 'weixin',
  onlyAuthorize: true, // 仅请求授权认证
  success: async (authResult) => {
    const { code } = authResult;
    
    // 调用store中的微信登录处理方法
    const result = await userStore.wechatLogin({
      code: code
    });
    
    if (result.success) {
      // 登录成功处理
      handleLoginSuccess(result.message);
    } else {
      // 登录失败处理
      handleLoginError(result);
    }
  },
  fail: (error) => {
    // 错误处理
    console.log('微信登录失败:', error);
  }
});
```

#### 2. 统一登录接口
所有登录方式（短信验证码、微信、QQ、一键登录）都使用统一的 `/auth/login` 接口：

**接口地址**：`POST /auth/login`

**请求参数**：
```json
// 短信验证码登录
{
  "username": "手机号",
  "code": "验证码"
}

// 微信登录
{
  "code": "微信授权码",
  "loginType": "wechat"
}

// QQ登录
{
  "openid": "QQ openid",
  "access_token": "QQ access_token",
  "userInfo": "QQ用户信息",
  "loginType": "qq"
}

// 一键登录
{
  "phone": "手机号",
  "code": "验证码"
}
```

**返回数据**：
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "tokenValue": "用户token",
    "tokenName": "token",
    "loginId": "用户ID",
    "loginKey": "登录密钥"
  }
}
```

#### 3. 后端处理流程
后端需要根据 `loginType` 参数判断登录方式：
1. **短信验证码登录**：验证手机号和验证码
2. **微信登录**：使用code + AppSecret向微信服务器换取用户信息
3. **QQ登录**：使用openid + access_token向QQ服务器获取用户信息
4. **一键登录**：验证手机号和运营商token

### 注意事项

1. **测试环境**：HBuilderX标准基座仅供功能体验，正式使用需要自定义基座
2. **权限配置**：确保manifest.json中已开启相应的OAuth模块
3. **平台差异**：不同平台的登录配置可能有差异，请参考官方文档

### 微信登录特殊说明

1. **安全性**：
   - 推荐在服务器端保存AppSecret，客户端只传递code
   - 避免将AppSecret打包到客户端，防止密钥泄露
   - 实现code有效期校验（微信code有效期5分钟）

2. **UnionID机制**：
   - 同一微信开放平台下的不同应用，同一用户的UnionID相同
   - 可实现多应用间的用户身份统一
   - 新应用需要绑定到同一微信开放平台账号

3. **审核要求**：
   - 微信登录功能需要提交审核，审核通过后才能正式使用
   - 确保应用符合微信开放平台的规范要求
   - 准备好应用截图、隐私政策等审核材料

4. **开发调试**：
   - 开发期间可以使用测试号进行调试
   - 正式发布需要使用审核通过的正式AppID
   - iOS需要配置正确的UniversalLinks

## 登录功能测试指南

### 功能测试清单

- [ ] **一键登录**：检查是否能正常弹出授权页面
- [ ] **短信登录**：验证码发送和验证流程
- [ ] **QQ登录**：授权登录和用户信息获取
- [ ] **微信登录**：code获取和后端验证流程
- [ ] **用户协议**：必须勾选协议才能登录
- [ ] **登录状态**：登录成功后跳转到首页
- [ ] **错误处理**：网络异常、取消登录等情况

### 常见问题排查

1. **登录按钮无响应**：
   - 检查manifest.json中OAuth模块是否已启用
   - 确认是否使用了自定义基座或云端打包
   - 查看控制台是否有错误日志

2. **QQ/微信登录失败**：
   - 确认AppID配置是否正确
   - 检查应用签名是否与开放平台一致
   - 验证iOS UniversalLinks配置

3. **短信验证码收不到**：
   - 检查手机号格式是否正确
   - 确认短信接口配置和余额
   - 查看网络请求是否成功

4. **登录成功但跳转失败**：
   - 检查token是否正确保存
   - 验证用户状态管理是否正常
   - 确认路由配置是否正确

### 开发环境配置检查

```bash
# 检查HBuilderX版本
HBuilderX >= 3.0.0

# 检查uni-app版本
Vue 3 + Composition API

# 检查必要依赖
pinia - 状态管理
```

## 更新日志

### v1.0.0 (2024-03-xx)
- 初始版本发布
- 实现基础功能框架
- 完成用户系统开发
- 实现动态发布功能
- 集成基础UI组件

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 发起 Pull Request

## 许可证

MIT License

## 联系方式

- 项目作者：diwanjian
- 邮箱：<EMAIL>
- 微信：[diwanjian]
- QQ群：[your-qq-group]

## 致谢

## 登录功能配置

### QQ登录配置

1. **申请QQ登录**
   - 访问 [腾讯开放平台](https://connect.qq.com/)
   - 创建移动应用并获取 AppID
   - 配置应用信息和回调地址

2. **项目配置**
   ```json
   // manifest.json -> App模块配置 -> OAuth(登录鉴权) -> QQ登录
   {
     "appid": "您的QQ应用AppID",
     "UniversalLinks": "您的iOS通用链接"
   }
   ```

3. **使用说明**
   - 云端打包或使用自定义调试基座
   - 支持获取QQ用户基本信息（昵称、头像等）
   - 自动处理登录态和用户信息存储

### 微信登录配置

1. **申请微信登录**
   - 访问 [微信开放平台](https://open.weixin.qq.com/)
   - 添加移动应用并提交审核，获取AppID和AppSecret
   - 申请开通微信登录功能并等待审核通过

2. **项目配置**
   ```json
   // manifest.json -> App模块配置 -> OAuth(登录鉴权) -> 微信登录
   {
     "appid": "您的微信应用AppID",
     "UniversalLinks": "您的iOS通用链接"
   }
   ```

3. **安全配置（推荐）**
   - 不在客户端配置AppSecret，由服务器端保存
   - 客户端只获取临时授权码(code)
   - 服务器使用code+AppSecret向微信换取用户信息

4. **使用说明**
   - 支持UnionID机制，实现多应用间用户身份统一
   - 云端打包或使用自定义调试基座
   - 自动处理登录态和用户信息存储

### 其他登录方式

- **一键登录**：集成uni一键登录，支持三大运营商
- **短信登录**：4位验证码，自动登录
- **QQ登录**：支持获取用户基本信息
- **微信登录**：支持UnionID机制

## 聊天功能

### 功能特点
✅ **类似微信的聊天界面**
- 支持文本消息发送和接收
- 支持语音录制和发送（按住说话）
- 支持表情输入面板
- 支持图片发送（相册/拍摄）
- 支持位置分享
- 支持文件发送（开发中）

✅ **用户体验优化**
- 键盘自适应高度调整
- 消息实时显示
- 支持消息历史记录加载
- 语音录制可视化反馈
- 图片预览功能

✅ **悟空IM集成**
- 完整的消息收发功能
- 实时消息同步
- 连接状态监控
- 重连机制

### 使用方法

#### 1. 进入聊天页面
从消息列表或通讯录点击联系人进入聊天页面，或者直接通过路由跳转：

```javascript
uni.navigateTo({
  url: `/pages/chat/index?channelID=${channelID}&channelType=1&nickName=${nickName}`
});
```

#### 2. 发送消息
- **文本消息**：在输入框输入内容，点击发送按钮
- **语音消息**：点击🎤切换到语音模式，按住录音按钮说话
- **表情消息**：点击😀打开表情面板，选择表情
- **图片消息**：点击➕选择相册或拍摄
- **位置消息**：点击➕选择位置分享

#### 3. 消息交互
- **图片预览**：点击图片消息可放大预览
- **语音播放**：点击语音消息可播放
- **位置查看**：点击位置消息可在地图中查看

### 界面截图说明

```
┌─────────────────────────┐
│      聊天界面布局       │
├─────────────────────────┤
│   📱 导航栏: 对方昵称   │
├─────────────────────────┤
│                         │
│     💬 消息列表区域     │
│   (支持上拉加载历史)    │
│                         │
├─────────────────────────┤
│ 🎤  💬输入框  😀  ➕   │
│                         │
│   [表情面板/更多功能]   │
└─────────────────────────┘
```

### 技术特点

#### 1. 响应式设计
- 适配不同屏幕尺寸
- 键盘弹出自动调整布局
- 支持横屏和竖屏模式

#### 2. 性能优化
- 消息列表虚拟滚动
- 图片懒加载
- 语音文件缓存机制

#### 3. 兼容性
- 支持 H5、小程序、App 多端
- 统一的API调用
- 平台特性适配

### 开发说明

#### 文件结构
```
pages/chat/
├── index.vue                 # 主聊天页面
└── components/
    ├── ChatList.vue         # 消息列表组件
    └── ChatMessage.vue      # 单条消息组件
```

#### 主要功能模块

1. **消息管理**
   - 消息发送和接收
   - 消息类型处理
   - 历史消息加载

2. **输入处理**
   - 文本输入
   - 语音录制
   - 多媒体选择

3. **UI交互**
   - 面板切换
   - 键盘适配
   - 滚动控制

#### 配置要求

1. **悟空IM配置**
   需要在本地存储中配置IM连接信息：
   ```javascript
   uni.setStorageSync('im_config', {
     wsUrl: 'wss://your-wukong-server.com',
     userId: 'user_id',
     token: 'auth_token'
   });
   ```

2. **权限配置**
   ```json
   // manifest.json - App权限配置
   {
     "mp-weixin": {
       "permission": {
         "scope.record": "需要录音权限发送语音消息",
         "scope.camera": "需要相机权限拍摄照片",
         "scope.userLocation": "需要位置权限分享位置"
       }
     }
   }
   ```

### 常见问题

#### Q: 无法发送消息？
A: 检查以下几点：
1. 悟空IM是否正确连接
2. 用户是否已登录
3. 网络连接是否正常

#### Q: 语音录制失败？
A: 确认以下设置：
1. 设备是否支持录音
2. 是否授予录音权限
3. 在真机上测试（模拟器可能不支持）

#### Q: 图片发送失败？
A: 检查以下问题：
1. 图片是否过大（建议压缩）
2. 网络是否稳定
3. 服务器是否支持图片上传

### 自定义扩展

#### 1. 添加新的消息类型
在 `ChatMessage.vue` 中添加新的消息类型处理：

```vue
<!-- 添加新消息类型 -->
<view v-if="type === 'custom'" class="custom-message">
  <!-- 自定义消息内容 -->
</view>
```

#### 2. 修改界面主题
在样式文件中修改颜色变量：

```scss
// 主色调
$primary-color: #007AFF;
$success-color: #07c160;
$background-color: #f5f5f5;
```

#### 3. 扩展更多功能
在更多功能面板中添加新功能：

```vue
<view class="more-item" @tap="customFunction">
  <view class="more-icon">
    <text class="icon-text">🔧</text>
  </view>
  <text>自定义</text>
</view>
```

---

## 其他功能文档

### 1. 应用生命周期

应用生命周期仅可在App.vue中监听，在页面监听无效。

- onLaunch: 当uni-app 初始化完成时触发（全局只触发一次）
- onShow: 当 uni-app 启动，或从后台进入前台显示
- onHide: 当 uni-app 从前台进入后台
- onError: 当 uni-app 报错时触发

### 2. 页面生命周期

uni-app 支持如下页面生命周期函数：

- onLoad: 监听页面加载，其参数为上个页面传递的数据，参数类型为Object（用于页面传参）
- onShow: 监听页面显示
- onReady: 监听页面初次渲染完成
- onHide: 监听页面隐藏
- onUnload: 监听页面卸载

### 3. 下拉刷新

在 js 中定义 onPullDownRefresh 处理函数（和onLoad等生命周期函数同级），监听该页面用户下拉刷新事件。

- 需要在 pages.json 里，找到的当前页面的pages节点，并在 style 选项中开启 enablePullDownRefresh
- 当处理完数据刷新后，uni.stopPullDownRefresh 可以停止当前页面的下拉刷新。

### 4. 上拉加载

使用onReachBottom处理函数（和onLoad等生命周期函数同级），监听该页面用户上拉触底事件。

- 可以在pages.json里定义具体页面底部的触发距离onReachBottomDistance，比如设为50，那么滚动页面到距离底部50px时，就会触发onReachBottom事件。
