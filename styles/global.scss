@import './mixins.scss';

// 重置样式
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

view, text, image, button, input, textarea {
  box-sizing: border-box;
}

button {
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  background: none;
  
  &::after {
    border: none;
  }
}

// 通用样式
.safe-bottom {
  @include safe-bottom;
}

.text-ellipsis {
  @include text-ellipsis;
}

.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

// 颜色变量
:root {
  --primary-color: #66D47E;
  --success-color: #4cd964;
  --warning-color: #f0ad4e;
  --error-color: #dd524d;
  --text-color: #333;
  --text-color-secondary: #666;
  --text-color-placeholder: #999;
  --border-color: #eee;
  --background-color: #f5f5f5;
}

// 字体图标
@font-face {
  font-family: 'iconfont';
  src: url('~@/static/fonts/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} 