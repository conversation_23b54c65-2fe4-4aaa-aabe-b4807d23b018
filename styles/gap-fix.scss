/* 
 * Gap 替代方案工具类
 * 用于解决真机中flex布局gap无效的问题
 */

// 水平间距替代方案
@mixin flex-gap-horizontal($gap) {
  > * {
    margin-right: $gap;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

// 垂直间距替代方案
@mixin flex-gap-vertical($gap) {
  > * {
    margin-bottom: $gap;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 网格间距替代方案
@mixin flex-gap-grid($gap, $columns: 3) {
  > * {
    margin-right: $gap;
    margin-bottom: $gap;
    
    &:nth-child(#{$columns}n) {
      margin-right: 0;
    }
    
    &:nth-last-child(-n + #{$columns}) {
      margin-bottom: 0;
    }
  }
}

// 双向间距替代方案
@mixin flex-gap-both($gap) {
  > * {
    margin-right: $gap;
    margin-bottom: $gap;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

// 常用间距类
.flex-gap-8 {
  @include flex-gap-horizontal(8rpx);
}

.flex-gap-12 {
  @include flex-gap-horizontal(12rpx);
}

.flex-gap-16 {
  @include flex-gap-horizontal(16rpx);
}

.flex-gap-20 {
  @include flex-gap-horizontal(20rpx);
}

.flex-gap-24 {
  @include flex-gap-horizontal(24rpx);
}

.flex-gap-32 {
  @include flex-gap-horizontal(32rpx);
}

// 垂直间距类
.flex-gap-v-8 {
  @include flex-gap-vertical(8rpx);
}

.flex-gap-v-12 {
  @include flex-gap-vertical(12rpx);
}

.flex-gap-v-16 {
  @include flex-gap-vertical(16rpx);
}

.flex-gap-v-20 {
  @include flex-gap-vertical(20rpx);
}

// 网格间距类
.flex-gap-grid-16 {
  @include flex-gap-grid(16rpx, 3);
}

.flex-gap-grid-20 {
  @include flex-gap-grid(20rpx, 3);
}

// 双向间距类
.flex-gap-both-8 {
  @include flex-gap-both(8rpx);
}

.flex-gap-both-10 {
  @include flex-gap-both(10rpx);
}

.flex-gap-both-12 {
  @include flex-gap-both(12rpx);
}

.flex-gap-both-16 {
  @include flex-gap-both(16rpx);
}

.flex-gap-both-20 {
  @include flex-gap-both(20rpx);
} 