<template>
	<view v-if="visible" class="date-picker-overlay" @click="closePicker">
		<view class="date-picker-popup" @click.stop>
			<!-- 头部 -->
			<view class="picker-header">
				<text class="picker-title">下单日期</text>
				<view class="close-icon" @click="closePicker">
					<text class="close-text">×</text>
				</view>
			</view>
			
			<!-- 时间范围预设 -->
			<view class="time-range-section">
				<view class="section-title">时间范围</view>
				<view class="range-buttons">
					<view 
						v-for="(range, index) in timeRanges" 
						:key="index"
						:class="['range-btn', selectedRangeIndex === index ? 'active' : '']"
						@click="selectTimeRange(index)"
					>
						{{ range.label }}
					</view>
				</view>
			</view>
			
			<!-- 自定义日期 -->
			<view class="custom-section">
				<view class="custom-header">
					<text class="section-title">自定义</text>
					<uni-icons type="trash" size="16" color="#999" @click="clearCustomDate" />
				</view>
				<view class="date-inputs">
					<view class="date-input-group">
						<input 
							class="date-input"
							:class="{ 'active': activeInput === 'start' }"
							v-model="customStartDate"
							placeholder="开始时间"
							@focus="setActiveInput('start')"
							readonly
						/>
						<text class="date-separator">至</text>
						<input 
							class="date-input"
							:class="{ 'active': activeInput === 'end' }"
							v-model="customEndDate"
							placeholder="结束时间"
							@focus="setActiveInput('end')"
							readonly
						/>
					</view>
					<text class="date-hint">最长可查找时间跨度半年的交易</text>
				</view>
			</view>
			
			<!-- 日期选择器 -->
			<view class="date-picker-section">
				<picker-view 
					class="date-picker-view"
					:value="pickerValue"
					@change="onPickerChange"
				>
					<picker-view-column>
						<view class="picker-item" v-for="year in years" :key="year">{{ year }}年</view>
					</picker-view-column>
					<picker-view-column>
						<view class="picker-item" v-for="month in months" :key="month">{{ month }}月</view>
					</picker-view-column>
					<picker-view-column>
						<view class="picker-item" v-for="day in days" :key="day">{{ day }}日</view>
					</picker-view-column>
				</picker-view>
			</view>
			
					<!-- 确认按钮 -->
		<view class="picker-footer">
			<button class="confirm-btn" @click="confirmSelection">确定</button>
		</view>
	</view>

	<!-- 自定义提示消息 -->
	<view v-if="showCustomToast" class="custom-toast">
		<text class="toast-text">{{ toastMessage }}</text>
	</view>
</view>
</template>

<script>
/**
 * 日期范围选择器组件
 * @description 支持预设时间范围和自定义日期选择
 */
export default {
	name: 'DateRangePicker',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		value: {
			type: Object,
			default: () => ({
				startDate: '',
				endDate: ''
			})
		}
	},
	data() {
		return {
			selectedRangeIndex: -1,
			customStartDate: '',
			customEndDate: '',
			activeInput: '',
			pickerValue: [0, 0, 0],
			years: [],
			months: [],
			days: [],
			currentPickerDate: null, // 当前picker显示的日期
			showCustomToast: false, // 控制自定义toast显示
			toastMessage: '', // toast消息内容
			timeRanges: [
				{ label: '近一个月', days: 30 },
				{ label: '近三个月', days: 90 },
				{ label: '近半年', days: 180 }
			]
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.initPicker();
			}
		},
		// 监听activeInput变化，同步picker显示
		activeInput(newVal) {
			if (newVal === 'start' && this.customStartDate) {
				this.syncPickerWithDate(this.customStartDate);
			} else if (newVal === 'end' && this.customEndDate) {
				this.syncPickerWithDate(this.customEndDate);
			}
		}
	},
	methods: {
		/**
		 * 初始化选择器
		 */
		initPicker() {
			this.generateDateOptions();
			this.resetToCurrentDate();
			// 如果有初始值，使用初始值
			if (this.value.startDate && this.value.endDate) {
				this.customStartDate = this.value.startDate;
				this.customEndDate = this.value.endDate;
			}
		},
		
		/**
		 * 生成日期选项
		 */
		generateDateOptions() {
			const currentYear = new Date().getFullYear();
			
			// 生成年份选项
			this.years = [];
			for (let i = currentYear - 2; i <= currentYear + 2; i++) {
				this.years.push(i);
			}
			
			// 生成月份选项
			this.months = [];
			for (let i = 1; i <= 12; i++) {
				this.months.push(i);
			}
			
			// 初始天数（后续会根据年月动态更新）
			this.updateDaysForCurrentMonth();
		},
		
		/**
		 * 重置到当前日期
		 */
		resetToCurrentDate() {
			const now = new Date();
			this.currentPickerDate = new Date(now);
			this.updatePickerValueFromDate(now);
		},
		
		/**
		 * 根据日期更新picker值
		 * @param {Date} date - 要设置的日期
		 */
		updatePickerValueFromDate(date) {
			const year = date.getFullYear();
			const month = date.getMonth() + 1; // 转换为1-12
			const day = date.getDate();
			
			const yearIndex = this.years.indexOf(year);
			const monthIndex = this.months.indexOf(month);
			const dayIndex = Math.min(day - 1, this.days.length - 1); // 确保不超出范围
			
			this.pickerValue = [
				yearIndex >= 0 ? yearIndex : 0,
				monthIndex >= 0 ? monthIndex : 0,
				dayIndex >= 0 ? dayIndex : 0
			];
			
			// 更新天数选项
			this.updateDaysForCurrentMonth();
		},
		
		/**
		 * 根据字符串日期同步picker
		 * @param {String} dateStr - 日期字符串 YYYY-MM-DD
		 */
		syncPickerWithDate(dateStr) {
			if (!dateStr) return;
			
			try {
				const date = new Date(dateStr);
				if (!isNaN(date.getTime())) {
					this.currentPickerDate = new Date(date);
					this.updatePickerValueFromDate(date);
				}
			} catch (error) {
				console.error('日期解析错误:', error);
			}
		},
		
		/**
		 * 选择预设时间范围
		 * @param {Number} index - 时间范围索引
		 */
		selectTimeRange(index) {
			this.selectedRangeIndex = index;
			this.activeInput = ''; // 清除活动输入框
			
			const range = this.timeRanges[index];
			const endDate = new Date();
			const startDate = new Date();
			startDate.setDate(endDate.getDate() - range.days);
			
			this.customStartDate = this.formatDate(startDate);
			this.customEndDate = this.formatDate(endDate);
			
			// 同步picker到结束日期
			this.currentPickerDate = new Date(endDate);
			this.updatePickerValueFromDate(endDate);
		},
		
		/**
		 * 设置活动输入框
		 * @param {String} input - 输入框类型 'start' | 'end' | ''
		 */
		setActiveInput(input) {
			this.activeInput = input;
			this.selectedRangeIndex = -1; // 清除预设选择
		},
		
		/**
		 * 清空自定义日期
		 */
		clearCustomDate() {
			this.customStartDate = '';
			this.customEndDate = '';
			this.selectedRangeIndex = -1;
			this.activeInput = '';
			this.resetToCurrentDate();
		},
		
		/**
		 * picker值改变事件
		 * @param {Object} e - 事件对象
		 */
		onPickerChange(e) {
			const [yearIndex, monthIndex, dayIndex] = e.detail.value;
			this.pickerValue = e.detail.value;
			
			// 先更新天数选项，确保日期有效
			this.updateDaysForCurrentMonth();
			
			// 确保日期索引在有效范围内
			const validDayIndex = Math.min(dayIndex, this.days.length - 1);
			if (validDayIndex !== dayIndex) {
				this.pickerValue[2] = validDayIndex;
			}
			
			// 构造当前选择的日期
			const year = this.years[yearIndex];
			const month = this.months[monthIndex];
			const day = this.days[this.pickerValue[2]];
			
			this.currentPickerDate = new Date(year, month - 1, day);
			
			// 更新对应的日期输入框
			this.updateCustomDateFromPicker();
		},
		
		/**
		 * 根据picker更新自定义日期
		 */
		updateCustomDateFromPicker() {
			if (!this.currentPickerDate) return;
			
			const dateStr = this.formatDate(this.currentPickerDate);
			
			if (this.activeInput === 'start') {
				this.customStartDate = dateStr;
			} else if (this.activeInput === 'end') {
				this.customEndDate = dateStr;
			}
		},
		
		/**
		 * 更新当前月份的天数选项
		 */
		updateDaysForCurrentMonth() {
			const yearIndex = this.pickerValue[0];
			const monthIndex = this.pickerValue[1];
			
			if (yearIndex >= 0 && monthIndex >= 0) {
				const year = this.years[yearIndex];
				const month = this.months[monthIndex];
				
				// 获取该月的天数
				const daysInMonth = new Date(year, month, 0).getDate();
				
				this.days = [];
				for (let i = 1; i <= daysInMonth; i++) {
					this.days.push(i);
				}
			}
		},
		
		/**
		 * 格式化日期
		 * @param {Date} date - 日期对象
		 * @returns {String} 格式化后的日期字符串 YYYY-MM-DD
		 */
		formatDate(date) {
			if (!(date instanceof Date) || isNaN(date.getTime())) {
				return '';
			}
			
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		
		/**
		 * 验证日期范围
		 * @returns {Object} 验证结果
		 */
		validateDateRange() {
			if (!this.customStartDate || !this.customEndDate) {
				return { valid: false, message: '请选择完整的日期范围' };
			}
			
			try {
				const startDate = new Date(this.customStartDate);
				const endDate = new Date(this.customEndDate);
				
				// 检查日期是否有效
				if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
					return { valid: false, message: '日期格式不正确' };
				}
				
				// 检查开始日期是否晚于结束日期
				if (startDate > endDate) {
					return { valid: false, message: '开始日期不能晚于结束日期' };
				}
				
				// 计算时间差（天数）
				const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
				const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
				
				// 检查是否超过半年（180天）
				if (diffDays > 180) {
					return { valid: false, message: '查询时间跨度不能超过半年' };
				}
				
				// 检查是否查询未来日期
				const today = new Date();
				today.setHours(23, 59, 59, 999); // 设置为今天的最后一秒
				
				if (endDate > today) {
					return { valid: false, message: '不能查询未来日期的订单' };
				}
				
				// 检查是否查询过早的日期（可选，比如不允许查询一年前的数据）
				const oneYearAgo = new Date();
				oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
				
				if (startDate < oneYearAgo) {
					return { valid: false, message: '不能查询一年前的订单数据' };
				}
				
				return { valid: true };
				
			} catch (error) {
				console.error('日期验证出错:', error);
				return { valid: false, message: '日期验证失败，请重新选择' };
			}
		},
		
		/**
		 * 显示自定义提示消息
		 * @param {String} message - 提示消息
		 * @param {Number} duration - 显示时长（毫秒）
		 */
		showCustomToastMessage(message, duration = 2000) {
			this.toastMessage = message;
			this.showCustomToast = true;
			
			// 自动隐藏
			setTimeout(() => {
				this.showCustomToast = false;
				this.toastMessage = '';
			}, duration);
		},

		/**
		 * 确认选择
		 */
		confirmSelection() {
			const validation = this.validateDateRange();
			
			if (!validation.valid) {
				this.showCustomToastMessage(validation.message, 2000);
				return;
			}
			
			this.$emit('confirm', {
				startDate: this.customStartDate,
				endDate: this.customEndDate,
				rangeIndex: this.selectedRangeIndex
			});
			this.closePicker();
		},
		
		/**
		 * 关闭选择器
		 */
		closePicker() {
			// 清除自定义toast
			this.showCustomToast = false;
			this.toastMessage = '';
			this.$emit('update:visible', false);
		}
	}
}
</script>

<style lang="scss" scoped>
.date-picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
}

.date-picker-popup {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
	animation: slideUp 0.3s ease-out;
	max-height: 90vh;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

.picker-header {
	position: relative;
	text-align: center;
	padding: 32rpx 24rpx 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.close-icon {
	position: absolute;
	right: 24rpx;
	top: 50%;
	transform: translateY(-50%);
	padding: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10;
	cursor: pointer;
}

.close-text {
	font-size: 32rpx;
	color: #999;
	font-weight: bold;
}

/* 时间范围预设 */
.time-range-section {
	padding: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 20rpx;
}

.range-buttons {
	display: flex;
	gap: 20rpx;
}

.range-btn {
	flex: 1;
	height: 70rpx;
	line-height: 70rpx;
	text-align: center;
	background: #fff;
	border: 1rpx solid #ddd;
	border-radius: 35rpx;
	font-size: 28rpx;
	color: #666;
	transition: all 0.2s;
	
	&.active {
		background: #66D47E;
		border-color: #66D47E;
		color: #fff;
	}
}

/* 自定义日期 */
.custom-section {
	padding: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.custom-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.date-inputs {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.date-input-group {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.date-input {
	flex: 1;
	height: 70rpx;
	padding: 0 20rpx;
	border: 1rpx solid #ddd;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	text-align: center;
	
	&.active {
		border-color: #66D47E;
		color: #66D47E;
	}
	
	&::placeholder {
		color: #999;
	}
}

.date-separator {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.date-hint {
	font-size: 24rpx;
	color: #FF9500;
	text-align: center;
}

/* 日期选择器 */
.date-picker-section {
	padding: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.date-picker-view {
	height: 300rpx;
	width: 100%;
}

/* 确保picker-view的对齐 - 高度必须是item高度的奇数倍 */
.date-picker-view ::v-deep .uni-picker-view-wrapper {
	height: 300rpx;
}

.date-picker-view ::v-deep .uni-picker-view-column {
	height: 300rpx;
	line-height: 60rpx;
}

.date-picker-view ::v-deep .uni-picker-view-group {
	height: 300rpx;
}

.date-picker-view ::v-deep .uni-picker-view-mask {
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.95), transparent 30%, transparent 70%, rgba(255, 255, 255, 0.95));
}

.date-picker-view ::v-deep .uni-picker-view-indicator {
	height: 60rpx;
	border-top: 1rpx solid #e0e0e0;
	border-bottom: 1rpx solid #e0e0e0;
}

.picker-item {
	height: 60rpx;
	line-height: 60rpx;
	text-align: center;
	font-size: 28rpx;
	color: #333;
	display: flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
}

/* 确认按钮 */
.picker-footer {
	padding: 24rpx;
	padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.confirm-btn {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	background: #66D47E;
	color: #fff;
	font-size: 32rpx;
	font-weight: 500;
	border-radius: 45rpx;
	border: none;
	text-align: center;
}

/* 自定义提示消息样式 */
.custom-toast{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	transform: unset !important;
  text-align: center;
  max-width: 100%;
}
.toast-text {
	background: #fff !important;
  border-radius: 0.5rem;
	max-width: 80%;
	background: #fff !important;
	color: #666 !important;
	font-size: 28rpx;
	padding: 40rpx;
}

@keyframes fadeInOut {
	0% {
		opacity: 0;
		transform: translate(-50%, -50%) scale(0.8);
	}
	100% {
		opacity: 1;
		transform: translate(-50%, -50%) scale(1);
	}
}

.toast-text {
	font-size: 28rpx;
	line-height: 1.4;
	word-wrap: break-word;
	color: #fff;
}
</style> 