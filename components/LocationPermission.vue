<template>
	<view class="location-permission" v-if="visible">
		<view class="permission-mask" @tap="close"></view>
		<view class="permission-content">
			<view class="permission-header">
				<image src="/static/index/<EMAIL>" class="local-badge" mode="widthFix"></image>
				<text class="permission-title">开启位置权限</text>
				<text class="permission-title">遇见附近的有趣灵魂！</text>
			</view>
			<view class="permission-body">
				<text class="permission-desc">请在手机设置中开启位置授权，以便我们为你推荐附近的人和活动</text>
				
			</view>
			<view class="permission-footer">
				<button class="btn-confirm" @tap="openSettings">一键开启</button>
				<button class="btn-cancel" @tap="close">暂不开启</button>
			</view>
		</view>
	</view>
</template>

<script>
import { checkOpenGPSServiceByAndroid } from '@/utils/openSettings.js'

export default {
	name: 'LocationPermission',
	props: {
		visible: {
			type: <PERSON>olean,
			default: false
		}
	},
	methods: {
		close() {
			this.$emit('close')
		},
		openSettings() {
			checkOpenGPSServiceByAndroid()
			this.$emit('success')
		}
	}
}
</script>

<style lang="scss" scoped>
.location-permission {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.permission-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
}

.permission-content {
	position: relative;
	width: 600rpx;
	background-color: #fff;
	border-radius: 40rpx;
	padding: 56rpx 40rpx 40rpx 40rpx;
	box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.12);
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 1;
	background-image: url('/static/index/<EMAIL>');
	background-size: cover;
	background-repeat: no-repeat;
	background-position: top center;
}

.permission-header {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 32rpx;
	position: relative;
}

.local-badge {
	position: relative;
    top: -37px;
	width: 190rpx;
	margin-bottom: 12rpx;
	display: block;
}

.location-icon {
	width: 160rpx;
	height: 160rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 128, 0, 0.10);
	border-radius: 50%;
}

.permission-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #222;
	text-align: center;
	margin-bottom: 12rpx;
}

.permission-body {
	margin-bottom: 40rpx;
	width: 100%;
}

.permission-desc {
	font-size: 28rpx;
	color: #999;
	line-height: 1.7;
	margin-bottom: 30rpx;
	display: block;
	text-align: center;
}

.permission-list {
	width: 100%;
	.permission-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 16rpx;
		.item-dot {
			color: #4cd964;
			font-size: 28rpx;
			margin-right: 16rpx;
			line-height: 1.4;
		}
		.item-text {
			font-size: 28rpx;
			color: #666;
			line-height: 1.4;
			flex: 1;
		}
	}
}

.permission-footer {
	display: flex;
	flex-direction: column;
	width: 100%;
	align-items: center;
}

.btn-cancel, .btn-confirm {
	width: 100%;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 34rpx;
	border: none;
	margin: 0;
}

.btn-cancel {
	background-color: #fff;
	color: #999;
	margin-bottom: 24rpx;
	border: 0;
}
.btn-cancel:after{
	border: none;
}
.btn-confirm {
	background-color: #4cd964;
	color: #fff;
	font-weight: bold;
	box-shadow: 0 4rpx 16rpx rgba(76, 217, 100, 0.15);
}
</style> 