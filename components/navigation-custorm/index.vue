<template>
  <view class="tag-page-header" :class="{ fixed: isFixed }">
    <view class="tag-page-header-title">
      <uni-icons type="left" size="24" :color="color" class="back-icon" @click="goBack"></uni-icons>
      <text>
        <slot name="title"></slot>
      </text>
      <text class="right-text">
        <slot name="right"></slot>
      </text>
    </view>
  </view>
</template>
<script setup>
import { defineEmits } from 'vue'
const emit = defineEmits(['goBack'])
const props = defineProps({
  isFixed: {
    type: Boolean,
    default: true
  },
  color:{
    type: String,
    default: '#666'
  }
})
const goBack = () => {
  emit('goBack')
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.tag-page-header {
  padding-top: 100rpx;
  display: block;

  &.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9;
    background-color: #fff;
  }
}

.tag-page-header-title {
  width: 100%;
  height: 92rpx;
  line-height: 92rpx;
  font-size: 34rpx;
  color: #000000;
  font-weight: 600;
  text-align: center;
  position: relative;

}

.back-icon {
  position: absolute;
  left: 32rpx;
}

.right-text {
  position: absolute;
  right: 30rpx;
  font-size: 26rpx;
  color: #000;
  font-family: 苹方-简, 苹方-简;
}
</style>