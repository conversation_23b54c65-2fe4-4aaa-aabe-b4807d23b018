<template>
	<view class="page-header">
		<text class="back-btn" @click="goBack">‹</text>
		<text class="page-title">{{ title }}</text>
		<text class="visitor-mode" v-if="showVisitor" @click="enterVisitorMode">{{ rightText || '游客进入' }}</text>
	</view>
</template>

<script>
export default {
	name: 'PageHeader',
	props: {
		title: {
			type: String,
			default: ''
		},
		showVisitor: {
			type: Boolean,
			default: false
		},
		rightText: {
			type: String,
			default: '游客进入'
		}
	},
	emits: ['back', 'visitor'],
	setup(props, { emit }) {
		const goBack = () => {
			emit('back')
		}
		
		const enterVisitorMode = () => {
			emit('visitor')
		}
		
		return {
			goBack,
			enterVisitorMode
		}
	}
}
</script>

<style lang="scss" scoped>
.page-header {
	display: flex;
	align-items: center;
	padding: 24rpx 32rpx;
	position: relative;
	border-bottom: none;
	
	.back-btn {
		font-size: 48rpx;
		color: #333;
		padding: 20rpx;
		width: 80rpx;
		flex-shrink: 0;
	}
	
	.page-title {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		flex: 1;
		text-align: center;
	}
	
	.visitor-mode {
		font-size: 28rpx;
		color: #666;
		width: 80rpx;
		text-align: center;
		flex-shrink: 0;
	}
}
</style> 