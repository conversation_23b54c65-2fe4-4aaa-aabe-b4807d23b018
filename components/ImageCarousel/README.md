# ImageCarousel 图片轮播组件

一个功能完整的图片轮播组件，支持顶部banner轮播和底部缩略图切换。

## 功能特性

- ✅ 顶部banner图片轮播
- ✅ 底部缩略图列表
- ✅ 点击缩略图切换banner图片
- ✅ 自动播放控制
- ✅ 循环播放
- ✅ 图片计数器显示
- ✅ 缩略图自动滚动居中
- ✅ 支持本地和网络图片
- ✅ 图片点击预览

## 基础用法

```vue
<template>
  <view>
    <ImageCarousel 
      :imageList="images" 
      @change="onChange"
      @imageClick="onImageClick"
    />
  </view>
</template>

<script>
import ImageCarousel from '@/components/ImageCarousel.vue'

export default {
  components: {
    ImageCarousel
  },
  data() {
    return {
      images: [
        '/static/image1.jpg',
        '/static/image2.jpg',
        '/static/image3.jpg'
      ]
    }
  },
  methods: {
    onChange(e) {
      console.log('轮播切换:', e)
    },
    onImageClick(e) {
      console.log('图片点击:', e)
    }
  }
}
</script>
```

## Props 属性

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| imageList | Array | [] | 图片列表数组 |
| autoplay | Boolean | true | 是否自动播放 |
| interval | Number | 3000 | 自动播放间隔时间(ms) |
| duration | Number | 500 | 滑动动画时长(ms) |
| circular | Boolean | true | 是否循环播放 |
| initialIndex | Number | 0 | 初始显示的图片索引 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | {current, image} | 轮播图切换时触发 |
| imageClick | {image, index} | 点击banner图片时触发 |

## 完整示例

```vue
<template>
  <view class="container">
    <!-- 基础用法 -->
    <ImageCarousel 
      :imageList="basicImages" 
      @change="onChange"
      @imageClick="onImageClick"
    />
    
    <!-- 自定义配置 -->
    <ImageCarousel 
      :imageList="customImages" 
      :autoplay="false"
      :interval="5000"
      :duration="800"
      :circular="true"
      :initialIndex="1"
      @change="onChange"
      @imageClick="onImageClick"
    />
  </view>
</template>

<script>
import ImageCarousel from '@/components/ImageCarousel.vue'

export default {
  components: {
    ImageCarousel
  },
  data() {
    return {
      basicImages: [
        '/static/image1.jpg',
        '/static/image2.jpg',
        '/static/image3.jpg'
      ],
      customImages: [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg',
        'https://example.com/image3.jpg'
      ]
    }
  },
  methods: {
    onChange(e) {
      console.log('当前图片索引:', e.current)
      console.log('当前图片:', e.image)
    },
    onImageClick(e) {
      // 使用uni.previewImage预览图片
      uni.previewImage({
        current: e.index,
        urls: this.basicImages
      })
    }
  }
}
</script>
```

## 样式自定义

组件使用scoped样式，如需自定义样式，可以通过以下方式：

```scss
// 在父组件中覆盖样式
.image-carousel {
  .banner-container {
    height: 500rpx; // 自定义banner高度
  }
  
  .thumbnail-item {
    width: 100rpx; // 自定义缩略图大小
    height: 100rpx;
  }
  
  .image-counter {
    background: rgba(255, 0, 0, 0.8); // 自定义计数器样式
  }
}
```

## 注意事项

1. 确保图片路径正确，支持本地图片和网络图片
2. 网络图片需要配置域名白名单
3. 建议图片尺寸保持一致，避免显示异常
4. 缩略图会自动滚动到当前选中位置
5. 点击banner图片会触发imageClick事件，可用于图片预览

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础轮播功能
- 支持缩略图切换
- 支持自动播放和循环播放 