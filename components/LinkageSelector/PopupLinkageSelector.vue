<template>
  <view v-if="visible" class="popup-linkage-selector" @click.stop="onMaskClick">
    <view class="popup-content" @click.stop="">
      <!-- 标题栏 -->
      <view class="popup-header">
        <text class="popup-title">{{ title }}</text>
        <view v-if="selectedDisplay" class="selected-display">
          {{ selectedDisplay }}
        </view>
      </view>
      
      <!-- 主体内容 -->
      <view class="popup-body">
        <!-- 左侧分类列表 -->
        <scroll-view 
          class="category-list" 
          scroll-y 
          :scroll-top="leftScrollTop"
          :scroll-with-animation="true"
        >
          <view 
            v-for="(category, index) in categories" 
            :key="index"
            :class="['category-item', currentCategory === index ? 'active' : '']"
            @click="switchCategory(index)"
          >
            <text class="category-name">{{ category.name }}</text>
            <view v-if="showBadge && getSelectedCount(category.items) > 0" class="badge">
              {{ getSelectedCount(category.items) }}
            </view>
          </view>
        </scroll-view>

        <!-- 右侧选项列表 -->
        <scroll-view 
          class="options-list" 
          scroll-y 
          :scroll-top="rightScrollTop"
          :scroll-with-animation="true"
          @scroll="onRightScroll"
        >
          <view v-for="(category, categoryIndex) in categories" :key="categoryIndex" class="category-section">
            <view class="section-title">{{ category.name }}</view>
            <view class="options-grid">
              <view 
                v-for="(item, itemIndex) in category.items" 
                :key="itemIndex"
                :class="['option-item', isSelected(item) ? 'selected' : '', isDisabled(item) ? 'disabled' : '']"
                @click="toggleOption(item)"
              >
                <text class="option-text">{{ getItemLabel(item) }}</text>
                <view v-if="isSelected(item)" class="check-icon">
                  <text class="check-mark">✓</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 底部操作按钮 -->
      <view class="popup-footer">
        <button class="cancel-btn" @click="onCancel">取消</button>
        <button class="confirm-btn" @click="onConfirm">确定</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PopupLinkageSelector',
  props: {
    // 是否显示弹窗
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: '选择技能'
    },
    // 数据源
    categories: {
      type: Array,
      default: () => []
    },
    // 选中的值
    value: {
      type: Array,
      default: () => []
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 是否显示徽章
    showBadge: {
      type: Boolean,
      default: true
    },
    // 自定义取值字段
    valueKey: {
      type: String,
      default: 'value'
    },
    // 自定义显示字段
    labelKey: {
      type: String,
      default: 'label'
    },
    // 自定义禁用字段
    disabledKey: {
      type: String,
      default: 'disabled'
    },
    // 点击遮罩是否关闭
    closeOnMask: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      currentCategory: 0,
      leftScrollTop: 0,
      rightScrollTop: 0,
      sectionHeights: [],
      sectionTops: [],
      isUserScrolling: false,
      tempValue: [] // 临时选中值，确定时才提交
    }
  },

  computed: {
    selectedDisplay() {
      if (!this.tempValue.length) return ''
      
      // 找到第一个选中项作为显示
      for (const category of this.categories) {
        for (const item of category.items) {
          const value = this.getItemValue(item)
          if (this.tempValue.includes(value)) {
            return this.getItemLabel(item)
          }
        }
      }
      return ''
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        // 弹窗打开时，初始化临时选中值
        this.tempValue = [...this.value]
        this.$nextTick(() => {
          this.calculateSectionPositions()
        })
      }
    },
    
    categories: {
      handler() {
        if (this.visible) {
          this.$nextTick(() => {
            this.calculateSectionPositions()
          })
        }
      },
      deep: true
    }
  },

  methods: {
    // 获取选项显示文本
    getItemLabel(item) {
      if (typeof item === 'string') return item
      return item[this.labelKey] || item.label || item.name || item
    },

    // 获取选项值
    getItemValue(item) {
      if (typeof item === 'string') return item
      return item[this.valueKey] || item.value || item.id || item
    },

    // 判断选项是否被选中
    isSelected(item) {
      const value = this.getItemValue(item)
      return this.tempValue.includes(value)
    },

    // 判断选项是否禁用
    isDisabled(item) {
      if (typeof item === 'string') return false
      return item[this.disabledKey] || item.disabled || false
    },

    // 切换选项
    toggleOption(item) {
      if (this.isDisabled(item)) return

      const value = this.getItemValue(item)
      let newValue = [...this.tempValue]

      if (this.multiple) {
        // 多选模式
        const index = newValue.indexOf(value)
        if (index > -1) {
          newValue.splice(index, 1)
        } else {
          newValue.push(value)
        }
      } else {
        // 单选模式
        newValue = this.isSelected(item) ? [] : [value]
      }

      this.tempValue = newValue
      this.$emit('change', newValue, item)
    },

    // 切换分类
    switchCategory(index) {
      if (this.currentCategory === index) return
      
      this.currentCategory = index
      this.isUserScrolling = false
      
      // 滚动右侧到对应分类位置
      if (this.sectionTops[index] !== undefined) {
        this.rightScrollTop = this.sectionTops[index]
      }

      this.$emit('category-change', index, this.categories[index])
    },

    // 右侧滚动监听
    onRightScroll(e) {
      if (!this.isUserScrolling) {
        this.isUserScrolling = true
        return
      }

      const scrollTop = e.detail.scrollTop
      
      // 根据滚动位置确定当前分类
      for (let i = this.sectionTops.length - 1; i >= 0; i--) {
        if (scrollTop >= this.sectionTops[i] - 10) {
          if (this.currentCategory !== i) {
            this.currentCategory = i
            this.$emit('category-change', i, this.categories[i])
          }
          break
        }
      }
    },

    // 计算各分类区域位置
    calculateSectionPositions() {
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)
        
        this.categories.forEach((_, index) => {
          query.select(`.category-section:nth-child(${index + 1})`).boundingClientRect()
        })
        
        query.exec((res) => {
          if (res && res.length > 0) {
            this.sectionTops = []
            let currentTop = 0
            
            res.forEach((rect, index) => {
              this.sectionTops.push(currentTop)
              if (rect) {
                currentTop += rect.height
              }
            })
          }
        })
      })
    },

    // 获取分类下选中项数量
    getSelectedCount(items) {
      if (!items || !Array.isArray(items)) return 0
      
      return items.filter(item => this.isSelected(item)).length
    },

    // 遮罩点击
    onMaskClick() {
      if (this.closeOnMask) {
        this.onCancel()
      }
    },

    // 取消操作
    onCancel() {
      this.tempValue = [...this.value] // 恢复原始值
      this.$emit('cancel')
      this.$emit('update:visible', false)
    },

    // 确定操作
    onConfirm() {
      this.$emit('input', this.tempValue)
      this.$emit('confirm', this.tempValue)
      this.$emit('update:visible', false)
    },

    // 清空选择
    clear() {
      this.tempValue = []
    },

    // 全选当前分类
    selectAll(categoryIndex = this.currentCategory) {
      if (!this.multiple) return
      
      const category = this.categories[categoryIndex]
      if (!category || !category.items) return

      const availableItems = category.items.filter(item => !this.isDisabled(item))
      const newValues = availableItems.map(item => this.getItemValue(item))
      
      // 合并到现有选择中
      let finalValues = [...this.tempValue]
      newValues.forEach(val => {
        if (!finalValues.includes(val)) {
          finalValues.push(val)
        }
      })

      this.tempValue = finalValues
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-linkage-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.popup-content {
  width: 90%;
  max-width: 750rpx;
  height: 80%;
  max-height: 1200rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
}

.popup-header {
  position: relative;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
  
  .popup-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
  }
  
  .selected-display {
    position: absolute;
    right: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 28rpx;
    color: #4CAF50;
    background: #f1f8e9;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
  }
}

.popup-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.category-list {
  width: 200rpx;
  background: #f8f8f8;
  border-right: 1px solid #eee;
}

.category-item {
  position: relative;
  padding: 30rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;

  &.active {
    background: #fff;
    border-right: 4rpx solid #4CAF50;

    .category-name {
      color: #4CAF50;
      font-weight: 500;
    }
  }

  .category-name {
    font-size: 28rpx;
    color: #333;
    transition: all 0.3s ease;
  }

  .badge {
    min-width: 32rpx;
    height: 32rpx;
    background: #ff4757;
    color: #fff;
    font-size: 20rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8rpx;
  }
}

.options-list {
  flex: 1;
  padding: 0 30rpx;
}

.category-section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    padding: 30rpx 0 20rpx;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 20rpx;
  }
}

.options-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.option-item {
  position: relative;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 120rpx;
  transition: all 0.3s ease;

  &.selected {
    border-color: #4CAF50;
    background: #f1f8e9;

    .option-text {
      color: #4CAF50;
      font-weight: 500;
    }
  }

  &.disabled {
    opacity: 0.5;
    background: #f5f5f5;
    border-color: #ddd;

    .option-text {
      color: #999;
    }
  }

  .option-text {
    font-size: 28rpx;
    color: #333;
    transition: all 0.3s ease;
  }

  .check-icon {
    margin-left: 8rpx;
    
    .check-mark {
      font-size: 24rpx;
      color: #4CAF50;
      font-weight: bold;
    }
  }
}

.popup-footer {
  display: flex;
  padding: 30rpx;
  gap: 30rpx;
  border-top: 1px solid #f0f0f0;
  background: #fff;

  button {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 32rpx;
    border: none;
    transition: all 0.3s ease;
  }

  .cancel-btn {
    background: #f5f5f5;
    color: #666;
    
    &:active {
      background: #ebebeb;
    }
  }

  .confirm-btn {
    background: linear-gradient(135deg, #4CAF50, #81c784);
    color: #fff;
    
    &:active {
      background: linear-gradient(135deg, #45a049, #75bb78);
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style> 