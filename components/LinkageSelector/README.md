# LinkageSelector 联动选择器组件

本目录包含两个联动选择器组件：

## 1. LinkageSelector - 内嵌式联动选择器

适用于页面内嵌入的场景，左右分栏布局。

### 基本用法

```vue
<template>
  <LinkageSelector
    :categories="categories"
    v-model="selectedValues"
    :multiple="true"
    :show-badge="true"
    @change="onChange"
  />
</template>
```

## 2. PopupLinkageSelector - 弹窗式联动选择器

适用于弹窗选择的场景，具有模态框样式。

### 基本用法

```vue
<template>
  <view>
    <!-- 触发按钮 -->
    <button @click="showSelector = true">选择技能</button>
    
    <!-- 弹窗选择器 -->
    <PopupLinkageSelector
      :visible.sync="showSelector"
      title="选择技能"
      :categories="skillCategories"
      v-model="selectedSkills"
      :multiple="true"
      @confirm="onConfirm"
      @cancel="onCancel"
    />
  </view>
</template>

<script>
import PopupLinkageSelector from '@/components/LinkageSelector/PopupLinkageSelector.vue'

export default {
  components: {
    PopupLinkageSelector
  },
  
  data() {
    return {
      showSelector: false,
      selectedSkills: [],
      skillCategories: [
        {
          name: '运动类',
          items: ['爬山', '羽毛球', '网球', '篮球']
        },
        {
          name: '娱乐类', 
          items: ['唱歌', '跳舞', '游戏', '看电影']
        }
      ]
    }
  },
  
  methods: {
    onConfirm(values) {
      console.log('确认选择:', values)
    },
    
    onCancel() {
      console.log('取消选择')
    }
  }
}
</script>
```

## Props 属性

### 共同属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| categories | Array | [] | 分类数据数组 |
| value | Array | [] | 选中的值（支持v-model） |
| multiple | Boolean | true | 是否多选 |
| showBadge | Boolean | true | 是否显示选中数量徽章 |
| valueKey | String | 'value' | 自定义取值字段 |
| labelKey | String | 'label' | 自定义显示字段 |
| disabledKey | String | 'disabled' | 自定义禁用字段 |

### PopupLinkageSelector 特有属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | Boolean | false | 是否显示弹窗（支持.sync修饰符） |
| title | String | '选择技能' | 弹窗标题 |
| closeOnMask | Boolean | true | 点击遮罩是否关闭弹窗 |

## Events 事件

### 共同事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | (values) | 选中值变化时触发（用于v-model） |
| change | (values, item) | 选择变化时触发 |
| category-change | (index, category) | 分类切换时触发 |

### PopupLinkageSelector 特有事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| confirm | (values) | 点击确定按钮时触发 |
| cancel | () | 点击取消按钮时触发 |
| update:visible | (visible) | 弹窗显示状态变化时触发 |

## 数据格式

### 基础数据格式

```javascript
const categories = [
  {
    name: '分类名称',
    items: ['选项1', '选项2', '选项3']
  }
]
```

### 自定义字段格式

```javascript
const categories = [
  {
    name: '电子产品',
    items: [
      { id: 1, name: 'iPhone 15', price: 5999, unavailable: false },
      { id: 2, name: 'MacBook Pro', price: 12999, unavailable: true }
    ]
  }
]

// 使用时指定字段映射
<PopupLinkageSelector
  :categories="categories"
  value-key="id"
  label-key="name"
  disabled-key="unavailable"
/>
```

## 方法

通过ref调用组件方法：

### 共同方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| clear | - | 清空所有选择 |
| selectAll | categoryIndex | 全选指定分类（默认当前分类） |

### 使用示例

```vue
<template>
  <PopupLinkageSelector
    ref="selector"
    :visible.sync="showSelector"
    :categories="categories"
    v-model="selectedValues"
  />
</template>

<script>
export default {
  methods: {
    clearSelection() {
      this.$refs.selector.clear()
    },
    
    selectAllCurrent() {
      this.$refs.selector.selectAll()
    }
  }
}
</script>
```

## 样式自定义

组件使用scoped样式，可以通过深度选择器自定义样式：

```scss
// 自定义弹窗圆角
::v-deep .popup-content {
  border-radius: 32rpx;
}

// 自定义选中项颜色
::v-deep .option-item.selected {
  border-color: #007AFF;
  background: #E3F2FD;
  
  .option-text {
    color: #007AFF;
  }
}

// 自定义确定按钮样式
::v-deep .confirm-btn {
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
}
```

## 完整示例

参考演示页面：
- `pages/example/linkage-selector-demo.vue` - 内嵌式选择器演示
- `pages/example/popup-linkage-selector-demo.vue` - 弹窗式选择器演示 