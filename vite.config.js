import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

export default defineConfig({
  plugins: [uni()],
  optimizeDeps: {
    include: ['wukongimjssdk']
  },
  define: {
    global: 'globalThis'
  },
  resolve: {
    alias: {
      // 处理一些 Node.js 模块在浏览器环境下的兼容性
      buffer: 'buffer',
      process: 'process/browser',
      util: 'util'
    }
  },
  build: {
    rollupOptions: {
      external: [],
      output: {
        globals: {}
      }
    }
  }
}) 