import { defineStore } from 'pinia'
import { userApi, messageApi, homeApi, tagApi, momentsApi, recruitApi } from '@/common/api'
import { setGlobalToken } from '@/common/request'
import WKIMManager from '@/utils/wukongim'
import { useConversationStore } from '@/stores/conversation'
export const useUserStore = defineStore('user', {
  state: () => ({
    // 用户信息
    userInfo: {
      userId: '',
      nickname: '',
      avatar: '',
      signature: '',
      sex: 0,
      birthday: '',
      location: '',
      height: 0,
      weight: 0,
      occupation: '',
      hobbies: [''],
      albumPhotos: [''],
      followerCount: 0,
      followingCount: 0,
      friendCount: 0,
      isRealNameVerified: false,
      vipStatus: '',
      talentLevel: '',
      showOfflineVerifyButton: false
    },
    // 是否已登录
    isLoggedIn: false,
    // 登录相关信息
    loginInfo: {
      tokenValue: null,
      tokenName: null,
      loginId: null,
      loginKey: null
    },
    // 未读消息数
    unreadCount: 0,
    // 主题设置
    theme: {
      primaryColor: '#007AFF',
      backgroundColor: '#f5f5f5'
    },
    hasLogin: false,
    isUniverifyLogin: false,
    loginProvider: '',
    openid: null,
    // 用户完成状态
    userSetupStatus: {
      hasCompletedChoose: false, // 是否完成标签选择
      hasCompletedCharacter: false, // 是否完成性格测评
      selectedTags: [], // 用户选择的标签
      characterResult: null // 性格测评结果
    }
  }),

  getters: {
    // 获取用户ID
    userId: state => (state.userInfo ? state.userInfo.id : state.loginInfo.loginId || null),
    // 获取用户昵称
    nickname: state => (state.userInfo ? state.userInfo.nickname : ''),
    // 获取用户头像
    avatar: state => (state.userInfo ? state.userInfo.avatar : ''),
    // 获取主题颜色
    primaryColor: state => state.theme.primaryColor,
    // 获取背景颜色
    backgroundColor: state => state.theme.backgroundColor,
    // 获取token
    token: state => state.loginInfo.tokenValue,
    // 检查是否已登录
    checkIsLoggedIn: state => state.isLoggedIn && !!state.loginInfo.tokenValue,
    // 检查用户是否完成初始设置
    hasCompletedSetup: state =>
      state.userSetupStatus.hasCompletedChoose && state.userSetupStatus.hasCompletedCharacter
  },

  actions: {
    // 首页列表
    async getRecommendList(params) {
      return await homeApi.getRecommendList(params)
    },
    async getNewUserList(params) {
      return await homeApi.getNewUserList(params)
    },
    async getFollowList(params) {
      return await homeApi.getFollowList(params)
    },
    // 获取动态广场列表
    async getMomentsList(params) {
      return await momentsApi.getMomentsList(params)
    },
    // 获取首页招募列表
    async getRecruitList(params) {
      return await recruitApi.getRecruitList(params)
    },
    // 获取首页招募列表
    async getRecruitListMy(params) {
      return await recruitApi.getRecruitListMy(params)
    },
    // 获取用户在线状态
    async getOnlineStatus(userIds) {
      return await userApi.getOnlineStatus(userIds)
    },
    // 获取所有标签
    async queryAllTags() {
      return await tagApi.queryAllTags()
    },
    // 提交选择的标签
    async commitTags(tags) {
      return await tagApi.commitTags(tags)
    },
    // 初始化用户状态（从本地存储恢复）
    initUserState() {
      try {
        console.log('开始初始化用户状态')

        // 恢复登录信息
        const savedLoginInfo = uni.getStorageSync('loginInfo')
        if (savedLoginInfo) {
          this.loginInfo = savedLoginInfo
          this.isLoggedIn = true
          console.log('恢复登录信息:', savedLoginInfo)
        }

        // 恢复用户信息
        const savedUserInfo = uni.getStorageSync('userInfo')
        if (savedUserInfo) {
          this.userInfo = savedUserInfo
          console.log('恢复用户信息:', savedUserInfo)
        }

        // 恢复主题设置
        const savedTheme = uni.getStorageSync('theme')
        if (savedTheme) {
          this.theme = savedTheme
          console.log('恢复主题设置:', savedTheme)
        }

        // 恢复用户完成状态
        const savedSetupStatus = uni.getStorageSync('userSetupStatus')
        if (savedSetupStatus) {
          this.userSetupStatus = savedSetupStatus
          console.log('恢复用户设置状态:', savedSetupStatus)
        } else {
          console.log('未找到保存的用户设置状态，使用默认值')
        }

        console.log('用户状态初始化完成')
      } catch (error) {
        console.error('初始化用户状态失败:', error)
      }
    },

    // 设置用户信息
    setUserInfo(userInfo) {
      console.log('userInfo--->', userInfo)
      this.userInfo = userInfo
      // 持久化存储
      uni.setStorageSync('userInfo', userInfo)
    },

    // 设置登录信息
    setLoginInfo(loginInfo) {
      this.loginInfo = { ...this.loginInfo, ...loginInfo }
      this.isLoggedIn = true

      // 持久化存储登录信息
      uni.setStorageSync('loginInfo', this.loginInfo)

      // 保持token的向后兼容性
      if (loginInfo.tokenValue) {
        uni.setStorageSync('token', loginInfo.tokenValue)
      }
    },

    // 设置未读消息数
    setUnreadCount(count) {
      this.unreadCount = count
    },

    // 初始化IM相关接口
    async initIM(userId, token) {
      try {
        // 先注册token
        const tokenResult = await messageApi.registerToken({
          uid: userId,
          token: userId,
          device_flag: 0, // 0.app 1.web
          device_level: 0 // 0.从设备 1.主设备
        })
        console.log('注册IM token成功:', tokenResult)

        // 再获取长连接路由
        const routeResult = await messageApi.getRoute({
          uid: userId
        })
        console.log('获取IM路由成功:', routeResult)

        // 如果获取到了WebSocket地址，存储起来供消息页面使用
        if (routeResult && routeResult.ws_addr) {
          const wsAddr = routeResult.ws_addr
          // 确保地址格式正确，如果没有协议前缀则添加
          const wsUrl =
            wsAddr.startsWith('ws://') || wsAddr.startsWith('wss://') ? wsAddr : `ws://${wsAddr}`

          console.log('存储WuKongIM WebSocket地址:', wsUrl)

          // 存储WebSocket地址和IM相关信息到本地存储
          const imConfig = {
            wsUrl: wsUrl,
            userId: userId,
            token: token,
            timestamp: Date.now()
          }
          uni.setStorageSync('im_config', imConfig)
          console.log('IM配置已存储到本地')
          WKIMManager.initConnect()
        }

        return {
          success: true,
          wsAddr: routeResult?.ws_addr,
          tokenResult,
          routeResult
        }
      } catch (error) {
        console.error('初始化IM失败:', error)
        return { success: false, error }
      }
    },

    // 更新主题设置
    updateTheme(theme) {
      this.theme = { ...this.theme, ...theme }
      // 持久化存储主题设置
      uni.setStorageSync('theme', this.theme)
    },

    // 清除用户信息
    clearUserInfo() {
      this.userInfo = null
      this.isLoggedIn = false
      this.loginInfo = {
        tokenValue: null,
        tokenName: null,
        loginId: null,
        loginKey: null
      }
      // 注意：退出登录时保留用户设置状态(userSetupStatus)，不清除

      // 清除本地存储
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('loginInfo')
      uni.removeStorageSync('token')
      uni.removeStorageSync('im_config') // 清除IM配置
      // 注意：保留userSetupStatus数据，不清除
    },

    // 发送短信验证码
    async sendSmsCode(phone) {
      try {
        const result = await userApi.sendSms({ phone })
        if (result.code === 200) {
          return { success: true, message: '验证码发送成功' }
        } else {
          return { success: false, message: result.msg || '发送失败' }
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        return { success: false, message: '发送失败，请重试' }
      }
    },

    // 登录（使用验证码）
    async login(loginData) {
      console.log('loginData--->', loginData)
      try {
        const result = await userApi.login({
          username: loginData.phone,
          code: loginData.code
        })
        if (result && result.code === 200 && result.data) {
          // 立即设置全局token，确保后续请求能使用最新token
          setGlobalToken(result.data.tokenValue)

          // 保存用户登录信息
          this.setLoginInfo({
            tokenValue: result.data.tokenValue,
            tokenName: result.data.tokenName,
            loginId: result.data.loginId,
            loginKey: result.data.loginKey
          })
          const userDetail = await userApi.getUserDetail(result.data?.loginId)
          console.log('userDetail--->', userDetail)
          if (userDetail.data?.avatar?.includes('default-header.png')) {
            userDetail.data.avatar =
              'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
          }
          const user = {
            ...result.data,
            ...userDetail.data,
            avatar: userDetail.data?.avatar?.includes('http')
              ? userDetail.data.avatar
              : 'http://47.123.3.183:9000/' + userDetail.data?.avatar,
            userId: userDetail?.data?.userId ? userDetail?.data?.userId : result.data?.loginId
          }
          this.setUserInfo(user)

          // 登录成功后初始化IM相关接口
          await this.initIM(result.data.loginId, result.data.tokenValue)

          return { success: true, message: '登录成功' }
        } else {
          return { success: false, message: result.msg || '登录失败' }
        }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, message: '登录失败，请重试' }
      }
    },

    // 注册
    async register(registerData) {
      try {
        const result = await userApi.register(registerData)
        if (result.token) {
          uni.setStorageSync('token', result.token)
          await this.getUserInfo()
          return true
        }
        return false
      } catch (error) {
        console.error('注册失败:', error)
        return false
      }
    },

    // 退出登录
    logout() {
      // 清除全局token
      setGlobalToken(null)
      this.clearUserInfo()
      uni.reLaunch({
        url: '/pages/login/login'
      })
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const userInfo = await userApi.getUserDetail(this.userInfo?.userId)
        this.setUserInfo({
          ...this.userInfo,
          ...userInfo.data,
          userId: userInfo.data?.userId ? userInfo.data.userId : this.userInfo?.userId
        })
        return userInfo
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return null
      }
    },

    // 更新用户信息
    async updateUserInfo(userData) {
      try {
        const result = await userApi.updateUserInfo(userData)
        if (result && result.code === 200) {
          // 更新本地用户信息
          this.userInfo = { ...this.userInfo, ...userData }
          uni.setStorageSync('userInfo', this.userInfo)
          return true
        }
        return false
      } catch (error) {
        console.error('更新用户信息失败:', error)
        return false
      }
    },

    // 更新位置信息
    async updateLocation(locationData) {
      try {
        const result = await userApi.updateLocation(locationData)
        if (result && result.code === 200) {
          console.log('位置信息更新成功:', result)
          return { success: true, message: '位置信息更新成功' }
        } else {
          console.error('位置信息更新失败:', result?.msg || '未知错误')
          return { success: false, message: result?.msg || '位置信息更新失败' }
        }
      } catch (error) {
        console.error('更新位置信息失败:', error)
        return { success: false, message: '位置信息更新失败，请重试' }
      }
    },

    // 获取用户OpenID
    async getUserOpenId() {
      return new Promise((resolve, reject) => {
        if (this.openid) {
          resolve(this.openid)
        } else {
          uni.login({
            success: data => {
              this.hasLogin = true
              setTimeout(() => {
                const openid = '123456789'
                console.log('uni.request mock openid[' + openid + ']')
                this.openid = openid
                resolve(openid)
              }, 1000)
            },
            fail: err => {
              console.log('uni.login 接口调用失败，将无法正常使用开放接口等服务', err)
              reject(err)
            }
          })
        }
      })
    },

    // 获取手机号
    async getPhoneNumber(univerifyInfo) {
      return new Promise((resolve, reject) => {
        uni.request({
          url: 'https://97fca9f2-41f6-449f-a35e-3f135d4c3875.bspapp.com/http/univerify-login',
          method: 'POST',
          data: univerifyInfo,
          success: res => {
            const data = res.data
            if (data.success) {
              resolve(data.phoneNumber)
            } else {
              reject(res)
            }
          },
          fail: err => {
            reject(err)
          }
        })
      })
    },

    // 设置一键登录状态
    setUniverifyLogin(status) {
      this.isUniverifyLogin = status
    },

    // 设置一键登录错误信息
    setUniverifyErrorMsg(msg) {
      console.log('Univerify登录错误:', msg)
    },

    // 一键登录（univerify）
    async univerifyLogin(loginData) {
      try {
        console.log('一键登录数据:', loginData)

        // The data from univerify already contains login info.
        const result = loginData.phoneData

        if (result.code === 200 && result.data) {
          const resultData = result.data
          // 立即设置全局token，确保后续请求能使用最新token
          setGlobalToken(resultData.tokenValue)

          // 保存用户登录信息
          this.setLoginInfo({
            tokenValue: resultData.tokenValue,
            tokenName: resultData.tokenName,
            loginId: resultData.loginId,
            loginKey: resultData.loginKey
          })
          const userDetail = await userApi.getUserDetail(resultData.loginId)
          console.log('userDetail--->', userDetail)
          if (userDetail.data?.avatar?.includes('default-header.png')) {
            userDetail.data.avatar =
              'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
          }
          const user = {
            ...resultData,
            ...userDetail.data,
            avatar: userDetail.data?.avatar?.includes('http')
              ? userDetail.data?.avatar
              : 'http://47.123.3.183:9000/' + userDetail.data?.avatar,
            userId: userDetail.data?.userId ? userDetail.data.userId : resultData.loginId,
            loginType: 'univerify'
          }
          this.setUserInfo(user)

          // 登录成功后初始化IM相关接口
          await this.initIM(resultData.loginId, resultData.tokenValue)

          return { success: true, message: '一键登录成功' }
        } else {
          return { success: false, message: result.msg || '一键登录失败' }
        }
      } catch (error) {
        console.error('一键登录失败:', error)
        return { success: false, message: '一键登录失败，请重试' }
      }
    },

    // QQ登录
    async qqLogin(loginData) {
      try {
        console.log('QQ登录数据:', loginData)

        const { code } = loginData

        // 首先调用新的QQ登录接口
        try {
          const qqResult = await userApi.qqAppLogin(code)
          console.log('QQ登录接口返回:', qqResult)

          // 如果接口正常返回数据，说明用户已登录
          if (qqResult.code === 0 && qqResult.data) {
            const resultData = qqResult.data

            // 立即设置全局token，确保后续请求能使用最新token
            setGlobalToken(resultData.tokenValue)

            // 保存用户登录信息
            this.setLoginInfo({
              tokenValue: resultData.tokenValue,
              tokenName: resultData.tokenName,
              loginId: resultData.loginId,
              loginKey: resultData.loginKey
            })

            // 获取用户详细信息
            const userDetail = await userApi.getUserDetail(resultData.loginId)
            console.log('userDetail--->', userDetail)

            if (userDetail.data?.avatar?.includes('default-header.png')) {
              userDetail.data.avatar =
                'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
            }

            const user = {
              ...resultData,
              ...userDetail.data,
              avatar: userDetail.data?.avatar?.includes('http')
                ? userDetail.data?.avatar
                : 'http://47.123.3.183:9000/' + userDetail.data?.avatar,
              userId: userDetail.data?.userId ? userDetail.data.userId : resultData.loginId,
              loginType: 'qq'
            }

            this.setUserInfo(user)

            // 登录成功后初始化IM相关接口
            await this.initIM(resultData.loginId, resultData.tokenValue)

            return { success: true, message: 'QQ登录成功' }
          }

          // 检查是否是未注册用户（code === 500 且 msg === "未注册"）
          if (qqResult.code === 500 && qqResult.msg === '未注册') {
            console.log('QQ用户未注册，需要绑定手机号')

            // 存储QQ code，用于后续绑定
            uni.setStorageSync('qqCode', code)

            // 返回特殊状态，表示需要绑定手机号
            return {
              success: false,
              needBindPhone: true,
              message: '首次登录需要绑定手机号',
              code: code
            }
          }
        } catch (qqError) {
          console.log('QQ登录接口调用失败:', qqError)

          // 检查是否是500错误（首次登录需要绑定手机号）
          if (qqError.statusCode === 500 || qqError.code === 500) {
            console.log('QQ用户首次登录，需要绑定手机号')

            // 存储QQ code，用于后续绑定
            uni.setStorageSync('qqCode', code)

            // 返回特殊状态，表示需要绑定手机号
            return {
              success: false,
              needBindPhone: true,
              message: '首次登录需要绑定手机号',
              code: code
            }
          }

          // 其他错误，继续使用原有逻辑
          console.log('QQ登录接口异常，使用原有登录逻辑')
        }

        // 如果新接口失败或返回异常，使用原有的登录逻辑作为备用
        console.log('使用原有QQ登录逻辑')
        const { loginResult, userInfo } = loginData

        // 提取QQ登录信息
        const qqInfo = {
          openid: loginResult?.openid || userInfo?.openid,
          access_token: loginResult?.access_token,
          // QQ用户信息
          nickname: userInfo?.userInfo?.nickname || userInfo?.nickname,
          avatar:
            userInfo?.userInfo?.figureurl_qq ||
            userInfo?.userInfo?.figureurl_qq_1 ||
            userInfo?.avatar,
          gender: userInfo?.userInfo?.gender,
          province: userInfo?.userInfo?.province,
          city: userInfo?.userInfo?.city
        }

        // 调用现有的登录接口，传递QQ登录信息
        const result = await userApi.login({
          openid: qqInfo.openid,
          access_token: qqInfo.access_token,
          userInfo: qqInfo,
          loginType: 'qq' // 标识这是QQ登录
        })

        if (result.code === 200 && result.data) {
          const resultData = result.data
          // 立即设置全局token，确保后续请求能使用最新token
          setGlobalToken(resultData.tokenValue)

          // 保存用户登录信息
          this.setLoginInfo({
            tokenValue: resultData.tokenValue,
            tokenName: resultData.tokenName,
            loginId: resultData.loginId,
            loginKey: resultData.loginKey
          })

          // 获取用户详细信息
          const userDetail = await userApi.getUserDetail(resultData.loginId)
          console.log('userDetail--->', userDetail)

          if (userDetail.data?.avatar?.includes('default-header.png')) {
            userDetail.data.avatar =
              'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
          }

          const user = {
            ...resultData,
            ...userDetail.data,
            avatar: userDetail.data?.avatar?.includes('http')
              ? userDetail.data?.avatar
              : 'http://47.123.3.183:9000/' + userDetail.data?.avatar,
            userId: userDetail.data?.userId ? userDetail.data.userId : resultData.loginId,
            loginType: 'qq'
          }

          this.setUserInfo(user)

          // 登录成功后初始化IM相关接口
          await this.initIM(resultData.loginId, resultData.tokenValue)

          return { success: true, message: 'QQ登录成功' }
        } else {
          return { success: false, message: result.msg || 'QQ登录失败' }
        }
      } catch (error) {
        console.error('QQ登录处理失败:', error)

        // 如果是网络错误或接口未实现，使用模拟数据
        if (error.message?.includes('404') || error.message?.includes('网络')) {
          console.log('使用QQ登录模拟数据')

          const { loginResult, userInfo } = loginData
          const qqInfo = {
            openid: loginResult?.openid || userInfo?.openid,
            nickname: userInfo?.userInfo?.nickname || userInfo?.nickname,
            avatar:
              userInfo?.userInfo?.figureurl_qq ||
              userInfo?.userInfo?.figureurl_qq_1 ||
              userInfo?.avatar
          }

          // 模拟成功登录
          const mockToken = 'qq_token_' + Date.now()
          const mockUserId = 'qq_user_' + Date.now()

          // 立即设置全局token，确保后续请求能使用最新token
          setGlobalToken(mockToken)

          this.setLoginInfo({
            tokenValue: mockToken,
            tokenName: 'token',
            loginId: mockUserId,
            loginKey: 'qq_key_' + Date.now()
          })

          this.setUserInfo({
            id: mockUserId,
            nickname: qqInfo.nickname || 'QQ用户',
            avatar: qqInfo.avatar || '/static/avatar/default.png',
            openid: qqInfo.openid,
            loginType: 'qq'
          })

          // 登录成功后初始化IM相关接口
          await this.initIM(mockUserId, mockToken)

          return { success: true, message: 'QQ登录成功（模拟）' }
        }

        return { success: false, message: 'QQ登录失败，请重试' }
      }
    },

    // QQ绑定手机号
    async qqBindPhone(bindData) {
      try {
        console.log('QQ绑定手机号数据:', bindData)

        const { code, phone, smsCode } = bindData

        // 使用现有的登录接口，添加QQ授权码参数
        const result = await userApi.login({
          username: phone,
          code: smsCode,
          qqAuthorationCode: code
        })

        if (result.code === 200 && result.data) {
          const resultData = result.data
          // 立即设置全局token，确保后续请求能使用最新token
          setGlobalToken(resultData.tokenValue)

          // 保存用户登录信息
          this.setLoginInfo({
            tokenValue: resultData.tokenValue,
            tokenName: resultData.tokenName,
            loginId: resultData.loginId,
            loginKey: resultData.loginKey
          })

          // 获取用户详细信息
          const userDetail = await userApi.getUserDetail(resultData.loginId)
          console.log('userDetail--->', userDetail)

          if (userDetail.data?.avatar?.includes('default-header.png')) {
            userDetail.data.avatar =
              'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
          }

          const user = {
            ...resultData,
            ...userDetail.data,
            avatar: userDetail.data?.avatar?.includes('http')
              ? userDetail.data?.avatar
              : 'http://47.123.3.183:9000/' + userDetail.data?.avatar,
            userId: userDetail.data?.userId ? userDetail.data.userId : resultData.loginId,
            loginType: 'qq'
          }

          this.setUserInfo(user)

          // 登录成功后初始化IM相关接口
          await this.initIM(resultData.loginId, resultData.tokenValue)

          return { success: true, message: 'QQ绑定手机号成功' }
        } else {
          return { success: false, message: result.msg || 'QQ绑定手机号失败' }
        }
      } catch (error) {
        console.error('QQ绑定手机号失败:', error)

        // 根据错误类型提供不同的错误信息
        let errorMessage = '绑定失败，请重试'

        if (error.code === 'INVALID_CODE') {
          errorMessage = '验证码错误，请重新输入'
        } else if (error.code === 'CODE_EXPIRED') {
          errorMessage = '验证码已过期，请重新获取'
        } else if (error.message?.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络后重试'
        } else if (error.message?.includes('404')) {
          errorMessage = '服务暂时不可用，请稍后重试'
        } else if (error.message?.includes('QQ授权')) {
          errorMessage = 'QQ授权信息丢失，请重新登录'
        }

        return { success: false, message: errorMessage }
      }
    },

    // 微信登录
    async wechatLogin(loginData) {
      try {
        console.log('微信登录数据:', loginData)

        const { code } = loginData

        // 首先调用新的微信登录接口
        try {
          const wechatResult = await userApi.wechatAppLogin(code)
          console.log('微信登录接口返回:', wechatResult)

          // 如果接口正常返回数据，说明用户已登录
          if (wechatResult.code === 0 && wechatResult.data) {
            const resultData = wechatResult.data

            // 立即设置全局token，确保后续请求能使用最新token
            setGlobalToken(resultData.tokenValue)

            // 保存用户登录信息
            this.setLoginInfo({
              tokenValue: resultData.tokenValue,
              tokenName: resultData.tokenName,
              loginId: resultData.loginId,
              loginKey: resultData.loginKey
            })

            // 获取用户详细信息
            const userDetail = await userApi.getUserDetail(resultData.loginId)
            console.log('userDetail--->', userDetail)

            if (userDetail.data?.avatar?.includes('default-header.png')) {
              userDetail.data.avatar =
                'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
            }

            const user = {
              ...resultData,
              ...userDetail.data,
              avatar: userDetail.data?.avatar?.includes('http')
                ? userDetail.data?.avatar
                : 'http://47.123.3.183:9000/' + userDetail.data?.avatar,
              userId: userDetail.data?.userId ? userDetail.data.userId : resultData.loginId,
              loginType: 'wechat'
            }

            this.setUserInfo(user)

            // 登录成功后初始化IM相关接口
            await this.initIM(resultData.loginId, resultData.tokenValue)

            return { success: true, message: '微信登录成功' }
          }

          // 检查是否是未注册用户（code === 500 且 msg === "未注册"）
          if (wechatResult.code === 500 && wechatResult.msg === '未注册') {
            console.log('微信用户未注册，需要绑定手机号')

            // 存储微信code，用于后续绑定
            uni.setStorageSync('wechatCode', code)

            // 返回特殊状态，表示需要绑定手机号
            return {
              success: false,
              needBindPhone: true,
              message: '首次登录需要绑定手机号',
              code: code
            }
          }
        } catch (wechatError) {
          console.log('微信登录接口调用失败:', wechatError)

          // 检查是否是500错误（首次登录需要绑定手机号）
          if (wechatError.statusCode === 500 || wechatError.code === 500) {
            console.log('微信用户首次登录，需要绑定手机号')

            // 存储微信code，用于后续绑定
            uni.setStorageSync('wechatCode', code)

            // 返回特殊状态，表示需要绑定手机号
            return {
              success: false,
              needBindPhone: true,
              message: '首次登录需要绑定手机号',
              code: code
            }
          }

          // 其他错误，继续使用原有逻辑
          console.log('微信登录接口异常，使用原有登录逻辑')
        }

        // 如果新接口失败或返回异常，使用原有的登录逻辑作为备用
        console.log('使用原有微信登录逻辑')
        const result = await userApi.login({
          code: code,
          loginType: 'wechat' // 标识这是微信登录
        })

        if (result.code === 200 && result.data) {
          const resultData = result.data
          // 立即设置全局token，确保后续请求能使用最新token
          setGlobalToken(resultData.tokenValue)

          // 保存用户登录信息
          this.setLoginInfo({
            tokenValue: resultData.tokenValue,
            tokenName: resultData.tokenName,
            loginId: resultData.loginId,
            loginKey: resultData.loginKey
          })

          // 获取用户详细信息
          const userDetail = await userApi.getUserDetail(resultData.loginId)
          console.log('userDetail--->', userDetail)

          if (userDetail.data?.avatar?.includes('default-header.png')) {
            userDetail.data.avatar =
              'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
          }

          const user = {
            ...resultData,
            ...userDetail.data,
            avatar: userDetail.data?.avatar?.includes('http')
              ? userDetail.data?.avatar
              : 'http://47.123.3.183:9000/' + userDetail.data?.avatar,
            userId: userDetail.data?.userId ? userDetail.data.userId : resultData.loginId,
            loginType: 'wechat'
          }

          this.setUserInfo(user)

          // 登录成功后初始化IM相关接口
          await this.initIM(resultData.loginId, resultData.tokenValue)

          return { success: true, message: '微信登录成功' }
        } else {
          return { success: false, message: result.msg || '微信登录失败' }
        }
      } catch (error) {
        console.error('微信登录处理失败:', error)

        // 如果是网络错误或接口未实现，使用模拟数据
        if (error.message?.includes('404') || error.message?.includes('网络')) {
          console.log('使用微信登录模拟数据')

          // 模拟成功登录
          const mockToken = 'wechat_token_' + Date.now()
          const mockUserId = 'wechat_user_' + Date.now()

          // 立即设置全局token，确保后续请求能使用最新token
          setGlobalToken(mockToken)

          this.setLoginInfo({
            tokenValue: mockToken,
            tokenName: 'token',
            loginId: mockUserId,
            loginKey: 'wechat_key_' + Date.now()
          })

          this.setUserInfo({
            id: mockUserId,
            nickname: '微信用户',
            avatar: '/static/avatar/default.png',
            openid: 'mock_openid_' + Date.now(),
            unionid: 'mock_unionid_' + Date.now(),
            loginType: 'wechat'
          })

          // 登录成功后初始化IM相关接口
          await this.initIM(mockUserId, mockToken)

          return { success: true, message: '微信登录成功（模拟）' }
        }

        return { success: false, message: '微信登录失败，请重试' }
      }
    },

    // 微信绑定手机号
    async wechatBindPhone(bindData) {
      try {
        console.log('微信绑定手机号数据:', bindData)

        const { code, phone, smsCode } = bindData

        // 使用现有的登录接口，添加微信授权码参数
        const result = await userApi.login({
          username: phone,
          code: smsCode,
          wechatAuthorationCode: code
        })

        if (result.code === 200 && result.data) {
          const resultData = result.data
          // 立即设置全局token，确保后续请求能使用最新token
          setGlobalToken(resultData.tokenValue)

          // 保存用户登录信息
          this.setLoginInfo({
            tokenValue: resultData.tokenValue,
            tokenName: resultData.tokenName,
            loginId: resultData.loginId,
            loginKey: resultData.loginKey
          })

          // 获取用户详细信息
          const userDetail = await userApi.getUserDetail(resultData.loginId)
          console.log('userDetail--->', userDetail)

          if (userDetail.data?.avatar?.includes('default-header.png')) {
            userDetail.data.avatar =
              'https://p6.itc.cn/q_70/images03/20200924/f642fab39c2c4f0d8b9eb0be7e26d47c.jpeg'
          }

          const user = {
            ...resultData,
            ...userDetail.data,
            avatar: userDetail.data?.avatar?.includes('http')
              ? userDetail.data?.avatar
              : 'http://47.123.3.183:9000/' + userDetail.data?.avatar,
            userId: userDetail.data?.userId ? userDetail.data.userId : resultData.loginId,
            loginType: 'wechat'
          }

          this.setUserInfo(user)

          // 登录成功后初始化IM相关接口
          await this.initIM(resultData.loginId, resultData.tokenValue)

          return { success: true, message: '微信绑定手机号成功' }
        } else {
          return { success: false, message: result.msg || '微信绑定手机号失败' }
        }
      } catch (error) {
        console.error('微信绑定手机号失败:', error)

        // 根据错误类型提供不同的错误信息
        let errorMessage = '绑定失败，请重试'

        if (error.code === 'INVALID_CODE') {
          errorMessage = '验证码错误，请重新输入'
        } else if (error.code === 'CODE_EXPIRED') {
          errorMessage = '验证码已过期，请重新获取'
        } else if (error.message?.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络后重试'
        } else if (error.message?.includes('404')) {
          errorMessage = '服务暂时不可用，请稍后重试'
        } else if (error.message?.includes('微信授权')) {
          errorMessage = '微信授权信息丢失，请重新登录'
        }

        return { success: false, message: errorMessage }
      }
    },

    // 更新头像
    async updateAvatar(filePath) {
      try {
        const result = await userApi.updateAvatar(filePath)
        if (result && result.data) {
          // 更新本地用户信息
          this.userInfo.avatar = result.data.avatar
          uni.setStorageSync('userInfo', this.userInfo)
          return result.data.avatar
        }
        return null
      } catch (error) {
        console.error('更新头像失败:', error)
        return null
      }
    },

    // 保存用户标签选择
    async saveUserTags(tags) {
      try {
        // 更新本地状态
        this.userSetupStatus.selectedTags = tags
        this.userSetupStatus.hasCompletedChoose = true

        // 持久化存储
        uni.setStorageSync('userSetupStatus', this.userSetupStatus)

        // 调用后端接口保存用户标签（如果有的话）
        // const result = await userApi.saveUserTags({ tags })
        // if (result.code === 200) {
        //   return { success: true, message: '标签保存成功' }
        // } else {
        //   return { success: false, message: result.msg || '保存失败' }
        // }

        // 暂时返回成功，后续可以对接后端接口
        return { success: true, message: '标签保存成功' }
      } catch (error) {
        console.error('保存用户标签失败:', error)
        return { success: false, message: '保存失败，请重试' }
      }
    },

    // 保存性格测评结果
    async saveCharacterResult(result) {
      try {
        // 更新本地状态
        this.userSetupStatus.characterResult = result
        this.userSetupStatus.hasCompletedCharacter = true

        // 持久化存储
        uni.setStorageSync('userSetupStatus', this.userSetupStatus)

        // 调用后端接口保存性格测评结果（如果有的话）
        // const apiResult = await userApi.saveCharacterResult({ result })
        // if (apiResult.code === 200) {
        //   return { success: true, message: '测评结果保存成功' }
        // } else {
        //   return { success: false, message: apiResult.msg || '保存失败' }
        // }

        // 暂时返回成功，后续可以对接后端接口
        return { success: true, message: '测评结果保存成功' }
      } catch (error) {
        console.error('保存性格测评结果失败:', error)
        return { success: false, message: '保存失败，请重试' }
      }
    },

    // 检查用户是否需要完成初始设置
    checkUserSetupRequired() {
      console.log('检查用户设置状态:', this.userSetupStatus)
      console.log('hasCompletedChoose:', this.userSetupStatus.hasCompletedChoose)
      console.log('hasCompletedCharacter:', this.userSetupStatus.hasCompletedCharacter)
      console.log('hasCompletedSetup:', this.hasCompletedSetup)
      return !this.hasCompletedSetup
    },

    // 获取用户设置状态
    getUserSetupStatus() {
      return this.userSetupStatus
    },

    // 重置用户设置状态
    resetUserSetupStatus() {
      this.userSetupStatus = {
        hasCompletedChoose: false,
        hasCompletedCharacter: false,
        selectedTags: [],
        characterResult: null
      }
      uni.removeStorageSync('userSetupStatus')
    }
  }
})
