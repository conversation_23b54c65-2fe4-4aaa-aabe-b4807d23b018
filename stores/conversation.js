import { defineStore } from 'pinia'
import { conversationApi } from '@/common/api'
import { useUserStore } from './user'

export const useConversationStore = defineStore('conversation', {
  state: () => ({
    currentConversation: null,
    conversationHistory: [],
    conversationList: [],
    currentChannelID: '',
    currentChannelType: 1,
    pinnedChannelIds: [] // 存储置顶的频道ID列表
  }),

  actions: {
    setCurrentConversation(conversation) {
      this.currentConversation = conversation
      if (conversation) {
        this.currentChannelID = conversation.channel?.channelID || conversation.channelID || ''
        this.currentChannelType = conversation.channel?.channelType || conversation.channelType || 1
      }
    },

    setConversationHistory(history) {
      this.conversationHistory = history
    },

    prependConversationHistory(messages) {
      const newMessages = messages.concat(this.conversationHistory)
      this.conversationHistory = newMessages
    },

    appendConversationHistory(message) {
      this.conversationHistory = [...this.conversationHistory, message]
    },

    updateConversations(conversations) {
      // 更新会话列表，保持置顶状态
      // 注意：conversations 参数应该已经是按照置顶状态排序好的
      this.conversationList = conversations
    },

    setConversationList(list) {
      this.conversationList = list
    },

    addOrUpdateMessage(message) {
      // 检查是否是当前会话的消息
      if (
        message.channel.channelID === this.currentChannelID &&
        message.channel.channelType === this.currentChannelType
      ) {
        // 查找是否已存在相同消息（处理重复消息）
        const existingIndex = this.conversationHistory.findIndex(
          msg =>
            msg.sending &&
            msg.content === message.content &&
            msg.fromUID === message.fromUID &&
            msg.messageType === message.messageType
        )

        if (existingIndex > -1) {
          // 更新已存在的消息
          this.conversationHistory[existingIndex] = {
            ...message,
            timestamp: message.timestamp * 1000,
            sending: false
          }
        } else {
          // 添加新消息
          // 根据contentType判断消息类型
          let messageType = 'text'
          if (message.content && message.content.contentType) {
            switch (message.content.contentType) {
              case 1:
                messageType = 'text'
                break
              case 2:
                messageType = 'image'
                // 处理图片尺寸
                if (message.content.width && message.content.height) {
                  const maxWidth = 160
                  const maxHeight = 120
                  const ratio = Math.min(
                    maxWidth / message.content.width,
                    maxHeight / message.content.height
                  )
                  message.content.displayWidth = Math.floor(message.content.width * ratio)
                  message.content.displayHeight = Math.floor(message.content.height * ratio)
                }
                break
              default:
                messageType = 'text'
            }
          }

          const formattedMessage = {
            ...message,
            timestamp: message.timestamp * 1000,
            type: messageType,
            avatar: message.avatar || '/static/icons/avatar.png',
            nickname: message.nickname || message.fromUID || '未知用户'
          }

          this.appendConversationHistory(formattedMessage)
        }
      }

      // 更新会话列表
      this.updateConversationList(message)
    },

    updateConversationList(message) {
      // 查找是否已存在该会话
      const existingIndex = this.conversationList.findIndex(
        conv =>
          conv.channel.channelID === message.channel.channelID &&
          conv.channel.channelType === message.channel.channelType
      )

      if (existingIndex > -1) {
        // 更新已存在的会话
        const conversation = this.conversationList[existingIndex]
        // 保存置顶状态
        const isPinned = conversation.isPinned || false

        conversation.lastMessage = message
        conversation.timestamp = message.timestamp * 1000

        // 如果不是当前会话，增加未读数
        if (
          message.channel.channelID !== this.currentChannelID ||
          message.channel.channelType !== this.currentChannelType
        ) {
          conversation.unread = (conversation.unread || 0) + 1
        }

        // 将更新的会话移到列表顶部（考虑置顶状态）
        this.conversationList.splice(existingIndex, 1)

        // 如果是置顶会话，放在所有置顶会话的最前面
        // 如果不是置顶会话，放在所有置顶会话的后面
        if (isPinned) {
          // 找到第一个置顶会话的位置
          const firstPinnedIndex = this.conversationList.findIndex(conv => conv.isPinned)
          if (firstPinnedIndex === -1) {
            this.conversationList.unshift(conversation)
          } else {
            this.conversationList.splice(firstPinnedIndex, 0, conversation)
          }
        } else {
          // 找到最后一个置顶会话的位置
          const lastPinnedIndex = this.conversationList.findIndex(conv => !conv.isPinned)
          if (lastPinnedIndex === -1) {
            this.conversationList.unshift(conversation)
          } else {
            this.conversationList.splice(lastPinnedIndex, 0, conversation)
          }
        }
      } else {
        // 添加新会话
        const newConversation = {
          channel: message.channel,
          lastMessage: message,
          timestamp: message.timestamp * 1000,
          unread: 1,
          isPinned: false // 默认不置顶
        }

        // 找到最后一个置顶会话的位置
        const lastPinnedIndex = this.conversationList.findIndex(conv => !conv.isPinned)
        if (lastPinnedIndex === -1) {
          this.conversationList.unshift(newConversation)
        } else {
          this.conversationList.splice(lastPinnedIndex, 0, newConversation)
        }
      }
    },

    // 置顶/取消置顶会话
    async togglePin(channelID, channelType) {
      const index = this.conversationList.findIndex(
        conv => conv.channel.channelID === channelID && conv.channel.channelType === channelType
      )

      if (index > -1) {
        const conversation = this.conversationList[index]
        const isPinned = !conversation.isPinned

        // 调用API进行置顶/取消置顶操作
        try {
          const userStore = useUserStore()
          await conversationApi.doTopConversation({
            channelId: channelID,
            type: isPinned ? 1 : 0, // 1为置顶，0为取消置顶
            uid: userStore.userInfo.userId
          })

          // 更新本地状态
          conversation.isPinned = isPinned

          // 从列表中移除
          this.conversationList.splice(index, 1)

          if (conversation.isPinned) {
            // 置顶：添加到列表最前面
            this.conversationList.unshift(conversation)

            // 更新置顶ID列表
            if (!this.pinnedChannelIds.includes(channelID)) {
              this.pinnedChannelIds.push(channelID)
            }
          } else {
            // 取消置顶：找到合适的位置插入
            // 找到最后一个置顶会话的位置
            const lastPinnedIndex = this.conversationList.findIndex(conv => !conv.isPinned)
            if (lastPinnedIndex === -1) {
              this.conversationList.push(conversation)
            } else {
              this.conversationList.splice(lastPinnedIndex, 0, conversation)
            }

            // 从置顶ID列表中移除
            const pinnedIndex = this.pinnedChannelIds.indexOf(channelID)
            if (pinnedIndex > -1) {
              this.pinnedChannelIds.splice(pinnedIndex, 1)
            }
          }

          // 重新排序会话列表，确保置顶的在前面且按时间排序
          this.sortConversationsByPin()

          return conversation.isPinned
        } catch (error) {
          console.error('置顶/取消置顶会话失败:', error)
          return false
        }
      }

      return false
    },

    // 获取置顶会话列表
    async fetchPinnedConversations() {
      try {
        const userStore = useUserStore()
        const response = await conversationApi.getTopConversation({
          uid: userStore.userInfo.userId
        })

        if (response && response.code === 200 && Array.isArray(response.data)) {
          // 更新置顶ID列表 - 直接使用返回的字符串数组
          this.pinnedChannelIds = response.data

          // 更新会话列表中的置顶状态
          this.conversationList.forEach(conv => {
            conv.isPinned = this.pinnedChannelIds.includes(conv.channel.channelID)
          })

          // 重新排序会话列表，置顶的在前面
          this.sortConversationsByPin()
        }
      } catch (error) {
        console.error('获取置顶会话列表失败:', error)
      }
    },

    // 根据置顶状态排序会话列表
    sortConversationsByPin() {
      // 分离置顶和非置顶会话
      const pinned = this.conversationList.filter(conv => conv.isPinned)
      const unpinned = this.conversationList.filter(conv => !conv.isPinned)

      // 按时间戳排序（最新的在前面）
      pinned.sort((a, b) => b.timestamp - a.timestamp)
      unpinned.sort((a, b) => b.timestamp - a.timestamp)

      // 合并列表
      this.conversationList = [...pinned, ...unpinned]
    },

    // 删除会话
    deleteConversation(channelID, channelType) {
      const index = this.conversationList.findIndex(
        conv => conv.channel.channelID === channelID && conv.channel.channelType === channelType
      )

      if (index > -1) {
        this.conversationList.splice(index, 1)
        return true
      }

      return false
    },

    clearCurrentConversation() {
      this.currentConversation = null
      this.currentChannelID = ''
      this.currentChannelType = 1
      this.conversationHistory = []
    },

    clearUnread(channelID, channelType) {
      const conversation = this.conversationList.find(
        conv => conv.channel.channelID === channelID && conv.channel.channelType === channelType
      )

      if (conversation) {
        conversation.unread = 0

        // 清除未读消息后，更新TabBar角标
        try {
          const userStore = useUserStore()
          conversationApi.clearUnread({
            uid: userStore.userInfo.userId,
            channel_id: channelID,
            channel_type: parseInt(channelType),
            unread: 0
          })
        } catch (error) {
          console.error('更新TabBar角标失败:', error)
        }
      }
    },

    // 获取所有未读消息数量
    getAllUnreadCount() {
      if (!this.conversationList || this.conversationList.length === 0) {
        return 0
      }

      return this.conversationList.reduce((total, conv) => {
        return total + (conv.unread || 0)
      }, 0)
    }
  }
})
