import { defineStore } from 'pinia'
import { groupsApi } from '@/common/api'

export const useGroupStore = defineStore('group', {
  state: () => ({
    // 群聊历史列表
    groupHistoryList: [],
    // 当前群组信息
    currentGroup: null,
    // 群组成员列表
    groupMembers: [],
    // 群组成员加载状态
    loadingMembers: false,
    // 当前选中的@用户
    atUsers: [],
    // 当前群组ID
    currentGroupCode: '',
    // 所有群组成员信息，格式为 {'groupCode': {uid: memberInfo}}
    allGroupMembers: {}
  }),

  getters: {
    // 获取群聊历史列表
    getGroupHistoryList: state => state.groupHistoryList,
    // 获取群组成员列表
    getGroupMembers: state => state.groupMembers,

    // 获取当前选中的@用户
    getAtUsers: state => state.atUsers,

    // 检查用户是否在当前群组中
    isUserInGroup: state => userId => {
      return state.groupMembers.some(member => member.uid === userId)
    },

    // 根据用户ID获取群成员信息
    getMemberById: state => userId => {
      return state.groupMembers.find(member => member.uid === userId) || null
    },

    // 根据群组ID和用户ID获取群成员信息
    getMemberByGroupAndUid: state => (groupCode, uid) => {
      if (state.allGroupMembers[groupCode] && state.allGroupMembers[groupCode][uid]) {
        return state.allGroupMembers[groupCode][uid]
      }
      return null
    },

    // 获取指定群组的所有成员
    getGroupMembersByGroupCode: state => groupCode => {
      return state.allGroupMembers[groupCode] || {}
    },

    // 根据用户名称模糊搜索群成员
    searchMembersByName: state => keyword => {
      if (!keyword) return []
      const lowerKeyword = keyword.toLowerCase()
      return state.groupMembers.filter(member => member.name.toLowerCase().includes(lowerKeyword))
    }
  },

  actions: {
    // 设置群聊历史列表
    setGroupHistoryList(list) {
      this.groupHistoryList = list

      // 加载每个群组的成员信息
      if (list && list.length > 0) {
        list.forEach(item => {
          const groupCode = item.groupCode || item.groupRes?.groupCode
          if (groupCode) {
            this.fetchAndStoreGroupMembers(groupCode)
          }
        })
      }
    },

    // 设置当前群组
    setCurrentGroup(group) {
      this.currentGroup = group
      if (group && group.groupCode) {
        this.currentGroupCode = group.groupCode
        this.fetchGroupMembers(group.groupCode)
      }
    },

    // 设置当前群组ID
    setCurrentGroupCode(groupCode) {
      this.currentGroupCode = groupCode
      if (groupCode) {
        this.fetchGroupMembers(groupCode)
      }
    },

    // 获取并存储群组成员信息
    async fetchAndStoreGroupMembers(groupCode) {
      if (!groupCode) return

      try {
        const res = await groupsApi.getGroupMembers({
          groupCode: groupCode
        })

        if (res.code === 200 && res.data) {
          // 将成员数据转换为 {uid: memberInfo} 格式
          const membersMap = {}
          res.data.forEach(member => {
            membersMap[member.uid] = member
          })

          // 更新 allGroupMembers 状态
          this.allGroupMembers = {
            ...this.allGroupMembers,
            [groupCode]: membersMap
          }

          console.log(`群组 ${groupCode} 成员信息已更新`, membersMap)
          return membersMap
        } else {
          console.error('获取群成员失败:', res.msg || '未知错误')
          return {}
        }
      } catch (error) {
        console.error('获取群成员异常:', error)
        return {}
      }
    },

    // 获取群组成员列表
    async fetchGroupMembers(groupCode) {
      if (!groupCode) return

      try {
        this.loadingMembers = true
        const res = await groupsApi.getGroupMembers({
          groupCode: groupCode
        })

        if (res.code === 200 && res.data) {
          this.groupMembers = res.data

          // 同时更新 allGroupMembers
          const membersMap = {}
          res.data.forEach(member => {
            membersMap[member.uid] = member
          })

          this.allGroupMembers = {
            ...this.allGroupMembers,
            [groupCode]: membersMap
          }
        } else {
          console.error('获取群成员失败:', res.msg || '未知错误')
          uni.showToast({
            title: '获取群成员失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取群成员异常:', error)
      } finally {
        this.loadingMembers = false
      }
    },

    // 添加@用户
    addAtUser(user) {
      // 检查是否已经添加过该用户
      if (!this.atUsers.some(u => u.uid === user.uid)) {
        this.atUsers.push(user)
      }
    },

    // 移除@用户
    removeAtUser(userId) {
      this.atUsers = this.atUsers.filter(user => user.uid !== userId)
    },

    // 清空@用户列表
    clearAtUsers() {
      this.atUsers = []
    },

    // 重置状态
    resetState() {
      this.currentGroup = null
      this.groupMembers = []
      this.atUsers = []
      this.currentGroupCode = ''
    },

    // 获取所有群组未读消息数量
    getAllGroupUnreadCount() {
      if (!this.groupHistoryList || this.groupHistoryList.length === 0) {
        return 0
      }

      return this.groupHistoryList.reduce((total, group) => {
        // 优先使用unread字段
        if (group.unread !== undefined) {
          return total + group.unread
        }
        // 如果有原始群组数据，尝试从中获取
        if (group.groupRes && group.groupRes.unread !== undefined) {
          return total + group.groupRes.unread
        }
        return total
      }, 0)
    },

    // 更新群组未读消息数量
    updateGroupUnread(groupCode, unreadCount) {
      const group = this.groupHistoryList.find(g => g.groupCode === groupCode)
      if (group) {
        group.unread = unreadCount

        // 更新TabBar角标
        try {
          const WKIMManager = require('@/utils/wukongim').default
          if (WKIMManager && typeof WKIMManager.updateTabBarBadge === 'function') {
            setTimeout(() => {
              WKIMManager.updateTabBarBadge()
            }, 100)
          }
        } catch (error) {
          console.error('更新TabBar角标失败:', error)
        }
      }
    },

    // 清除群组未读消息数量
    clearGroupUnread(channelId) {
      const group = this.groupHistoryList.find(g => g.channelId === channelId)
      if (group) {
        group.unread = 0

        // 更新TabBar角标
        try {
          const WKIMManager = require('@/utils/wukongim').default
          if (WKIMManager && typeof WKIMManager.updateTabBarBadge === 'function') {
            setTimeout(() => {
              WKIMManager.updateTabBarBadge()
            }, 100)
          }
        } catch (error) {
          console.error('更新TabBar角标失败:', error)
        }
      }
    },

    // 从列表中移除群组
    removeGroup(channelId) {
      const index = this.groupHistoryList.findIndex(g => g.channelId === channelId)
      if (index > -1) {
        this.groupHistoryList.splice(index, 1)

        // 更新TabBar角标
        try {
          const WKIMManager = require('@/utils/wukongim').default
          if (WKIMManager && typeof WKIMManager.updateTabBarBadge === 'function') {
            setTimeout(() => {
              WKIMManager.updateTabBarBadge()
            }, 100)
          }
        } catch (error) {
          console.error('更新TabBar角标失败:', error)
        }

        return true
      }
      return false
    }
  }
})
