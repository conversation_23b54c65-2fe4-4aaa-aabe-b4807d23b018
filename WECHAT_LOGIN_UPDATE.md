# 微信登录逻辑更新说明

## 更新概述

根据需求，对微信一键登录逻辑进行了调整，在原有逻辑基础上增加了新的接口调用和手机号绑定流程。

## 主要修改内容

### 1. API接口更新 (`common/api.js`)

新增了微信小程序登录接口：

```javascript
// 微信小程序登录
wechatAppLogin: code => get(`/wechat/wx-open/app-login?code=${code}`),
```

### 2. 用户Store更新 (`stores/user.js`)

#### 修改了 `wechatLogin` 方法：
- 首先调用新的 `/wechat/wx-open/app-login` 接口
- 如果接口返回 `code === 0` 且有数据，说明用户已登录，直接处理登录信息
- 如果接口返回 `code === 500` 且 `msg === "未注册"`，说明用户未注册，需要绑定手机号
- 返回特殊状态 `needBindPhone: true`，引导用户到绑定页面
- 保留原有登录逻辑作为备用方案

#### 新增了 `wechatBindPhone` 方法：
- 处理微信用户首次登录时的手机号绑定
- 使用现有的 `/auth/login` 接口，添加 `wechatAuthorationCode` 参数传递微信授权码
- 绑定成功后设置用户登录状态

### 3. 登录页面更新 (`pages/login/login.vue`)

修改了 `handleWechatLogin` 方法：
- 当微信登录返回 `needBindPhone: true` 时，跳转到手机号绑定页面
- 保持原有的成功和失败处理逻辑

### 4. 新增页面

#### 微信绑定手机号页面 (`pages/login/wechat-bind-phone.vue`)
- 专门用于微信用户首次登录时绑定手机号
- 界面与普通手机号登录页面类似，但标题和提示文案不同
- 发送验证码后跳转到微信绑定验证码页面

#### 微信绑定验证码页面 (`pages/login/wechat-bind-code.vue`)
- 专门用于微信用户绑定手机号时的验证码输入
- 界面与普通验证码输入页面类似，但按钮文案为"绑定手机号"
- 验证成功后完成微信绑定流程

### 5. 路由配置更新 (`pages.json`)

添加了新页面的路由配置：
```json
{
  "path": "pages/login/wechat-bind-phone",
  "style": {
    "navigationStyle": "custom"
  }
},
{
  "path": "pages/login/wechat-bind-code",
  "style": {
    "navigationStyle": "custom"
  }
}
```

## 新的登录流程

### 微信用户已注册（非首次）
1. 用户点击微信登录
2. 调用 `/wechat/wx-open/app-login` 接口
3. 接口返回 `code: 0` 且有数据，直接完成登录
4. 跳转到相应页面（首页或初始设置页面）

### 微信用户未注册（首次登录）
1. 用户点击微信登录
2. 调用 `/wechat/wx-open/app-login` 接口
3. 接口返回 `code: 500` 且 `msg: "未注册"`，识别为未注册用户
4. 跳转到微信绑定手机号页面
5. 用户输入手机号，发送验证码
6. 跳转到微信绑定验证码页面
7. 用户输入验证码，完成绑定
8. 跳转到相应页面（首页或初始设置页面）

## 接口规范

### GET `/wechat/wx-open/app-login`
**入参：** `code` (微信授权码)

**出参：**
```json
// 已注册用户
{
  "code": 0,
  "msg": "",
  "data": {
    "tokenName": "",
    "tokenValue": "",
    "isLogin": false,
    "loginId": {},
    "loginKey": "",
    "tokenTimeout": 0,
    "sessionTimeout": 0,
    "tokenSessionTimeout": 0,
    "tokenActivityTimeout": 0,
    "loginDevice": ""
  }
}

// 未注册用户
{
  "code": 500,
  "msg": "未注册",
  "data": null
}
```

**状态说明：**
- `code === 0`：用户已注册，返回登录信息
- `code === 500` 且 `msg === "未注册"`：用户未注册，需要绑定手机号

### POST `/auth/login` (微信绑定手机号)
**入参：**
```json
{
  "username": "手机号",
  "code": "短信验证码",
  "wechatAuthorationCode": "微信授权码"
}
```

**出参：** 与普通登录接口相同，返回用户登录信息

## 错误处理

- 网络错误：显示"网络连接失败，请检查网络后重试"
- 验证码错误：显示"验证码错误，请重新输入"
- 验证码过期：显示"验证码已过期，请重新获取"
- 微信授权丢失：显示"微信授权信息丢失，请重新登录"
- 服务不可用：显示"服务暂时不可用，请稍后重试"

## 兼容性说明

- 保留了原有的微信登录逻辑作为备用方案
- 如果新接口调用失败，会自动回退到原有逻辑
- 确保在各种情况下都能正常处理微信登录
- 微信绑定手机号使用现有的登录接口，保持接口统一性

## 测试建议

1. 测试微信用户已注册登录流程
2. 测试微信用户未注册绑定流程
3. 测试各种错误情况的处理
4. 测试网络异常时的回退机制
5. 测试验证码输入和重新发送功能 