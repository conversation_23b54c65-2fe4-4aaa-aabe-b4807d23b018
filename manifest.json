{"name": "小岛屿", "appid": "__UNI__B0B0006", "description": "应用描述", "versionName": "1.0.0", "versionCode": "101", "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "nvueLaunchMode": "fast", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Payment": {}, "Push": {}, "Share": {}, "Speech": {}, "VideoPlayer": {}, "OAuth": {}, "FacialRecognitionVerify": {}, "Camera": {}, "Geolocation": {}, "Maps": {}, "Barcode": {}, "Record": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>"], "minSdkVersion": 21}, "ios": {"swift": true, "frameworks": ["SwiftSupport"], "UIBackgroundModes": ["audio"], "urlschemewhitelist": ["b<PERSON><PERSON><PERSON>", "iosamap"], "dSYMs": false, "idfa": false, "capabilities": {"entitlements": {"com.apple.developer.associated-domains": ["applinks:static-mp-15a7410c-ae89-44ab-904a-137eac7f7fb6.next.bspapp.com"]}}}, "sdkConfigs": {"speech": {}, "oauth": {"univerify": {}, "weixin": {"appid": "wxd55a943553bb707a", "UniversalLinks": "https://static-mp-15a7410c-ae89-44ab-904a-137eac7f7fb6.next.bspapp.com/uni-universallinks/__UNI__218117B/"}, "qq": {"appid": "", "UniversalLinks": ""}}, "ad": {}, "maps": {"amap": {"name": "amapyoRf0Am6", "appkey_ios": "b215b325ecbf24e2a671e41e55bbf7a9", "appkey_android": "293cb2f5e29bb3577d52e625a8ecf638"}}, "push": {"unipush": {"version": "2", "offline": true, "icons": {"small": {"xhdpi": "D:/my-projects/文件/app logo/app logo/Android/设置菜单_Spotlight 搜索*********"}}}}, "geolocation": {"system": {"__platform__": ["ios", "android"]}, "amap": {"name": "amapyoRf0Am6", "__platform__": ["ios", "android"], "appkey_ios": "b215b325ecbf24e2a671e41e55bbf7a9", "appkey_android": "293cb2f5e29bb3577d52e625a8ecf638"}}, "payment": {"alipay": {"__platform__": ["ios", "android"]}, "weixin": {"__platform__": ["ios", "android"], "appid": "wxd55a943553bb707a", "UniversalLinks": "https://static-mp-15a7410c-ae89-44ab-904a-137eac7f7fb6.next.bspapp.com/uni-universallinks/__UNI__218117B/"}}, "share": {"weixin": {"appid": "wxd55a943553bb707a", "UniversalLinks": "https://static-mp-15a7410c-ae89-44ab-904a-137eac7f7fb6.next.bspapp.com/uni-universallinks/__UNI__218117B/"}}}, "orientation": ["portrait-primary"], "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}}, "uniStatistics": {"enable": true}}, "quickapp": {}, "quickapp-native": {"icon": "/static/logo.png", "package": "com.example.demo", "features": [{"name": "system.clipboard"}]}, "quickapp-webview": {"icon": "/static/logo.png", "package": "com.example.demo", "minPlatformVersion": 1070, "versionName": "1.0.0", "versionCode": 100}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于发送位置消息"}}, "uniStatistics": {"enable": true}}, "mp-alipay": {"usingComponents": true, "uniStatistics": {"enable": true}}, "mp-baidu": {"usingComponents": true, "uniStatistics": {"enable": true}, "dynamicLib": {"editorLib": {"provider": "swan-editor"}}}, "mp-toutiao": {"usingComponents": true, "uniStatistics": {"enable": true}}, "mp-jd": {"usingComponents": true, "uniStatistics": {"enable": true}}, "h5": {"template": "template.h5.html", "router": {"mode": "history", "base": "/"}, "sdkConfigs": {"maps": {"qqmap": {"key": "TKUBZ-D24AF-GJ4JY-JDVM2-IBYKK-KEBCU"}}}, "async": {"timeout": 20000}, "uniStatistics": {"enable": true}}, "vueVersion": "3", "mp-kuaishou": {"uniStatistics": {"enable": true}}, "mp-lark": {"uniStatistics": {"enable": true}}, "mp-qq": {"uniStatistics": {"enable": true}}, "quickapp-webview-huawei": {"uniStatistics": {"enable": true}}, "quickapp-webview-union": {"uniStatistics": {"enable": true}}, "uniStatistics": {"version": "2", "enable": true}, "_spaceID": "mp-15a7410c-ae89-44ab-904a-137eac7f7fb6"}