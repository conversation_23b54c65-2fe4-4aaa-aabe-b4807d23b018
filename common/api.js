import { get, post, put, del, upload, uploadFile } from './request'

// 用户相关接口
export const userApi = {
  // 发送短信验证码
  sendSms: data => post('/common/sms/send', data),
  // 登录（使用验证码、微信、QQ等）
  login: data => {
    data.deviceId = uni.getStorageSync('pushClientId')
    return post('/auth/login', data)
  },
  // 微信小程序登录
  wechatAppLogin: code => get(`/wechat/wx-open/app-login?code=${code}`),
  // 注册
  register: data => {
    return post('/user/register', data)
  },
  // 获取用户信息
  getUserInfo: () => get('/user/info'),
  // 更新用户信息
  updateUserInfo: data => post('/user/profile/update', data),
  // 更新位置信息
  updateLocation: data => post('/user/updateLocation', data),
  // 更新头像
  updateAvatar: filePath =>
    upload('/user/profile/avatar/update', filePath, {
      'content-type': 'multipart/form-data'
    }),
  // 更新个人相册
  addAlbum: filePath =>
    upload('/user/profile/album/add', filePath, {
      'content-type': 'multipart/form-data'
    }),
  // 关注用户
  followUser: userId => post(`/user/follow/${userId}`),
  // 取消关注
  unfollowUser: userId => post(`/user/unfollow/${userId}`),
  // 收藏，喜欢用户
  collectUser: data => post(`/user/interaction`, data),
  // 用户交互操作（新接口）
  userInteraction: data => post('/user/interaction', data),
  // 获取关注列表
  getFollowings: params => get('/user/followings', params),
  // 获取粉丝列表
  getFollowers: params => get('/user/followers', params),
  //获取用户详情 统一这个，字段最全
  getUserDetail: userId => get(`/user/profile/${userId}`),
  // 查看他人个人主页信息
  getUserProfile: userId => get(`/user/profile/${userId}`),
  // 查询标签
  // getTags: params => get('/user/tag/queryAllTags'),
  // 获取喜欢列表
  getFavorite: params => post('/user/likedList', params),
  // 获取收藏列表
  getCollect: params => post('/user/collectedList', params),
  // 查询用户在线状态
  getOnlineStatus: userIds => post('/user/onlinestatus', userIds),
  // 查询用户详情（包含标签完成状态）
  queryDetail: () => post('/user/queryDetail'),
  // 标记用户标签选择完成
  tagDone: () => get('/user/tagDone'),
  // 实名认证
  realAuthentication: data => post('/user/updateRealInfo', data),
  // 获取实名认证信息
  getRealAuthentication: () => get('/user/queryIdcard'),
  // 申请达人
  applyTalent: data => post('/daren/apply', data),
  // 举报用户
  reportUser: data => post('/daren/report', data),
}
// 我的 相关接口
export const profileApi = {
  getMyIncome: userId => get(`/income/info`),
  getMyWallet: userId => get(`/wallet/info`),
  queryAllTags: () => get('/tag/queryAllTags'),
  queryWithdrawalRecord: params => get('/incomeRecord/list', params),
  deleteAlbum: params => post(`/user/profile/album/delete`, params),
  recharge: params => post(`/income/recharge`, params),
  deduct: params => post(`/income/deduct`, params)
}

// 地址管理相关接口
export const addressApi = {
  // 获取地址列表
  getAddress: id => get(`/user/address/${id}`),
  // 获取地址列表
  getAddressList: () => get('/user/address/list'),
  // 添加地址
  addAddress: data => post('/user/address/add', data),
  // 更新地址
  updateAddress: data => post(`/user/address/update`, data),
  // 删除地址
  deleteAddress: data => post(`/user/address/delete`, data),
  // 设置默认地址
  setDefaultAddress: data => post(`/user/address/setDefault/`, data)
}

// 标签相关接口
export const tagApi = {
  // 查询所有标签
  queryAllTags: () => get('/tag/queryAllTags'),
  // 提交选择的标签
  commitTags: data => post('/tag/commit', data),
  // 获取职业列表
  getOccupationList: () => get('/dict/queryByDictType?dictType=occupation')
}

// 动态相关接口
export const postApi = {
  // 发布动态
  createPost: data => post('/post/create', data),
  // 上传动态图片
  uploadPostImage: filePath => upload('/post/image', filePath),
  // 获取动态列表
  getPosts: params => get('/post/list', params),
  // 获取动态详情
  getPostDetail: postCode => get(`/moments/posts/${postCode}/detail`),
  // 点赞动态
  likePost: postId => post(`/post/like/${postId}`),
  // 取消点赞
  unlikePost: postId => del(`/post/like/${postId}`),
  // 点赞动态（新接口）
  likePostNew: (postCode, operationType) => post('/moments/posts/like', { postCode, operationType }),
  // 删除动态
  deletePost: postId => del(`/post/${postId}`),
  // 获取用户动态列表
  getUserPosts: (userId, params) => get(`/post/user/${userId}`, params),
  // 获取浏览列表
  queryBrowePage: params => get(`/user/browse/queryPage`, params),
  // 上传浏览
  uploadBrowse: params => get(`/user/browse/view`, params)
}

// 评论相关接口
export const commentApi = {
  // 发表评论
  createComment: data => post('/comment/create', data),
  // 添加评论（新接口）
  addComment: data => post('/moments/comment/add', data),
  // 获取评论列表
  getComments: params => get('/comment/list', params),
  // 获取评论列表（新接口）
  getCommentList: data => post('/moments/comment/list', data),
  // 删除评论
  deleteComment: commentId => del(`/comment/${commentId}`),
  // 删除评论（新接口）
  deleteCommentNew: data => post('/moments/comment/delete', data),
  // 点赞评论
  likeComment: commentCode => post(`/comment/${commentCode}/like`),
  // 取消点赞评论
  unlikeComment: commentId => del(`/comment/like/${commentId}`)
}

// 消息相关接口
export const messageApi = {
  //获取长连接
  getRoute: params => get('/im/route', params),
  // 注册token
  registerToken: params => post('/im/user/token', params),
  // 获取消息列表
  getMessages: params => get('/message/list', params),
  // 获取未读消息数
  getUnreadCount: () => get('/message/unread'),
  // 标记消息已读
  markRead: messageId => put(`/message/read/${messageId}`),
  // 标记所有消息已读
  markAllRead: () => put('/message/read/all'),
  // 删除消息
  deleteConversation: params => post('/im/conversations/delete', params)
}

// 话题相关接口
export const topicApi = {
  // 获取话题列表
  getTopics: params => get('/topic/list', params),
  // 获取话题详情
  getTopicDetail: topicId => get(`/topic/${topicId}`),
  // 关注话题
  followTopic: topicId => post(`/topic/follow/${topicId}`),
  // 取消关注话题
  unfollowTopic: topicId => del(`/topic/follow/${topicId}`),
  // 获取话题下的动态
  getTopicPosts: (topicId, params) => get(`/topic/${topicId}/posts`, params)
}

// 搜索相关接口
export const searchApi = {
  // 搜索用户
  searchUsers: params => get('/search/users', params),
  // 搜索动态
  searchPosts: params => get('/search/posts', params),
  // 搜索话题
  searchTopics: params => get('/search/topics', params),
  // 获取热门搜索
  getHotSearches: () => get('/search/hot'),
  // 获取搜索建议
  getSuggestions: keyword => get('/search/suggest', { keyword })
}

// 文件上传相关接口
export const fileApi = {
  // 上传单个文件（文件路径）
  uploadOne: (filePath, bucket = 'chat') =>
    upload('/common/file/uploadOne', filePath, {
      formData: { bucket }
    }),
  // 上传单个文件（File对象）
  uploadFileObj: (fileObj, bucket = 'chat') =>
    uploadFile('/common/file/uploadOne', fileObj, {
      formData: { bucket }
    }),
  // 上传多个文件
  uploadMultiple: (filePaths, bucket = 'chat') => {
    const promises = filePaths.map(filePath =>
      upload('/common/file/uploadOne', filePath, {
        formData: { bucket }
      })
    )
    return Promise.all(promises)
  }
}

// 会话相关接口
export const conversationApi = {
  // 同步会话列表
  syncConversations: async params => {
    return await post('/im/conversation/sync', params)
  },
  getUserInfo: async params => {
    return await post('/user/queryBatch', params)
  },
  syncMessages: async params => {
    return await post('/im/channel/messagesync', params)
  },
  clearUnread: async params => {
    return await post('/im/conversations/setUnread', params)
  },
  // 置顶会话
  doTopConversation: async params => {
    return await get('/userData/topConversation', params)
  },
  // 获取置顶id列表
  getTopConversation: async params => {
    return await get('/userData/getTopConversation', params)
  }
}
//群聊相关api
export const groupsApi = {
  getGroups: async params => {
    return await post('/im/groups/searchAll', params)
  },
  joinGroup: async params => {
    return await post('/im/groups/join', params)
  },
  leaveGroup: async params => {
    return await post('/im/groups/leave', params)
  },
  getGroupInfo: async params => {
    return await post('/im/groups/leave', params)
  },
  getGroupMembers: async params => {
    return await post(`/im/groups/groupMembers?groupCode=${params.groupCode}`)
  },
  getGroupDetail: async params => {
    return await post(`/im/groups/queryBatch`, params)
  }
}
// 首页相关接口
export const homeApi = {
  // 获取推荐列表
  getRecommendList: params => post('/home/<USER>/recommend', params),
  // 获取新用户列表
  getNewUserList: params => post('/home/<USER>/queryNewUserList', params),
  // 获取关注列表
  getFollowList: params => post('/home/<USER>/queryFollowList', params)
}

// 动态广场相关接口
export const momentsApi = {
  // 获取动态广场列表
  getMomentsList: params => get('/moments/square', params),
  // 获取用户个人动态列表
  getUserMomentsList: (userId, params) => get(`/moments/user/${userId}`, params)
}
// 招募接口
export const recruitApi = {
  // 获取活动列表
  getRecruitList: params => post('/recruitments/list', params),
  getRecruitListMy: params => post('/recruitments/mylist', params),
  // 获取活动详情
  getRecruitDetail: recruitId => get(`/recruitments/detail/${recruitId}`),
  // 加入活动
  joinActivity: params => post('/recruitments/applications', params),
  // 参与人退出活动
  leaveActivity: (applicationId, params) =>
    post(`/recruitments/applications/${applicationId}/cancel`, params),
  // 队长取消招募
  cancelRecruit: (activityCode, params) =>
    post(`/recruitments/${activityCode}/cancel`, params),
  // 队长完成招募
  completeRecruit: (activityCode, params) =>
    post(`/recruitments/activities/${activityCode}/complete`, params),
  // 同意/拒绝
  agreeOrReject: (applicationId, params) =>
    post(`/recruitments/applications/${applicationId}/action`, params),
  // 队长踢出
  kickOut: (applicationId, params) =>
    post(`/recruitments/applications/${applicationId}/kickout`, params),
  // 获取技能模板列表
  getSkillTemplates: params => get('/skill/queryAllTemplates', params),
  createActivity: params => post('/recruitments/createActivity', params)
}

// 招募订单相关接口
export const recruitOrderApi = {
  // 搜索招募订单
  searchOrders: params => get('/recruit/orders/search', params),
  // 获取招募订单列表（使用和招募页面相同的接口）
  getOrderList: params => post('/recruitments/mylist', params),
  // 获取全部招募订单
  getAllOrders: params => post('/recruitments/mylist', { status: '', pageSize: 20, ...params }),
  // 获取招募中订单
  getRecruitingOrders: params => post('/recruitments/mylist', { status: 1, pageSize: 20, ...params }),
  // 获取招募成功订单
  getSuccessOrders: params => post('/recruitments/mylist', { status: 2, pageSize: 20, ...params }),
  // 获取招募失败订单
  getFailedOrders: params => post('/recruitments/mylist', { status: 3, pageSize: 20, ...params }),
  // 获取招募订单详情
  getOrderDetail: orderId => get(`/recruit/orders/detail/${orderId}`)
}

// 商城相关接口
export const shopApi = {
  getShopsList: params => get('/merchant/app/list', params),
  getGoodList: params => post('/good/app/list', params),
  getGoodDetail: params => get('/good/app/detail', params),
  addOrder: params => post('/order/create', params),
  getMerchantList: params => get('/mall/review/merchant/page', params),
  getMerchantPositiveCount: params => get('/mall/review/merchant/positive-count', params)
}

// 商城订单相关接口
export const shopOrderApi = {
  // 搜索商城订单
  searchOrders: params => get('/shop/orders/search', params),
  // 获取商城订单列表
  getOrderList: params => get('/shop/orders/list', params),
  // 获取商城订单详情
  getOrderDetail: orderId => get(`/shop/orders/detail/${orderId}`)
}

// 支付相关接口
export const payApi = {
  initiatePayment: params => post('/client/order/initiatePayment', params),
  getWallet: params => get('/wallet/info', params),
  getWalletRecord: params => get('/walletRecord/list', params),
  getQuotaList: params => get('/quota/list', params),
  // initiatePaymentById: params => post('/client/order/initiatePaymentById', params),
  recharge: params => post('/wallet/recharge', params),
  initiatePaymentById: params => get('/order/repay?orderCode='+params.orderNo, params),
}
// 订单相关接口
export const orderApi = {
  getOrderList: params => get('/order/app/list', params),
  createOrder: params => post('/order/create', params),
  getOrderDetail: params => get('/order/personal/detail/' + params.orderId),
  // cancelOrder: params => post('/client/order/cancel', params),
  verifyOrder: params => post('/verification/verify', params),
  getOrderDetailByOrderNo: params => get('/mall/review/check', params),
  reviewSubmit: params => post('/review/submit', params),
  getRefundList: params => get('/refund/list', params),
  refundApply: params => post('/refund/apply', params),
  getRefundDetail: params => get('/refund/' + params.id, params),
  refundProcess: params => post('/refund/process', params),
  confirmOrder: params => post('/order/accept/' + params.orderNo),
  workedOrder: params => post('/order/worked/' + params.orderNo),
  cancelOrder: params => post('/order/cancel/' + params.orderNo),
  getOrderCount: params => get('/order/count', params),
}
// 达人技能相关接口
export const skillApi = {
  // 获取技能列表
  queryAllTemplates: () => get('/skill/queryAllTemplates'),
  // 查询达人技能列表
  getSkillsList: params => get('/daren/skills/list', params),
  // 修改，申请
  addSkillApply: params => post('/daren/skills/batchApply', params),
  // 修改，申请
  updateSkill: params => post('/daren/skills/apply', params),
  // 获取审核记录
  getReviewRecord: params => get('/daren/skills/audits', params),
  // 交通费
  getTravelCost: params => get('/userData/queryTrafficConfig', params),
  // 更新交通费
  updateTravelCost: params => post('/userData/saveTrafficConfig', params)

}
// 达人技能相关接口
export const settingApi = {
  // 保存联系人
  saveEmergencyContact: params => post('/userData/saveEmergencyContact', params),
  // 获取联系人
  getEmergencyContact: params => get('/userData/queryEmergencyContact', params),


}

// 技能订单相关接口
export const skillOrderApi = {
  // 搜索技能订单
  searchOrders: params => get('/skill/orders/daren/list', params),
  // 获取技能订单列表（分页查询）
  getOrderList: params => get('/skill/orders/daren/list', params),
  // 获取全部技能订单
  getAllOrders: params => get('/skill/orders/daren/list', { pageNum: 1, pageSize: 20, ...params }),
  // 获取待接单订单
  getWaitingOrders: params => get('/skill/orders/daren/list', { pageNum: 1, pageSize: 20, statusFilter: 'pendingAccept', ...params }),
  // 获取待开始订单
  getBeforeStartOrders: params => get('/skill/orders/daren/list', { pageNum: 1, pageSize: 20, statusFilter: 'pendingStart', ...params }),
  // 获取进行中订单
  getDoingOrders: params => get('/skill/orders/daren/list', { pageNum: 1, pageSize: 20, statusFilter: 'inProgress', ...params }),
  // 获取已完成订单
  getDoneOrders: params => get('/skill/orders/daren/list', { pageNum: 1, pageSize: 20, statusFilter: 'completed', ...params }),
    // 获取已取消订单
  getCancelOrders: params => get('/skill/orders/daren/list', { pageNum: 1, pageSize: 20, statusFilter: 'cancelled', ...params }),
  // 获取退款/售后订单
  getRefundingOrders: params => get('/skill/orders/daren/list', { pageNum: 1, pageSize: 20, statusFilter: 'refunded', ...params }),
  // 获取技能订单详情
  getOrderDetail: orderId => get(`/skill/orders/daren/detail/${orderId}`),
  // 获取技能订单详情（通过订单号）
  getOrderDetailByOrderNo: orderNo => get(`/skill/orders/detail/${orderNo}`),
  // 接单
  acceptOrder: orderId => post(`/skill/orders/daren/accept/${orderId}`),
  // 开始服务
  startService: orderId => post(`/skill/orders/daren/start/${orderId}`),
  // 完成服务
  completeService: orderId => post(`/skill/orders/daren/complete/${orderId}`),
  // 取消订单
  cancelOrder: (orderId, params) => post(`/skill/orders/daren/cancel/${orderId}`, params),
  // 获取各状态技能订单数量
  getSkillOrderNumByStatus: () => get('/skill/orders/daren/statusCount'),
  // 查询技能订单列表
  getSkillOrderList: params => get('/skill/orders/daren/list', params),
  // 查询技能订单详情
  getSkillOrderDetail: orderId => get(`/skill/orders/detail/${orderId}`)
}

