/**
 * 格式化时间
 * @param {Date|number|string} time 时间对象或时间戳
 * @param {string} format 格式化模式 默认 YYYY-MM-DD HH:mm:ss
 * @returns {string} 格式化后的时间字符串
 */
export const formatTime = (time, format = 'YYYY-MM-DD HH:mm:ss') => {
    if (!time) return '';
    const date = new Date(time);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();

    const formatNumber = n => {
        n = n.toString();
        return n[1] ? n : `0${n}`;
    };

    return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => {
        switch (match) {
            case 'YYYY':
                return year;
            case 'MM':
                return formatNumber(month);
            case 'DD':
                return formatNumber(day);
            case 'HH':
                return formatNumber(hour);
            case 'mm':
                return formatNumber(minute);
            case 'ss':
                return formatNumber(second);
            default:
                return match;
        }
    });
};

/**
 * 格式化相对时间
 * @param {Date|number|string} time 时间对象或时间戳
 * @returns {string} 相对时间字符串
 */
export const formatRelativeTime = (time) => {
    if (!time) return '';
    const now = new Date().getTime();
    const timestamp = new Date(time).getTime();
    const diff = (now - timestamp) / 1000;

    if (diff < 60) {
        return '刚刚';
    } else if (diff < 3600) {
        return Math.floor(diff / 60) + '分钟前';
    } else if (diff < 86400) {
        return Math.floor(diff / 3600) + '小时前';
    } else if (diff < 2592000) {
        return Math.floor(diff / 86400) + '天前';
    } else {
        return formatTime(time, 'MM-DD');
    }
};

/**
 * 防抖函数
 * @param {Function} func 需要防抖的函数
 * @param {number} wait 等待时间
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, wait = 500) => {
    let timeout;
    return function (...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(this, args);
        }, wait);
    };
};

/**
 * 节流函数
 * @param {Function} func 需要节流的函数
 * @param {number} wait 等待时间
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, wait = 500) => {
    let timeout;
    let previous = 0;
    return function (...args) {
        const now = Date.now();
        const remaining = wait - (now - previous);
        if (remaining <= 0) {
            clearTimeout(timeout);
            timeout = null;
            previous = now;
            func.apply(this, args);
        } else if (!timeout) {
            timeout = setTimeout(() => {
                previous = Date.now();
                timeout = null;
                func.apply(this, args);
            }, remaining);
        }
    };
};

/**
 * 检查图片URL是否有效
 * @param {string} url 图片URL
 * @returns {Promise<boolean>} 是否有效
 */
export const checkImageUrl = (url) => {
    return new Promise((resolve) => {
        uni.getImageInfo({
            src: url,
            success: () => resolve(true),
            fail: () => resolve(false)
        });
    });
};

/**
 * 获取文件扩展名
 * @param {string} filename 文件名
 * @returns {string} 扩展名
 */
export const getFileExt = (filename) => {
    if (!filename) return '';
    return filename.substring(filename.lastIndexOf('.') + 1);
};

/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (size) => {
    if (!size) return '0B';
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let index = 0;
    while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
    }
    return Math.round(size * 100) / 100 + units[index];
};

/**
 * 深拷贝对象
 * @param {Object} obj 需要拷贝的对象
 * @returns {Object} 拷贝后的对象
 */
export const deepClone = (obj) => {
    if (obj === null || typeof obj !== 'object') return obj;
    const clone = Array.isArray(obj) ? [] : {};
    for (let key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            clone[key] = deepClone(obj[key]);
        }
    }
    return clone;
};

/**
 * 生成UUID
 * @returns {string} UUID
 */
export const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};

/**
 * 检查是否为空对象
 * @param {Object} obj 对象
 * @returns {boolean} 是否为空
 */
export const isEmptyObject = (obj) => {
    return Object.keys(obj).length === 0;
};

/**
 * 获取当前页面路由
 * @returns {string} 当前页面路由
 */
export const getCurrentPageRoute = () => {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    return currentPage ? currentPage.route : '';
};

/**
 * 显示消息提示框
 * @param {string} title 提示内容
 * @param {string} icon 图标类型
 * @returns {Promise} Promise对象
 */
export const showToast = (title, icon = 'none') => {
    return new Promise((resolve) => {
        uni.showToast({
            title,
            icon,
            success: resolve
        });
    });
}; 
/*
* 金额格式化
* @param {number} amount 金额
* @returns {string} 格式化后的金额  
*/
export const formatAmount = (amount=0) => {
    return (amount/100).toFixed(2);
};

/**
 * 获取版本号
 * @returns {string} 版本号
 */
export const getVersion = () => {
	const year = new Date().getFullYear()
	const month = new Date().getMonth() + 1
	const day = new Date().getDate()
	return `1.0.0.${year.toString().slice(-2)}${month>9?month:'0'+month}${day>9?day:'0'+day}_beta`
};

// 获取两个时间戳隔间时间
export const getTimeDiff = (startTimeStamp, endTimeStamp) => {
	const timeDiff = endTimeStamp - startTimeStamp
	let seconds = timeDiff / 1000
	let minutes = Math.floor(seconds / 60)
	let hours = Math.floor(minutes / 60)
	
	seconds %= 60
	minutes %= 60
	return { hours, minutes, seconds }
}

