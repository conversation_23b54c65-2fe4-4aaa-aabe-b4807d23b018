// 技能相关常量配置

// 技能名称映射
export const SKILL_NAMES = {
  1: '美食向导',
  2: '搭子向导',
  3: '旅游向导',
  4: '陪护向导',
  5: '跟拍向导',
  6: '法律咨询',
  7: '医院陪伴',
  8: '家政服务',
  9: '导游向导',
  10: '音乐向导',
  11: '运动向导',
  12: '游戏向导'
}

// 技能等级映射
export const SKILL_LEVELS = {
  BEGINNER: '入门',
  INTERMEDIATE: '熟练',
  ADVANCED: '专业级',
  EXPERT: '专家级'
}

// 根据价格判断技能等级
export const getSkillLevelByPrice = (price) => {
  if (price >= 500) {
    return SKILL_LEVELS.EXPERT
  } else if (price >= 300) {
    return SKILL_LEVELS.ADVANCED
  } else if (price >= 200) {
    return SKILL_LEVELS.INTERMEDIATE
  } else {
    return SKILL_LEVELS.BEGINNER
  }
}

// 技能图标映射 (暂时用emoji占位)
export const SKILL_ICONS = {
  1: '🍽️',
  2: '👥',
  3: '🗺️',
  4: '🏥',
  5: '📸',
  6: '⚖️',
  7: '🏥',
  8: '🏠',
  9: '🗺️',
  10: '🎵',
  11: '⚽',
  12: '🎮'
}

// 技能状态映射
export const SKILL_STATUS = {
  0: '下架',
  1: '上架'
}

// 审核状态映射
export const AUDIT_STATUS = {
  0: '待审核',
  1: '审核通过',
  2: '审核驳回'
}

// 技能订单状态
export const SKILL_ORDER_STATUS = {
	TO_AGREE: 10, // 待同意 
	TO_ALLOCATE: 15, // 待分配
	TO_BEGIN: 20, // 待开始
	IN_PROGRESS: 30, // 进行中 
	TO_EVALUATE: 40, // 待评价 
	COMPLETE: 50, // 已完成 
	IN_DISPUTE: 60, // 争议中 
	USER_CANCEL: 90, // 用户取消 
	EXPERT_REFUSE: 91, // 达人拒单
	TIMEOUT: 92, // 超时未接单
	REFUNDING: 93, // 退款中
	REFUNDED: 94, // 已退款 
}

// 技能订单状态
export const SKILL_ORDER_STATUS_DESC = {
	TO_AGREE: '待同意', // 待同意
	TO_ALLOCATE: '待分配', // 待分配
	TO_BEGIN: '待开始', // 待开始
	IN_PROGRESS: '进行中', // 进行中 
	TO_EVALUATE: '待评价', // 待评价 
	COMPLETE: '已完成', // 已完成 
	// IN_DISPUTE: 60, // 争议中 
	USER_CANCEL: '已取消', // 用户取消 
	EXPERT_REFUSE: '已拒单', // 达人拒单
	TIMEOUT: '已超时', // 超时未接单
	REFUNDING: '退款中', // 退款中
	REFUNDED: '已退款', // 已退款 
}