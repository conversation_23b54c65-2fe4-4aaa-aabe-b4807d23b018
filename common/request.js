import { showToast } from './utils';

// 配置基础URL
const BASE_URL = 'http://47.116.217.156:8020/api';
// const BASE_URL = 'http://192.168.3.218:8020/api';


// 全局token管理
let globalToken = null;

/**
 * 设置全局token
 * @param {string} token 新的token值
 */
export const setGlobalToken = (token) => {
    globalToken = token;
    uni.setStorageSync('token', token);
    console.log('全局token已更新:', token ? '已设置' : '已清除');
};

/**
 * 获取全局token
 * @returns {string} 当前token值
 */
export const getGlobalToken = () => {
    // 优先使用内存中的token
    if (globalToken) {
        return globalToken;
    }
    
    // 从storage获取
    const storageToken = uni.getStorageSync('token');
    if (storageToken) {
        globalToken = storageToken;
        return storageToken;
    }
    
    // 尝试从loginInfo获取
    const loginInfo = uni.getStorageSync('loginInfo');
    if (loginInfo && loginInfo.tokenValue) {
        globalToken = loginInfo.tokenValue;
        uni.setStorageSync('token', globalToken);
        return globalToken;
    }
    
    return null;
};

// 请求拦截器
const requestInterceptor = (config) => {
    // 获取最新的token
    const token = getGlobalToken();
    
    // 设置token到请求头
    if (token) {
        config.header = {
            ...config.header,
            token: token
        };
    }
    
    // 添加请求日志（开发环境）
    console.log('请求拦截器 - URL:', config.url, 'Token:', token ? '已设置' : '未设置');
    
    return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
    const { statusCode, data } = response;
    console.log(response, '--response')
    // 请求成功
    if (statusCode === 200) {
        if(data && data.code === 403){
            uni.reLaunch({
                url: '/pages/login/login'
            });
            throw new Error('请先登录');
        }
        if(data && data.code === 500){
            showToast(data.msg);
            throw new Error(data.msg);
        }
        return data;
    }
    
    // 未授权
    if (statusCode === 401) {
        // 清除所有token相关数据
        setGlobalToken(null);
        uni.removeStorageSync('loginInfo');
        uni.removeStorageSync('userInfo');
        
        // 使用reLaunch而不是navigateTo，防止用户返回到需要登录的页面
        uni.reLaunch({
            url: '/pages/login/login'
        });
        throw new Error('请先登录');
    }
    console.log(response)
    
    // 其他错误
    const error = new Error(data.message || '请求失败');
    error.response = response;
    throw error;
};

/**
 * 封装请求方法
 * @param {Object} options 请求配置
 * @returns {Promise} Promise对象
 */
const request = (options) => {
    // 判断是否为完整URL，如果以http开头则不拼接基础URL
    const isFullUrl = options.url.startsWith('http://') || options.url.startsWith('https://');
    const requestUrl = isFullUrl ? options.url : `${BASE_URL}${options.url}`;
    
    const config = requestInterceptor({
        ...options,
        url: requestUrl,
        header: {
            'Content-Type': 'application/json',
            ...options.header
        }
    });

    return new Promise((resolve, reject) => {
        uni.request({
            ...config,
            success: (response) => {
                try {
                    const result = responseInterceptor(response);
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            },
            fail: (error) => {
                showToast('网络请求失败');
                reject(error);
            }
        });
    });
};

/**
 * GET请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他配置
 * @returns {Promise} Promise对象
 */
export const get = (url, data = {}, options = {}) => {
    return request({
        url,
        data,
        method: 'GET',
        ...options
    });
};

/**
 * POST请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他配置
 * @returns {Promise} Promise对象
 */
export const post = (url, data = {}, options = {}) => {
    return request({
        url,
        data,
        method: 'POST',
        ...options
    });
};

/**
 * PUT请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他配置
 * @returns {Promise} Promise对象
 */
export const put = (url, data = {}, options = {}) => {
    return request({
        url,
        data,
        method: 'PUT',
        ...options
    });
};

/**
 * DELETE请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他配置
 * @returns {Promise} Promise对象
 */
export const del = (url, data = {}, options = {}) => {
    return request({
        url,
        data,
        method: 'DELETE',
        ...options
    });
};

/**
 * 上传文件
 * @param {string} url 上传地址
 * @param {string} filePath 文件路径
 * @param {Object} options 其他配置
 * @returns {Promise} Promise对象
 */
export const upload = (url, filePath, options = {}) => {
    const config = requestInterceptor({
        ...options,
        url: `${BASE_URL}${url}`
    });

    let finalFilePath = filePath;
    return new Promise((resolve, reject) => {
        // #ifdef APP-PLUS
        // 原生包环境下的路径处理
        try {
            // 检查文件是否存在
            plus.io.resolveLocalFileSystemURL(
                filePath,
                () => {
                    // 文件存在，检查路径格式
                    if (!filePath.startsWith('file:///')) {
                        const convertedPath = plus.io.convertLocalFileSystemURL(filePath);
                        if (convertedPath) {
                            finalFilePath = convertedPath;
                        }
                    }
                    performUpload();
                },
                () => {
                    // 文件不存在，尝试转换路径
                    const convertedPath = plus.io.convertLocalFileSystemURL(filePath);
                    if (convertedPath) {
                        finalFilePath = convertedPath;
                    }
                    performUpload();
                }
            );
        } catch (error) {
            console.warn('原生包路径处理失败，使用原始路径:', error);
            performUpload();
        }
        // #endif
        
        // #ifndef APP-PLUS
        performUpload();
        // #endif
        
        function performUpload() {
            uni.uploadFile({
                ...config,
                filePath: finalFilePath,
                name: 'file',
                formData: options.formData || {}, // 支持额外的表单数据
                success: (response) => {
                    try {
                        // 上传接口返回的data可能是字符串，需要解析
                        if (typeof response.data === 'string') {
                            try {
                                response.data = JSON.parse(response.data);
                            } catch (e) {
                                console.warn('解析上传响应JSON失败:', e);
                            }
                        }
                        
                        const result = responseInterceptor(response);
                        resolve(result);
                    } catch (error) {
                        reject(error);
                    }
                },
                fail: (error) => {
                    showToast('上传失败');
                    reject(error);
                }
            });
        }
    });
};

/**
 * 上传文件（File对象）
 * @param {string} url 上传地址
 * @param {File} fileObj File对象
 * @param {Object} options 其他配置
 * @returns {Promise} Promise对象
 */
export const uploadFile = (url, fileObj, options = {}) => {
    const config = requestInterceptor({
        ...options,
        url: `${BASE_URL}${url}`
    });

    return new Promise((resolve, reject) => {
        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', fileObj);
        
        // 添加额外的表单数据
        if (options.formData) {
            Object.keys(options.formData).forEach(key => {
                formData.append(key, options.formData[key]);
            });
        }

        // 使用XMLHttpRequest进行上传（支持File对象）
        const xhr = new XMLHttpRequest();
        
        xhr.open('POST', config.url, true);
        
        // 设置请求头（必须在open之后）
        if (config.header) {
            Object.keys(config.header).forEach(key => {
                if (key.toLowerCase() !== 'content-type') { // 让浏览器自动设置content-type
                    xhr.setRequestHeader(key, config.header[key]);
                }
            });
        }
        
        xhr.onload = () => {
            try {
                let responseData = xhr.responseText;
                try {
                    responseData = JSON.parse(responseData);
                } catch (e) {
                    console.warn('解析上传响应JSON失败:', e);
                }
                
                const response = {
                    statusCode: xhr.status,
                    data: responseData
                };
                
                const result = responseInterceptor(response);
                resolve(result);
            } catch (error) {
                reject(error);
            }
        };
        
        xhr.onerror = () => {
            showToast('上传失败');
            reject(new Error('网络错误'));
        };
        
        xhr.send(formData);
    });
}; 


/**
 * 上传文件
 * @param {string} url 上传地址
 * @param {string} filePath 文件路径
 * @param {Object} options 其他配置
 * @returns {Promise} Promise对象
 */
export const uploads = (url, filePath, options = {}) => {
    const config = requestInterceptor({
        ...options,
        url: `${BASE_URL}${url}`
    });

    let finalFilePath = filePath;
    return new Promise((resolve, reject) => {
        // #ifdef APP-PLUS
        // 原生包环境下的路径处理
        try {
            // 检查文件是否存在
            plus.io.resolveLocalFileSystemURL(
                filePath,
                () => {
                    // 文件存在，检查路径格式
                    if (!filePath.startsWith('file:///')) {
                        const convertedPath = plus.io.convertLocalFileSystemURL(filePath);
                        if (convertedPath) {
                            finalFilePath = convertedPath;
                        }
                    }
                    performUpload();
                },
                () => {
                    // 文件不存在，尝试转换路径
                    const convertedPath = plus.io.convertLocalFileSystemURL(filePath);
                    if (convertedPath) {
                        finalFilePath = convertedPath;
                    }
                    performUpload();
                }
            );
        } catch (error) {
            console.warn('原生包路径处理失败，使用原始路径:', error);
            performUpload();
        }
        // #endif
        
        // #ifndef APP-PLUS
        performUpload();
        // #endif
        
        function performUpload() {
            uni.uploadFile({
                ...config,
                filePath: finalFilePath,
                name: 'files',
                formData: options.formData || {}, // 支持额外的表单数据
                success: (response) => {
                    try {
                        // 上传接口返回的data可能是字符串，需要解析
                        if (typeof response.data === 'string') {
                            try {
                                response.data = JSON.parse(response.data);
                            } catch (e) {
                                console.warn('解析上传响应JSON失败:', e);
                            }
                        }
                        
                        const result = responseInterceptor(response);
                        resolve(result);
                    } catch (error) {
                        reject(error);
                    }
                },
                fail: (error) => {
                    showToast('上传失败');
                    reject(error);
                }
            });
        }
    });
};