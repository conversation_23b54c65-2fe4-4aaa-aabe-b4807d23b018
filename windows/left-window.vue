<template>
	<view class="left-window-style">
		<view class="second-menu">
			<component v-bind:is="active" :hasLeftWin="hasLeftWin" :leftWinActive="leftWinActive"></component>
		</view>
	</view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, getCurrentInstance } from 'vue'
import { useRoute } from 'vue-router'
import componentPage from '@/pages/tabBar/component/component.nvue'
import API from '@/pages/tabBar/API/API.nvue'
import extUI from '@/pages/tabBar/extUI/extUI.nvue'
import templatePage from '@/pages/tabBar/template/template.nvue'
import { useUserStore } from '@/stores/user'

export default {
	components: {
		componentPage,
		API,
		extUI,
		templatePage
	},
	setup() {
		const route = useRoute()
		const userStore = useUserStore()
		
		const nav = ['component', 'API', 'extUI', 'template']
		const isPC = ref(false)
		const leftWinActive = ref(false)
		const matchLeftWindow = ref(true)
		const active = ref(false)
		
		let isPcObserver, isPhoneObserver
		
		const hasLeftWin = computed(() => {
			return matchLeftWindow.value
		})
		
		const setMatchLeftWindow = (match) => {
			matchLeftWindow.value = match
		}
		
		const setActive = (activeValue) => {
			active.value = activeValue
		}
		
		const setLeftWinActive = (activeValue) => {
			leftWinActive.value = activeValue
		}
		
		const handlerRoute = (newRoute) => {
			if (isPC.value) {
				if (newRoute.path === '/') {
					// uni.redirectTo({
					// 	url: '/pages/component/view/view'
					// })
				} else if (!newRoute.matched.length) {
					uni.redirectTo({
						url: '/pages/error/404'
					})
				} else {
					setLeftWinActive(newRoute.path)
					let activeValue = newRoute.path.split('/')[2]
					if (nav.includes(activeValue)) {
						if (activeValue === 'component') {
							activeValue = 'componentPage'
						}
						if (activeValue === 'template') {
							activeValue = 'templatePage'
						}
						setActive(activeValue)
					}
				}
			}
		}
		
		onMounted(() => {
			// #ifdef MP
			const instance = getCurrentInstance()
			if (!instance || !instance.proxy) return
			const proxy = instance.proxy

			isPcObserver = uni.createMediaQueryObserver()
			isPhoneObserver = uni.createMediaQueryObserver()

			isPcObserver.observe(proxy, {
				minWidth: 768
			}, matched => {
				isPC.value = matched
				const pageUrl = route.path
				if (!pageUrl) return
				const pageName = route.path.split('/')[4]
				if (pageUrl === '/' || nav.includes(pageName)) {
					const tabbarUrl = pageName ? (pageName === 'component' ? '/' : `/pages/tabBar/${pageName}/${pageName}`) : '/'
					if (pageUrl === '/' || pageUrl === tabbarUrl) {
						uni.switchTab({
							url: pageUrl,
						})
					}
				} else {
					uni.reLaunch({
						url: pageUrl
					})
				}
			})

			isPhoneObserver.observe(proxy, {
				maxWidth: 767
			}, matched => {
				isPC.value = !matched
				if (matched) {
					const pageUrl = route.path
					const tabbarName = route.path.split('/')[2]
					const tabbarUrl = tabbarName && (tabbarName === 'component' ? '/' : `/pages/tabBar/${tabbarName}/${tabbarName}`)
					uni.switchTab({
						url: tabbarUrl,
						success(e) {
							uni.navigateTo({
								url: pageUrl
							})
						}
					})
				}
			})
			// #endif

			// #ifdef H5
			const pcMedia = window.matchMedia('(min-width: 768px)')
			const phoneMedia = window.matchMedia('(max-width: 767px)')

			const pcListener = (e) => {
				isPC.value = e.matches
				const pageUrl = route.path
				if (!pageUrl) return
				const pageName = route.path.split('/')[4]
				if (pageUrl === '/' || nav.includes(pageName)) {
					const tabbarUrl = pageName ? (pageName === 'component' ? '/' : `/pages/tabBar/${pageName}/${pageName}`) : '/'
					if (pageUrl === '/' || pageUrl === tabbarUrl) {
						uni.switchTab({
							url: pageUrl,
						})
					}
				} else {
					uni.reLaunch({
						url: pageUrl
					})
				}
			}
			const phoneListener = (e) => {
				isPC.value = !e.matches
				if (e.matches) {
					const pageUrl = route.path
					const tabbarName = route.path.split('/')[2]
					const tabbarUrl = tabbarName && (tabbarName === 'component' ? '/' : `/pages/tabBar/${tabbarName}/${tabbarName}`)
					uni.switchTab({
						url: tabbarUrl,
						success(e) {
							uni.navigateTo({
								url: pageUrl
							})
						}
					})
				}
			}

			pcMedia.addEventListener('change', pcListener)
			phoneMedia.addEventListener('change', phoneListener)
			// 初始化
			isPC.value = pcMedia.matches

			onUnmounted(() => {
				pcMedia.removeEventListener('change', pcListener)
				phoneMedia.removeEventListener('change', phoneListener)
			})
			// #endif
		})
		
		watch(
			() => isPC.value,
			(newMatches) => {
				setMatchLeftWindow(newMatches)
			},
			{ immediate: true }
		)
		
		watch(
			() => route,
			(newRoute) => {
				handlerRoute(newRoute)
			}
		)
		
		return {
			hasLeftWin,
			leftWinActive,
			active
		}
	}
}
</script>

<style>
	@import '../common/uni-nvue.css';

	.left-window-style {
		min-height: calc(100vh - var(--top-window-height));
		background-color: #f8f8f8;
	}

	.second-menu {
		width: 350px;
		background-color: #F8F8F8;
	}

	.icon-image {
		width: 30px;
		height: 30px;
	}
</style>
