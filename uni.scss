/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.custom-nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  height: 88rpx;
  // border-bottom: 1rpx solid #eaeaea;
  background: transparent !important;
  padding-top: env(safe-area-inset-top);
  .nav-left,
  .nav-right {
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .nav-back-icon {
    font-size: 48rpx;
    font-weight: bold;
  }

  .nav-menu-icon {
    font-size: 32rpx;
    color: #999;
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
/* 颜色变量 */
$color-primary: #007aff;
$color-success: #4cd964;
$color-warning: #f0ad4e;
$color-error: #dd524d;
$color-danger: #dd524d; // Alias for error color

$text-color: #333333;
$text-color-inverse: #ffffff;
$text-color-grey: #999999;
$text-color-placeholder: #808080;
$text-color-disable: #c0c0c0;

$bg-color: #f8f8f8;
$bg-color-grey: #f5f5f5;
$bg-color-hover: #f1f1f1;
$bg-color-mask: rgba(0, 0, 0, 0.4);

/* 边框颜色 */
$border-color: #c8c7cc;
$border-color-light: #eee;

/* 尺寸变量 */
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;

/* 图片尺寸 */
$img-size-sm: 40rpx;
$img-size-base: 80rpx;
$img-size-lg: 120rpx;

/* 水平间距 */
$spacing-row-sm: 10rpx;
$spacing-row-base: 20rpx;
$spacing-row-lg: 30rpx;

/* 垂直间距 */
$spacing-col-sm: 8rpx;
$spacing-col-base: 16rpx;
$spacing-col-lg: 24rpx;

/* 边框圆角 */
$border-radius-sm: 4rpx;
$border-radius-base: 8rpx;
$border-radius-lg: 12rpx;
$border-radius-circle: 50%;

/* 阴影 */
$shadow-sm: 0 0 5rpx rgba(0, 0, 0, 0.1);
$shadow-base: 0 1rpx 5rpx rgba(0, 0, 0, 0.15);
$shadow-lg: 0 3rpx 10rpx rgba(0, 0, 0, 0.2);

/* 混合宏 */
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin text-ellipsis($lines: 1) {
  overflow: hidden;
  @if $lines == 1 {
    white-space: nowrap;
    text-overflow: ellipsis;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    white-space: normal;
  }
}

/* 通用样式 */
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-row-between{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.flex-column {
  @include flex-column;
}

.flex-center {
  @include flex-center;
}

.text-ellipsis {
  @include text-ellipsis;
}

.text-primary {
  color: $color-primary;
}

.bg-primary {
  background-color: $color-primary;
}

.border-bottom {
  border-bottom: 1rpx solid $border-color-light;
}

.page-container {
  // min-height: 100vh;
  background: url('/static/bg.png') no-repeat center center !important;
  background-size: cover !important;
  min-height: 100% !important;
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 26px;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 15px;

/* 隐藏滚动条但保持可滚动 */
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

/* 适配火狐浏览器 */
* {
  scrollbar-width: none;
}

/* 适配IE */
* {
  -ms-overflow-style: none;
}
